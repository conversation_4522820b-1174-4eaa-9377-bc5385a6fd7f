from django.contrib import messages
from django.shortcuts import get_object_or_404
from django.urls import reverse
from django.views.generic import UpdateView

from app.forms import VideoPreferencesForm
from app.models import Video


class VideoPreferencesView(UpdateView):
    model = Video
    form_class = VideoPreferencesForm

    def get_object(self, queryset=None):
        video = get_object_or_404(Video, asset__uuid=self.kwargs.get("asset_uuid"))
        return video

    def form_valid(self, form):
        response = super().form_valid(form)
        if self.object.use_global_player_preferences:
            self.object.use_global_player_preferences = False
            self.object.save()
        messages.success(
            self.request, "Your default video setting has been changed successfully."
        )
        return response

    def get_success_url(self):
        return reverse("asset_detail", kwargs={"asset_uuid": self.object.asset.uuid})
