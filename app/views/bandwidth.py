from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views.generic.list import ListView

from app.filters import AssetUsageFilter
from app.models import AssetUsage
from app.utils.filter import get_month_start_date


@method_decorator(login_required, name="dispatch")
class AssetUsageListView(ListView):
    model = AssetUsage
    context_object_name = "daily_usages"
    template_name = "settings/bandwidth.html"
    filter_set = AssetUsageFilter

    def get_queryset(self):
        queryset = AssetUsage.objects.filter(
            time_frame=AssetUsage.TimeFrames.DAILY,
        ).order_by("-date")
        self.filter = self.filter_set(self.request.GET, queryset=queryset)
        if self.filter.is_valid():
            queryset = self.filter.qs
        return queryset

    def get_context_data(self, **kwargs):
        month = self.request.GET.get("month")
        context = super().get_context_data(**kwargs)
        context["month"] = get_month_start_date(month)
        context["monthly_usage"] = self.get_this_month_usage(month)
        context["filter"] = self.filter
        return context

    def get_this_month_usage(self, month, **kwargs):
        month_start_date = get_month_start_date(month)
        return AssetUsage.objects.filter(
            time_frame=AssetUsage.TimeFrames.MONTHLY, date=month_start_date
        ).first
