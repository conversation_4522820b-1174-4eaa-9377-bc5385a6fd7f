import json

from django.contrib import messages
from django.shortcuts import get_object_or_404, redirect
from django.views.generic import View

from app.models import Asset, VideoChapter
from app.utils.datetime import convert_time_string_to_timedelta


class ChapterView(View):
    def dispatch(self, request, *args, **kwargs):
        self.asset = get_object_or_404(Asset, uuid=kwargs.get("asset_uuid"))
        return super().dispatch(request, *args, **kwargs)

    def post(self, request, asset_uuid):
        chapters, deleted_chapters = self.get_chapter_data_from_request(request)
        self.update_chapters(chapters)
        self.delete_chapter(deleted_chapters)

        messages.success(self.request, "Chapters have been updated successfully.")
        return redirect("asset_detail", asset_uuid=asset_uuid)

    def get_chapter_data_from_request(self, request):
        chapter_data = request.POST.get("chapter_data")
        deletedChapters_data = request.POST.get("deleteChapter_data")
        chapters = json.loads(chapter_data)
        deletedChapters = json.loads(deletedChapters_data)
        return chapters, deletedChapters

    def update_chapters(self, chapters):
        for chapter in chapters:
            chapter_id = chapter.get("id")
            start_time_str = chapter.get("starttime")
            chapter_title = chapter.get("title")
            start_time_delta = convert_time_string_to_timedelta(start_time_str)

            if chapter_id:
                self.update_existing_chapter(
                    chapter_id, start_time_delta, chapter_title
                )
            else:
                self.create_new_chapter(start_time_delta, chapter_title)

    def update_existing_chapter(self, chapter_id, start_time_delta, chapter_title):
        chapter = get_object_or_404(VideoChapter, id=chapter_id, video=self.asset.video)
        chapter.start_time = start_time_delta
        chapter.label = chapter_title
        chapter.save()

    def create_new_chapter(self, start_time_delta, chapter_title):
        VideoChapter.objects.create(
            start_time=start_time_delta,
            label=chapter_title,
            video=self.asset.video,
            organization=self.asset.organization,
        )

    def delete_chapter(self, chapters):
        VideoChapter.objects.filter(id__in=chapters, video=self.asset.video).delete()
