from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse
from django.utils.decorators import method_decorator
from django.views.generic import UpdateView, View

from app.domain.cloud_storage import delete_file
from app.domain.subtitle import upload_subtitle
from app.forms import SubtitleEditForm
from app.models import Asset
from app.models.track import Track


@method_decorator(login_required, name="dispatch")
class SubtitleUploadView(UpdateView):
    model = Asset
    slug_field = "uuid"
    slug_url_kwarg = "asset_uuid"

    def post(self, request, *args, **kwargs):
        language = request.POST.get("language") or "en"
        name = request.POST.get("name") or f"{language}_sub"
        uploaded_file = request.FILES.get("file")

        if uploaded_file and uploaded_file.name.endswith((".vtt", ".srt")):
            upload_subtitle(self.get_object(), name, language, uploaded_file)
            messages.success(self.request, "Subtitle uploaded")
        else:
            messages.error(
                self.request, "Invalid file format. Please upload a .vtt file."
            )
        return redirect(
            reverse("asset_detail", kwargs={"asset_uuid": self.get_object().uuid})
        )


@method_decorator(login_required, name="dispatch")
class SubtitleDeleteView(View):
    def post(self, request, asset_uuid, subtitle_id):
        asset = get_object_or_404(Asset, uuid=asset_uuid)
        subtitle = get_object_or_404(Track, id=subtitle_id, video=asset.video)
        if subtitle.subtitle_type != Track.SubtitleType.AUTO_GENERATED:
            delete_file(asset.organization, subtitle.url)
            subtitle.delete()
            messages.success(request, "Caption deleted successfully")
        return redirect(reverse("asset_detail", kwargs={"asset_uuid": asset.uuid}))


@method_decorator(login_required, name="dispatch")
class SubtitleUpdateView(UpdateView):
    form_class = SubtitleEditForm

    def get_object(self, queryset=None):
        asset = get_object_or_404(Asset, uuid=self.kwargs.get("asset_uuid"))
        subtitle = get_object_or_404(
            Track, pk=self.kwargs.get("subtitle_id"), video=asset.video
        )
        return subtitle

    def get_success_url(self):
        return reverse(
            "asset_detail", kwargs={"asset_uuid": self.object.video.asset.uuid}
        )
