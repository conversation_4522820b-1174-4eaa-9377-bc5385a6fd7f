from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.views import PasswordChangeView
from django.contrib.messages.views import SuccessMessageMixin
from django.urls import reverse_lazy
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from django.views.generic import TemplateView, UpdateView

from app.forms import CustomPasswordChangeForm, OrganizationVideoPreferencesForm
from app.forms.user import UserForm
from app.forms.video_embedding import VideoEmbeddingSettingsForm
from app.models import Organization
from app.models.zoom import ZoomAccount
from app.utils.http import AuthenticatedHttpRequest


@method_decorator(login_required, name="dispatch")
class InternalsView(TemplateView):
    template_name = "settings/internals.html"
    request: AuthenticatedHttpRequest

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(
            {
                "cdn_console_url": f"https://us-east-1.console.aws.amazon.com/cloudfront/v3/home?region=us-east-1#/distributions/{self.request.user.current_organization.cdn_id}",  # noqa
                "bucket_console_url": f"https://console.wasabisys.com/#/file_manager/{self.request.user.current_organization.bucket_name}?region=eu-central-1",  # noqa
            }
        )
        return context


@method_decorator(login_required, name="dispatch")
class GeneralView(UpdateView):
    form_class = UserForm
    template_name = "settings/general.html"
    success_url = reverse_lazy("general_settings")
    request: AuthenticatedHttpRequest

    def get_object(self, queryset=None):
        return self.request.user


@method_decorator(login_required, name="dispatch")
class CustomPasswordChangeView(SuccessMessageMixin, PasswordChangeView):
    form_class = CustomPasswordChangeForm
    template_name = "settings/password.html"
    success_url = reverse_lazy("password_settings")
    success_message = _("Your password has been changed successfully.")

    def form_invalid(self, form):
        response = super().form_invalid(form)
        for error in form.errors.values():
            messages.error(self.request, error[0])
        return response


@method_decorator(login_required, name="dispatch")
class VideoSettingsView(SuccessMessageMixin, UpdateView):
    form_class = VideoEmbeddingSettingsForm
    template_name = "settings/videos.html"
    success_url = reverse_lazy("video_settings")
    success_message = _("Domains have been updated successfully.")

    def get_object(self, queryset=None):
        return self.request.user.current_organization  # type: ignore


class OrganizationVideoPreferencesView(UpdateView):
    model = Organization
    form_class = OrganizationVideoPreferencesForm
    template_name = "settings/player.html"
    success_url = reverse_lazy("player_settings")

    def get_object(self, queryset=None):
        return self.request.user.current_organization  # type: ignore

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(
            self.request, "Your default video setting has been changed successfully."
        )
        return response


@method_decorator(login_required, name="dispatch")
class IntegrationsView(TemplateView):
    template_name = "settings/integrations.html"
    request: AuthenticatedHttpRequest

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        zoom_account = self._get_zoom_account(self.request.user)

        context.update(
            {
                "zoom_account": zoom_account,
            }
        )
        return context

    def _get_zoom_account(self, user):
        try:
            return ZoomAccount.objects.get(
                organization=user.current_organization, user=user
            )
        except ZoomAccount.DoesNotExist:
            return None
