from allauth.account.views import <PERSON>ginView as AllAuthLoginView
from allauth.account.views import SignupView as AllAuthSignupView
from django.contrib import messages
from django.shortcuts import redirect
from django.utils.translation import gettext_lazy as _

from app.models import Organization
from app.utils.browser import get_user_country_code
from app.utils.hubspot import record_hubspot_lead


class SignupView(AllAuthSignupView):
    def form_valid(self, form):
        form_data = dict(form.cleaned_data)
        record_hubspot_lead(
            name=form_data["name"],
            email=form_data["email"],
            organization_name=form_data["organization"],
            phone=str(form_data["phone_number"]),
            page_uri=self.request.build_absolute_uri(),
            page_name="Sign Up",
        )
        return super().form_valid(form)

    def get_success_url(self, user=None):
        success_url = self.request.GET.get("next", "/")
        query_params = self.request.GET.urlencode()

        if query_params:
            success_url = self._attach_query_params(success_url, query_params)

        return success_url

    def _attach_query_params(self, url, query_params):
        if "?" in url:
            url += f"&{query_params}"
        else:
            url += f"?{query_params}"
        return url

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["country_code"] = get_user_country_code(self.request)
        return context


class AccountLoginView(AllAuthLoginView):
    def form_valid(self, form):
        email = form.cleaned_data["login"].lower()
        user_organization = Organization.objects.get(memberships__user__email=email)
        if user_organization.status == Organization.Status.BLOCKED:
            messages.error(
                self.request,
                "Your account is blocked. <NAME_EMAIL>.",
            )
            return redirect("account_login")

        if user_organization.status == Organization.Status.SUSPENDED:
            messages.warning(
                self.request,
                _("You account is Suspended, <NAME_EMAIL>."),
            )

        return super().form_valid(form)
