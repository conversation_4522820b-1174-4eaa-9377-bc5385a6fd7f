from datetime import datetime

import pytz
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse
from django.utils.decorators import method_decorator
from django.views import View
from import_export import fields, resources
from import_export.widgets import DateTimeWidget

from app.models import LiveStreamUsage
from app.models.track import Track


class ExportMixin(View):
    resource_class = None
    filename = "export"

    def get(self, request, *args, **kwargs):
        export_type = request.GET.get("export_type", "csv")
        data = self.get_queryset()
        response = self.generate_export_response(export_type, data)
        return response

    def generate_export_response(self, export_type, data):
        response = HttpResponse(content_type=self.get_content_type(export_type))
        response[
            "Content-Disposition"
        ] = f'attachment; filename="{self.get_filename(export_type)}"'
        resource = self.resource_class()
        dataset = resource.export(data, format=export_type)
        response.write(getattr(dataset, export_type))
        return response

    def get_queryset(self):
        raise NotImplementedError("Subclasses must implement the get_data method.")

    def get_filename(self, export_type):
        month_param = self.request.GET.get("month")
        date = datetime.now()
        if month_param:
            date = datetime.strptime(month_param, "%Y-%m-%d")
        filename = f"{self.filename}_{date.strftime('%B')}_{date.year}"
        return f"{filename}.{export_type}"

    def get_content_type(self, export_type):
        content_types = {
            "csv": "text/csv",
            "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "json": "application/json",
            "tsv": "text/tab-separated-values",
            "html": "text/html",
            "ods": "application/vnd.oasis.opendocument.spreadsheet",
        }
        return content_types.get(export_type, "text/csv")


class LiveStreamUsageResource(resources.ModelResource):
    start_time = fields.Field(
        column_name="Start Time",
        attribute="start_time",
        widget=DateTimeWidget(format="%Y-%m-%d %H:%M:%S"),
    )
    end_time = fields.Field(
        column_name="End Time",
        attribute="end_time",
        widget=DateTimeWidget(format="%Y-%m-%d %H:%M:%S"),
    )
    duration = fields.Field(column_name="Duration in Mins", attribute="duration")

    class Meta:
        model = LiveStreamUsage
        fields = [
            "live_stream__asset__uuid",
            "live_stream__asset__title",
            "start_time",
            "end_time",
        ]
        export_order = [
            "live_stream__asset__uuid",
            "live_stream__asset__title",
            "start_time",
            "end_time",
            "duration",
        ]
        widgets = {
            "start_time": {"format": "%Y-%m-%d %H:%M:%S"},
            "end_time": {"format": "%Y-%m-%d %H:%M:%S"},
        }

    def dehydrate_duration(self, live_stream_usage):
        if live_stream_usage.end_time and live_stream_usage.start_time:
            duration = live_stream_usage.end_time - live_stream_usage.start_time
            minutes = duration.total_seconds() / 60
            return minutes


class LiveStreamUsageExportView(ExportMixin):
    resource_class = LiveStreamUsageResource
    filename = "live_stream_export"

    def get_queryset(self):
        month_param = self.request.GET.get("month")
        date = datetime.now()
        if month_param:
            date = datetime.strptime(month_param, "%Y-%m-%d")

        LiveStreamUsageModel = LiveStreamUsage
        data = LiveStreamUsageModel.objects.filter(
            end_time__month=date.month, end_time__year=date.year
        ).select_related("live_stream__asset", "organization")
        return data


class SubtitlesGenerationResource(resources.ModelResource):
    asset_id = fields.Field(
        column_name="Asset Id",
        attribute="video__asset__uuid",
    )
    asset_title = fields.Field(
        column_name="Asset title",
        attribute="video__asset__title",
    )
    created_date = fields.Field(
        column_name="Generated date & time",
        attribute="created",
        widget=DateTimeWidget(format="%Y-%m-%d %H:%M:%S"),
    )
    duration = fields.Field(
        column_name="Subtitles Generated(in minutes)",
        attribute="duration",
    )

    class Meta:
        model = Track
        fields = [
            "asset_id",
            "asset_title",
            "created_date",
            "duration",
        ]
        export_order = [
            "asset_id",
            "asset_title",
            "created_date",
            "duration",
        ]

    def dehydrate_created_date(self, track):
        if track.created:
            created = track.created.astimezone(pytz.timezone("Asia/Kolkata"))
            return created.strftime("%Y-%m-%d %I:%M:%S %p")

    def dehydrate_duration(self, track):
        return (
            (track.video.duration.total_seconds() / 60)
            if track.video.duration
            else None
        )


@method_decorator(login_required, name="dispatch")
class SubtitlesGenerationUsageExportView(ExportMixin):
    resource_class = SubtitlesGenerationResource
    filename = "subtitles_generation_usage"

    def get_queryset(self):
        month_param = self.request.GET.get("month")
        date = datetime.now()
        if month_param:
            date = datetime.strptime(month_param, "%Y-%m-%d %H:%M:%S.%f")
        data = Track.objects.filter(
            bytes__isnull=False,
            url__isnull=False,
            subtitle_type=Track.SubtitleType.AUTO_GENERATED,
            created__year=date.year,
            created__month=date.month,
        ).select_related("organization")
        return data
