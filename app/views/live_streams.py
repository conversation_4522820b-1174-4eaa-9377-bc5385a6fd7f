import json
from datetime import datetime

import requests
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import Http404, HttpResponseRedirect, JsonResponse
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse, reverse_lazy
from django.utils.decorators import method_decorator
from django.utils.timezone import now
from django.views import View
from django.views.decorators.clickjacking import xframe_options_exempt
from django.views.generic import (
    CreateView,
    DetailView,
    ListView,
    TemplateView,
    UpdateView,
)
from django_filters.views import FilterView
from django_multitenant.utils import get_current_tenant, set_current_tenant

from app.domain import cloudfront
from app.domain.access_token import get_or_create_access_token_without_validity
from app.domain.cloud_storage import generate_presigned_url
from app.domain.live_stream import (
    calculate_total_stream_duration,
    create_chat_room_task,
    create_live_stream,
    create_live_stream_event,
    create_remote_live_stream_server,
    delete_live_stream_server,
    schedule_files_deletion,
    schedule_stop_disconnected_live_stream,
    stop_live_stream,
    store_live_stream_server_details,
    update_live_stream_status,
    update_termination_log,
    upload_live_stream_recording,
)
from app.filters.live_stream import LiveStreamFilter
from app.forms.live_streams import LiveStreamForm
from app.models import Asset, LiveStream, LiveStreamEvent, Organization
from app.utils.datetime import format_seconds_to_hms
from app.utils.http import AuthenticatedHttpRequest

from .assets import AssetMoveView


class LiveStreamMixin:
    def create_live_stream(self, form):
        title = form.cleaned_data.get("title")
        enable_drm_for_recording = form.cleaned_data.get("enable_drm_for_recording")
        enable_drm = form.cleaned_data.get("enable_drm")
        enable_llhls = form.cleaned_data.get("enable_llhls")
        latency = form.cleaned_data.get("latency")
        if not latency:
            latency = LiveStream.Latency.NORMAL_LATENCY

        # Low latency is not supported in DRM right now
        if int(latency) == LiveStream.Latency.LOW_LATENCY:
            enable_drm = False
        start = form.cleaned_data.get("start") or now()
        organization = get_current_tenant()

        return create_live_stream(
            title,
            organization,
            enable_drm_for_recording,
            self.request.user,
            start=start,
            enable_drm=enable_drm,
            enable_llhls=enable_llhls,
            latency=latency,
        )

    def form_valid(self, form):
        live_stream = self.create_live_stream(form)
        create_chat_room_task(live_stream)

        return HttpResponseRedirect(
            reverse("live_stream_settings", kwargs={"uuid": live_stream.asset.uuid})
        )


@method_decorator(login_required, name="dispatch")
class LiveStreamCreateView(LiveStreamMixin, CreateView):
    request: AuthenticatedHttpRequest
    template_name = "assets/live_stream/create.html"
    form_class = LiveStreamForm


@method_decorator(login_required, name="dispatch")
class ScheduleLiveStreamView(LiveStreamMixin, CreateView):
    request: AuthenticatedHttpRequest
    template_name = "assets/live_stream/schedule.html"
    form_class = LiveStreamForm

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        form.fields["start"].required = True
        return form


@login_required
def create_live_stream_server_view(request, uuid):
    asset = get_object_or_404(Asset, uuid=uuid, type=Asset.Type.LIVESTREAM)

    if asset.live_stream.server_status == LiveStream.ServerStatus.NOT_CREATED:
        live_stream_server = create_remote_live_stream_server(asset)
        store_live_stream_server_details(asset.live_stream, live_stream_server)

    return redirect(reverse("live_stream_settings", kwargs={"uuid": asset.uuid}))


@method_decorator(login_required, name="dispatch")
class LiveStreamDetailView(DetailView):
    slug_field = "uuid"
    template_name = "assets/live_stream/detail.html"
    slug_url_kwarg = "uuid"

    def get_queryset(self):
        return Asset.objects.all().prefetch_related("live_stream")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["live_stream"] = self.object.live_stream  # type: ignore
        context["embed_code"] = self.get_embed_code()
        context["events"] = self.object.live_stream.events.order_by("created")
        return context

    def get_embed_code(self):
        access_token = get_or_create_access_token_without_validity(asset=self.object)  # type: ignore
        embed_url = (
            f"{settings.SITE_URL}/embed/{self.object.organization.uuid}"  # type: ignore
            f"/{self.object.uuid}/?access_token={access_token.uuid}"  # type: ignore
        )
        if self.request.GET.get("use_new_playback_url"):
            embed_url += "&use_new_playback_url=true"
        return f"""
        <div style="padding-top:56.25%;position:relative;width: 100%">
            <iframe
                src="{embed_url}"
                style="border:0;max-width:100%;position:absolute;top:0;left:0;height:100%;width:100%;"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen=""
                frameborder="0"
            >
            </iframe>
        </div>
        """


class StopLiveStreamView(View):
    def post(self, request, *args, **kwargs):
        asset = get_object_or_404(Asset, uuid=self.kwargs["uuid"])
        stop_live_stream(asset.live_stream)
        update_termination_log(asset.live_stream, request)
        messages.success(request, "Live Stream Stopped")
        return redirect(reverse("live_stream_settings", kwargs={"uuid": asset.uuid}))


@method_decorator(login_required, name="dispatch")
class LiveStreamListView(ListView):
    template_name = "assets/live_stream/list.html"
    context_object_name = "assets"
    paginate_by = 20
    request: AuthenticatedHttpRequest

    def get_queryset(self):
        return (
            Asset.objects.filter(
                live_stream__isnull=False,
                live_stream__status__in=[
                    LiveStream.Status.STREAMING,
                    LiveStream.Status.DISCONNECTED,
                    LiveStream.Status.NOT_STARTED,
                ],
                live_stream__server_status__in=[
                    LiveStream.ServerStatus.CREATED,
                    LiveStream.ServerStatus.CREATING,
                ],
            )
            .prefetch_related("live_stream", "live_stream__organization", "video")
            .order_by("-created")
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(
            {
                "is_empty": self.is_empty,
                "subtitle": "",
                "cta_text": "Create Live Stream",
                "cta_link": reverse("create_live_stream"),
            }
        )
        return context

    @property
    def is_empty(self):
        return not self.get_queryset().exists()


@method_decorator(login_required, name="dispatch")
class ScheduledLiveStreamListView(LiveStreamListView):
    def get_queryset(self):
        return (
            Asset.objects.filter(
                live_stream__isnull=False,
                live_stream__server_status=LiveStream.ServerStatus.NOT_CREATED,
                live_stream__start__date__gte=now().date(),
            )
            .prefetch_related("live_stream", "live_stream__organization")
            .order_by("live_stream__start")
        )


@method_decorator(login_required, name="dispatch")
class LiveStreamUpdateView(UpdateView):
    slug_field = "uuid"
    slug_url_kwarg = "uuid"
    form_class = LiveStreamForm
    template_name = "assets/live_stream/update_live_stream.html"
    context_object_name = "live_stream"
    model = Asset

    def get_object(self, queryset=None):
        asset = super().get_object(queryset=queryset)
        return asset.live_stream

    def get_success_url(self):
        return reverse_lazy(
            "live_stream_settings", kwargs={"uuid": self.object.asset.uuid}
        )

    def get_initial(self):
        initial = super().get_initial()
        initial["title"] = self.object.asset.title
        return initial

    def form_valid(self, form):
        response = super().form_valid(form)
        if self.object.use_global_player_preferences:
            self.object.use_global_player_preferences = False
            self.object.save()
        self.save_title(form)
        messages.success(
            self.request, "Your Live setting has been changed successfully."
        )
        return response

    def save_title(self, form):
        instance = form.save(commit=False)
        if form.cleaned_data["title"]:
            instance.asset.title = form.cleaned_data["title"]
            instance.asset.save()
        instance.start = instance.start or form.initial.get("start")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["is_live_stream_started"] = self.is_live_stream_started
        return context

    def is_live_stream_started(self):
        return self.object.get_status_display() != "Not Started"


@method_decorator(login_required, name="dispatch")
class CompletedLiveStreamListView(LiveStreamListView):
    def get_queryset(self):
        return (
            Asset.objects.filter(
                live_stream__isnull=False,
                live_stream__status__in=[
                    LiveStream.Status.COMPLETED,
                    LiveStream.Status.RECORDING,
                    LiveStream.Status.ERROR,
                ],
                live_stream__has_moved=False,
            )
            .prefetch_related(
                "live_stream",
                "live_stream__organization",
                "live_stream__live_stream_usage",
                "video",
            )
            .order_by("-created")
        )


@method_decorator(login_required, name="dispatch")
class TPStreamsAdminLiveStreamListView(FilterView):
    template_name = "assets/live_stream/internals/main.html"
    context_object_name = "assets"
    paginate_by = 20
    filterset_class = LiveStreamFilter

    def dispatch(self, request, *args, **kwargs):
        if request.user.email not in settings.SUPPORT_EMAILS:
            raise Http404("The requested page does not exist.")
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        return (
            Asset.objects.filter(live_stream__isnull=False)
            .prefetch_related("live_stream", "live_stream__organization")
            .order_by("-created")
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        title = "Live Streams"
        context.update(
            {
                "title": title,
                "organizations": json.dumps(
                    list(
                        Organization.objects.filter(livestreams__isnull=False)
                        .exclude(status=Organization.Status.BLOCKED)
                        .distinct()
                        .values("uuid", "name")
                    )
                ),
            }
        )
        return context

    @property
    def is_empty(self):
        return not self.get_queryset().exists()


@method_decorator(login_required, name="dispatch")
class LiveStreamMoveView(AssetMoveView):
    def post(self, request, *args, **kwargs):
        self.asset.move(self.folder)
        self.asset.live_stream.has_moved = True
        self.asset.live_stream.save()
        messages.success(self.request, "Live Stream has been moved successfully.")
        return redirect(self.redirect_url())

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(
            {
                "source": self.asset,
                "subtitle": "",
                "cta_text": "",
                "title": "Move Live Stream",
            }
        )
        return context


class ChatEmbedView(DetailView):
    context_object_name = "asset"
    template_name = "assets/live_stream/live_chat.html"

    @xframe_options_exempt
    def dispatch(self, request, *args, **kwargs):
        organization = get_object_or_404(Organization, uuid=kwargs["organization_id"])
        set_current_tenant(organization)
        return super().dispatch(request, *args, **kwargs)

    def get_object(self, queryset=None):
        asset = get_object_or_404(
            Asset, uuid=self.kwargs.get("uuid"), live_stream__isnull=False
        )
        if not asset.live_stream.chat_room_id:
            raise Http404()
        return asset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["chat_url"] = settings.CHAT_URL
        context["api_key"] = self.get_api_key()
        return context

    def get_api_key(self):
        if self.request.user.is_authenticated:
            return settings.CHAT_ADMIN_KEY
        return settings.CHAT_ANONYMOUS_USER_KEY


@method_decorator(login_required, name="dispatch")
class LiveStreamActivityLogView(TemplateView):
    template_name = "assets/live_stream/log.html"

    def dispatch(self, request, *args, **kwargs):
        if request.user.email not in settings.SUPPORT_EMAILS:
            raise Http404("The requested page does not exist.")
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        uuid = self.kwargs.get("uuid")
        live_stream = get_object_or_404(LiveStream, asset__uuid=uuid)
        events = live_stream.events.order_by("created")

        context["live_stream"] = live_stream
        context["events"] = events
        context["video_input_url"] = self.get_video_input_url(live_stream)
        context["ffmpeg_log_url"] = self.get_ffmpeg_log(live_stream)
        context["transmux_log_url"] = self.get_transmux_log(live_stream)
        context["transcoding_url"] = self.get_live_stream_transcoding_job_url(
            live_stream.asset
        )
        context["widevine_packger_log_url"] = self.get_drm_packager_log(
            live_stream, "packager_widevine.txt"
        )
        context["fairplay_packger_log_url"] = self.get_drm_packager_log(
            live_stream, "packager_fairplay.txt"
        )
        context["live_stream_duration"] = self.get_live_stream_duration(live_stream)
        context["check_cdn_status"] = self.check_cdn_status(live_stream)
        context["check_haproxy_status"] = self.check_haproxy_status(live_stream)
        context["check_oprenresty_status"] = self.check_oprenresty_status(live_stream)
        context["check_live_server_status"] = self.check_live_server_status(live_stream)

        return context

    def post(self, request, **kwargs):
        uuid = self.kwargs.get("uuid")
        live_stream = get_object_or_404(LiveStream, asset__uuid=uuid)
        action = request.POST.get("action")

        if action == "upload_live_stream_recording":
            try:
                update_live_stream_status(live_stream, LiveStream.Status.STOPPED)
                upload_live_stream_recording(live_stream)
                messages.success(
                    request, "Upload recording task has been triggered successfully."
                )
            except Exception as e:
                messages.error(
                    request, f"Failed to trigger upload recording task: {str(e)}"
                )
        elif action == "stop_live_stream":
            try:
                stop_live_stream(live_stream)
                update_termination_log(live_stream, request)
                messages.success(request, "Live Stream Stopped")
            except Exception as e:
                messages.error(request, f"Failed to stop live stream: {str(e)}")

        return redirect(reverse("live_stream_log", kwargs={"uuid": uuid}))

    def get_video_input_url(self, live_stream):
        if hasattr(live_stream.asset, "video"):
            return live_stream.asset.video.inputs.first().get_input_url()

    def get_ffmpeg_log(self, live_stream):
        if hasattr(live_stream.asset, "video"):
            path = f"private/{live_stream.asset.uuid}/ffmpeg_log.txt"
            if live_stream.organization.uses_s3_storage:
                return cloudfront.generate_presigned_url(live_stream.organization, path)
            return generate_presigned_url(live_stream.organization, path)

    def get_transmux_log(self, live_stream):
        if hasattr(live_stream.asset, "video"):
            path = f"private/{live_stream.asset.uuid}/transmux_log.txt"
            if live_stream.organization.uses_s3_storage:
                return cloudfront.generate_presigned_url(live_stream.organization, path)
            return generate_presigned_url(live_stream.organization, path)

    def get_drm_packager_log(self, live_stream, file_name):
        if hasattr(live_stream.asset, "video"):
            path = f"private/{live_stream.asset.uuid}/{file_name}"
            if live_stream.organization.uses_s3_storage:
                return cloudfront.generate_presigned_url(live_stream.organization, path)
            return generate_presigned_url(live_stream.organization, path)

    def get_live_stream_duration(self, live_stream):
        duration_in_seconds = calculate_total_stream_duration(live_stream)
        return format_seconds_to_hms(duration_in_seconds)

    def get_live_stream_transcoding_job_url(self, asset):
        if hasattr(asset, "video"):
            return (
                f"https://lumberjack.testpress.in/admin/jobs/job/{asset.video.job_id}/"
            )

    def check_cdn_status(self, live_stream):
        url = f"{live_stream.organization.cdn_url}live/{live_stream.organization.uuid}/{live_stream.asset.uuid}/video.m3u8"
        if not live_stream.get_server_status_display() == "Destroyed":
            try:
                response = requests.head(url, timeout=5)
                return {"result": response.status_code == 200, "url": url}
            except requests.RequestException as e:
                return {"result": False, "url": url, "error": str(e)}
        return {"result": False, "url": url}

    def check_haproxy_status(self, live_stream):
        url = f"http://live.tpstreams.com/live/{live_stream.organization.uuid}/{live_stream.asset.uuid}/video.m3u8"
        if not live_stream.get_server_status_display() == "Destroyed":
            try:
                response = requests.head(url, timeout=5)
                return {"result": response.status_code == 200, "url": url}
            except requests.RequestException as e:
                return {"result": False, "url": url, "error": str(e)}
        return {"result": False, "url": url}

    def check_oprenresty_status(self, live_stream):
        url = f"http://live2.tpstreams.com/live2/{live_stream.organization.uuid}/{live_stream.asset.uuid}/video.m3u8"
        if not live_stream.get_server_status_display() == "Destroyed":
            try:
                response = requests.head(url, timeout=5)
                return {"result": response.status_code == 200, "url": url}
            except requests.RequestException as e:
                return {"result": False, "url": url, "error": str(e)}
        return {"result": False, "url": url}

    def check_live_server_status(self, live_stream):
        url = f"http://{live_stream.server_ip}/live/{live_stream.organization.uuid}/{live_stream.asset.uuid}/video.m3u8"
        if not live_stream.get_server_status_display() == "Destroyed":
            try:
                response = requests.head(url, timeout=5)
                return {"result": response.status_code == 200, "url": url}
            except requests.RequestException as e:
                return {"result": False, "url": url, "error": str(e)}
        return {"result": False, "url": url}


@method_decorator(login_required, name="dispatch")
class LiveStreamSearchView(FilterView, CompletedLiveStreamListView):
    from app.filters.live_stream import LiveStreamFilter

    filterset_class = LiveStreamFilter

    def get_queryset(self):
        queryset = (
            Asset.objects.filter(live_stream__isnull=False)
            .prefetch_related("live_stream", "live_stream__organization")
            .order_by("-live_stream__live_stream_usage__end_time")
        )

        return queryset

    def update_context_with_date(self, context, streamed_at):
        if streamed_at:
            parsed_date = datetime.strptime(streamed_at, "%Y-%m-%d").date()
            context["date"] = parsed_date

    def update_context_with_search_query(self, context, search_query):
        if search_query:
            title = f"Search results for '{search_query}'"
            context.update(
                {
                    "subtitle": "",
                    "cta_text": "",
                    "title": title,
                }
            )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        streamed_at = self.request.GET.get("streamed_at")
        search_query = self.request.GET.get("q")

        self.update_context_with_date(context, streamed_at)
        self.update_context_with_search_query(context, search_query)

        return context


class StreamStatusView(View):
    def get(self, request):
        server_ip = request.GET.get("server_ip")
        server_id = request.GET.get("server_id")

        if not server_ip or not server_id:
            return JsonResponse({"error": "Server details not provided"}, status=400)

        try:
            response = requests.get(
                f"http://{server_ip}/stream/{server_id}/status/",
                headers={"X-API-KEY": server_id},
            )
            return JsonResponse(response.json(), status=response.status_code)
        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)


class PendingEventsView(View):
    def get(self, request):
        server_ip = request.GET.get("server_ip")
        server_id = request.GET.get("server_id")

        if not server_ip or not server_id:
            return JsonResponse({"error": "Server details not provided"}, status=400)

        try:
            response = requests.get(
                f"http://{server_ip}/pending-events/",
                headers={"X-API-KEY": server_id},
            )
            return JsonResponse(
                response.json(), status=response.status_code, safe=False
            )
        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)


@method_decorator(login_required, name="dispatch")
class AcknowledgeStreamEventView(View):
    def post(self, request):
        asset_uuid = request.POST.get("asset_uuid")

        asset = get_object_or_404(Asset, uuid=asset_uuid, live_stream__isnull=False)
        live_stream = asset.live_stream
        event_id = request.POST.get("event_id")
        event_type = request.POST.get("event_type")

        if live_stream.server_status == LiveStream.ServerStatus.DESTROYED:
            messages.error(request, "live stream server is already destroyed")
            return redirect(reverse("live_streams_internals"))

        if event_type == "on_unpublish":
            self._acknowledge_event_on_server(event_id, live_stream)
            self.acknowledge_on_unpublish_event(live_stream)

        elif event_type == "upload_complete":
            self._acknowledge_event_on_server(event_id, live_stream)
            self.acknowledge_on_upload_complete_event(live_stream)

        else:
            messages.info(
                request,
                f"Event type '{event_type}' is not supported for acknowledgment.",
            )
            return redirect(reverse("live_streams_internals"))

        messages.success(request, "Live stream Acknowledged successfully.")
        return redirect(reverse("live_streams_internals"))

    def _acknowledge_event_on_server(self, event_id, live_stream):
        requests.post(
            f"http://{live_stream.server_ip}/stream-event/{event_id}/acknowledge/",
            headers={"X-API-KEY": live_stream.server_id},
        )

    def acknowledge_on_unpublish_event(self, live_stream):
        update_live_stream_status(live_stream, LiveStream.Status.DISCONNECTED)
        LiveStreamEvent.objects.create(
            type=LiveStreamEvent.Type.ON_PUBISH_DONE,
            live_stream=live_stream,
            organization=live_stream.organization,
        )
        schedule_stop_disconnected_live_stream(live_stream)
        live_stream.asset.notify_webhook()

    def acknowledge_on_upload_complete_event(self, live_stream):
        if live_stream.status != LiveStream.Status.STOPPED:
            update_live_stream_status(live_stream, LiveStream.Status.STOPPED)

        if live_stream.store_recorded_video:
            upload_live_stream_recording(live_stream)
        else:
            delete_live_stream_server(live_stream)
            update_live_stream_status(live_stream, LiveStream.Status.COMPLETED)
            create_live_stream_event(
                live_stream,
                LiveStreamEvent.Type.COMPLETED,
                {
                    "message": "Recording upload skipped as 'store_recorded_video' is disabled. Live stream marked as completed."
                },
            )

        schedule_files_deletion(live_stream)
