from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.shortcuts import redirect
from django.urls import reverse
from django.utils.decorators import method_decorator
from django.views.generic import UpdateView

from app.domain.cloud_storage import delete_file
from app.domain.thumbnail import (
    remove_thumbnail_from_video,
    update_cover_thumbnail,
    upload_thumbnail,
)
from app.models import Asset


@method_decorator(login_required, name="dispatch")
class ThumbnailUpdateView(UpdateView):
    model = Asset
    slug_field = "uuid"
    slug_url_kwarg = "asset_uuid"

    def post(self, request, *args, **kwargs):
        uploaded_file = request.FILES.get("file")
        selected_thumbnail = request.POST.get("path")
        delete_thumbnail = request.POST.get("delete_thumbnail")
        thumbnail_url_to_delete = request.POST.get("thumbnail_url")

        if uploaded_file:
            self.upload_and_store_thumbnail(uploaded_file)
        elif selected_thumbnail:
            self.update_selected_thumbnail(selected_thumbnail)
        elif delete_thumbnail and thumbnail_url_to_delete:
            self.delete_thumbnail(thumbnail_url_to_delete)

        return redirect(
            reverse("asset_detail", kwargs={"asset_uuid": self.get_object().uuid})
        )

    def upload_and_store_thumbnail(self, uploaded_file):
        if not uploaded_file.name.lower().endswith((".png", ".jpeg", ".jpg")):
            messages.error(
                self.request,
                "Invalid file format. Please upload a .png, .jpeg, or .jpg file.",
            )
        elif not uploaded_file.size <= settings.MAXIMUM_THUMBNAIL_SIZE_BYTES:
            messages.error(self.request, "Thumbnail size should be no more than 2 MB.")
        elif (
            self.get_object().video.thumbnails
            and len(self.get_object().video.thumbnails)
            >= settings.MAXIMUM_THUMBNAIL_COUNT
        ):
            messages.error(
                self.request,
                f"The maximum number of thumbnails allowed is {settings.MAXIMUM_THUMBNAIL_COUNT}. Please remove some existing thumbnails before adding new ones.",
            )
        else:
            upload_thumbnail(self.get_object(), uploaded_file)
            messages.success(self.request, "Thumbnail uploaded")

    def update_selected_thumbnail(self, selected_thumbnail):
        update_cover_thumbnail(self.get_object(), selected_thumbnail)
        messages.success(self.request, "Thumbnail Updated")

    def delete_thumbnail(self, thumbnail_url):
        asset = self.get_object()
        remove_thumbnail_from_video(asset.video, thumbnail_url)
        delete_file(asset.organization, thumbnail_url)
        messages.success(self.request, "Thumbnail deleted successfully")
