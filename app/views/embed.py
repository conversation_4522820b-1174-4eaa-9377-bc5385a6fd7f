import json
from typing import cast
from urllib.parse import urlparse

import requests
from django.conf import settings
from django.core.serializers.json import DjangoJSONEncoder
from django.http import Http404
from django.shortcuts import get_object_or_404, render
from django.utils.decorators import method_decorator
from django.views.decorators.clickjacking import xframe_options_exempt
from django.views.generic import TemplateView
from django.views.generic.detail import DetailView
from django_multitenant.utils import set_current_tenant

from app.api.v1.serializers.access_token import AccessTokenAnnotationSerializer
from app.domain import cloudfront
from app.domain.access_token import (
    expire_single_usage_access_token,
    get_annotations_for_token,
    validate_access_token,
)
from app.domain.video import (
    check_cdn_status,
    get_migrated_dash_url,
    get_migrated_playback_url,
)
from app.models import Asset, LiveStream, Organization, Track, Video


def can_embed(function=None):
    def decorator(request, *args, **kwargs):
        organization = get_object_or_404(Organization, uuid=kwargs["organization_id"])
        asset = get_object_or_404(Asset, uuid=kwargs.get("uuid"))

        if asset.disable_domain_restriction:
            return function(request, *args, **kwargs)

        allowed_domains = organization.allowed_domains_for_embedding + [
            urlparse(settings.SITE_URL).hostname
        ]

        if (
            len(organization.allowed_domains_for_embedding) == 0
            or urlparse(request.META.get("HTTP_REFERER", "")).hostname
            in allowed_domains
        ):
            return function(request, *args, **kwargs)
        else:
            raise Http404

    return decorator


def can_embed_uploader(function=None):
    def decorator(request, *args, **kwargs):
        organization = get_object_or_404(Organization, uuid=kwargs["organization_id"])

        allowed_domains = organization.allowed_domains_for_embedding + [
            urlparse(settings.SITE_URL).hostname
        ]

        if (
            len(organization.allowed_domains_for_embedding) == 0
            or urlparse(request.META.get("HTTP_REFERER", "")).hostname
            in allowed_domains
        ):
            return function(request, *args, **kwargs)
        else:
            raise Http404

    return decorator


class EmbedView(DetailView):
    template_name = "embed/index.html"
    queryset = Asset.objects.all().prefetch_related("video")
    slug_field = "uuid"
    slug_url_kwarg = "uuid"

    @method_decorator(can_embed)
    @xframe_options_exempt
    def dispatch(self, request, *args, **kwargs):
        organization = get_object_or_404(Organization, uuid=kwargs["organization_id"])
        set_current_tenant(organization)
        if (
            not organization.is_member(request.user)
            and not self.is_access_token_valid()
            and organization.uuid in settings.USE_ACCESS_TOKEN_FROM_CACHE
        ):
            raise Http404()
        return super().dispatch(request, *args, **kwargs)

    def is_access_token_valid(self):
        asset = get_object_or_404(Asset, uuid=self.kwargs.get("uuid"))
        token = self.request.GET.get("access_token")
        if token:
            return validate_access_token(asset, token)
        return False

    def get(self, request, *args, **kwargs):
        self.object: Asset = cast(Asset, self.get_object())
        if self.object.organization.status == Organization.Status.BLOCKED:
            return render(
                request,
                "embed/blocked_player.html",
            )

        self.update_single_usage_access_token_expiry()
        context = self.get_context_data(object=self.object)
        return self.render_to_response(context)

    def update_single_usage_access_token_expiry(self):
        token = self.request.GET.get("access_token")
        video = getattr(self.object, "video", None)

        if token and video and not (video.is_drm_encrypted or video.is_aes_encrypted):
            # Expiry for DRM/AES Encrypted video is handled in respective license API
            expire_single_usage_access_token(video.asset, token)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["fairplay_certificate_url"] = settings.FAIRPLAY_CERTIFICATE_URL
        context["player_preference"] = self.get_player_preference()
        context["annotations"] = self.get_annotations_from_access_token()
        context["subtitles"] = self.get_subtitles()
        context["preview_thumbnail"] = self.get_preview_thumbnail()
        context["static_url"] = self.get_static_url()
        context[
            "is_live_stream_streaming_started"
        ] = self.is_live_stream_streaming_started()
        context["chapters"] = self.get_chapters()
        context["is_live_stream"] = (
            True if hasattr(self.object, "live_stream") else False
        )
        context["is_cdn_available"] = self.is_cdn_available()
        context["cassandra_server_url"] = settings.CASSANDRA_SERVER_URL
        context["sentry_url"] = settings.PLAYER_SENTRY_URL

        context.update(**self.get_video_options())

        return context

    def get_annotations_from_access_token(self):
        access_token = self.request.GET.get("access_token")
        if (
            not access_token
            or self.object.organization.uuid not in settings.USE_ACCESS_TOKEN_FROM_CACHE
        ):
            return []
        annotations = AccessTokenAnnotationSerializer(
            get_annotations_for_token(self.object, access_token),
            many=True,
        ).data
        return json.dumps(annotations, cls=DjangoJSONEncoder)

    def get_video_options(self):
        if hasattr(self.object, "live_stream"):
            return self.get_livestream_options
        else:
            return {
                "hls_url": self.get_playback_url(),
                "dash_url": self.get_dash_url(),
                "has_drm": self.object.video.is_drm_encrypted,
                "thumbnail_url": self.object.video.get_cover_thumbnail_url(),
            }

    def is_cdn_available(self):
        if (
            hasattr(self.object, "video") or hasattr(self.object, "live_stream")
        ) and self.object.organization.cdn_url:
            return check_cdn_status(self.object).get("result", False)
        return False

    def get_playback_url(self):
        video = self.object.video
        if video.is_aes_encrypted:
            return cloudfront.generate_presigned_url(
                video.organization, video.playback_url, expires_in=300
            )
        tpstreams_drm = self.request.GET.get("tpstreams_drm")
        if (
            tpstreams_drm
            and video.meta_data
            and video.meta_data.get("has_tpstreams_drm")
        ):
            return get_migrated_playback_url(video)
        return video.get_playback_url()

    def get_dash_url(self):
        video = self.object.video
        tpstreams_drm = self.request.GET.get("tpstreams_drm")
        if (
            tpstreams_drm
            and video.meta_data
            and video.meta_data.get("has_tpstreams_drm")
        ):
            return get_migrated_dash_url(video)
        return video.get_dash_url()

    @property
    def get_livestream_options(self):
        if (
            hasattr(self.object, "video")
            and self.object.video.status == Video.Status.COMPLETED
        ):
            return {
                "hls_url": self.object.video.get_playback_url(),
                "dash_url": self.object.video.get_dash_url(),
                "has_drm": self.object.video.is_drm_encrypted,
                "thumbnail_url": self.object.video.get_cover_thumbnail_url(),
            }
        has_low_latency = (
            self.object.live_stream.latency == LiveStream.Latency.LOW_LATENCY
        )
        use_new_playback_url = self.request.GET.get("use_new_playback_url") == "true"
        hls_url = (
            self.object.live_stream.get_hls_url_using_openresty
            if use_new_playback_url
            else self.object.live_stream.get_hls_url
        )
        dash_url = (
            self.object.live_stream.get_dash_url_using_openresty
            if use_new_playback_url
            else self.object.live_stream.get_dash_url
        )
        return {
            "hls_url": hls_url,
            "dash_url": dash_url,
            "has_drm": self.object.live_stream.enable_drm,
            "enable_llhls": self.object.live_stream.enable_llhls,
            "is_low_latency_stream": has_low_latency,
        }

    def get_subtitles(self):
        subtitles = []
        if hasattr(self.object, "video"):
            for subtitle in self.object.video.tracks.filter(
                type=Track.Type.SUBTITLE, is_active=True, url__isnull=False
            ):
                subtitles.append(
                    [subtitle, f"{subtitle.organization.cdn_url}{subtitle.url}"]
                )
        return subtitles

    def get_preview_thumbnail(self):
        if hasattr(self.object, "video"):
            video = self.object.video
            track = video.tracks.filter(
                type=Track.Type.PREVIEW_THUMBNAIL, preview_thumbnail__url__isnull=False
            )
            if track.first() and track.first().preview_thumbnail:
                return track.first().preview_thumbnail

    def is_live_stream_streaming_started(self):
        if hasattr(self.object, "video"):
            return True
        live_stream = self.object.live_stream
        use_new_playback_url = self.request.GET.get("use_new_playback_url") == "true"
        if live_stream.get_status_display() == "Streaming" and not use_new_playback_url:
            url = (
                live_stream.get_hls_url_using_openresty
                if use_new_playback_url
                else self.object.live_stream.get_hls_url
            )
            response = requests.head(url)
            return response.status_code == 200
        else:
            return True

    def get_chapters(self):
        chapters = []
        if hasattr(self.object, "video"):
            for chapter in self.object.video.chapters.all().order_by("start_time"):
                chapters.append(
                    {
                        "startTime": chapter.get_start_time_in_seconds(),
                        "label": chapter.label,
                    }
                )
        return chapters

    def get_player_preference(self):
        if hasattr(self.object, "video"):
            if self.object.video.embed_preset:
                return self.object.video.embed_preset
            elif self.object.video.use_global_player_preferences:
                return self.object.organization
            else:
                return self.object.video
        if hasattr(self.object, "live_stream"):
            if self.object.live_stream.embed_preset:
                return self.object.live_stream.embed_preset
            elif self.object.live_stream.use_global_player_preferences:
                return self.object.organization
            else:
                return self.object.live_stream

    def get_static_url(self):
        if (
            self.object.organization.uuid == "dcek2m"
            or self.object.organization.uuid == "48bqht"
        ):
            return "https://static.tpstreams.com"
        return ""


class AssetUploadEmbedView(TemplateView):
    template_name = "assets/videos/embed_uploader/index.html"

    @method_decorator(can_embed_uploader)
    @xframe_options_exempt
    def dispatch(self, request, *args, **kwargs):
        self.organization = get_object_or_404(
            Organization, uuid=kwargs["organization_id"]
        )
        set_current_tenant(self.organization)
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(
            {
                "organization": self.organization,
                "enabled_resolutions": self.organization.enabled_resolutions or [],
                "disable_folder_selection": self.organization.uuid in ["4khc8z"],
                "hide_video_settings": self.organization.uuid in ["4khc8z"],
                "hide_transcode_settings": self.organization.uuid in ["4khc8z"],
            }
        )
        return context
