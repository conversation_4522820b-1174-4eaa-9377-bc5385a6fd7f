from datetime import timedelta

from django import forms
from django.utils.timezone import now

from app.models import LiveStream


def validate_start_datetime(value):
    if value and value < (now() - timedelta(minutes=1)):
        """Since current time user clicked and current time in backend might be off by few seconds we check
        with 1 minute contingency time"""
        raise forms.ValidationError("Start time must be greater than current time")


class LiveStreamForm(forms.ModelForm):
    title = forms.CharField(
        max_length=254,
        label="Title",
        widget=forms.TextInput(
            attrs={"placeholder": "Add a title that describes your stream"}
        ),
    )
    enable_drm_for_recording = forms.BooleanField(required=False, initial=True)
    enable_drm = forms.BooleanField(required=False, initial=False)
    start = forms.DateTimeField(
        label="Start Date & Time",
        required=False,
        validators=[validate_start_datetime],
        widget=forms.TextInput(attrs={"class": "flatpickr"}),
    )
    enable_llhls = forms.BooleanField(required=False, initial=False)
    latency = forms.ChoiceField(
        choices=LiveStream.Latency.choices, required=False, widget=forms.RadioSelect
    )
    show_player_controls = forms.BooleanField(required=False)
    show_video_title = forms.BooleanField(required=False)
    show_play_button = forms.BooleanField(required=False)
    show_progress_bar = forms.BooleanField(required=False)
    show_volume_bar = forms.BooleanField(required=False)
    show_transcript = forms.BooleanField(required=False)
    show_video_quality_control = forms.BooleanField(required=False)
    show_speed_control = forms.BooleanField(required=False)
    show_picture_in_picture_control = forms.BooleanField(required=False)
    show_full_screen_control = forms.BooleanField(required=False)
    autoplay_enabled = forms.BooleanField(required=False)
    background_mode_enabled = forms.BooleanField(required=False)
    loop_enabled = forms.BooleanField(required=False)
    muted_on_start = forms.BooleanField(required=False)
    default_quality = forms.ChoiceField(
        choices=LiveStream.ResolutionChoices.choices, required=False
    )
    play_button_position = forms.ChoiceField(
        choices=LiveStream.PlayButtonPosition.choices, required=False
    )
    primary_color = forms.CharField(max_length=12, required=False)
    icons_color = forms.CharField(max_length=12, required=False)
    accent_color = forms.CharField(max_length=12, required=False)
    background_color = forms.CharField(max_length=12, required=False)
    show_subtitles = forms.BooleanField(required=False)
    default_subtitle_language = forms.ChoiceField(
        choices=LiveStream.LANGUAGE_CODES, required=False
    )

    class Meta:
        model = LiveStream
        fields = [
            "title",
            "enable_drm_for_recording",
            "enable_drm",
            "start",
            "enable_llhls",
            "latency",
            "show_player_controls",
            "show_video_title",
            "show_play_button",
            "show_progress_bar",
            "show_volume_bar",
            "show_transcript",
            "show_video_quality_control",
            "show_speed_control",
            "show_picture_in_picture_control",
            "show_full_screen_control",
            "autoplay_enabled",
            "background_mode_enabled",
            "loop_enabled",
            "muted_on_start",
            "default_quality",
            "play_button_position",
            "primary_color",
            "icons_color",
            "accent_color",
            "background_color",
            "show_subtitles",
            "default_subtitle_language",
        ]
