from django import forms

from app.models.asset import Asset
from app.models.zoom import ZoomAccount


class ZoomAccountForm(forms.ModelForm):
    import_destination = forms.ModelChoiceField(
        queryset=Asset.objects.none(), to_field_name="uuid", required=False
    )

    class Meta:
        model = ZoomAccount
        fields = ["import_destination", "enable_drm"]

    def __init__(self, *args, **kwargs):
        user = kwargs.pop("user", None)
        super().__init__(*args, **kwargs)

        if user:
            self.fields["import_destination"].queryset = Asset.objects.filter(
                organization=user.current_organization, type=Asset.Type.FOLDER
            )
