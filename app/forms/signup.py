from allauth.account.forms import SignupForm as AllAuthSignupForm
from django import forms
from django.conf import settings
from django.db import transaction
from django.utils.translation import gettext_lazy as _
from phonenumber_field.formfields import <PERSON><PERSON><PERSON>berField

from app.models import Membership, Organization, User


class SignupForm(AllAuthSignupForm):
    name = forms.CharField(
        label=_("Name"),
        max_length=254,
        widget=forms.TextInput(
            attrs={
                "placeholder": "John Doe",
                "autocomplete": "name",
            }
        ),
        required=True,
    )
    organization = forms.CharField(
        label=_("Organization Name"),
        max_length=254,
        widget=forms.TextInput(
            attrs={
                "placeholder": "Acme Corp",
                "autocomplete": "organization",
            }
        ),
        required=True,
    )
    phone_number = PhoneNumberField(required=True)

    def clean_email(self):
        email = self.cleaned_data.get("email")
        if email and User.objects.filter(email=email).exists():
            raise forms.ValidationError(
                _("An account with this email already exists.   ")
            )
        return email.lower()

    def clean_phone_number(self):
        phone_number = self.cleaned_data.get("phone_number")
        if phone_number and User.objects.filter(phone_number=phone_number).exists():
            raise forms.ValidationError(
                _("A user is already registered with this phone number.")
            )
        return phone_number

    def custom_signup(self, request, user):
        user.name = self.cleaned_data["name"]
        user.phone_number = self.cleaned_data["phone_number"]
        user.save()
        organization = Organization.objects.create(
            name=self.cleaned_data["organization"],
            created_by=user,
            **self.get_storage_and_cdn_configs()
        )
        Membership.objects.create(organization=organization, user=user)
        user.current_organization_uuid = organization.uuid
        user.save()

    def get_storage_and_cdn_configs(self):
        # These keys were obtained from our AWS account (<EMAIL>)
        ONE_YEAR_CACHE_POLICY_ID = "d8d9fe18-407a-4999-ad00-29025fc0c497"
        EXPIRE_IN_3_SECONDS_CACHE_POLICY_ID = "18b0d37a-529c-4f4a-b38c-6816ad22433c"
        CDN_PUBLIC_KEY_ID = "K2XWKDWM065EGO"

        return {
            "storage_region": settings.WASABI_BUCKET_REGION,
            "storage_access_key_id": settings.WASABI_ACCESS_KEY_ID,
            "storage_secret_access_key": settings.WASABI_SECRET_ACCESS_KEY,
            "cdn_access_key_id": settings.AWS_ACCESS_KEY_ID,
            "cdn_secret_access_key": settings.AWS_SECRET_ACCESS_KEY,
            "cdn_one_year_cache_policy_id": ONE_YEAR_CACHE_POLICY_ID,
            "cdn_expire_in_3_seconds_cache_policy_id": EXPIRE_IN_3_SECONDS_CACHE_POLICY_ID,
            "cdn_public_key_id": CDN_PUBLIC_KEY_ID,
            "cdn_private_key": settings.DEFAULT_CLOUDFRONT_PRIVATE_KEY,
        }

    def save(self, request):
        with transaction.atomic():
            return super().save(request)
