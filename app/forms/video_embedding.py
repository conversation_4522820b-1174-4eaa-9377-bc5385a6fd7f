import re

from django import forms
from django.contrib.postgres.forms import SimpleArrayField
from django.core.exceptions import ValidationError

from app.models.organization import Organization


def validate_domain_names(domain_names):
    domain_regex = re.compile("(?!-)[A-Z\\d-]{1,63}(?<!-)$", re.IGNORECASE)
    for domain_name in domain_names:
        if "." not in domain_name or not all(
            domain_regex.match(x) for x in domain_name.split(".")
        ):
            raise ValidationError(
                "%(hostname)s is not a valid host name",
                params={"hostname": domain_name},
            )


class VideoEmbeddingSettingsForm(forms.ModelForm):
    allowed_domains_for_embedding = SimpleArrayField(
        forms.CharField(max_length=253),
        validators=[validate_domain_names],
        required=False,
    )

    class Meta:
        model = Organization
        fields = ("allowed_domains_for_embedding",)
