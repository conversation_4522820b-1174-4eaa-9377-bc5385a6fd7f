from django import forms

from app.models import Video


class VideoPreferencesForm(forms.ModelForm):
    show_player_controls = forms.BooleanField(required=False)
    show_video_title = forms.BooleanField(required=False)
    show_play_button = forms.BooleanField(required=False)
    show_progress_bar = forms.BooleanField(required=False)
    show_volume_bar = forms.BooleanField(required=False)
    show_transcript = forms.BooleanField(required=False)
    show_video_quality_control = forms.BooleanField(required=False)
    show_speed_control = forms.BooleanField(required=False)
    show_picture_in_picture_control = forms.BooleanField(required=False)
    show_full_screen_control = forms.BooleanField(required=False)
    autoplay_enabled = forms.BooleanField(required=False)
    background_mode_enabled = forms.BooleanField(required=False)
    loop_enabled = forms.BooleanField(required=False)
    muted_on_start = forms.BooleanField(required=False)
    default_quality = forms.ChoiceField(
        choices=Video.ResolutionChoices.choices, required=False
    )
    play_button_position = forms.ChoiceField(
        choices=Video.PlayButtonPosition.choices, required=False
    )
    primary_color = forms.CharField(max_length=12, required=False)
    icons_color = forms.CharField(max_length=12, required=False)
    accent_color = forms.CharField(max_length=12, required=False)
    background_color = forms.CharField(max_length=12, required=False)
    show_subtitles = forms.BooleanField(required=False)
    default_subtitle_language = forms.ChoiceField(
        choices=Video.LANGUAGE_CODES, required=False
    )

    class Meta:
        model = Video
        fields = [
            "show_player_controls",
            "show_video_title",
            "show_play_button",
            "show_progress_bar",
            "show_volume_bar",
            "show_transcript",
            "show_video_quality_control",
            "show_speed_control",
            "show_picture_in_picture_control",
            "show_full_screen_control",
            "autoplay_enabled",
            "background_mode_enabled",
            "loop_enabled",
            "muted_on_start",
            "default_quality",
            "play_button_position",
            "primary_color",
            "icons_color",
            "accent_color",
            "background_color",
            "show_subtitles",
            "default_subtitle_language",
        ]
