import django_filters
from django import forms

from app.models.live_stream import LiveStream
from app.filters.utils import SearchFilter
from app.models.video import Asset


class LiveStreamFilter(django_filters.FilterSet):
    q = SearchFilter(
        search_fields=["title", "uuid"], label="Search"
    )
    streamed_at = django_filters.DateFilter(
        field_name="live_stream__live_stream_usage__end_time", lookup_expr="date"
    )
    status = django_filters.MultipleChoiceFilter(
        field_name="live_stream__status",
        choices=LiveStream.Status.choices,
        widget=forms.CheckboxSelectMultiple,
        label="Live Stream Status",
    )
    server_status = django_filters.MultipleChoiceFilter(
        field_name="live_stream__server_status",
        choices=LiveStream.ServerStatus.choices,
        widget=forms.CheckboxSelectMultiple,
        label="Live Stream Server Status",
    )
    organization_uuid = django_filters.CharFilter(
        field_name="organization__uuid", label="Organization"
    )
    server_started_date = django_filters.DateFilter(
        field_name="live_stream__start", lookup_expr="date", label="Server Started Date"
    )

    class Meta:
        model = Asset
        fields = (
            "title",
            "live_stream__status",
            "live_stream__server_status",
            "organization__uuid",
            "live_stream__live_stream_usage__end_time",
            "live_stream__start",
        )

