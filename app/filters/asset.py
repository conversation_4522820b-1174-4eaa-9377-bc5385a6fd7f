import django_filters
from django import forms
from django.contrib.postgres.search import SearchQuery, SearchRank, SearchVector
from django.db.models import Q

from app.models import Asset, AssetUsage
from app.models.video import Video


class AssetFilter(django_filters.FilterSet):
    q = django_filters.CharFilter(method="filter_by_q", label="Search")
    created_at = django_filters.DateFilter(field_name="created", lookup_expr="date")
    state = django_filters.MultipleChoiceFilter(
        field_name="video__status",
        choices=Video.Status.choices,
        widget=forms.CheckboxSelectMultiple,
    )
    parent = django_filters.CharFilter(field_name="parent__uuid")
    has_parent = django_filters.BooleanFilter(
        field_name="parent",
        lookup_expr="isnull",
        exclude=True,
    )

    class Meta:
        model = Asset
        fields = (
            "type",
            "created_by",
            "created",
            "parent",
            "video__status",
            "has_parent",
        )

    def filter_by_q(self, queryset, name, value):
        queryset = queryset.filter(Q(title__icontains=value) | Q(uuid__icontains=value))
        vector = SearchVector("title")
        query = SearchQuery(value)
        rank = SearchRank(vector, query)
        queryset = queryset.annotate(rank=rank).order_by("-rank", "-created")
        return queryset


TIME_FRAME_CHOICES = {
    key.strip(): value for value, key in AssetUsage.TimeFrames.choices
}


class AssetUsageFilter(django_filters.FilterSet):
    month = django_filters.NumberFilter(field_name="date__month", label="Month")
    year = django_filters.NumberFilter(field_name="date__year", label="Year")
    day = django_filters.NumberFilter(field_name="date__day", label="Day")
    time_frame = django_filters.CharFilter(
        method="filter_by_time_frame", label="Time Frame"
    )
    start = django_filters.DateFilter(
        field_name="date", lookup_expr="gte", label="From"
    )
    end = django_filters.DateFilter(field_name="date", lookup_expr="lte", label="To")

    ordering = django_filters.OrderingFilter(
        fields=(("date", "date"),),
        choices=(
            ("-date", "Latest"),
            ("date", "Oldest"),
        ),
    )

    class Meta:
        model = AssetUsage
        fields = ["time_frame", "date"]

    def filter_by_time_frame(self, queryset, name, value):
        if value:
            time_frame_value = TIME_FRAME_CHOICES.get(value.capitalize(), None)
            if time_frame_value is not None:
                return queryset.filter(time_frame=time_frame_value)
        return queryset
