import django_filters

from app.models import AssetUsage
from app.utils.filter import MonthFilter


class AssetUsageFilter(django_filters.FilterSet):
    month = MonthFilter(field_name="date", empty_label="Current Month")

    ordering = django_filters.OrderingFilter(
        fields=(("date", "date"),),
        choices=(
            ("-date", "Latest"),
            ("date", "Oldest"),
        ),
    )

    class Meta:
        model = AssetUsage
        fields = ["date"]
