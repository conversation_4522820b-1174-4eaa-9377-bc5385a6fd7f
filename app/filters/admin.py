import datetime

from django.contrib.admin import SimpleListFilter
from utm_tracker.models import LeadSource

from app.models import AssetUsage


class CustomYearFilter(SimpleListFilter):
    title = "Year"
    parameter_name = "year"
    template = "../templates/admin/year_filter.html"

    def __init__(self, request, params, model, model_admin, field_name="created"):
        if model == AssetUsage:
            field_name = "date"
        self.field_name = field_name
        super().__init__(request, params, model, model_admin)

    def lookups(self, request, model_admin):
        years = [
            (year, str(year)) for year in range(2022, datetime.date.today().year + 1)
        ]
        return years

    def queryset(self, request, queryset):
        if self.value() is None:
            return queryset
        return queryset.filter(**{f"{self.field_name}__year": self.value()})

    def choices(self, changelist):
        for year, year_label in self.lookup_choices:
            yield {
                "selected": self.value() == str(year),
                "query_string": changelist.get_query_string(
                    {self.parameter_name: year}
                ),
                "display": year_label,
            }


class CustomMonthFilter(SimpleListFilter):
    title = "Month"
    parameter_name = "month"
    template = "../templates/admin/month_filter.html"

    def __init__(self, request, params, model, model_admin, field_name="created"):
        if model == AssetUsage:
            field_name = "date"
        self.field_name = field_name
        super().__init__(request, params, model, model_admin)

    def lookups(self, request, model_admin):
        months = [
            (month, datetime.date(1900, month, 1).strftime("%B"))
            for month in range(1, 13)
        ]
        return months

    def queryset(self, request, queryset):
        if self.value() is None:
            return queryset
        return queryset.filter(**{f"{self.field_name}__month": self.value()})

    def choices(self, changelist):
        for month, month_label in self.lookup_choices:
            yield {
                "selected": self.value() == str(month),
                "query_string": changelist.get_query_string(
                    {self.parameter_name: month}
                ),
                "display": month_label,
            }


class KeywordFilter(SimpleListFilter):
    title = "Keyword"
    parameter_name = "keyword"

    def lookups(self, request, model_admin):
        keywords_queryset = LeadSource.objects.values_list(
            "custom_tags__keyword", flat=True
        ).distinct()
        keywords = [keyword for keyword in keywords_queryset if keyword]
        keyword_choices = [(keyword, keyword) for keyword in keywords]
        return keyword_choices

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(custom_tags__keyword=self.value())
