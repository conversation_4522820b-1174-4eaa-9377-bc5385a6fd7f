import django_filters
from django.contrib.postgres.search import <PERSON><PERSON>uery, SearchRank, SearchVector
from django.db.models import Q

class SearchFilter(django_filters.CharFilter):
    def __init__(self, search_fields, *args, **kwargs):

        self.search_fields = search_fields
        super().__init__(*args, **kwargs)

    def filter(self, qs, value):
        if not value:
            return qs

        query = Q()
        for field in self.search_fields:
            query |= Q(**{f"{field}__icontains": value})

        qs = qs.filter(query)

        vector = SearchVector(*self.search_fields)
        search_query = SearchQuery(value)
        rank = SearchRank(vector, search_query)

        return qs.annotate(rank=rank).order_by("-rank", "-created")
