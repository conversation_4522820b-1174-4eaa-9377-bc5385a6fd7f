from django import template

from app.utils.datetime import get_total_seconds

register = template.Library()


@register.filter
def humanize_time(value):
    if not value:
        return ""

    seconds = get_total_seconds(value)
    hours, remainder = divmod(seconds, 3600)
    minutes, seconds = divmod(remainder, 60)

    parts = []

    if hours:
        parts.append(f"{int(hours)} hour{'s' if hours != 1 else ''}")
    if minutes:
        parts.append(f"{int(minutes)} minute{'s' if minutes != 1 else ''}")
    if seconds:
        parts.append(f"{int(seconds)} second{'s' if seconds != 1 else ''}")

    return " ".join(parts)
