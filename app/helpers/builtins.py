from django import template

from app.utils.datetime import days_until_today

register = template.Library()


@register.simple_tag(takes_context=True)
def set(context, *args, **kwargs):
    context.push(**kwargs)
    return ""


@register.simple_tag(takes_context=True)
def get_updated_query_string(context, name, value):
    query_params = context["request"].GET.copy()
    query_params[name] = value
    return query_params.urlencode()


@register.simple_tag
def days_count(given_date):
    return 30 - days_until_today(given_date)


@register.filter
def seconds_to_hours_minutes(seconds):
    if not seconds:
        return "0 Mins"
    seconds = int(seconds)
    hours, remainder = divmod(seconds, 3600)
    minutes, _ = divmod(remainder, 60)
    if hours == 0:
        return f"{minutes} Mins"
    elif hours == 1:
        return f"1 Hr {minutes} Mins"
    return f"{hours} Hrs {minutes} Mins"


@register.filter
def calculate_time_difference_in_seconds(start_time, end_time):
    if start_time and end_time:
        return (end_time - start_time).total_seconds()
