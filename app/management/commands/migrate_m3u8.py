from django.core.management import BaseCommand
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.models import Asset, Organization, Video
from app.utils.wasabi import copy_m3u8_file, get_wasabi_config


class Command(BaseCommand):
    help = "Migrate video asset's M3U8 input to organization's storage"

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument("--asset-id", type=str, default=None)

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        asset_id = options.get("asset_id")

        organization = Organization.objects.get(uuid=org_code)
        set_current_tenant(organization)

        if asset_id:
            video = Asset.objects.get(uuid=asset_id).video
            self.migrate_video_asset(organization, video)
        else:
            for video in self.get_videos_for_migration():
                self.migrate_video_asset(organization, video)

        unset_current_tenant()

    def migrate_video_asset(self, organization, video):
        if not self.requires_migration(video):
            return

        print(f"Migrating {video.asset.uuid}")
        try:
            input_url = video.inputs.first().url
            output_path = f"{organization.bucket_name}/transcoded/{video.asset.uuid}/"
            video.update_status(Video.Status.UPLOADING)
            config = get_wasabi_config(organization)
            copy_m3u8_file(input_url, output_path, config)
            video.update_status(Video.Status.COMPLETED)
        except Exception:
            video.update_status(Video.Status.INPUT_READ_ERROR)

    def get_videos_for_migration(self):
        return Video.objects.filter(
            inputs__url__icontains=".m3u8", status=Video.Status.NOT_STARTED
        )

    def requires_migration(self, video):
        return (
            video.status == Video.Status.NOT_STARTED
            and ".m3u8" in video.inputs.first().url
        )
