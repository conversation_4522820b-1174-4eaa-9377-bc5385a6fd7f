from django.core.management.base import BaseCommand
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.domain.video import transcode_video_with_backup
from app.models import Organization, Video


class Command(BaseCommand):
    help = "Transcode videos with DRM protection while preserving existing transcoded videos"

    def add_arguments(self, parser):
        parser.add_argument(
            "--org-uuid", type=str, help="Organization UUID to process videos for"
        )
        parser.add_argument(
            "--asset-ids",
            type=str,
            nargs="+",
            help="List of specific asset IDs to process",
        )
        parser.add_argument(
            "--all-assets",
            action="store_true",
            help="Process all assets in the organization",
        )

    def handle(self, *args, **options):
        org_uuid = options.get("org_uuid")
        asset_ids = options.get("asset_ids")
        all_assets = options.get("all_assets")

        if not org_uuid:
            self.stdout.write(self.style.ERROR("Organization UUID is required"))
            return

        if not asset_ids and not all_assets:
            self.stdout.write(
                self.style.ERROR("Either asset-ids or all-assets must be specified")
            )
            return

        organization = self.get_organization_by_uuid(org_uuid)
        if not organization:
            return

        set_current_tenant(organization)
        self.stdout.write(
            self.style.SUCCESS(f"Processing organization: {organization.name}")
        )

        try:
            initiated_transcoding_count = self.transcode_videos_with_drm(
                organization, asset_ids, all_assets
            )
            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully initiated transcoding for {initiated_transcoding_count} videos with DRM protection"
                )
            )
        finally:
            unset_current_tenant()

    def get_organization_by_uuid(self, org_uuid):
        try:
            return Organization.objects.get(uuid=org_uuid)
        except Organization.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Organization with UUID {org_uuid} does not exist")
            )
            return None

    def transcode_videos_with_drm(self, organization, asset_ids, all_assets):
        initiated_transcoding_count = 0

        videos = self.get_videos_to_process(organization, asset_ids, all_assets)

        for video in videos:
            try:
                video.enable_drm_protection()
                transcode_video_with_backup(video)

                self.stdout.write(
                    self.style.SUCCESS(
                        f"Successfully initiated DRM transcoding for video {video.asset.uuid}"
                    )
                )
                initiated_transcoding_count += 1

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f"Error processing video {video.asset.uuid}: {str(e)}"
                    )
                )

        return initiated_transcoding_count

    def get_videos_to_process(self, organization, asset_ids, all_assets):
        base_query = Video.objects.filter(
            organization=organization,
            status=Video.Status.COMPLETED,
            enable_drm=False,
        )

        if all_assets:
            return base_query.all()

        if asset_ids:
            return base_query.filter(asset__uuid__in=asset_ids).all()

        return []
