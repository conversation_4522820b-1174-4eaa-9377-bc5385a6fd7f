from django.core.management import BaseCommand
from django.db.models import Q
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.domain.cloud_storage import has_object_in_folder
from app.domain.video import delete_old_transcoded_files
from app.models.asset import Asset
from app.models.organization import Organization
from app.models.video import Video


class Command(BaseCommand):
    help = "Delete vdocipher files of migrated videos for organizations."

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument("--folder-id", type=str, default=None)
        parser.add_argument("--asset-id", type=str, default=None)

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        folder_id = options.get("folder_id")
        asset_id = options.get("asset_id")

        organizations = self.get_organizations(org_code=org_code)
        for organization in organizations:
            set_current_tenant(organization)
            videos = self.get_videos_to_process(organization, folder_id, asset_id)
            if videos:
                self.delete_vdocipher_files(videos)
            else:
                print("No videos available ")
            unset_current_tenant()

    def get_organizations(self, org_code):
        if org_code:
            return [Organization.objects.get(uuid=org_code)]
        return Organization.objects.all()

    def get_videos_to_process(self, organization, folder_id, asset_id):
        if asset_id:
            return self.get_video(asset_id)
        if folder_id:
            return self.get_videos_in_folder(organization, folder_id)
        return self.get_videos(organization)

    def get_video(self, asset_id):
        try:
            asset = Asset.objects.get(uuid=asset_id)
            return [asset.video] if asset else None
        except Asset.DoesNotExist:
            print("Asset does not exist")
            return None

    def get_videos_in_folder(self, organization, folder_id):
        try:
            return Video.objects.filter(
                Q(meta_data__isnull=False) | Q(meta_data__has_tpstreams_drm=True),
                organization__uuid=organization.uuid,
                status=Video.Status.COMPLETED,
                asset__parent__uuid=folder_id,
            )
        except Asset.DoesNotExist:
            print("Folder does not exist")
            return None

    def get_videos(self, organization):
        return Video.objects.filter(
            Q(meta_data__isnull=False) | Q(meta_data__has_tpstreams_drm=True),
            organization__uuid=organization.uuid,
            status=Video.Status.COMPLETED,
        )

    def delete_vdocipher_files(self, videos):
        for video in videos:
            migrated_tpstreams_files_folder = f"transcoded/{video.asset.uuid}/new"
            if not has_object_in_folder(
                video.organization, migrated_tpstreams_files_folder
            ):
                print(f"Does not have tpstreams files - {video.asset.uuid}")
                continue
            delete_old_transcoded_files(video.asset)
            print(f"Deleting initiated for - {video.asset.uuid}")
