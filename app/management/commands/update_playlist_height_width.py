from django.core.management.base import BaseCommand
from app.models import Organization, Video, Track, Playlist
import m3u8
from django_multitenant.utils import set_current_tenant, unset_current_tenant

class Command(BaseCommand):
    help = 'Generate and store resolution and bandwidth info in Track playlists for videos uploaded before September 9 across all organizations'

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        organizations = self.get_organizations(org_code=org_code)
        for organization in organizations:
            set_current_tenant(organization)
            self.stdout.write(self.style.SUCCESS(f"Processing organization: {organization.name}"))
            videos = self.get_videos(organization)
            updated_count = self.process_videos(videos, organization)
            self.stdout.write(self.style.SUCCESS(f'Successfully updated {updated_count} Playlist instances across all organizations.'))
            unset_current_tenant()
    
    def get_organizations(self, org_code):
        if org_code:
            return Organization.objects.filter(uuid=org_code).exclude(
                status=Organization.Status.BLOCKED
            )
        return Organization.objects.exclude(status=Organization.Status.BLOCKED)

    def get_videos(self, organization):
        return Video.objects.filter(
            organization=organization,
            tracks__playlists__isnull=True,
            status=Video.Status.COMPLETED
        ).distinct()

    def process_videos(self, videos, organization):
        updated_count = 0
        for video in videos:
            try:
                track = self.get_or_create_track(video, organization)
                if self.update_playlists(track, video, organization):
                    updated_count += 1
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"An error occurred with ASSET UUID {video.asset.uuid} in Organization {organization.name}: {e}"))
        return updated_count

    def get_or_create_track(self, video, organization):
        organization_id = video.organization_id
        return Track.objects.get_or_create(
            video=video,
            organization_id=organization_id,
            type=Track.Type.PLAYLIST,
            name=f"{video.id} - Track(playlist)",
        )[0]

    def update_playlists(self, track, video, organization):
        m3u8_obj = m3u8.load(video.get_playback_url())
        playlists_data = self.create_playlists(track, m3u8_obj, video.organization_id)

        if playlists_data:
            track.playlists.set(playlists_data)
            track.save()
            self.stdout.write(self.style.SUCCESS(f"Updated Track for Video ID {video.id} in Organization {organization.name}."))
            return True
        else:
            self.stdout.write(self.style.WARNING(f"No valid playlists found in the m3u8 file for Video ID {video.id} in Organization {organization.name}."))
            return False

    def create_playlists(self, track, m3u8_obj, organization_id):
        playlists_data = []
        for playlist in m3u8_obj.playlists:
            stream_info = playlist.stream_info
            if stream_info.resolution and stream_info.bandwidth:
                width, height = stream_info.resolution
                playlist_obj = Playlist.objects.create(
                    track_id=track.id,
                    organization_id=organization_id,
                    name=f"{height}p",
                    bytes=stream_info.bandwidth,
                    width=width,
                    height=height,
                )
                playlists_data.append(playlist_obj)
        return playlists_data
