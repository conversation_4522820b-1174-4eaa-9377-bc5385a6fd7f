import math

import m3u8
from django.core.management import BaseCommand
from django.shortcuts import get_object_or_404
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.domain.cloud_storage import get_client, get_size
from app.models import Asset, Organization, Video


class Command(BaseCommand):
    help = "This will calculate correct bandwidth and update in m3u8"

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument("--asset-id", type=str, default=None)
        parser.add_argument("--all_assets", action="store_true")

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        asset_id = options.get("asset_id")
        all_assets = options.get("all_assets")

        organization = Organization.objects.get(uuid=org_code)
        set_current_tenant(organization)
        self.client = get_client(organization)

        if asset_id:
            asset = get_object_or_404(
                Asset,
                uuid=asset_id,
                video__isnull=False,
                video__status=Video.Status.COMPLETED,
            )
            self.fix_incorrect_bandwidth(asset)
        elif all_assets:
            for asset in Asset.objects.filter(
                type=Asset.Type.VIDEO, video__status=Video.Status.COMPLETED
            ):
                self.fix_incorrect_bandwidth(asset)

        unset_current_tenant()

    def fix_incorrect_bandwidth(self, asset):
        main_manifest = m3u8.load(asset.video.get_playback_url())
        for variant_manifest in main_manifest.playlists:
            video_size = self.get_resolution_size(asset, variant_manifest.base_path)
            try:
                video_duration = self.get_video_duration(variant_manifest.absolute_uri)
                variant_manifest.stream_info.bandwidth = math.floor(
                    video_size / video_duration
                )
            except Exception:
                print(f"Error occurred for : {asset.uuid}")
        self.replace_m3u8_in_storage(asset, main_manifest)
        print(asset.uuid)

    def get_resolution_size(self, asset, resolution_name):
        variant_path = asset.video.playback_url.replace("video.m3u8", resolution_name)
        video_size = get_size(asset.organization, variant_path)
        return video_size * 8  # Size in bits

    def get_video_duration(self, video_url):
        playlist = m3u8.load(video_url)
        video_duration = sum(i.get("duration") for i in playlist.data.get("segments"))
        return video_duration

    def replace_m3u8_in_storage(self, asset, main_manifest):
        self.client.put_object(
            Bucket=asset.organization.bucket_name,
            Key=asset.video.playback_url,
            Body=main_manifest.dumps(),
            ACL="public-read",
        )
