from django.core.management import BaseCommand
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.domain.subtitle import generate_subtitle
from app.models import Asset, Organization, Track


class Command(BaseCommand):
    help = "Generate subtitle for videos"

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument("--asset-id", type=str, default=None)

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        asset_id = options.get("asset_id")
        organization = Organization.objects.get(uuid=org_code)
        set_current_tenant(organization)
        if asset_id:
            self.generate_subtitle_for_asset(asset_id)
        else:
            self.generate_subtitle_for_all_assets()
        unset_current_tenant()

    def generate_subtitle_for_asset(self, asset_id):
        asset = Asset.objects.prefetch_related("video").get(uuid=asset_id)
        if not self.has_auto_generated_subtitle(asset):
            generate_subtitle(asset)
            self.update_asset(asset)
            self.stdout.write(
                self.style.SUCCESS(f"Generated subtitles for Asset - {asset}")
            )
        else:
            self.stdout.write(
                self.style.WARNING(f"Subtitle is already generated for {asset.uuid}")
            )

    def generate_subtitle_for_all_assets(self):
        assets = self.get_all_assets()
        self.stdout.write(f"Fetched {assets.count()} Assets")
        count = 0
        for asset in assets:
            if not self.has_auto_generated_subtitle(asset):
                generate_subtitle(asset)
                self.stdout.write(f"Generating subtitle for Asset {asset}")
                self.update_asset(asset)
                count += 1
        self.stdout.write(self.style.SUCCESS(f"Generated subtitles for {count} Assets"))

    def update_asset(self, asset):
        asset.video.generate_subtitle = True
        asset.video.save()

    def has_auto_generated_subtitle(self, asset):
        return asset.video.tracks.filter(
            subtitle_type=Track.SubtitleType.AUTO_GENERATED
        ).exists()

    def get_all_assets(self):
        return Asset.objects.filter(video__isnull=False).prefetch_related("video")
