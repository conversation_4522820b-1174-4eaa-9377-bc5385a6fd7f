import re
from collections import OrderedDict

from django.core.management.base import BaseCommand


class Command(BaseCommand):
    ENV_PATH = ".env"

    def add_arguments(self, parser):
        parser.add_argument(
            "--key",
            type=str,
            help="Environment variable key",
        )
        parser.add_argument("--value", type=str, help="Environment variable value")

    def handle(self, *args, **options):
        if not options.get("key") or not options.get("value"):
            raise NotImplementedError("--key and --value must be set")

        key, value = options["key"], options["value"]
        env_lines = self._load_env()
        updated_lines = self._update_key_value(env_lines, key, value)
        self._write_env(updated_lines)

    def _load_env(self):
        try:
            with open(self.ENV_PATH, "r", encoding="utf-8") as f:
                return f.readlines()
        except FileNotFoundError:
            return []
        except OSError as e:
            raise Exception(f"Failed to read {self.ENV_PATH}: {e}")

    def _update_key_value(self, lines, key, value):
        key_pattern = re.compile(rf"^(?:export )?{re.escape(key)}=")
        updated = False
        new_lines = []

        for line in lines:
            if key_pattern.match(line):
                new_lines.append(f"{key}={value}\n")
                updated = True
            else:
                new_lines.append(line)
        if not updated:
            new_lines.append(f"{key}={value}\n")

        return new_lines

    def _write_env(self, lines):
        try:
            with open(self.ENV_PATH, "w", encoding="utf-8") as f:
                f.writelines(lines)
        except OSError as e:
            raise Exception(f"Failed to write to {self.ENV_PATH}: {e}")
