import requests
from django.core.management import BaseCommand
from django.db.models import Q
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.domain.transcoder import get_transcoder
from app.models import Asset, Organization, Video


class Command(BaseCommand):
    help = "Generate thumbnails for videos without thumbnails for organizations"

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument("--folder-id", type=str, default=None)
        parser.add_argument("--asset-id", type=str, default=None)

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        folder_id = options.get("folder_id")
        asset_id = options.get("asset_id")

        organizations = self.get_organizations(org_code)

        for organization in organizations:
            set_current_tenant(organization)
            videos = self.get_videos_without_thumbnail(
                organization.uuid, folder_id, asset_id
            )
            if videos:
                self.generate_thumbnail(videos)
            else:
                print("No videos available to generate thumbnails")
            unset_current_tenant()

    def get_organizations(self, org_code):
        if org_code:
            return [Organization.objects.get(uuid=org_code)]
        else:
            return Organization.objects.all()

    def get_videos_without_thumbnail(self, org_uuid, folder_id, asset_id):
        if folder_id:
            folder = self.get_asset(folder_id)
            return self.get_videos_in_folder_without_thumbnail(folder)

        if asset_id:
            asset = self.get_asset(asset_id)
            return [asset.video] if asset else None

        return self.get_all_videos_without_thumbnail(org_uuid)

    def get_asset(self, asset_id):
        try:
            return Asset.objects.get(uuid=asset_id)
        except Asset.DoesNotExist:
            print("Asset does not exist")
            return None

    def get_videos_in_folder_without_thumbnail(self, org_uuid, folder_asset):
        if folder_asset:
            return Video.objects.filter(
                Q(thumbnails=[]) | Q(thumbnails__isnull=True),
                organization__uuid=org_uuid,
                status=Video.Status.COMPLETED,
                asset__parent__uuid=folder_asset.uuid,
            )

    def get_all_videos_without_thumbnail(self, org_uuid):
        return Video.objects.filter(
            Q(thumbnails=[]) | Q(thumbnails__isnull=True),
            organization__uuid=org_uuid,
            status=Video.Status.COMPLETED,
        )

    def generate_thumbnail(self, videos):
        for video in videos:
            try:
                get_transcoder(video.asset).generate_thumbnail()
            except requests.exceptions.HTTPError:
                pass
            print(f"Thumbnail generating initiated for {video.asset.uuid}")
