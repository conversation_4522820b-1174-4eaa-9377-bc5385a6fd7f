import os

import openpyxl
from django.core.management.base import BaseCommand
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.domain.storages import get_source_files_size, get_transcoded_file_size
from app.models import Asset, Organization, Video


class Command(BaseCommand):
    help = "Generates an Excel report of asset sizes for specified organizations"

    def add_arguments(self, parser):
        parser.add_argument(
            "--org-ids",
            nargs="+",
            help="List of organization UUIDs to process",
            required=True,
        )
        parser.add_argument(
            "--output",
            type=str,
            help="Output Excel file name",
            default="assets_size_info.xlsx",
        )

    def handle(self, *args, **options):
        org_ids = options["org_ids"]
        output_file = options["output"]
        workbook = self._create_workbook()
        self._write_organization_assets_to_workbook(workbook, org_ids)
        if workbook.sheetnames:
            self._save_workbook(workbook, output_file)
        else:
            self._cleanup_empty_workbook(output_file)

    def _create_workbook(self):
        workbook = openpyxl.Workbook()
        workbook.remove(workbook.active)
        return workbook

    def _write_organization_assets_to_workbook(self, workbook, org_ids):
        organizations = self._get_organizations(org_ids)

        if not organizations.exists():
            self.stdout.write(
                self.style.ERROR("No valid organizations found to process.")
            )
            return

        for organization in organizations:
            try:
                set_current_tenant(organization)
                asset_count = self._write_organization_assets_to_sheet(
                    workbook, organization
                )
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Processed {asset_count} assets for organization: {organization}"
                    )
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f"Error processing organization {organization.uuid}: {str(e)}"
                    )
                )
            finally:
                unset_current_tenant()

    def _get_organizations(self, org_ids):
        return Organization.objects.filter(uuid__in=org_ids).exclude(
            status=Organization.Status.BLOCKED
        )

    def _write_organization_assets_to_sheet(self, workbook, organization):
        worksheet = self._create_worksheet(workbook, organization)
        self._write_headers(worksheet)
        asset_count = self._write_assets_to_sheet(worksheet, organization)
        return asset_count

    def _write_assets_to_sheet(self, worksheet, organization):
        assets = Asset.objects.filter(video__status=Video.Status.COMPLETED).order_by(
            "-created"
        )
        asset_count = 0
        for asset in assets:
            self._write_asset_to_sheet(worksheet, asset)
            asset_count += 1
        return asset_count

    def _write_asset_to_sheet(self, worksheet, asset):
        self.stdout.write(f"Processing asset: {asset}")
        try:
            source_mb = get_source_files_size(asset) / (1024 * 1024)
            transcoded_mb = get_transcoded_file_size(asset) / (1024 * 1024)
            total_mb = source_mb + transcoded_mb
            worksheet.append(
                [
                    str(asset.uuid),
                    asset.title,
                    asset.created.strftime("%d %b %Y"),
                    source_mb,
                    transcoded_mb,
                    total_mb,
                ]
            )
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f"Error processing asset {asset.uuid}: {str(e)}")
            )

    def _create_worksheet(self, workbook, organization):
        sheet_name = f"{organization.name} - {str(organization.uuid)}"
        worksheet = workbook.create_sheet(title=sheet_name)
        self.stdout.write(
            self.style.SUCCESS(f"Processing Assets of Organization: {organization}")
        )
        return worksheet

    def _write_headers(self, worksheet):
        headers = [
            "Asset ID",
            "Title",
            "Created Date",
            "Source Size (MB)",
            "Transcoded Size (MB)",
            "Total Size (MB)",
        ]
        worksheet.append(headers)

    def _save_workbook(self, workbook, output_file):
        workbook.save(output_file)
        self.stdout.write(
            self.style.SUCCESS(
                f"Excel file '{output_file}' saved with one sheet per organization."
            )
        )

    def _cleanup_empty_workbook(self, output_file):
        self.stdout.write(
            self.style.ERROR("No organizations were successfully processed.")
        )
        if os.path.exists(output_file):
            os.unlink(output_file)
