from django.core.management import BaseCommand
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.domain.vimeo_to_wasabi_migrator import Migrator
from app.models import Organization, Video

CONTENT_PROTECTION_TYPE_MAPPING = {
    "disabled": Video.ContentProtectionType.DISABLED,
    "drm": Video.ContentProtectionType.DRM,
    "aes": Video.ContentProtectionType.AES,
}


class Command(BaseCommand):
    help = "Import videos from Vimeo"

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument("--vimeo-access-token", type=str)
        parser.add_argument("--video-url", type=str, default=None)
        parser.add_argument("--folder-id", type=str, default=None)
        parser.add_argument(
            "--content-protection-type",
            choices=CONTENT_PROTECTION_TYPE_MAPPING.keys(),
            help="Specify the content protection type for imported videos",
        )

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        vimeo_access_token = options.get("vimeo_access_token")
        video_url = options.get("video_url")
        folder_id = options.get("folder_id")
        content_protection_type = CONTENT_PROTECTION_TYPE_MAPPING.get(
            options.get("content_protection_type")
        )

        organization = Organization.objects.get(uuid=org_code)
        set_current_tenant(organization)

        migrator = Migrator(
            access_token=vimeo_access_token,
            org_id=org_code,
            content_protection_type=content_protection_type,
        )

        if folder_id:
            migrator.migrate_folder(folder_id)
        elif video_url:
            migrator.migrate_video(video_url)
        else:
            migrator.migrate_all()

        unset_current_tenant()
