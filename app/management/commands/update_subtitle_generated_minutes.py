from django.core.management import BaseCommand

from app.models.organization import Organization
from app.tasks.subtitle import UpdateSubtitlesGenerationMinutesTask


class Command(BaseCommand):
    help = "Stores minutes of subtitles generated to db for all organizations."

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument(
            "--date",
            help="Date for which to store total subtitle generated duration (YYYY-MM-DD format)",
        )

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        date_str = options.get("date")
        date = date_str if date_str else ""
        organizations = self.get_organizations(org_code=org_code)
        for organization in organizations:
            UpdateSubtitlesGenerationMinutesTask.apply_async(
                kwargs={
                    "date": date,
                    "organization_uuid": organization.uuid,
                },
                queue="usage_update_queue",
            )

    def get_organizations(self, org_code):
        if org_code:
            return Organization.objects.filter(uuid=org_code).exclude(
                status=Organization.Status.BLOCKED
            )
        return Organization.objects.exclude(status=Organization.Status.BLOCKED)
