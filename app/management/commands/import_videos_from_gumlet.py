from django.core.management import BaseCommand
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.domain.gumlet_to_wasabi_migrator import Migrator
from app.models import Organization, Video

CONTENT_PROTECTION_TYPE_MAPPING = {
    "disabled": Video.ContentProtectionType.DISABLED,
    "drm": Video.ContentProtectionType.DRM,
    "aes": Video.ContentProtectionType.AES,
}


class Command(BaseCommand):
    help = "Import videos from Vimeo"

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument("--playlist-id", type=str, default=None)
        parser.add_argument("--api-key", type=str)
        parser.add_argument(
            "--content-protection-type",
            choices=CONTENT_PROTECTION_TYPE_MAPPING.keys(),
            help="Specify the content protection type for imported videos",
        )

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        playlist_id = options.get("playlist_id")
        api_key = options.get("api_key")
        content_protection_type = CONTENT_PROTECTION_TYPE_MAPPING.get(
            options.get("content_protection_type")
        )
        organization = Organization.objects.get(uuid=org_code)
        set_current_tenant(organization)

        gumlet_migrator = Migrator(
            playlist_id=playlist_id,
            api_key=api_key,
            org_id=org_code,
            content_protection_type=content_protection_type,
        )

        gumlet_migrator.migrate_all_videos()

        unset_current_tenant()
