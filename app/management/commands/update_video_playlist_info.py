from django.core.management import BaseCommand

from app.models import Organization
from app.tasks.video import UpdateVideoPlaylistTask


class Command(BaseCommand):
    help = "This stores video resolutions info in the database"

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument("--asset-id", type=str, default=None)

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        asset_id = options.get("asset_id")
        organizations = self.get_organizations(org_code=org_code)
        for organization in organizations:
            UpdateVideoPlaylistTask.apply_async(
                kwargs={
                    "date": asset_id,
                    "organization_uuid": organization.uuid,
                }
            )

    def get_organizations(self, org_code):
        if org_code:
            return Organization.objects.filter(uuid=org_code).exclude(
                status=Organization.Status.BLOCKED
            )
        return Organization.objects.exclude(status=Organization.Status.BLOCKED)
