import csv

import requests
from django.core.management import BaseCommand
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.models import Organization
from app.tasks.video import UploadSourceVideoFromLink
from app.utils.browser import is_valid_url


class Command(BaseCommand):
    help = "Restores inaccessible source videos by fetching video URLs from a CSV file and uploading them to the organization's storage."

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument("--sheet-url", type=str)
        parser.add_argument("--asset-id", type=str, default=None)

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        asset_id = options.get("asset_id")
        sheet_url = options.get("sheet_url")

        if not org_code:
            self.stdout.write(self.style.ERROR("You must provide org_code"))
            return

        try:
            organization = Organization.objects.get(uuid=org_code)
            set_current_tenant(organization)
            self.process_assets(organization, asset_id, sheet_url)
        except Organization.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Organization with code {org_code} does not exist.")
            )

    def process_assets(self, organization, asset_id, sheet_url):
        assets = self.get_assets_with_invalid_source(asset_id, organization)

        for asset in assets:
            try:
                video_url = self.get_video_url(sheet_url, asset)
                self.stdout.write(
                    f"Video URL retrieved for asset {asset.uuid}: {video_url}"
                )

                if video_url:
                    UploadSourceVideoFromLink.apply_async(
                        kwargs={
                            "asset_id": asset.uuid,
                            "video_url": video_url,
                            "organization_uuid": organization.uuid,
                        },
                        queue="migration_queue",
                    )
                else:
                    self.stdout.write(f"No video URL found for asset {asset.uuid}")
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Error processing asset {asset.uuid}: {str(e)}")
                )
        unset_current_tenant()

    def get_assets_with_invalid_source(self, asset_id, organization):
        if asset_id:
            return self._get_asset_by_id(asset_id, organization)
        return self._get_all_invalid_assets(organization)

    def _get_asset_by_id(self, asset_id, organization):
        from app.models import Asset

        assets = []
        try:
            asset = Asset.objects.get(uuid=asset_id, organization=organization)
            if self.is_invalid_source(asset):
                assets.append(asset)
        except Asset.DoesNotExist:
            self._log_asset_not_found(asset_id)
        return assets

    def _get_all_invalid_assets(self, organization):
        from app.models import Asset, Video

        assets = []
        all_assets = Asset.objects.filter(
            type=0, video__status=Video.Status.COMPLETED, organization=organization
        )
        for asset in all_assets:
            if self.is_invalid_source(asset):
                assets.append(asset)
        return assets

    def is_invalid_source(self, asset):
        is_source_video_accessible = is_valid_url(asset.get_download_url())
        if is_source_video_accessible:
            return False
        if not asset.video.meta_data:
            asset.video.meta_data = {}
        asset.video.meta_data["is_source_encrypted"] = True
        asset.video.save(update_fields=["meta_data"])
        return True

    def get_video_url(self, sheet_url, target_asset):
        response = requests.get(sheet_url)
        response.raise_for_status()
        csv_data = response.text
        csv_reader = csv.DictReader(csv_data.splitlines())

        for row in csv_reader:
            if row["asset_uuid"] == target_asset.uuid:
                return row["video_url"]
