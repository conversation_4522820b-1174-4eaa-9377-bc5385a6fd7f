from django.core.management.base import BaseCommand

from app.models.organization import Organization


class Command(BaseCommand):
    help = "Update CDN credentials for all organizations using the specified old CDN access key."

    def add_arguments(self, parser):
        parser.add_argument(
            "--old-cdn-access-key-id",
            type=str,
            required=True,
            help="Old CDN Access Key ID to filter organizations",
        )
        parser.add_argument(
            "--new-cdn-access-key-id",
            type=str,
            required=True,
            help="New CDN Access Key ID to set",
        )
        parser.add_argument(
            "--new-cdn-secret-access-key",
            type=str,
            required=True,
            help="New CDN Secret Access Key to set",
        )

    def handle(self, *args, **options):
        old_cdn_access_key_id = options.get("old_cdn_access_key_id")
        new_cdn_access_key_id = options.get("new_cdn_access_key_id")
        new_cdn_secret_access_key = options.get("new_cdn_secret_access_key")

        organizations = Organization.objects.filter(
            cdn_access_key_id=old_cdn_access_key_id
        )
        count = organizations.update(
            cdn_access_key_id=new_cdn_access_key_id,
            cdn_secret_access_key=new_cdn_secret_access_key,
        )

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully updated CDN credentials for {count} organization(s)."
            )
        )
