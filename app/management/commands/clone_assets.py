from django.core.management import BaseCommand

from app.domain.asset import Asset<PERSON>loner
from app.models.asset import Asset
from app.models.organization import Organization


class Command(BaseCommand):
    help = "Clone assets from one organization to another. Can clone specific assets or all assets."

    def add_arguments(self, parser):
        parser.add_argument(
            "--source-org",
            type=str,
            required=True,
            help="UUID of the source organization",
        )
        parser.add_argument(
            "--dest-org",
            type=str,
            required=True,
            help="UUID of the destination organization",
        )
        parser.add_argument(
            "--asset-uuids",
            type=str,
            nargs="+",
            help="One or more asset UUIDs to clone. Multiple UUIDs can be space-separated.",
        )
        parser.add_argument(
            "--all-assets",
            action="store_true",
            help="Clone all assets from source organization",
        )

    def handle(self, *args, **options):
        source_org_uuid = options.get("source_org")
        dest_org_uuid = options.get("dest_org")
        asset_uuids = options.get("asset_uuids", [])
        all_assets = options.get("all-assets", False)

        source_org, dest_org = self.get_organizations(source_org_uuid, dest_org_uuid)
        if not (source_org and dest_org):
            return

        assets_to_clone = self.get_assets_to_clone(source_org, asset_uuids, all_assets)
        if not assets_to_clone:
            self.stdout.write(self.style.ERROR("No assets found to clone"))
            return

        self.stdout.write(
            self.style.HTTP_INFO(f"Found {len(assets_to_clone)} assets to clone")
        )
        self.clone_assets(assets_to_clone, source_org, dest_org)

    def get_organizations(self, source_org_uuid, dest_org_uuid):
        try:
            source_org = Organization.objects.get(uuid=source_org_uuid)
        except Organization.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Source organization not found: {source_org_uuid}")
            )
            return None, None

        try:
            dest_org = Organization.objects.get(uuid=dest_org_uuid)
        except Organization.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Destination organization not found: {dest_org_uuid}")
            )
            return None, None

        if source_org == dest_org:
            self.stdout.write(
                self.style.ERROR(
                    "Source and destination organizations must be different"
                )
            )
            return None, None

        return source_org, dest_org

    def get_assets_to_clone(self, source_org, asset_uuids, all_assets):
        if not asset_uuids and not all_assets:
            self.stdout.write(
                self.style.ERROR("Please provide either --asset-uuids or --all-assets")
            )
            return []

        if asset_uuids and all_assets:
            self.stdout.write(
                self.style.ERROR(
                    "Cannot use both --asset-uuids and --all-assets together"
                )
            )
            return []

        try:
            base_query = Asset.objects.filter(organization=source_org).order_by(
                "created"
            )

            if all_assets:
                return list(base_query.all())

            if asset_uuids:
                return list(base_query.filter(uuid__in=asset_uuids).all())

            return []

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error fetching assets: {str(e)}"))
            return []

    def clone_assets(self, assets, source_org, dest_org):
        cloner = AssetCloner(source_org, dest_org)

        for asset in assets:
            try:
                self.stdout.write(
                    self.style.HTTP_INFO(f"Cloning {asset.title} ({asset.uuid})")
                )
                cloned_asset = cloner.clone(asset.uuid)
                if cloned_asset:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Successfully cloned {asset.title} ({asset.uuid}) -> {cloned_asset.uuid}"
                        )
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR(
                            f"Failed to clone {asset.title} ({asset.uuid})"
                        )
                    )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Failed to clone asset {asset.uuid}: {str(e)}")
                )
