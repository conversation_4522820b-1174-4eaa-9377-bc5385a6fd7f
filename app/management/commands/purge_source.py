from django.core.management import BaseCommand
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.models import Asset, Organization, Video
from app.tasks import DeleteSourceVideoTask


class Command(BaseCommand):
    help = "Delete Source video files"

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument("--asset-id", type=str, default=None)
        parser.add_argument(
            "--all-assets", action="store_true", help="Perform operation for all assets"
        )

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        asset_id = options.get("asset_id")
        all_assets = options.get("all_assets")
        organization = Organization.objects.get(uuid=org_code)
        set_current_tenant(organization)

        print(f"Organization: {organization.name}")

        videos = self.get_videos_to_process(all_assets, asset_id)
        num_videos = len(videos) if videos else 0
        print(f"Number of assets to delete: {num_videos}")

        confirm = (
            input("Are you sure you want to delete these assets? (yes/no): ")
            .strip()
            .lower()
        )
        if confirm != "yes":
            print("Operation cancelled.")
            unset_current_tenant()
            return

        if videos:
            for video in videos:
                DeleteSourceVideoTask.apply_async(
                    kwargs={
                        "video_id": video.id,
                        "organization_uuid": video.organization.uuid,
                    },
                    queue="migration_queue",
                )
            print("Deletion tasks have been initiated.")
        else:
            print("No assets found for deletion.")

        unset_current_tenant()

    def get_videos_to_process(self, all_assets, asset_id):
        if all_assets:
            return Video.objects.filter(status=Video.Status.COMPLETED)

        if asset_id:
            video = Asset.objects.get(uuid=asset_id).video
            if video.status == Video.Status.COMPLETED:
                return [video]

        return None
