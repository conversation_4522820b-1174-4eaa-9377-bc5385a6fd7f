import json
import sys

from django.core.cache import cache
from django.core.management.base import BaseCommand
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.models import AccessToken, Asset, Organization, Video


class Command(BaseCommand):
    help = "Migrate access tokens from Redis cache to database"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.total_tokens_found_in_cache = 0
        self.successful_migrations = 0
        self.failed_migrations = 0
        self.duplicate_tokens = []

    def add_arguments(self, parser):
        parser.add_argument(
            "--org-id",
            type=str,
            required=False,
            help="Organization UUID to process. If not provided, processes all organizations.",
        )

    def handle(self, *args, **options):
        try:
            org_id = options.get("org_id")
            organizations = self.get_organizations(org_id)

            for organization in organizations:
                set_current_tenant(organization)
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Processing organization: {organization.name} ({organization.uuid})"
                    )
                )
                assets = Asset.objects.filter(
                    type=Asset.Type.VIDEO, video__status=Video.Status.COMPLETED
                )
                for asset in assets:
                    try:
                        self._migrate_asset_tokens_from_cache(organization, asset)
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(
                                f"Error processing asset {asset.uuid}: {str(e)}"
                            )
                        )
                        self.failed_migrations += 1
                unset_current_tenant()

            self._log_migration_statistics()

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"An error occurred: {e}"))
            sys.exit(1)

    def get_organizations(self, org_id):
        if org_id:
            return Organization.objects.filter(uuid=org_id).exclude(
                status=Organization.Status.BLOCKED
            )
        return Organization.objects.exclude(status=Organization.Status.BLOCKED)

    def _migrate_asset_tokens_from_cache(self, organization, asset):
        pattern = self._build_redis_cache_key_pattern(organization, asset)
        keys = cache.keys(pattern)

        if not keys:
            return

        cached_data = cache.get_many(keys)
        self.total_tokens_found_in_cache += len(keys)

        for data in cached_data.values():
            if self._save_token_to_database(data, organization, asset):
                self.successful_migrations += 1

    def _save_token_to_database(self, data, organization, asset):
        try:
            access_token = self._parse_token_data(data, organization, asset)

            if self._should_skip_token_migration(access_token, organization, asset):
                return False

            access_token.save()
            return True

        except (json.JSONDecodeError, Exception) as e:
            self.stdout.write(
                self.style.ERROR(f"Error processing token data: {str(e)}")
            )
            self.failed_migrations += 1
            return False

    def _log_migration_statistics(self):
        self.stdout.write(
            self.style.SUCCESS(
                f"Migration completed:\n"
                f"Total tokens found in cache: {self.total_tokens_found_in_cache}\n"
                f"Successfully migrated: {self.successful_migrations}\n"
                f"Failed migrations: {self.failed_migrations}\n"
                f"Duplicate tokens found: {len(self.duplicate_tokens)}"
            )
        )
        self._log_duplicate_tokens_details()

    def _build_redis_cache_key_pattern(self, organization, asset):
        return f"org_id_{organization.uuid}_asset_id_{asset.uuid}_token_*"

    def _is_token_already_exists_in_db(self, access_token, organization):
        return AccessToken.objects.filter(
            uuid=access_token.uuid, organization_id=organization.id
        ).exists()

    def _log_duplicate_token(self, access_token, asset, organization):
        self.duplicate_tokens.append(
            {
                "token_uuid": str(access_token.uuid),
                "asset_uuid": str(asset.uuid),
                "org_uuid": str(organization.uuid),
            }
        )

    def _is_valid_token(self, access_token, asset):
        if not hasattr(access_token, "uuid") or not access_token.uuid:
            self.stdout.write(
                self.style.WARNING(
                    f"Invalid token data for asset {asset.uuid}: Missing UUID"
                )
            )
            return False
        return True

    def _parse_token_data(self, data, organization, asset):
        token_dict = json.loads(data)
        token_dict["organization_id"] = str(organization.id)
        token_dict["asset_id"] = asset.id
        return AccessToken.from_dict(token_dict)

    def _should_skip_token_migration(self, access_token, organization, asset):
        is_not_permanent_token = (
            access_token.expires_after_first_usage
            or access_token.valid_until is not None
        )
        is_duplicate_token = self._is_token_already_exists_in_db(
            access_token, organization
        )
        is_invalid_token = not self._is_valid_token(access_token, asset)

        if is_duplicate_token:
            self._log_duplicate_token(access_token, asset, organization)

        return is_not_permanent_token or is_duplicate_token or is_invalid_token

    def _log_duplicate_tokens_details(self):
        if not self.duplicate_tokens:
            return

        self.stdout.write(self.style.WARNING("\nDuplicate tokens found:"))
        for token in self.duplicate_tokens:
            self.stdout.write(
                self.style.WARNING(
                    f"Token UUID: {token['token_uuid']}, "
                    f"Asset UUID: {token['asset_uuid']}, "
                    f"Org UUID: {token['org_uuid']}"
                )
            )
