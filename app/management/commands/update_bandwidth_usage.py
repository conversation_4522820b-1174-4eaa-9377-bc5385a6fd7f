from datetime import datetime

from django.core.management import BaseCommand

from app.tasks import UpdateBandwidthUsageTask


class Command(BaseCommand):
    help = "Updates all the organization bandwidth usage."

    def add_arguments(self, parser):
        parser.add_argument("--date", type=str, help="Date in YYYY-MM-DD format")

    def handle(self, *args, **options):
        try:
            date = (
                datetime.strptime(options["date"], "%Y-%m-%d").date()
                if options["date"]
                else None
            )
        except ValueError:
            self.stderr.write(self.style.ERROR("Invalid date format. Use YYYY-MM-DD"))
            return

        UpdateBandwidthUsageTask.apply_async(
            queue="usage_update_queue", kwargs={"date": date} if date else {}
        )
