from django.core.management import BaseCommand
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.models import Asset, Organization, Video
from app.tasks import DeleteSourceVideoTask
from app.utils.browser import is_valid_url


class Command(BaseCommand):
    help = "Delete Source video files"

    def add_arguments(self, parser):
        parser.add_argument(
            "--all-orgs",
            action="store_true",
            help="Perform operation for all organizations",
        )
        parser.add_argument("--org-code", type=str)
        parser.add_argument("--asset-id", type=str, default=None)
        parser.add_argument(
            "--all-assets", action="store_true", help="Perform operation for all assets"
        )

    def handle(self, *args, **options):
        all_orgs = options.get("all_orgs")
        org_code = options.get("org_code")
        asset_id = options.get("asset_id")
        all_assets = options.get("all_assets")

        organizations = self.get_organizations(all_orgs, org_code)

        for organization in organizations:
            set_current_tenant(organization)
            print(f"Organization: {organization.name}")

            videos = self.get_videos_to_process(all_assets, asset_id)
            num_videos = len(videos) if videos else 0
            print(f"Number of assets for {organization.name}: {num_videos}")

            if not self.confirm_deletion():
                print(f"Operation cancelled for {organization.name}.")
                unset_current_tenant()
                continue

            self.process_video_deletion(videos)
            unset_current_tenant()

    def get_organizations(self, all_orgs, org_code):
        if all_orgs:
            return Organization.objects.all()
        if org_code:
            return [Organization.objects.get(uuid=org_code)]
        return []

    def get_videos_to_process(self, all_assets, asset_id):
        if all_assets:
            return (
                Video.objects.filter(status=Video.Status.COMPLETED)
                .exclude(meta_data__is_source_deleted=True)
                .exclude(meta_data__is_source_available=True)
            )

        if asset_id:
            video = Asset.objects.get(uuid=asset_id).video
            if video.status == Video.Status.COMPLETED and not video.meta_data.get(
                "is_source_deleted", False
            ):
                if not video.meta_data.get("is_source_available", False):
                    return [video]

        return None

    def confirm_deletion(self):
        confirm = (
            input(
                "Are you sure you want to delete the encrypted video source? (yes/no): "
            )
            .strip()
            .lower()
        )
        return confirm == "yes"

    def process_video_deletion(self, videos):
        if not videos:
            print("No videos found for processing.")
            return

        for video in videos:
            video_input = video.inputs.first()
            if not video_input:
                print(f"Video ID {video.id}: No input available, skipping.")
                continue

            if is_valid_url(video_input.get_input_url()):
                self.mark_source_as_available(video)
            else:
                self.initiate_deletion_task(video)

    def mark_source_as_available(self, video):
        if not video.meta_data:
            video.meta_data = {}
        video.meta_data["is_source_available"] = True
        video.save(update_fields=["meta_data"])
        print(f"Video ID {video.id}: Source is valid, marked as available.")

    def initiate_deletion_task(self, video):
        DeleteSourceVideoTask.apply_async(
            kwargs={
                "video_id": video.id,
                "organization_uuid": video.organization.uuid,
            },
            queue="migration_queue",
        )
        print(f"Deletion task initiated for video ID {video.id}.")
