from django.core.management import BaseCommand
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.models import Asset, Organization


class Command(BaseCommand):
    help = (
        "This calculates the size of assets that haven't had their size computed yet."
    )

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)

    def handle(self, *args, **options):
        org_code = options.get("org_code")

        organization = Organization.objects.get(uuid=org_code)
        set_current_tenant(organization)
        print("Organization: ", organization)
        assets = Asset.objects.filter(bytes__isnull=True)
        for asset in assets:
            print("updating asset", asset.uuid, "-", asset.title)
            asset.update_size()
            print("updated size: ", asset.bytes)

        unset_current_tenant()
