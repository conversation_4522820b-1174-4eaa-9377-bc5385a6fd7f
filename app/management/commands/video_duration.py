from datetime import timed<PERSON><PERSON>

import m3u8
from django.core.management import Base<PERSON>ommand
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.models import Asset, Organization, Video
from app.utils.duration import get_duration


class Command(BaseCommand):
    help = "Update duration of videos"

    def add_arguments(self, parser):
        parser.add_argument(
            "--all-orgs",
            action="store_true",
            help="Perform operation for all organizations",
        )
        parser.add_argument("--org-code", type=str)
        parser.add_argument("--asset-id", type=str, default=None)
        parser.add_argument(
            "--all-assets", action="store_true", help="Perform operation for all assets"
        )
        parser.add_argument(
            "--force", action="store_true", help="Force update even if duration exists"
        )

    def handle(self, *args, **options):
        all_orgs = options.get("all_orgs")
        org_code = options.get("org_code")
        asset_id = options.get("asset_id")
        all_assets = options.get("all_assets")
        force_update = options.get("force")

        organizations = self.get_organizations(all_orgs, org_code)

        for organization in organizations:
            set_current_tenant(organization)
            videos = self.get_videos_to_process(all_assets, asset_id, force_update)

            if videos:
                self.update_video_duration(videos)
            else:
                print("No videos available to update duration")

            unset_current_tenant()

    def get_organizations(self, all_orgs, org_code):
        if all_orgs:
            return Organization.objects.all()
        if org_code:
            return [Organization.objects.get(uuid=org_code)]
        return []

    def get_videos_to_process(self, all_assets, asset_id, force_update):
        if all_assets and force_update:
            return Video.objects.filter(status=Video.Status.COMPLETED)

        if all_assets:
            return Video.objects.filter(duration=None, status=Video.Status.COMPLETED)

        if asset_id:
            video = Asset.objects.get(uuid=asset_id).video
            if video.status == Video.Status.COMPLETED and (
                force_update or not video.duration
            ):
                return [video]

        return None

    def update_video_duration(self, videos):
        for video in videos:
            duration = self.get_duration(video)
            self.save_duration(video, duration)

    def get_duration(self, video):
        try:
            input_url = video.inputs.first().get_input_url()
            duration = get_duration(input_url)
            if duration > 0:
                return timedelta(seconds=round(duration))
        except Exception as e:
            print(
                f"Error fetching duration using get_duration for {video.asset.uuid}: {str(e)}"
            )

        return self.fetch_duration_for_video_using_playback_url(video)

    def fetch_duration_for_video_using_playback_url(self, video):
        video_duration = 0
        try:
            main_manifest = m3u8.load(video.get_playback_url())

            for variant_manifest in main_manifest.playlists:
                video_duration = self.calculate_video_duration(
                    variant_manifest.absolute_uri
                )
                break

        except Exception as e:
            print(f"Error fetching duration for {video.asset.uuid}: {str(e)}")

        return video_duration

    def calculate_video_duration(self, video_url):
        playlist = m3u8.load(video_url)
        video_duration = sum(i.get("duration") for i in playlist.data.get("segments"))
        return timedelta(seconds=round(video_duration))

    def save_duration(self, video, duration):
        if duration and duration.total_seconds() > 0:
            video.duration = duration
            video.save(update_fields=["duration"])
            print(f"{video} duration: {video.duration}")
        else:
            print(f"No duration for {video.asset.uuid}")
