from django.core.management import BaseCommand

from app.models.organization import Organization
from app.tasks.analytics import StoreVideoAnalyticsTask


class Command(BaseCommand):
    help = "Stores video analytics from wasabi to db for all organizations."

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument(
            "--date", help="Date for which to store video analytics (YYYY-MM-DD format)"
        )

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        date_str = options.get("date")
        date = date_str if date_str else ""
        organizations = self.get_organizations(org_code=org_code)
        for organization in organizations:
            StoreVideoAnalyticsTask.apply_async(
                kwargs={
                    "date": date,
                    "organization_uuid": organization.uuid,
                }
            )

    def get_organizations(self, org_code):
        if org_code:
            return [Organization.objects.get(uuid=org_code)]
        return Organization.objects.all()
