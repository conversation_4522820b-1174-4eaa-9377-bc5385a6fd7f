from django.core.management import BaseCommand

from app.models import Organization
from app.tasks.video import GenerateSourceFromHLSVideo


class Command(BaseCommand):
    help = "This stores video resolutions info in the database"

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument("--asset-id", type=str, default=None)
        parser.add_argument("--input-contains", type=str, default=None)

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        asset_id = options.get("asset_id")
        input_contains = options.get("input_contains")

        if not asset_id and not input_contains:
            self.stdout.write(
                self.style.ERROR("You must provide either asset_id or input_contains")
            )
            return

        organizations = self.get_organizations(org_code=org_code)
        for organization in organizations:
            assets = self.get_assets(asset_id, input_contains, organization)

            for asset in assets:
                GenerateSourceFromHLSVideo.apply_async(
                    kwargs={
                        "asset_id": asset.uuid,
                        "input_contains": input_contains,
                        "organization_uuid": organization.uuid,
                    },
                    queue="migration_queue",
                )

    def get_organizations(self, org_code):
        if org_code:
            return Organization.objects.filter(uuid=org_code).exclude(
                status=Organization.Status.BLOCKED
            )
        return Organization.objects.exclude(status=Organization.Status.BLOCKED)

    def get_assets(self, asset_id, input_contains, organization):
        from app.models import Asset

        if asset_id:
            return [Asset.objects.get(uuid=asset_id, organization=organization)]
        return Asset.objects.filter(
            video__inputs__url__contains=input_contains, organization=organization
        )
