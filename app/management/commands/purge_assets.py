from django.core.management import BaseCommand

from app.models.organization import Organization
from app.tasks import purge_assets_task


class Command(BaseCommand):
    help = "Permanently remove assets deleted over 30 days ago."

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument(
            "--days", type=int, help="Number of days to look back for deleted assets"
        )

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        days = options.get("days", 30)
        organizations = self.get_organizations(org_code=org_code)

        for organization in organizations:
            purge_assets_task.delay(organization.uuid, days)

    def get_organizations(self, org_code):
        if org_code:
            return [Organization.objects.get(uuid=org_code)]
        return Organization.objects.all()
