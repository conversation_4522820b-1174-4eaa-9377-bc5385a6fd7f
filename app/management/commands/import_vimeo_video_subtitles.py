from django.core.management import BaseCommand
from django_multitenant.utils import set_current_tenant

from app.domain.subtitle import upload_subtitle_from_url
from app.models import Asset, Organization
from app.models.track import Track
from app.models.videoimport import ImportedVideo
from app.utils.vimeo import Interface as Vimeo


class Command(BaseCommand):
    help = "Import Vimeo Video Subtitles."

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument("--vimeo-access-token", type=str)
        parser.add_argument("--asset-id", type=str, default=None)

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        vimeo_access_token = options.get("vimeo_access_token")
        asset_id = options.get("asset_id")

        organization = Organization.objects.get(uuid=org_code)
        set_current_tenant(organization)
        self.vimeo = Vimeo(vimeo_access_token)

        if asset_id:
            self.import_subtitles_for_asset(asset_id=asset_id)
        else:
            self.import_subtitles_for_all_assets()

    def import_subtitles_for_asset(self, asset_id=None):
        asset = Asset.objects.get(uuid=asset_id)
        imported_videos = asset.video.importedvideo_set.all()
        self._import_subtitles_for_videos(imported_videos)

    def import_subtitles_for_all_assets(self, asset_id=None):
        imported_videos = ImportedVideo.objects.filter(
            source=ImportedVideo.Source.VIMEO
        )
        self._import_subtitles_for_videos(imported_videos)

    def _import_subtitles_for_videos(self, imported_videos):
        failed_asset_ids = []

        for imported_video in imported_videos:
            if not imported_video or not imported_video.video:
                continue
            asset = imported_video.video.asset

            if Track.objects.filter(
                video=asset.video, type=Track.Type.SUBTITLE
            ).exists():
                continue

            subtitles = self.vimeo.get_video_subtitles(imported_video.uri)

            for subtitle in subtitles:
                try:
                    upload_subtitle_from_url(
                        asset, subtitle.name, subtitle.language, subtitle.link
                    )
                except Exception as error:
                    failed_asset_ids.append(asset.uuid)
                    self.stderr.write(
                        self.style.ERROR(
                            f"Error uploading subtitle for video asset ' {asset.uuid}:': {error}"
                        )
                    )

        if failed_asset_ids:
            self.stdout.write(
                f"Subtitle import failed for the following Asset ids : {failed_asset_ids}"
            )
