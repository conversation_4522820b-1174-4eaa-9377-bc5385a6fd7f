from django.core.management import BaseCommand
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.domain.cloud_storage import get_bucket_policy, get_client
from app.models.organization import Organization


class Command(BaseCommand):
    help = "Deletes bucket policies for specific organizations based on the --org-code argument."

    def add_arguments(self, parser):
        parser.add_argument(
            "--org-code", nargs="+", type=str, help="List of organization UUIDs"
        )

    def handle(self, *args, **options):
        org_codes = options.get("org_code")

        for org_id in org_codes:
            self.delete_organization_bucket_policy(org_id)

    def delete_organization_bucket_policy(self, org_id):
        self.stdout.write(
            self.style.SUCCESS(f"Processing organization with UUID: {org_id}")
        )
        try:
            organization = Organization.objects.get(uuid=org_id)
            set_current_tenant(organization)
            current_bucket_policy = get_bucket_policy(organization)
            self.stdout.write(
                self.style.SUCCESS(
                    f"Retrieved organization: {organization.name} with bucket name: {organization.bucket_name}\n"
                    f"Current bucket policy: {current_bucket_policy}"
                )
            )
            client = get_client(organization)
            self.stdout.write(
                self.style.SUCCESS(
                    f"Obtained cloud storage client for organization: {organization.name}"
                )
            )
            response = client.delete_bucket_policy(Bucket=organization.bucket_name)
            self.stdout.write(
                self.style.SUCCESS(
                    f"Deleted bucket policy for bucket: {organization.bucket_name}, Response: {response}"
                )
            )
            unset_current_tenant()
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(
                    f"An error occurred while processing organization {org_id}: {e}"
                )
            )
