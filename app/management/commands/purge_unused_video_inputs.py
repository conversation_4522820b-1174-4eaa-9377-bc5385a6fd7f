import os

from django.core.management.base import BaseCommand
from django.db.models import Count
from django_multitenant.utils import set_current_tenant

from app.models import Video, VideoInput, Organization
from app.domain.cloud_storage import delete_file, DeleteError

class Command(BaseCommand):
    help = "Delete extra inputs from migrated videos"

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument("--asset-id", type=str, default=None)

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        asset_id = options.get("asset_id")
        organization = Organization.objects.get(uuid=org_code)
        set_current_tenant(organization)

        videos_with_multiple_inputs = self.get_videos_with_multiple_inputs(asset_id)

        for video_id in videos_with_multiple_inputs:
            video = Video.objects.get(id=video_id)
            self.delete_extra_inputs(video)

    def get_videos_with_multiple_inputs(self, asset_id):
        filter_params = {'inputs__isnull': False}
        if asset_id:
            filter_params['asset__uuid'] = asset_id

        return (
            Video.objects
            .filter(**filter_params)
            .values_list('id', flat=True)
            .annotate(input_count=Count('inputs'))
            .filter(input_count__gt=1)
        )

    def delete_extra_inputs(self, video):
        file_paths = list(video.inputs.values_list("url", flat=True))
        resolutions = [self.extract_resolution(path) for path in file_paths]

        highest_resolution = max(resolutions)

        for path, resolution in zip(file_paths, resolutions):
            if resolution != highest_resolution:
                try:
                    delete_file(video.organization, path)
                    self.delete_video_input(video, path)
                    self.stdout.write(self.style.SUCCESS(f"{video.asset.uuid} File '{path}' deleted."))
                except DeleteError as e:
                    self.stderr.write(self.style.ERROR(f"Error deleting {video.asset.uuid} file '{path}': {e}"))

    def delete_video_input(self, video, path):
        video_input = VideoInput.objects.get(video=video, url=path)
        video_input.delete()

    def extract_resolution(self, file_path):
        resolution = os.path.splitext(file_path)[0].split('/')[-1]
        return int(resolution.rstrip('p'))
