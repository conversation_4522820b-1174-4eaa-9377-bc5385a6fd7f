from django.core.management import BaseCommand
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.models import Organization
from app.tasks.video import GenerateSourceFromDRMVideo
from app.utils.browser import is_valid_url


class Command(BaseCommand):
    help = "Restores inaccessible source videos by DRM segments"

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument("--asset-id", type=str, default=None)

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        asset_id = options.get("asset_id")

        if not org_code:
            self.stdout.write(self.style.ERROR("You must provide org_code"))
            return

        self.stdout.write(self.style.NOTICE("Starting restoration process..."))
        self.stdout.write(
            self.style.NOTICE(
                f"Received arguments - org_code: {org_code}, asset_id: {asset_id}"
            )
        )

        try:
            self.stdout.write(
                self.style.NOTICE(f"Fetching organization with code: {org_code}")
            )
            organization = Organization.objects.get(uuid=org_code)
            self.stdout.write(
                self.style.SUCCESS(f"Organization {organization.name} found")
            )
            set_current_tenant(organization)
            self.stdout.write(self.style.NOTICE("Tenant set for the organization"))
            self.process_assets(organization, asset_id)
        except Organization.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Organization with code {org_code} does not exist.")
            )
        finally:
            unset_current_tenant()
            self.stdout.write(self.style.NOTICE("Tenant unset"))

    def process_assets(self, organization, asset_id):
        self.stdout.write(
            self.style.NOTICE(
                f"Processing assets for organization: {organization.name}"
            )
        )
        assets = self.get_assets_with_invalid_source(asset_id, organization)
        self.stdout.write(
            self.style.SUCCESS(f"Number of assets to process: {len(assets)}")
        )

        for asset in assets:
            self.stdout.write(
                self.style.NOTICE(f"Processing asset with UUID: {asset.uuid}")
            )
            try:
                GenerateSourceFromDRMVideo.apply_async(
                    kwargs={
                        "asset_id": asset.uuid,
                        "organization_uuid": organization.uuid,
                    },
                    queue="migration_queue",
                )
                self.stdout.write(
                    self.style.SUCCESS(f"Task queued for asset {asset.uuid}")
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Error processing asset {asset.uuid}: {str(e)}")
                )

    def get_assets_with_invalid_source(self, asset_id, organization):
        if asset_id:
            self.stdout.write(self.style.NOTICE(f"Fetching asset by ID: {asset_id}"))
            return self._get_asset_by_id(asset_id, organization)
        self.stdout.write(self.style.NOTICE("Fetching all invalid assets"))
        return self._get_all_invalid_assets(organization)

    def _get_asset_by_id(self, asset_id, organization):
        from app.models import Asset

        self.stdout.write(
            self.style.NOTICE(f"Getting single asset with UUID: {asset_id}")
        )
        assets = []
        try:
            asset = Asset.objects.get(uuid=asset_id, organization=organization)
            self.stdout.write(self.style.SUCCESS(f"Asset found: {asset.uuid}"))
            if self.is_invalid_source(asset):
                self.stdout.write(self.style.NOTICE(f"Asset {asset.uuid} is invalid"))
                assets.append(asset)
        except Asset.DoesNotExist:
            self._log_asset_not_found(asset_id)
        return assets

    def _get_all_invalid_assets(self, organization):
        from app.models import Asset, Video

        self.stdout.write(self.style.NOTICE("Fetching all invalid assets"))
        assets = []
        all_assets = Asset.objects.filter(
            type=0,
            video__status=Video.Status.COMPLETED,
            organization=organization,
            video__content_protection_type=Video.ContentProtectionType.DRM,
        )
        self.stdout.write(
            self.style.SUCCESS(f"Total assets retrieved: {len(all_assets)}")
        )
        for asset in all_assets:
            self.stdout.write(
                self.style.NOTICE(f"Checking asset with UUID: {asset.uuid}")
            )
            if self.is_invalid_source(asset):
                self.stdout.write(
                    self.style.NOTICE(f"Asset with UUID: {asset.uuid} is invalid")
                )
                assets.append(asset)
        return assets

    def is_invalid_source(self, asset):
        self.stdout.write(
            self.style.NOTICE(f"Validating source URL for asset {asset.uuid}")
        )
        is_source_video_accessible = is_valid_url(asset.get_download_url())
        if is_source_video_accessible:
            self.stdout.write(
                self.style.SUCCESS(f"Asset {asset.uuid} source is accessible")
            )
            return False
        self.stdout.write(
            self.style.NOTICE(f"Asset {asset.uuid} source is not accessible")
        )
        if not asset.video.meta_data:
            self.stdout.write(
                self.style.NOTICE(f"Initializing meta_data for asset {asset.uuid}")
            )
            asset.video.meta_data = {}
        asset.video.meta_data["is_source_encrypted"] = True
        asset.video.save(update_fields=["meta_data"])
        self.stdout.write(
            self.style.SUCCESS(f"Updated meta_data for asset {asset.uuid}")
        )
        return True

    def _log_asset_not_found(self, asset_id):
        self.stdout.write(self.style.ERROR(f"Asset with ID {asset_id} does not exist"))
