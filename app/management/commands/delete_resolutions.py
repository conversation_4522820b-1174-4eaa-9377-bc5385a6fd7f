from django.core.management import BaseCommand
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.domain.cloud_storage import delete_folder
from app.domain.cloudfront import invalidate_cache
from app.models import Asset, Organization, Video
from app.utils.file import delete_folder as delete_moved_file_from_local
from app.utils.m3u8 import (
    check_resolutions_in_playlist,
    create_m3u8_file_in_local,
    get_m3u8_content,
    remove_variant_playlists,
)
from app.utils.wasabi import get_wasabi_config, move_files_to_wasabi


class Command(BaseCommand):
    help = (
        "This calculates the size of assets that haven't had their size computed yet."
    )

    def add_arguments(self, parser):
        parser.add_argument("--org-code", type=str)
        parser.add_argument("--asset-id", type=str)
        parser.add_argument(
            "--resolutions", nargs="+", type=int, help="List of resolutions"
        )

    def handle(self, *args, **options):
        org_code = options.get("org_code")
        asset_id = options.get("asset_id")
        resolutions = options.get("resolutions")

        organization = Organization.objects.get(uuid=org_code)
        set_current_tenant(organization)

        assets = self.get_assets(asset_id)

        for asset in assets:
            m3u8_url = asset.video.get_playback_url()
            output_path = f"{asset.organization.bucket_name}/transcoded/{asset.uuid}"
            if check_resolutions_in_playlist(get_m3u8_content(m3u8_url), resolutions):
                print(f"Modifying assets : {asset.uuid}")
                m3u8_file_path = self.create_modified_m3u8_file(m3u8_url, resolutions)
                config = get_wasabi_config(asset.organization)
                move_files_to_wasabi(
                    input_file=m3u8_file_path,
                    output_path=output_path,
                    config=config,
                    make_public=True,
                )
                delete_moved_file_from_local(m3u8_file_path)
                invalidate_cache(
                    asset.organization,
                    [
                        f"/{asset.video.playback_url}",
                    ],
                )
                for resolution in resolutions:
                    delete_folder(
                        asset.organization, f"transcoded/{asset.uuid}/{resolution}p"
                    )
                self.save_deleted_resolution_info_to_video(
                    asset.video,
                    {"deleted_resolutions": resolutions, "resolution_deleted": True},
                )

        unset_current_tenant()

    def get_assets(self, asset_id):
        if asset_id:
            return Asset.objects.filter(
                uuid=asset_id,
                type=Asset.Type.VIDEO,
                video__status=Video.Status.COMPLETED,
                video__meta_data__resolution_deleted__isnull=True,
            )
        return Asset.objects.filter(
            video__status=Video.Status.COMPLETED,
            video__meta_data__resolution_deleted__isnull=True,
            type=Asset.Type.VIDEO,
        )

    def create_modified_m3u8_file(self, m3u8_url, resolutions):
        m3u8_content = remove_variant_playlists(get_m3u8_content(m3u8_url), resolutions)
        m3u8_file_path = create_m3u8_file_in_local(content=m3u8_content)
        return m3u8_file_path

    def save_deleted_resolution_info_to_video(self, video, deleted_resolution_data):
        video.meta_data = deleted_resolution_data
        video.save(update_fields=["meta_data"])
