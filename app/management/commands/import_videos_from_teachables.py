from django.core.management.base import BaseCommand

from app.domain.teachable_to_wasabi_migrator import TeachableMigrator
from app.models import Video


class Command(BaseCommand):
    help = "Import videos from Teachable"

    CONTENT_PROTECTION_MAP = {
        "drm": Video.ContentProtectionType.DRM,
        "aes": Video.ContentProtectionType.AES,
        "aes_signed_url": Video.ContentProtectionType.AES_WITH_SIGNED_URL,
        "none": Video.ContentProtectionType.DISABLED,
    }

    def add_arguments(self, parser):
        parser.add_argument(
            "--api-key",
            type=str,
            required=True,
            help="Teachable API key",
        )
        parser.add_argument(
            "--org-id",
            type=str,
            required=True,
            help="Organization ID",
        )
        parser.add_argument(
            "--course-id",
            type=str,
            required=False,
            help="Specific course ID to import (optional)",
        )
        parser.add_argument(
            "--content-protection",
            type=str,
            choices=list(self.CONTENT_PROTECTION_MAP.keys()),
            default="none",
            help="Content protection type (drm, aes, aes_signed_url, or none)",
        )
        parser.add_argument(
            "--transcoding-queue-name",
            type=str,
            required=False,
            help="Queue name for transcoding (optional)",
        )

    def handle(self, *args, **options):
        api_key = options["api_key"]
        org_id = options["org_id"]
        course_id = options.get("course_id")
        content_protection = self.CONTENT_PROTECTION_MAP[options["content_protection"]]
        transcoding_queue_name = options.get("transcoding_queue_name")

        migrator = TeachableMigrator(
            api_key, org_id, content_protection, transcoding_queue_name
        )
        migrator.migrate_course(course_id)
