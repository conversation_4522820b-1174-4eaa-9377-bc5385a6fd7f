from tenacity import retry, stop_after_attempt, wait_exponential

from app.utils.requests import Session


class APIInterface:
    def __init__(self):
        self.session = Session(retry_count=3)
        self.session.headers.update(self._get_headers())

    def _get_headers(self):
        raise NotImplementedError("Subclasses must implement _get_headers method.")

    def _get(self, url, params=None):
        return self._request(url=url, method="get", params=params)

    def _post(self, url, data):
        return self._request(url=url, method="post", data=data)

    @retry(
        wait=wait_exponential(multiplier=1, max=10),
        stop=stop_after_attempt(10),
        reraise=True,
    )
    def _request(self, url, method="get", data=None, params=None):
        response = self.session.request(
            url=url,
            method=method,
            data=data,
            params=params,
            timeout=20,
        )
        response.raise_for_status()
        return response.json()

    def _fetch_all_pages(self, fetch_func, func_kwargs={}):
        items = []
        page = 1
        while True:
            response, has_next_page = fetch_func(**func_kwargs, page=page)
            items.extend(response)
            if not has_next_page:
                break
            page += 1
        return items
