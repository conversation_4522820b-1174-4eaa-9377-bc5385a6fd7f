import os
import xml.etree.ElementTree as ET
from datetime import timedelta

import requests
from mpegdash.parser import MPEGDASHParser


class DASHTrimmerError(Exception):
    pass


def get_highest_resolution_folder_path(dash_url):
    mpd = MPEGDASHParser.parse(dash_url)
    video_adaptation_set = find_video_adaptation_set(mpd.periods[0].adaptation_sets)
    highest_resolution_base_url = find_highest_resolution_base_url(video_adaptation_set)
    video_transcoded_folder_path, mpd_file_path = os.path.split(dash_url)
    return os.path.join(video_transcoded_folder_path, highest_resolution_base_url)


def find_video_adaptation_set(adaptation_sets):
    for adaptation_set in adaptation_sets:
        if adaptation_set.content_type == "video":
            return adaptation_set


def find_highest_resolution_base_url(video_adaptation_set):
    highest_resolution = 0
    highest_resolution_base_url = ""
    for representation in video_adaptation_set.representations:
        if representation.height > highest_resolution:
            highest_resolution = representation.height
            highest_resolution_base_url = representation.base_urls[0].base_url_value
    return highest_resolution_base_url


def get_mpd_content(mpd_url):
    try:
        response = requests.get(mpd_url, timeout=30)
        response.raise_for_status()
        return response.text
    except Exception as e:
        raise DASHTrimmerError(
            f"Failed to fetch MPD content from {mpd_url}: {str(e)}"
        ) from e


def trim_mpd_content(mpd_content, start_time, end_time):
    try:
        root = parse_mpd_content(mpd_content)
        update_mpd_duration(root, start_time, end_time)
        trim_mpd_periods(root, start_time, end_time)
        return serialize_mpd(root)
    except Exception as e:
        raise DASHTrimmerError(f"Failed to parse and trim MPD: {str(e)}") from e


def parse_mpd_content(mpd_content):
    DASH_NAMESPACE = "urn:mpeg:dash:schema:mpd:2011"
    CENC_NAMESPACE = "urn:mpeg:cenc:2013"

    ET.register_namespace("", DASH_NAMESPACE)
    ET.register_namespace("cenc", CENC_NAMESPACE)

    return ET.fromstring(mpd_content)


def update_mpd_duration(root, start_time, end_time):
    trim_duration = end_time - start_time
    duration_str = seconds_to_duration_string(trim_duration)
    root.set("mediaPresentationDuration", duration_str)


def trim_mpd_periods(root, start_time, end_time):
    DASH_NAMESPACE = "urn:mpeg:dash:schema:mpd:2011"
    for period in root.findall(f".//{{{DASH_NAMESPACE}}}Period"):
        trim_period(period, start_time, end_time, DASH_NAMESPACE)


def serialize_mpd(root):
    return ET.tostring(root, encoding="unicode", xml_declaration=True)


def trim_period(period, start_time, end_time, namespace):
    for adaptation_set in period.findall(f".//{{{namespace}}}AdaptationSet"):
        trim_adaptation_set(adaptation_set, start_time, end_time, namespace)


def trim_adaptation_set(adaptation_set, start_time, end_time, namespace):
    for representation in adaptation_set.findall(f".//{{{namespace}}}Representation"):
        trim_representation(representation, start_time, end_time, namespace)


def trim_representation(representation, start_time, end_time, namespace):
    segment_template = get_segment_template(representation, namespace)
    if not segment_template:
        return

    segment_timeline = get_segment_timeline(segment_template, namespace)
    if not segment_timeline:
        return

    timescale = int(segment_template.get("timescale", 90000))
    start_time_scaled = int(start_time * timescale)
    end_time_scaled = int(end_time * timescale)

    new_segments, new_start_number, new_presentation_offset = trim_segment_timeline(
        segment_timeline, start_time_scaled, end_time_scaled, namespace
    )

    update_segment_template_attributes(
        segment_template, new_start_number, new_presentation_offset
    )
    rebuild_segment_timeline(segment_timeline, new_segments, namespace)


def get_segment_template(representation, namespace):
    return representation.find(f".//{{{namespace}}}SegmentTemplate")


def get_segment_timeline(segment_template, namespace):
    return segment_template.find(f".//{{{namespace}}}SegmentTimeline")


def update_segment_template_attributes(
    segment_template, start_number, presentation_offset
):
    if start_number is not None:
        segment_template.set("startNumber", str(start_number))
    if presentation_offset is not None:
        segment_template.set("presentationTimeOffset", str(presentation_offset))


def rebuild_segment_timeline(segment_timeline, new_segments, namespace):
    segment_timeline.clear()
    for segment_data in new_segments:
        create_segment_element(segment_timeline, segment_data, namespace)


def create_segment_element(segment_timeline, segment_data, namespace):
    s_element = ET.SubElement(segment_timeline, f"{{{namespace}}}S")
    s_element.set("t", str(segment_data["t"]))
    s_element.set("d", str(segment_data["d"]))
    if segment_data.get("r") is not None and segment_data["r"] > 0:
        s_element.set("r", str(segment_data["r"]))


def trim_segment_timeline(
    segment_timeline, start_time_scaled, end_time_scaled, namespace
):
    segments = parse_segments_from_timeline(segment_timeline, namespace)
    trimmed_segments = filter_segments_by_time_range(
        segments, start_time_scaled, end_time_scaled
    )
    new_segments = group_consecutive_segments(trimmed_segments)

    new_start_number = trimmed_segments[0]["number"] if trimmed_segments else 1
    new_presentation_offset = trimmed_segments[0]["start"] if trimmed_segments else 0

    return new_segments, new_start_number, new_presentation_offset


def parse_segments_from_timeline(segment_timeline, namespace):
    segments = []
    current_time = 0
    segment_number = 1

    for s_element in segment_timeline.findall(f".//{{{namespace}}}S"):
        t = int(s_element.get("t", current_time))
        d = int(s_element.get("d"))
        r = int(s_element.get("r", 0))

        for i in range(r + 1):
            segment_start = t + (i * d)
            segment_end = segment_start + d
            segments.append(
                {
                    "start": segment_start,
                    "end": segment_end,
                    "duration": d,
                    "number": segment_number,
                }
            )
            segment_number += 1
            current_time = segment_end

    return segments


def filter_segments_by_time_range(segments, start_time_scaled, end_time_scaled):
    trimmed_segments = []
    for segment in segments:
        if segment["end"] > start_time_scaled and segment["start"] < end_time_scaled:
            trimmed_segments.append(segment)

    if not trimmed_segments:
        closest_segment = min(
            segments, key=lambda x: abs(x["start"] - start_time_scaled)
        )
        trimmed_segments = [closest_segment]

    return trimmed_segments


def group_consecutive_segments(trimmed_segments):
    if not trimmed_segments:
        return []

    new_segments = []
    current_group = create_initial_segment_group(trimmed_segments[0])

    for segment in trimmed_segments[1:]:
        if is_consecutive_segment(segment, current_group):
            current_group["r"] += 1
        else:
            new_segments.append(current_group)
            current_group = create_initial_segment_group(segment)

    new_segments.append(current_group)
    return new_segments


def create_initial_segment_group(segment):
    return {
        "t": segment["start"],
        "d": segment["duration"],
        "r": 0,
    }


def is_consecutive_segment(segment, current_group):
    return (
        segment["duration"] == current_group["d"]
        and segment["start"]
        == current_group["t"] + (current_group["r"] + 1) * current_group["d"]
    )


def seconds_to_duration_string(seconds):
    td = timedelta(seconds=seconds)
    total_seconds = int(td.total_seconds())

    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    seconds = total_seconds % 60

    duration_parts = ["PT"]

    if hours > 0:
        duration_parts.append(f"{hours}H")
    if minutes > 0:
        duration_parts.append(f"{minutes}M")
    if seconds > 0 or (hours == 0 and minutes == 0):
        duration_parts.append(f"{seconds}S")

    return "".join(duration_parts)
