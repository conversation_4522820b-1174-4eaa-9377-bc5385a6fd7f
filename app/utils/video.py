import subprocess


def convert_fragmented_to_non_fragmented_video(local_file_path, output_path):
    try:
        command = [
            "ffmpeg",
            "-hide_banner",
            "-y",
            "-protocol_whitelist",
            "file",
            "-i",
            local_file_path,
            "-c",
            "copy",
            "-movflags",
            "faststart",
            output_path,
        ]
        subprocess.run(command, check=True)
        return "ffmpeg command executed successfully."
    except subprocess.CalledProcessError as e:
        return f"Failed to execute ffmpeg command: {str(e)}"
