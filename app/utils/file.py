import os
import shutil


def mkdir(dirname: str) -> None:
    try:
        os.makedirs(dirname)
    except OSError:
        pass


def delete_file(file_path):
    if os.path.exists(file_path):
        try:
            os.remove(file_path)
        except OSError:
            pass


def delete_folder(folder_path):
    if os.path.exists(folder_path):
        try:
            shutil.rmtree(folder_path)
        except OSError as e:
            print(f"Error: {e.strerror}")


def create_file_with_content(content, output_file_path, file_name):
    mkdir(output_file_path)
    file_path = os.path.join(output_file_path, file_name)
    with open(file_path, "w") as output_file:
        output_file.write(content)
    return file_path
