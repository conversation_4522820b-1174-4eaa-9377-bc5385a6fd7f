from math import ceil
from subprocess import PIPE, STDOUT, <PERSON><PERSON>


def get_duration(file_name):
    command = (
        "ffprobe",
        "-v",
        "error",
        "-show_entries",
        "format=duration",
        "-of",
        "default=noprint_wrappers=1:nokey=1",
        file_name,
    )
    try:
        with <PERSON><PERSON>(command, stdout=PIPE, stderr=STDOUT) as result:
            duration = float(result.stdout.readlines()[0].strip().decode("UTF-8"))
        return ceil(duration)
    except Exception:
        return 0
