import hashlib
import hmac

from django.conf import settings


def is_valid_zoom_signature(request):
    timestamp = request.headers.get("x-zm-request-timestamp")
    signature = request.headers.get("x-zm-signature")

    if not (timestamp and signature):
        return False

    try:
        body = request.body.decode("utf-8")
    except (UnicodeDecodeError, AttributeError):
        return False

    message = f"v0:{timestamp}:{body}"
    expected_signature = (
        "v0="
        + hmac.new(
            settings.ZOOM_WEBHOOK_SECRET.encode(), message.encode(), hashlib.sha256
        ).hexdigest()
    )

    return hmac.compare_digest(signature, expected_signature)


def generate_encrypted_token(secret_token, plain_token):
    encrypted_token = hmac.new(
        key=secret_token.encode("utf-8"),
        msg=plain_token.encode("utf-8"),
        digestmod=hashlib.sha256,
    ).hexdigest()
    return encrypted_token
