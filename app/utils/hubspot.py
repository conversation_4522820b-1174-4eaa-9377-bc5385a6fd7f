import json

import requests

from app.utils.datetime import get_current_time_in_milliseconds


def record_hubspot_lead(name, email, phone, organization_name, page_uri, page_name):
    url = "https://api.hsforms.com/submissions/v3/integration/submit/1548190/81b3079f-7816-49f1-aaa0-7559988bacef"
    payload = {
        "submittedAt": str(get_current_time_in_milliseconds()),
        "fields": [
            {"objectTypeId": "0-1", "name": "email", "value": email},
            {"objectTypeId": "0-1", "name": "firstname", "value": name},
            {"objectTypeId": "0-2", "name": "name", "value": organization_name},
            {"objectTypeId": "0-1", "name": "phone", "value": phone},
        ],
        "context": {
            "pageUri": page_uri,
            "pageName": page_name,
        },
        "legalConsentOptions": {
            "consent": {
                "consentToProcess": True,
                "text": "I agree to allow Example Company to store and process my personal data.",
                "communications": [
                    {
                        "value": True,
                        "subscriptionTypeId": 999,
                        "text": "I agree to receive marketing communications from Example Company.",
                    }
                ],
            }
        },
    }

    json_payload = json.dumps(payload)
    headers = {"Content-Type": "application/json"}
    response = requests.post(url, headers=headers, data=json_payload)

    return response.status_code
