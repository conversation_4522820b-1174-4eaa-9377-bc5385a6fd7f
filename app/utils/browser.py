import datetime
import os
import re
from urllib.parse import urlparse

import requests
from geolite2 import geolite2
from user_agents import parse


def get_user_details(request):
    user_agent = request.META.get("HTTP_USER_AGENT", "")[:255]
    ip_address = get_client_ip(request)

    user_data = {
        "name": request.user.name or None,
        "ip_address": ip_address,
        "user_agent": parse_user_agent(user_agent),
        "location": get_ip_location(ip_address),
        "time": get_parsed_browser_time(request),
    }

    return user_data


def get_client_ip(request):
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0]
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip


def parse_user_agent(user_agent_string):
    parsed_string = parse(user_agent_string)

    browser = parsed_string.browser.family
    browser_version = parsed_string.browser.version_string
    os = parsed_string.os.family
    os_version = parsed_string.os.version_string
    return f"{browser} {browser_version} on {os} {os_version}"


def get_device_from_user_agent(user_agent):
    from app.models.asset import AssetViewerLog

    mobile_patterns = ["iPhone", "Android"]
    system_patterns = ["Windows NT", "Macintosh", "X11"]
    tablet_patterns = ["iPad", "Android Tablet"]

    for pattern in mobile_patterns:
        if pattern in user_agent:
            return AssetViewerLog.DEVICE.MOBILE

    for pattern in system_patterns:
        if pattern in user_agent:
            return AssetViewerLog.DEVICE.SYSTEM

    for pattern in tablet_patterns:
        if pattern in user_agent:
            return AssetViewerLog.DEVICE.TABLET

    return AssetViewerLog.DEVICE.OTHER


def get_platform_from_user_agent(user_agent):
    from app.models.asset import AssetViewerLog

    ios_pattern = "iPhone|iPad|iPod"
    android_pattern = "Android"
    web_pattern = "Windows NT|Macintosh|X11"

    if re.search(ios_pattern, user_agent, re.I):
        return AssetViewerLog.PLATFORM.IOS

    if re.search(android_pattern, user_agent, re.I):
        return AssetViewerLog.PLATFORM.ANDROID

    if re.search(web_pattern, user_agent, re.I):
        return AssetViewerLog.PLATFORM.WEB

    return AssetViewerLog.DEVICE.OTHER


def get_client_from_user_agent(user_agent):
    from app.models.asset import AssetViewerLog

    ios_pattern = "like Mac OS X"
    android_pattern = "Android"
    flutter_pattern = "Flutter"
    browser_pattern = "Mozilla"

    if re.search(ios_pattern, user_agent):
        return AssetViewerLog.CLIENT.IOS

    if re.search(android_pattern, user_agent):
        return AssetViewerLog.CLIENT.ANDROID

    if re.search(flutter_pattern, user_agent):
        return AssetViewerLog.CLIENT.FLUTTER

    if re.search(browser_pattern, user_agent):
        return AssetViewerLog.CLIENT.BROWSER

    return AssetViewerLog.DEVICE.OTHER


def get_ip_location(ip_address):
    try:
        geo_data = geolite2.reader().get(ip_address)
        if geo_data and "country" in geo_data:
            return geo_data["country"]["names"]["en"]
    finally:
        geolite2.close()

    return None


def get_parsed_browser_time(request):
    browser_time_header = request.headers.get("X-Browser-Time")
    if browser_time_header:
        return datetime.datetime.strptime(browser_time_header, "%Y-%m-%dT%H:%M:%S.%fZ")
    return None


def is_valid_url(input_url):
    try:
        response = requests.get(input_url, headers={"Range": "bytes=0-0"})
    except requests.exceptions.ConnectionError:
        return False
    return response.status_code in (206, 200)


def is_valid_content_type(url, allowed_content_types):
    parsed_url = urlparse(url)
    file_extension = os.path.splitext(parsed_url.path)[1]
    return file_extension in allowed_content_types


def get_user_country_code(request):
    user_ip = get_client_ip(request)
    reader = geolite2.reader()
    location = reader.get(user_ip)
    country_code = "IN"
    if location and "country" in location:
        country_code = location["country"]["iso_code"]

    geolite2.close()
    return country_code


def is_android_browser(user_agent):
    for pattern in ["Android Tablet", "Android"]:
        if pattern in user_agent:
            return True


def get_url_status_code(url):
    response = requests.get(url, headers={"Range": "bytes=0-0"})
    return response.status_code
