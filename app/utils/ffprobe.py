import subprocess


def get_video_bitrate(file_path):
    command = [
        "ffprobe",
        "-v", "error",
        "-select_streams", "v:0",
        "-show_entries", "stream=bit_rate",
        "-of", "csv=s=x:p=0",
        file_path
    ]

    try:
        result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, check=True, text=True)
        bitrate = result.stdout.strip()
        if bitrate.lower() == "n/a":
            return None
        return bitrate
    except subprocess.CalledProcessError as e:
        raise Exception(e.output.strip())
