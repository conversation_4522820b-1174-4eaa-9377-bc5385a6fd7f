import uuid

import m3u8

from app.utils.m3u8 import get_m3u8_content


class HLSTrimmerError(Exception):
    pass


DEFAULT_TARGET_DURATION = 10
DEFAULT_BANDWIDTH = 1000000
DEFAULT_RESOLUTION = "1280x720"


def generate_trim_id():
    return str(uuid.uuid4())[:8]


def trim_playlist_content(playlist_content, start_time, end_time):
    try:
        playlist = m3u8.loads(playlist_content)

        if not playlist.segments:
            return create_empty_playlist()

        segment_timings = calculate_segment_timings(playlist)
        trimmed_segments = select_segments_in_range(
            segment_timings, start_time, end_time
        )

        return build_trimmed_playlist(playlist, trimmed_segments)

    except Exception as e:
        raise HLSTrimmerError(f"Failed to trim playlist content: {str(e)}") from e


def trim_playlist_from_url(playlist_url, start_time, end_time):
    try:
        playlist_content = get_m3u8_content(playlist_url)
        if not playlist_content:
            raise HLSTrimmerError(f"Could not fetch playlist: {playlist_url}")

        return trim_playlist_content(playlist_content, start_time, end_time)

    except Exception as e:
        raise HLSTrimmerError(
            f"Failed to trim playlist from URL {playlist_url}: {str(e)}"
        ) from e


def create_simple_master_playlist(variant_uri, bandwidth=None, resolution=None):
    bandwidth = bandwidth or DEFAULT_BANDWIDTH
    resolution = resolution or DEFAULT_RESOLUTION

    master_lines = [
        "#EXTM3U",
        "#EXT-X-VERSION:3",
        f"#EXT-X-STREAM-INF:BANDWIDTH={bandwidth},RESOLUTION={resolution}",
        variant_uri,
    ]
    return "\n".join(master_lines) + "\n"


def determine_quality_folder_from_variant(variant):
    if hasattr(variant, "uri") and variant.uri:
        uri_parts = variant.uri.split("/")
        if len(uri_parts) > 1:
            return uri_parts[0]

    if (
        hasattr(variant, "stream_info")
        and variant.stream_info
        and hasattr(variant.stream_info, "resolution")
        and variant.stream_info.resolution
    ):
        height = variant.stream_info.resolution[1]
        return f"{height}p_h264"

    return "default_h264"


def build_variant_url(variant_uri, master_url):
    if variant_uri.startswith("http"):
        return variant_uri

    master_base = "/".join(master_url.split("/")[:-1])
    return f"{master_base}/{variant_uri}"


def create_empty_playlist():
    lines = [
        "#EXTM3U",
        "#EXT-X-VERSION:3",
        f"#EXT-X-TARGETDURATION:{DEFAULT_TARGET_DURATION}",
        "#EXT-X-MEDIA-SEQUENCE:0",
        "#EXT-X-ENDLIST",
    ]
    return "\n".join(lines) + "\n"


def calculate_segment_timings(playlist):
    total_duration = 0
    segment_timings = []

    for segment in playlist.segments:
        segment_duration = (
            segment.duration
            if segment.duration
            else (playlist.target_duration or DEFAULT_TARGET_DURATION)
        )
        segment_timings.append(
            {
                "start": total_duration,
                "end": total_duration + segment_duration,
                "segment": segment,
            }
        )
        total_duration += segment_duration

    return segment_timings


def select_segments_in_range(segment_timings, start_time, end_time):
    trimmed_segments = []
    for timing in segment_timings:
        if timing["end"] > start_time and timing["start"] < end_time:
            trimmed_segments.append(timing["segment"])

    if not trimmed_segments:
        closest_segment = min(
            segment_timings, key=lambda x: abs(x["start"] - start_time)
        )
        trimmed_segments = [closest_segment["segment"]]

    return trimmed_segments


def build_trimmed_playlist(playlist, trimmed_segments):
    lines = ["#EXTM3U", "#EXT-X-VERSION:3"]

    target_duration = calculate_target_duration(playlist, trimmed_segments)
    lines.append(f"#EXT-X-TARGETDURATION:{target_duration}")

    media_sequence = get_media_sequence(trimmed_segments)
    lines.append(f"#EXT-X-MEDIA-SEQUENCE:{media_sequence}")

    if media_sequence > 0:
        lines.append("#EXT-X-DISCONTINUITY")

    add_segments_to_playlist(lines, trimmed_segments, target_duration)
    lines.append("#EXT-X-ENDLIST")

    return "\n".join(lines) + "\n"


def calculate_target_duration(playlist, trimmed_segments):
    if playlist.target_duration and playlist.target_duration > 0:
        return int(playlist.target_duration)

    max_segment_duration = 0
    for segment in trimmed_segments:
        if segment.duration and segment.duration > 0:
            max_segment_duration = max(max_segment_duration, segment.duration)

    return (
        int(max_segment_duration) + 1
        if max_segment_duration > 0
        else DEFAULT_TARGET_DURATION
    )


def get_media_sequence(trimmed_segments):
    if (
        trimmed_segments
        and hasattr(trimmed_segments[0], "media_sequence")
        and trimmed_segments[0].media_sequence is not None
    ):
        return trimmed_segments[0].media_sequence
    return 0


def add_segments_to_playlist(lines, trimmed_segments, target_duration):
    for segment in trimmed_segments:
        duration = (
            segment.duration
            if segment.duration and segment.duration > 0
            else target_duration
        )
        lines.append(f"#EXTINF:{duration:.6f},")
        lines.append(segment.uri)
