from django.conf import settings
from django.core.mail import EmailMultiAlternatives, get_connection
from django.utils.timezone import now


def send_email(
    subject,
    message_text,
    from_email,
    to_emails,
    bcc_emails=[],
    cc_emails=[],
    headers=None,
    message_html=None,
    attachments=[],
):
    email_connection = get_connection(
        username=settings.EMAIL_HOST_USER,
        password=settings.EMAIL_HOST_PASSWORD,
        fail_silently=True,
    )
    message = EmailMultiAlternatives(
        subject,
        message_text,
        from_email=from_email,
        to=to_emails,
        bcc=bcc_emails,
        cc=cc_emails,
        headers=headers,
        connection=email_connection,
    )
    if message_html:
        message.attach_alternative(message_html, "text/html")
    if attachments:
        for name, content, mime_type in attachments:
            message.attach(name, content, mime_type)
    is_sent = message.send()
    time_sent = now().isoformat()
    total_recipients = len(set(to_emails + bcc_emails + cc_emails))

    sent_info = {
        "is_sent": is_sent == 1,
        "time_sent": time_sent,
        "number_of_recipients": total_recipients,
    }
    return sent_info
