import subprocess
import tempfile

import boto3
import requests
from django.conf import settings

from app.utils.file import delete_folder as delete_moved_file_from_local
from app.utils.m3u8 import (
    convert_playlist_urls_to_relative_paths,
    create_m3u8_file_in_local,
    get_m3u8_content,
    get_playlist_segments,
    get_variant_playlists,
    has_https_playlist,
    remove_audio_playlist,
)

ONE_HOUR = 1 * 60 * 60


def copy_m3u8_file(input_url, output_path, config):
    upload_m3u8_to_wasabi(input_url, output_path, config)  # Copy main manifest
    variant_playlists = get_variant_playlists(input_url)
    for playlist in variant_playlists:
        variant_playlist_dir = get_variant_playlist_dir(playlist, output_path)
        copy_variant_playlist(playlist.absolute_uri, variant_playlist_dir, config)


def upload_m3u8_to_wasabi(input_url, output_path, config):
    if has_https_playlist(get_m3u8_content(input_url)):
        m3u8_content = get_m3u8_content_with_modified_playlist_path(input_url)
        m3u8_file_path = create_m3u8_file_in_local(content=m3u8_content)
        move_files_to_wasabi(
            input_file=m3u8_file_path,
            output_path=output_path,
            config=config,
            make_public=True,
        )
        delete_moved_file_from_local(m3u8_file_path)
    else:
        upload_from_url_to_wasabi(
            input_url, output_path + "video.m3u8", config, make_public=True
        )


def get_variant_playlist_dir(playlist, output_path):
    if playlist.base_path.startswith("https"):
        return output_path + playlist.base_path.split("/")[-1]
    return output_path + f"{playlist.base_path}"


def get_m3u8_content_with_modified_playlist_path(input_url):
    # Convert playlist URLs to paths to prevent fetching segments from external URLs
    m3u8_with_converted_path = convert_playlist_urls_to_relative_paths(
        get_m3u8_content(input_url)
    )
    # Remove audio playlist to prevent it from being shown in the resolution selector
    m3u8_with_removed_audio_playlist = remove_audio_playlist(m3u8_with_converted_path)
    return m3u8_with_removed_audio_playlist


def copy_variant_playlist(playlist_url, destination_path, config):
    upload_from_url_to_wasabi(
        playlist_url,
        destination_path,
        auto_filename=True,
        config=config,
        make_public=True,
    )  # Copy variant playlist
    copy_playlist_segments(playlist_url, destination_path, config)


def copy_playlist_segments(playlist_url, destination_path, config):
    segments = get_playlist_segments(playlist_url)

    for segment in segments:
        upload_from_url_to_wasabi(
            segment.absolute_uri,
            destination_path,
            config,
            auto_filename=True,
            make_public=True,
        )


def upload_from_url_to_wasabi(
    input_url, output_path, config, auto_filename=False, make_public=False
):
    if make_public:
        config += "\nacl = public-read"

    with tempfile.NamedTemporaryFile(mode="wt", delete=True) as cfg_file:
        cfg_file.write(config)
        cfg_file.flush()

        command_with_args = [
            "rclone",
            "copyurl",
            input_url,
            f"wasabi:{output_path}",
            "--config",
            cfg_file.name,
        ]

        if auto_filename:
            command_with_args.append("--auto-filename")

        try:
            subprocess.run(command_with_args, capture_output=True, check=True)
        except subprocess.SubprocessError as e:
            raise FileCopyError(str(e))

        cfg_file.close()


def get_wasabi_config(organization):
    return f"""
    [wasabi]
    type = s3
    provider = Wasabi
    access_key_id = {organization.storage_access_key_id}
    secret_access_key = {organization.storage_secret_access_key}
    region = {organization.storage_region}
    endpoint = s3.{organization.storage_region}.wasabisys.com
    """


def copy_files_to_wasabi(
    input_url, output_path, config, auto_filename=False, make_public=False
):
    if make_public:
        config += "\nacl = public-read"

    with tempfile.NamedTemporaryFile(mode="wt", delete=True) as cfg_file:
        cfg_file.write(config)
        cfg_file.flush()

        command_with_args = [
            "rclone",
            "copy",
            input_url,
            f"wasabi:{output_path}",
            "--config",
            cfg_file.name,
        ]

        if auto_filename:
            command_with_args.append("--auto-filename")

        try:
            subprocess.run(command_with_args, capture_output=True, check=True)
        except subprocess.SubprocessError as e:
            raise FileCopyError(str(e))

        cfg_file.close()


class FileCopyError(Exception):
    pass


def copy_files_to_local(source_path, local_destination_path, config):
    with tempfile.NamedTemporaryFile(mode="wt", delete=True) as cfg_file:
        cfg_file.write(config)
        cfg_file.flush()
        command_with_args = [
            "rclone",
            "copyto",
            f"wasabi://{source_path}",
            local_destination_path,
            "--config",
            cfg_file.name,
        ]
        process = subprocess.Popen(
            command_with_args, stdout=subprocess.PIPE, stderr=subprocess.PIPE
        )
        output, error = process.communicate()
        if process.returncode != 0:
            raise FileCopyError(
                "Rclone returned an error: {}".format(error.decode("utf-8"))
            )
    cfg_file.close()


def get_all_bucket_metrics(from_date, to_date, organization):
    url = f"https://billing.wasabisys.com/utilization/bucket/?withname=true&from={ from_date }&to={ to_date }"
    headers = {
        "Authorization": f"{organization.storage_access_key_id}:{organization.storage_secret_access_key}"
    }
    response = requests.get(url=url, headers=headers)
    data = response.json()

    return data


def generate_presigned_get_url(
    key, bucket=settings.ANALYTICS_LOG_BUCKET, expires_in=ONE_HOUR
):
    return get_boto_client().generate_presigned_url(
        "get_object",
        Params={"Bucket": bucket, "Key": key},
        ExpiresIn=expires_in,
    )


def generate_presigned_put_url(
    key, bucket=settings.ANALYTICS_LOG_BUCKET, expires_in=ONE_HOUR
):
    return get_boto_client().generate_presigned_url(
        "put_object",
        Params={"Bucket": bucket, "Key": key},
        ExpiresIn=expires_in,
        HttpMethod="PUT",
    )


def read_file(key, bucket=settings.ANALYTICS_LOG_BUCKET):
    return get_boto_client().get_object(Bucket=bucket, Key=key)["Body"].read()


def get_object_paths(
    file_path, bucket=settings.ANALYTICS_LOG_BUCKET, continuation_token=""
):
    s3_client = get_boto_client()
    object_paths = []
    response = s3_client.list_objects_v2(
        Bucket=bucket, Prefix=file_path, ContinuationToken=continuation_token
    )
    object_paths.extend(response.get("Contents", []))
    if not response.get("IsTruncated"):
        continuation_token = ""
        return object_paths, continuation_token
    continuation_token = response.get("NextContinuationToken")
    return object_paths, continuation_token


def get_boto_client():
    return boto3.client(
        "s3",
        endpoint_url=settings.WASABI_S3_ENDPOINT_URL,
        aws_access_key_id=settings.WASABI_ACCESS_KEY_ID,
        aws_secret_access_key=settings.WASABI_SECRET_ACCESS_KEY,
        region_name=settings.WASABI_BUCKET_REGION,
    )


def delete_files(path, config, exclude_folder=None):
    with tempfile.NamedTemporaryFile(mode="wt", delete=True) as cfg_file:
        cfg_file.write(config)
        cfg_file.flush()

        command_with_args = [
            "rclone",
            "delete",
            "--progress",
            "--transfers=1000",
            f"wasabi://{path}",
            "--config",
            cfg_file.name,
        ]

        if exclude_folder:
            command_with_args.extend(["--exclude", f"{exclude_folder}/**"])

        try:
            subprocess.run(command_with_args, capture_output=True, check=True)
        except subprocess.SubprocessError as e:
            raise Exception(str(e))

        cfg_file.close()


def move_files_to_wasabi(
    input_file, output_path, config, auto_filename=False, make_public=False
):
    if make_public:
        config += "\nacl = public-read"

    with tempfile.NamedTemporaryFile(mode="wt", delete=True) as cfg_file:
        cfg_file.write(config)
        cfg_file.flush()

        command_with_args = [
            "rclone",
            "move",
            input_file,
            f"wasabi:{output_path}",
            "--config",
            cfg_file.name,
        ]

        if auto_filename:
            command_with_args.append("--auto-filename")

        try:
            subprocess.run(command_with_args, capture_output=True, check=True)
        except subprocess.SubprocessError as e:
            raise FileCopyError(str(e))

        cfg_file.close()
