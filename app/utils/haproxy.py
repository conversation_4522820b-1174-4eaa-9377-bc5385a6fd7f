import json

import requests
from django.conf import settings

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Basic {settings.PROXY_API_KEY}",
}

FRONTEND_NAME = "livestream"


def get_configuration_version_number():
    url = f"{settings.PROXY_URL}/v2/info"
    payload = json.dumps({})
    response = requests.request("GET", url, headers=headers, data=payload)
    return response.headers["Configuration-Version"]


def add_new_backend_server(name, version_number):
    url = f"{settings.PROXY_URL}/v2/services/haproxy/configuration/backends?version={version_number}"
    payload = json.dumps({"name": name})
    response = requests.post(url, headers=headers, data=payload)
    if response.status_code == 409:
        data = response.json()
        if data.get("message", "").startswith("31:"):  # Backend already exists
            return response.headers["Configuration-Version"]
        version_number = get_configuration_version_number()
        add_new_backend_server(name, version_number)
    return response.headers["Configuration-Version"]


def add_ip_address_to_backend(ip_address, backend_name, version_number):
    url = (
        f"{settings.PROXY_URL}/v2/services/haproxy/configuration/servers?"
        f"version={version_number}&parent_type=backend&parent_name={backend_name}"
    )
    payload = json.dumps({"name": "server1", "address": ip_address, "port": 80})
    response = requests.request("POST", url, headers=headers, data=payload)
    if response.status_code == 409:
        data = response.json()
        if data.get("message", "").startswith("31:"):  # Server already exists
            return response.headers["Configuration-Version"]
        version_number = get_configuration_version_number()
        add_ip_address_to_backend(ip_address, backend_name, version_number)
    return response.headers["Configuration-Version"]


def add_backend_switching_rule(backend_name, path_rule, version_number):
    url = (
        f"{settings.PROXY_URL}/v2/services/haproxy/configuration/backend_switching_rules?"
        f"frontend={FRONTEND_NAME}&version={version_number}"
    )
    payload = json.dumps(
        {
            "cond": "if",
            "cond_test": f"{{ path_beg {path_rule} }}",
            "index": 0,
            "name": backend_name,
        }
    )
    response = requests.request("POST", url, headers=headers, data=payload)
    if response.status_code == 409:
        version_number = get_configuration_version_number()
        add_backend_switching_rule(backend_name, path_rule, version_number)


def begin_transaction(version_number):
    url = f"{settings.PROXY_URL}/v2/services/haproxy/transactions?version={version_number}"
    response = requests.post(url, headers=headers)

    if response.status_code == 409:
        version_number = get_configuration_version_number()
        return begin_transaction(version_number)

    return json.loads(response.content).get("id")


def end_transaction(version_number, transaction_number):
    url = f"{settings.PROXY_URL}/v2/services/haproxy/transactions/{transaction_number}?version={version_number}"
    response = requests.put(url, headers=headers)

    if response.status_code == 409:
        version_number = get_configuration_version_number()
        return end_transaction(version_number, transaction_number)
    elif response.status_code == 406:
        return False

    return response.status_code == 202


def get_backend_switching_rule_index(version_number, backend_name):
    url = (
        f"{settings.PROXY_URL}/v2/services/haproxy/configuration/"
        f"backend_switching_rules?frontend={FRONTEND_NAME}&version={version_number}"
    )
    response = requests.get(url, headers=headers)

    if response.status_code == 409:
        version_number = get_configuration_version_number()
        return get_backend_switching_rule_index(version_number, backend_name)

    if response.status_code == 200:
        data = json.loads(response.content).get("data")
        filtered_item = next(
            (item for item in data if item["name"] == backend_name), {}
        )  # type: ignore
        result = filtered_item.get("index", -1) or -1
        return result
    return -1


def remove_backend_switching_rule(version_number, backend_name):
    """
    As per HAProxy API, we need to remove backend_switching_rule by specifying the index. This might cause issue
    if another server got added(index will get changed) while this operation is being done.
    So to prevent this we are using transaction. When doing inside transaction, if another server got added then while
    committing transaction we will get transaction outdated error then we can repeat the operation with new transaction.
    """
    index = get_backend_switching_rule_index(version_number, backend_name)
    transaction_id = begin_transaction(version_number)
    url = (
        f"{settings.PROXY_URL}/v2/services/haproxy/configuration/"
        f"backend_switching_rules/{index}?transaction_id={transaction_id}&frontend={FRONTEND_NAME}"
    )
    response = requests.delete(url, headers=headers)
    end_transaction(response.headers["Configuration-Version"], transaction_id)


def remove_backend(version_number, backend_name):
    url = f"{settings.PROXY_URL}/v2/services/haproxy/configuration/backends/{backend_name}?version={version_number}"
    response = requests.delete(url, headers=headers)

    if response.status_code == 409:
        version_number = get_configuration_version_number()
        return remove_backend(version_number, backend_name)
