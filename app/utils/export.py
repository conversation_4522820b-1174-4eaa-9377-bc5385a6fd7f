import csv

from app.domain.video import get_all_asset_data


def export_asset_data_as_csv(folder_id="", output_filename="asset_details"):
    data = get_all_asset_data(folder_id)
    export_as_csv(data, output_filename)


def export_as_csv(data, output_filename):
    with open(output_filename, "w", newline="") as csv_file:
        fieldnames = list(data[0]._asdict().keys())
        writer = csv.DictWriter(csv_file, fieldnames=fieldnames)

        writer.writeheader()
        for item in data:
            writer.writerow(item._asdict())
