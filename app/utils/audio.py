from subprocess import PIPE, STDOUT, Pope<PERSON>


def get_audio(file_name, output_audio_path):
    command = (
        "ffmpeg",
        "-i",
        file_name,
        "-vn",
        "-acodec",
        "libmp3lame",
        output_audio_path,
    )
    try:
        popen = Popen(command, stdout=PIPE, stderr=STDOUT)
        popen.wait()
        return popen.returncode == 0
    except Exception:
        return False
