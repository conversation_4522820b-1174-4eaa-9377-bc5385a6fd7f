from dataclasses import dataclass

from app.utils.api_interface import APIInterface


class JwplayerInterface(APIInterface):
    VIDEO_LIST_URL = (
        "https://api.jwplayer.com/v2/sites/{}/media/?page_length={}&q=status:ready"
    )
    VIDEO_RESOLUTION_URL = (
        "https://api.jwplayer.com/v2/sites/{}/media/{}/media_renditions/"
    )
    VIDEO_THUMBNAIL_URL = (
        "https://api.jwplayer.com/v2/sites/{}/thumbnails/?q=media_id:{}"
    )
    VIDEO_DETAIL_URL = "https://cdn.jwplayer.com/v2/media/{}"

    VIDEO_M3U8_URL = "https://cdn.jwplayer.com/manifests/{}.m3u8"
    SITE_DRM_URL = "https://api.jwplayer.com/v2/sites/{}/is_drm_enabled/"

    def __init__(self, site_id, api_key):
        self.api_key = api_key
        self.site_id = site_id
        super().__init__()

    def _get_headers(self):
        return {"Authorization": f"bearer {self.api_key}"}

    def get_site(self, video_count=10000):
        data = self._get(self.VIDEO_LIST_URL.format(self.site_id, video_count))
        return Site(
            id=self.site_id,
            videos=self.fetch_all_video_ids(data),
            drm_enabled=self.is_drm_enabled(),
            total=data["total"],
            data=data,
        )

    def fetch_all_video_ids(self, data):
        videos = []
        for video in data.get("media", []):
            videos.append(video["id"])
        return videos

    def is_drm_enabled(self):
        return self._get(self.SITE_DRM_URL.format(self.site_id)).get("is_enabled")

    def get_video(self, video_id):
        data = {
            "files": self.get_video_resolutions(video_id),
            "link": self.VIDEO_M3U8_URL.format(video_id),
            "id": video_id,
            "duration": self.get_video_duration(video_id),
            "title": self.get_video_title(video_id),
            "thumbnail": self.get_thumbnail_url(video_id),
        }
        return Video.create(data)

    def get_video_resolutions(self, video_id):
        data = self._get(self.VIDEO_RESOLUTION_URL.format(self.site_id, video_id))
        resolutions = []
        for resolution in data.get("media_renditions", []):
            if resolution.get("media_type") == "video":
                resolution_details = {
                    "type": resolution.get("media_type"),
                    "width": resolution.get("width"),
                    "height": resolution.get("height"),
                    "url": resolution.get("delivery_url"),
                    "resolution": f"{resolution.get('height')}p",
                }
                resolutions.append(resolution_details)
        return resolutions

    def get_video_title(self, video_id):
        data = self._get(self.VIDEO_DETAIL_URL.format(video_id))
        title = data["playlist"][0]["title"]
        return title

    def get_video_duration(self, video_id):
        data = self._get(self.VIDEO_DETAIL_URL.format(video_id))
        duration = data["playlist"][0]["duration"]
        return duration

    def get_thumbnail_url(self, video_id):
        data = self._get(self.VIDEO_THUMBNAIL_URL.format(self.site_id, video_id))
        for thumbnail in data.get("thumbnails", []):
            if thumbnail.get("thumbnail_type") == "static":
                return thumbnail.get("delivery_url")


@dataclass
class Site:
    id: str
    drm_enabled: bool
    videos: list
    total: int
    data: dict


@dataclass
class VideoResolution:
    url: str
    resolution: str
    width: int
    height: int
    data: dict


@dataclass
class Video:
    id: str
    name: str
    link: str
    resolutions: list[VideoResolution]
    thumbnail: str
    duration: float
    data: dict

    @classmethod
    def create(cls, data):
        return cls(
            id=data["id"],
            name=data["title"],
            duration=data["duration"],
            link=data["link"],
            resolutions=cls._create_resolutions(data["files"]),
            thumbnail=data["thumbnail"],
            data=data,
        )

    @classmethod
    def _create_resolutions(cls, resolutions) -> list[VideoResolution]:
        return [
            VideoResolution(
                url=resolution["url"],
                resolution=resolution["resolution"],
                width=resolution["width"],
                height=resolution["height"],
                data=resolution,
            )
            for resolution in resolutions
            if resolution.get("type") == "video" and ".mp4" in resolution["url"]
        ]
