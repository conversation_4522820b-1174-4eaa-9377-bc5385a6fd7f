from dataclasses import dataclass

from app.utils.api_interface import APIInterface


class GumletInterface(APIInterface):
    COLLECTION_LIST_URL = "https://api.gumlet.com/v1/video/sources"
    PLAYLIST_LIST_URL = "https://api.gumlet.com/v1/video/playlist"
    ASSET_LIST_URL = "https://api.gumlet.com/v1/video/playlist/{playlist_id}/assets"
    ASSET_DETAIL_URL = "https://api.gumlet.com/v1/video/assets/{asset_id}"

    def __init__(self, api_key):
        self.api_key = api_key
        self._collection_cache = None
        super().__init__()

    def _get_headers(self):
        return {"Authorization": f"Bearer {self.api_key}", "accept": "application/json"}

    def get_all_collections(self):
        try:
            if self._collection_cache:
                return self._collection_cache
            data = self._get(self.COLLECTION_LIST_URL)
            self._collection_cache = [
                Collection(
                    id=collection["id"],
                    name=collection["name"],
                )
                for collection in data.get("all_sources", [])
            ]
            return self._collection_cache
        except Exception as e:
            raise Exception(f"Failed to fetch collections: {e}") from e

    def get_all_playlists(self):
        try:
            collections = self.get_all_collections()
            playlists = []

            for collection in collections:
                url = f"{self.PLAYLIST_LIST_URL}?collection_id={collection.id}"
                data = self._get(url)

                for playlist in data:
                    playlists.append(
                        Playlist(
                            id=playlist["id"],
                            collection_id=playlist["collection_id"],
                            title=playlist["title"],
                        )
                    )

            return playlists
        except Exception as e:
            raise Exception(f"Failed to fetch playlists: {e}") from e

    def _fetch_assets_page(self, playlist_id, page):
        try:
            url = (
                self.ASSET_LIST_URL.format(playlist_id=playlist_id)
                + f"?sort_order=1&page_number={page}&page_size=10"
            )
            data = self._get(url)
            assets = [
                Asset(id=asset["id"], title=asset["title"], duration=asset["duration"])
                for asset in data.get("asset_list", [])
            ]
            return assets, data.get("has_next_page", False)
        except Exception as e:
            raise Exception(f"Failed to fetch assets page: {e}") from e

    def get_all_assets(self, playlist_id):
        try:
            return self._fetch_all_pages(
                self._fetch_assets_page, {"playlist_id": playlist_id}
            )
        except Exception as e:
            raise Exception(f"Failed to fetch all assets: {e}") from e

    def get_asset(self, asset_id):
        try:
            asset_data = self._get(self.ASSET_DETAIL_URL.format(asset_id=asset_id))
            playlists = self.get_all_playlists()
            playlist_ids = asset_data.get("playlists", [])
            playlist_id = playlist_ids[0] if playlist_ids else None
            playlist_name = next(
                (
                    playlist.title
                    for playlist in playlists
                    if playlist.id in playlist_ids
                ),
                "Unknown Playlist",
            )

            return Asset(
                id=asset_data["asset_id"],
                title=asset_data["input"]["title"],
                status=asset_data["status"],
                created_at=asset_data["created_at"],
                duration=asset_data["input"]["duration"],
                original_download_url=asset_data["original_download_url"],
                thumbnail_url=asset_data["output"].get("thumbnail_url", [None])[0],
                playback_url=asset_data["output"].get("playback_url", ""),
                playlist_id=playlist_id,
                playlist_name=playlist_name,
            )
        except Exception as e:
            raise Exception(f"Failed to fetch asset details: {e}") from e


@dataclass
class Collection:
    id: str
    name: str


@dataclass
class Playlist:
    id: str
    collection_id: str
    title: str


@dataclass
class Asset:
    id: str
    title: str
    status: str | None = None
    created_at: str | None = None
    duration: float = 0.0
    original_download_url: str | None = None
    thumbnail_url: str | None = None
    playback_url: str | None = None
    playlist_id: str | None = None
    playlist_name: str | None = None
