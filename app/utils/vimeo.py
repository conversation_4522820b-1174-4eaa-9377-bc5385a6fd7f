import os
import re
from dataclasses import dataclass
from functools import cached_property

from django.conf.locale import LANG_INFO
from requests.exceptions import HTTPError

from app.utils.api_interface import APIInterface


class Interface(APIInterface):
    CURRENT_USER_DETAIL_URL = "https://api.vimeo.com/me"
    TEAMS_URL = "https://api.vimeo.com/users/{}/teams"
    ROOT_FOLDER_URL = "https://api.vimeo.com/users/{}/folders/root"
    FOLDER_DETAIL_URL = "https://api.vimeo.com/users/{}/projects/{}/"
    FOLDER_ITEMS_URL = "https://api.vimeo.com/users/{}/projects/{}/items"
    VIDEO_DETAIL_URL = "https://api.vimeo.com/videos/{}?video_links=non_expiring"
    VIDEO_SUBTITLE_URL = "https://api.vimeo.com/videos/{}/texttracks"

    def __init__(self, access_token):
        self.access_token = access_token
        super().__init__()

    def _get_headers(self):
        return {"Authorization": f"bearer {self.access_token}"}

    def get_team_members(self):
        teams = self._get(self.TEAMS_URL.format(self._current_user_id)).get("data", [])
        return [
            {"name": team["owner"]["name"], "id": team["owner"]["uri"].split("/")[-1]}
            for team in teams
        ]

    def get_root_folder(self, user_id=None):
        kwargs = {"user_id": user_id or self._current_user_id}
        items = self._fetch_all_pages(
            fetch_func=self._fetch_root_folders_and_videos, func_kwargs=kwargs
        )
        return Folder.create({"items": items, "name": "Root", "id": "root"})

    def get_folder(self, folder_id, user_id=None):
        kwargs = {"folder_id": folder_id, "user_id": user_id or self._current_user_id}
        folder_details = self.get_folder_details(folder_id)
        folder_details["id"] = folder_id
        folder_details["items"] = self._fetch_all_pages(
            fetch_func=self._fetch_folder_items, func_kwargs=kwargs
        )
        return Folder.create(folder_details)

    def get_folder_details(self, folder_id):
        url = self.FOLDER_DETAIL_URL.format(self._current_user_id, folder_id)
        return self._get(url)

    def get_video(self, uri):
        video_id = self.get_video_id(uri)
        response = self._get(self.VIDEO_DETAIL_URL.format(video_id))
        return Video.create(video_id, response)

    def get_video_subtitles(self, uri):
        video_id = self.get_video_id(uri)

        try:
            response = self._get(self.VIDEO_SUBTITLE_URL.format(video_id))

        except HTTPError as http_error:
            if http_error.response.status_code == 404:
                return []
            raise http_error

        subtitles = []
        for data in response["data"]:
            subtitle = VideoSubtitle.create(data)
            subtitles.append(subtitle)

        return subtitles

    def get_video_id(self, uri):
        match = re.search(r"/(\d+)(?:/|$)", uri)
        return match.group(1) if match else None

    def _fetch_root_folders_and_videos(self, user_id=None, page=1):
        response = self._get(
            self.ROOT_FOLDER_URL.format(user_id or self._current_user_id),
            params={"page": page, "per_page": 100},
        )
        items = response.get("data", [])
        has_next_page = bool(response.get("paging", {}).get("next"))
        return items, has_next_page

    def _fetch_folder_items(self, folder_id, user_id, page=1):
        response = self._get(
            self.FOLDER_ITEMS_URL.format(user_id or self._current_user_id, folder_id),
            params={"page": page, "per_page": 100},
        )
        items = response.get("data", [])
        has_next_page = bool(response.get("paging", {}).get("next"))
        return items, has_next_page

    @cached_property
    def _current_user_id(self):
        response = self._get(self.CURRENT_USER_DETAIL_URL)
        return response["uri"].split("/users/")[1]


@dataclass
class VideoResolution:
    url: str
    resolution: str
    width: int
    height: int
    data: dict


@dataclass
class VideoSubtitle:
    id: str
    active: str
    language: str
    link: str
    name: str

    @classmethod
    def create(cls, data):
        filename, _ = os.path.splitext(data["name"])
        return cls(
            id=data["id"],
            name=filename,
            active=data["active"],
            language=cls.get_language_code(data["language"]),
            link=data["link"],
        )

    @classmethod
    def get_language_code(cls, language_name):
        lang_component = re.sub(r"-x-autogen$", "", language_name)
        if lang_component in LANG_INFO:
            return lang_component
        return "en"


@dataclass
class Video:
    id: str
    name: str
    link: str
    resolutions: list[VideoResolution]
    owner: dict
    data: dict

    @classmethod
    def create(cls, video_id, data):
        return cls(
            id=video_id,
            name=data["name"],
            link=data["link"],
            resolutions=cls._create_resolutions(data["files"]),
            owner=data["user"]["name"],
            data=data,
        )

    @classmethod
    def _create_resolutions(cls, resolutions) -> list[VideoResolution]:
        return [
            VideoResolution(
                url=resolution["link"],
                resolution=resolution["rendition"],
                width=resolution["width"],
                height=resolution["height"],
                data=resolution,
            )
            for resolution in resolutions
            if resolution.get("type") == "video/mp4" and ".mp4" in resolution["link"]
        ]

    @property
    def is_transcoded(self):
        return self.data.get("transcode", {}).get("status") == "complete"


@dataclass
class Folder:
    id: str
    name: str
    items: list
    data: dict

    @classmethod
    def create(cls, data):
        return cls(id=data["id"], name=data["name"], items=data["items"], data=data)

    @property
    def video_links(self):
        return [
            item["video"]["link"]
            for item in self.items
            if item["type"].lower() == "video"
        ]

    @property
    def subfolder_ids(self):
        return [
            item["folder"]["uri"].split("/")[-1]
            for item in self.items
            if item["type"].lower() == "folder"
        ]
