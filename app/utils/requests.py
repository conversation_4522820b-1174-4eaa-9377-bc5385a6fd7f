import json

import requests
from django.core.serializers.json import DjangoJSONEncoder
from requests.adapters import HTT<PERSON><PERSON>pt<PERSON>, Retry


def post(url, data={}, headers={}):
    response = requests.request(
        "POST",
        url,
        data=json.dumps(data, cls=DjangoJSONEncoder),
        headers=headers,
    )
    response.raise_for_status()
    return response


def Session(retry_count=None, retry_for_http_statuses=None):
    session = requests.Session()
    if not retry_count:
        return session

    retries = Retry(
        total=retry_count,
        backoff_factor=1,
        status_forcelist=retry_for_http_statuses or [502, 503, 504],
        allowed_methods=False,
    )
    session.mount("https://", HTTPAdapter(max_retries=retries))
    return session
