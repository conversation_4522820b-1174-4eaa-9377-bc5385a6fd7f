from calendar import monthrange
from datetime import date

import django_filters
from dateutil.relativedelta import relativedelta

from app.utils.datetime import get_month_start_date


class MonthFilter(django_filters.ChoiceFilter):
    @property
    def field(self):
        field = super().field
        field.choices = self.choices()
        return field

    def choices(self):
        today = date.today()
        MONTH_CHOICES = []

        for delta in range(1, 12):
            choice = date(today.year, today.month, 1) - relativedelta(months=delta)
            MONTH_CHOICES.append((choice, choice.strftime("%B %Y")))
        return MONTH_CHOICES

    def filter(self, qs, value):
        month_start = get_month_start_date(value)
        no_of_days = monthrange(month_start.year, month_start.month)[1]
        month_end = date(month_start.year, month_start.month, no_of_days)
        kwargs = {
            f"{self.field_name}__range": [month_start, month_end],
        }
        return qs.filter(**kwargs)
