from datetime import timedelta

from django.db import transaction

from app.utils.ffprobe import get_video_bitrate
from app.utils.wasabi import get_wasabi_config, upload_from_url_to_wasabi


class MigratorInterface:
    def intialize(self, kwargs):
        from app.models import ImportedVideo

        self.imported_video = ImportedVideo.objects.get(
            id=kwargs.get("imported_video_id")
        )

    def _get_video(self):
        raise NotImplementedError("Subclasses must implement _get_video method.")

    def copy_highest_resolution_to_wasabi(self, video):
        from app.models import ImportedResolution

        highest_resolution = self.create_highest_resolution(video)
        highest_resolution.update_status(ImportedResolution.Status.UPLOADING)
        input_url, output_path = self.get_input_output_for_resolution(
            video, highest_resolution
        )
        config = get_wasabi_config(self.organization)
        upload_from_url_to_wasabi(input_url, output_path, config, make_public=True)
        highest_resolution.update_status(ImportedResolution.Status.COMPLETED)

    def create_highest_resolution(self, video):
        from app.models import ImportedResolution

        highest_resolution = max(
            video.resolutions, key=lambda resolution: resolution.height
        )
        return ImportedResolution.objects.create(
            video=self.imported_video,
            resolution=highest_resolution.resolution,
            organization=self.organization,
        )

    def get_input_output_for_resolution(self, video, resolution):
        for item in video.resolutions:
            if item.resolution == resolution.resolution:
                output_path = (
                    f"{self.organization.bucket_name}/{resolution.output_path}"
                )
                return item.url, output_path

    @transaction.atomic
    def create_asset(self, folder_name=None, input_url_path=None, transmux_only=True):
        from app.models import Asset, VideoInput

        if folder_name:
            folder = self.create_folder(folder_name)

        asset = Asset.objects.create(
            created_by=self.organization.created_by,
            organization=self.organization,
            title=self.imported_video.name,
            parent=folder,
        )

        folder.update_children_count()

        from app.domain.video import create_video_with_outputs

        video = create_video_with_outputs(
            asset=asset,
            organization=self.organization,
            video_data={
                "transmux_only": transmux_only,
                "duration": self.get_duration(),
                "content_protection_type": self.content_protection_type,
            },
        )
        self.imported_video.video = video
        self.imported_video.save(update_fields=["video"])

        if input_url_path:
            VideoInput.objects.create(
                url=input_url_path,
                video=video,
                organization=self.organization,
            )
            video.resolutions = [0, 1, 2, 3, 4]
            video.save(update_fields=["resolutions"])

        for resolution in self.imported_video.resolutions.all():
            VideoInput.objects.create(
                url=resolution.output_path,
                video=video,
                organization=self.organization,
            )

        return asset

    def create_folder(self, title, parent=None):
        from app.models import Asset

        folder, created = Asset.objects.get_or_create(
            title=title,
            organization=self.organization,
            parent=parent,
            type=Asset.Type.FOLDER,
            defaults={"created_by": self.organization.created_by},
        )
        if not self.imported_video.folder:
            return folder

        for imported_folder in self.imported_video.folder.get_ancestors(
            include_self=True
        ):
            folder, created = Asset.objects.get_or_create(
                title=imported_folder.name,
                organization=self.organization,
                parent=folder,
                type=Asset.Type.FOLDER,
                defaults={"created_by": self.organization.created_by},
            )
            folder.parent.update_children_count()
        return folder

    def get_duration(self):
        if self.imported_video.details["duration"]:
            duration = self.imported_video.details["duration"]
            return timedelta(seconds=round(duration))

    def get_transcoding_job_inputs(self, video):
        inputs = []
        for resolution in video.resolutions:
            bandwidth = get_video_bitrate(resolution.url)
            inputs.append(
                {
                    "url": resolution.url,
                    "name": resolution.resolution,
                    "bandwidth": bandwidth,
                }
            )
        return inputs
