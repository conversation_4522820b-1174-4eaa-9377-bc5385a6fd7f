from django.conf import settings
from django.utils.timezone import now
from hcloud import Client
from hcloud.images.domain import Image
from hcloud.server_types.domain import ServerType


def create_live_streaming_server(name):
    client = Client(token=settings.HETZNER_API_TOKEN)
    response = client.servers.create(
        name,
        server_type=ServerType(name="cx51"),
        image=Image(id=settings.LIVE_STREAMING_SERVER_IMAGE_ID),
    )
    return response


def generate_live_streaming_server_name(organization):
    return f"live-{organization.uuid}-{organization.bucket_name}-{now().isoformat()}"
