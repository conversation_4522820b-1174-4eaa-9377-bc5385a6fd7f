import uuid

import shortuuid


def is_valid_uuid(value):
    try:
        uuid.UUID(str(value))
        return True
    except ValueError:
        return False


def populate_uuid(instance, length, alphabet="abcdefghjkmnpqrstuxyz23456789"):
    if not instance.uuid:
        while True:
            uuid = shortuuid.ShortUUID(alphabet=alphabet).random(length=length)
            if not instance._meta.model.objects.filter(uuid=uuid).exists():
                instance.uuid = uuid
                break
