import base64


def url_safe_base64_encode(data):
    if isinstance(data, str):
        data = data.encode("utf-8")
    return base64.urlsafe_b64encode(data)


def url_safe_base64_decode(encoded_data):
    return base64.urlsafe_b64decode(encoded_data).decode("utf-8")


def encode_value(value):
    return base64.b64encode(value.encode("utf-8")).decode("utf-8")


def decode_value(encoded_value):
    return base64.b64decode(encoded_value).decode("utf-8")
