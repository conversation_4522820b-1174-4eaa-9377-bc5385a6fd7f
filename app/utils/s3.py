import subprocess
import tempfile

from app.utils.wasabi import FileCopy<PERSON>rror


def upload_from_url_to_s3(
    input_url, output_path, config, auto_filename=False, make_public=False
):
    if make_public:
        config += "\nacl = public-read"

    with tempfile.NamedTemporaryFile(mode="wt", delete=True) as cfg_file:
        cfg_file.write(config)
        cfg_file.flush()

        command_with_args = [
            "rclone",
            "copyurl",
            input_url,
            f"s3:{output_path}",
            "--config",
            cfg_file.name,
        ]

        if auto_filename:
            command_with_args.append("--auto-filename")

        try:
            subprocess.run(command_with_args, capture_output=True, check=True)
        except subprocess.SubprocessError as e:
            raise FileCopyError(str(e))

        cfg_file.close()


def copy_files_to_s3(
    input_url, output_path, config, auto_filename=False, make_public=False
):
    if make_public:
        config += "\nacl = public-read"

    with tempfile.NamedTemporaryFile(mode="wt", delete=True) as cfg_file:
        cfg_file.write(config)
        cfg_file.flush()

        command_with_args = [
            "rclone",
            "copy",
            input_url,
            f"s3:{output_path}",
            "--config",
            cfg_file.name,
        ]

        if auto_filename:
            command_with_args.append("--auto-filename")

        try:
            subprocess.run(command_with_args, capture_output=True, check=True)
        except subprocess.SubprocessError as e:
            raise FileCopyError(str(e))

        cfg_file.close()


def delete_files(path, config):
    with tempfile.NamedTemporaryFile(mode="wt", delete=True) as cfg_file:
        cfg_file.write(config)
        cfg_file.flush()

        command_with_args = [
            "rclone",
            "delete",
            "--progress",
            "--transfers=1000",
            f"s3://{path}",
            "--config",
            cfg_file.name,
        ]

        try:
            subprocess.run(command_with_args, capture_output=True, check=True)
        except subprocess.SubprocessError as e:
            raise Exception(str(e))

        cfg_file.close()


def get_s3_config(organization):
    return f"""
    [s3]
    type = s3
    provider = AWS
    access_key_id = {organization.storage_access_key_id}
    secret_access_key = {organization.storage_secret_access_key}
    region = {organization.storage_region}
    """
