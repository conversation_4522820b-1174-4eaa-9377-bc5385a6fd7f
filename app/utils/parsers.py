import json

from rest_framework.parsers import BaseParser


class ShakaPackagerParser(BaseParser):
    """
    Shaka packager sends additional data in request body. To strip that, we added this parser.
    If request is from shaka packager, then we change content-type as `application/shaka-json` in wsgi file, which
    will be parsed by the below.
    """

    media_type = "application/shaka-json"

    def parse(self, stream, media_type=None, parser_context=None):
        data = stream.read().decode("utf-8")
        lines = data.split("\r\n")
        return json.loads(lines[1])


class OctetStreamParser(BaseParser):
    charset = None
    format = None
    media_type = "application/octet-stream"
    render_style = "binary"

    def parse(self, stream, media_type=None, parser_context=None):
        return stream.read()


def bytes_to_human_readable(bytes):
    if not bytes:
        return

    suffixes = ["B", "KB", "MB", "GB", "TB", "PB"]
    i = 0
    while bytes >= 1024 and i < len(suffixes) - 1:
        bytes /= 1024
        i += 1
    return f"{bytes:.2f} {suffixes[i]}"
