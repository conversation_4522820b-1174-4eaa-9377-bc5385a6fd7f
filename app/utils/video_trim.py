def validate_trim_parameters(video, start_time, end_time):
    from app.domain.video_trim.exceptions import VideoTrimValidationError

    if not video.duration:
        raise VideoTrimValidationError("Video duration is not available")

    video_duration_seconds = int(video.duration.total_seconds())

    if start_time < 0:
        raise VideoTrimValidationError("Start time cannot be negative")

    if start_time >= video_duration_seconds:
        raise VideoTrimValidationError("Start time cannot exceed video duration")

    if end_time <= start_time:
        raise VideoTrimValidationError("End time must be greater than start time")

    if end_time > video_duration_seconds:
        raise VideoTrimValidationError("End time cannot exceed video duration")


def calculate_trimmed_duration(trim_job):
    return trim_job.end_time - trim_job.start_time
