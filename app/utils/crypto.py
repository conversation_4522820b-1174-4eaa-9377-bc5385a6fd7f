import base64
import binascii
import hashlib
import hmac
import os
import secrets

from Cryptodome.Cipher import AES
from Cryptodome.Util.Padding import pad


def create_sha256(key, message):
    if isinstance(key, str):
        key = key.encode("utf-8")
    if isinstance(message, str):
        message = message.encode("utf-8")
    return hmac.new(key, message, hashlib.sha256).digest()


def get_random_aes_key():
    return secrets.token_bytes(32).hex()


def get_random_aes_iv():
    return os.urandom(16).hex()


def populate_drm_fields(instance):
    if not instance.drm_aes_signing_key and not instance.drm_aes_signing_iv:
        instance.drm_aes_signing_key = get_random_aes_key()
        instance.drm_aes_signing_iv = get_random_aes_iv()


def generate_signature(data, key, iv):
    hash = hashlib.sha1(data.encode()).digest()
    cipher = AES.new(
        binascii.unhexlify(key),
        AES.MODE_CBC,
        binascii.unhexlify(iv),
    )
    padded_hash = pad(hash, AES.block_size, style="pkcs7")
    signature = cipher.encrypt(padded_hash)
    return base64.b64encode(signature).decode()


def base64_to_hex(base64_string):
    decoded_bytes = base64.b64decode(base64_string)
    hex_result = binascii.hexlify(decoded_bytes).decode("utf-8")
    return hex_result
