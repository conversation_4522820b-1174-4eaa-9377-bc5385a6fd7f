import os
import tempfile

from app.domain.cloud_storage import get_client


class ManifestStorageError(Exception):
    pass


M3U8_CONTENT_TYPE = "application/x-mpegURL"
MPD_CONTENT_TYPE = "application/dash+xml"


def upload_manifest_content(organization, content, file_path):
    try:
        with tempfile.NamedTemporaryFile(
            mode="w", suffix=".tmp", delete=False
        ) as temp_file:
            temp_file.write(content)
            temp_file_path = temp_file.name

        client = get_client(organization)
        content_type = get_manifest_content_type(file_path)

        with open(temp_file_path, "rb") as file_data:
            client.put_object(
                Bucket=organization.bucket_name,
                Key=file_path,
                Body=file_data,
                ContentType=content_type,
                ACL="public-read",
            )

        os.unlink(temp_file_path)

    except Exception as e:
        raise ManifestStorageError(
            f"Failed to upload content to {file_path}: {str(e)}"
        ) from e


def get_manifest_content(organization, file_path):
    try:
        client = get_client(organization)
        response = client.get_object(Bucket=organization.bucket_name, Key=file_path)
        return response["Body"].read().decode("utf-8")
    except Exception as e:
        raise ManifestStorageError(
            f"Failed to get content from {file_path}: {str(e)}"
        ) from e


def get_manifest_content_type(file_path):
    if file_path.endswith(".m3u8"):
        return M3U8_CONTENT_TYPE
    elif file_path.endswith(".mpd"):
        return MPD_CONTENT_TYPE
    return "application/octet-stream"
