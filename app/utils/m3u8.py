import os
import tempfile

import m3u8
import requests


def get_variant_playlists(main_manifest_url):
    main_manifest = m3u8.load(main_manifest_url)
    return main_manifest.playlists


def get_playlist_segments(playlist_url):
    playlist = m3u8.load(playlist_url)
    return playlist.segments


def create_m3u8_file_in_local(content):
    temp_folder = tempfile.mkdtemp()
    temp_file_path = os.path.join(temp_folder, "video.m3u8")
    with open(temp_file_path, "w") as temp_file:
        temp_file.write(content)
    return temp_file_path


def convert_playlist_urls_to_relative_paths(input_content):
    playlist = m3u8.loads(input_content)
    for entry in playlist.playlists:
        if entry.stream_info.resolution:
            modified_uri = (
                entry.uri.split("/")[-2] + "/" + entry.uri.split("/")[-1]
            )  # Extracting the last two components of the URL path and reconstructing
            entry.uri = modified_uri

    return playlist.dumps()


def remove_audio_playlist(input_content):
    playlist = m3u8.loads(input_content)
    for entry in playlist.playlists:
        if not entry.stream_info.resolution:
            playlist.playlists.remove(entry)

    return playlist.dumps()


def get_m3u8_content(m3u8_url):
    response = requests.get(m3u8_url)
    if response.status_code == 200:
        return response.text


def has_https_playlist(input_content):  # Check for HTTPS url in playlist URIs
    playlist = m3u8.loads(input_content)
    for entry in playlist.playlists:
        if entry.uri.startswith("https"):
            return True
    return False


def remove_variant_playlists(input_content, resolutions=[]):
    playlist = m3u8.loads(input_content)
    entries_to_remove = []
    for entry in playlist.playlists:
        width, height = entry.stream_info.resolution
        if height in resolutions:
            entries_to_remove.append(entry)

    for entry in entries_to_remove:
        playlist.playlists.remove(entry)

    return playlist.dumps()


def check_resolutions_in_playlist(input_content, resolutions=[]):
    playlist = m3u8.loads(input_content)
    heights = []
    for entry in playlist.playlists:
        width, height = entry.stream_info.resolution
        heights.append(height)
    return any(height in resolutions for height in heights)
