import subprocess


def remux_hls_stream_with_headers(
    input_url, output_file, user_agent=None, referer=None
):
    headers = []
    if user_agent:
        headers.extend(["-user_agent", user_agent])
    if referer:
        headers.extend(["-referer", referer])

    command = [
        "ffmpeg",
        *headers,
        "-i",
        input_url,
        "-c",
        "copy",
        "-vsync",
        "0",
        "-copyts",
        "-start_at_zero",
        "-avoid_negative_ts",
        "make_zero",
        "-fflags",
        "+genpts",
        "-max_interleave_delta",
        "0",
        output_file,
    ]

    process = subprocess.Popen(
        command,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        universal_newlines=True,
    )

    stdout, stderr = process.communicate()
    if process.returncode != 0:
        raise subprocess.CalledProcessError(process.returncode, "ffmpeg", stderr)
