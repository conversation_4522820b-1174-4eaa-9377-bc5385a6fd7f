import calendar
import datetime
from datetime import timedelta

import pytz
from django.utils.timezone import now


def get_month_start_date(date=None):
    year, month, day = split_date(date)
    month_start_date = datetime.date(year, month, 1)
    return month_start_date


def get_month_end_date(date=None):
    year, month, day = split_date(date)
    num_days = calendar.monthrange(year, month)[1]
    month_end_date = datetime.datetime(year, month, num_days, 23, 59, 59)
    return month_end_date


def split_date(date=None):
    today = datetime.date.today()
    year, month, day = (
        (int(x) for x in str(date).split("-"))
        if date
        else (int(x) for x in str(today).split("-"))
    )
    return year, month, day


def get_total_seconds(value):
    if isinstance(value, timedelta):
        return value.total_seconds()
    elif isinstance(value, int):
        return value
    else:
        raise ValueError(
            "Unsupported value type. Supported types are timedelta and int."
        )


def convert_iso8601_to_datetime(iso8601_time):
    if not iso8601_time:
        return
    if iso8601_time.endswith("+00:00"):
        iso8601_time = iso8601_time[:-6] + "Z"
    return datetime.datetime.strptime(iso8601_time, "%Y-%m-%dT%H:%M:%S.%fZ").replace(
        tzinfo=pytz.utc
    )


def get_time_difference(start_time, end_time, unit="seconds"):
    if not (start_time and end_time):
        return

    time_difference = end_time - start_time
    if unit == "minutes":
        return time_difference.total_seconds() / 60
    return time_difference.total_seconds()


def days_until_today(given_date):
    today = datetime.date.today()
    days = (today - given_date.date()).days
    return days


def should_start_instantly(start):
    return start < (now() + timedelta(minutes=1))


def get_current_time_in_milliseconds():
    current_time = datetime.datetime.now()
    milliseconds = int(current_time.timestamp() * 1000)
    return milliseconds


def format_seconds_to_hms(seconds):
    if seconds > 0:
        hours, remainder = divmod(seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{hours:02}:{minutes:02}:{seconds:02}"
    return "00:00:00"


def format_seconds_to_ms(seconds):
    if seconds > 0:
        minutes, seconds = divmod(seconds, 60)
        return f"{minutes:02}:{seconds:02}"
    return "00:00"


def convert_date_string_to_datetime_obj(date_string):
    if date_string:
        try:
            return datetime.datetime.strptime(date_string, "%Y-%m-%d").date()
        except ValueError:
            pass


def convert_time_string_to_timedelta(time_string):
    start_time_parts = list(map(int, time_string.split(":")))
    if len(start_time_parts) == 2:
        minutes, seconds = start_time_parts
        hours = 0
    elif len(start_time_parts) == 3:
        hours, minutes, seconds = start_time_parts

    return timedelta(hours=hours, minutes=minutes, seconds=seconds)
