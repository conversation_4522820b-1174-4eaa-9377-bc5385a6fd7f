from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from typing import Optional, Tuple, Type  # noqa

from django.db import models
from django.utils.timezone import now
from django.utils.translation import gettext_lazy as _
from django_multitenant.fields import Tenant<PERSON>oreign<PERSON>ey
from safedelete.managers import <PERSON>DeleteManager, SafeDeleteQueryset  # noqa

from .base import OrganizationModel


class AccessToken(OrganizationModel):
    valid_until = models.DateTimeField(null=True, blank=True)
    asset = TenantForeignKey(
        "app.Asset", related_name="access_tokens", on_delete=models.CASCADE
    )
    expires_after_first_usage = models.BooleanField(
        default=False,
        help_text=_("If this enabled, token will get expired after one use"),
    )

    def to_dict(self):
        return {
            "uuid": str(self.uuid),
            "valid_until": self.valid_until.isoformat() if self.valid_until else None,
            "asset_id": self.asset_id,
            "expires_after_first_usage": self.expires_after_first_usage,
            "organization_id": self.organization_id,
        }

    @classmethod
    def from_dict(cls, data):
        return cls(
            uuid=data["uuid"],
            valid_until=datetime.fromisoformat(data["valid_until"])
            if data["valid_until"]
            else None,
            asset_id=data["asset_id"],
            expires_after_first_usage=data["expires_after_first_usage"],
            organization_id=data["organization_id"],
        )

    @property
    def status(self):
        if not self.valid_until or now() < self.valid_until:
            return "Active"
        return "Expired"

    @property
    def is_active(self):
        return self.status == "Active"

    def annotate(self, annotations):
        for annotation in annotations:
            self.annotations.create(organization=self.organization, **annotation)

    def update_validity(self, time_to_live=None):
        if time_to_live is not None:
            time_to_live = now() + timedelta(seconds=time_to_live)

        self.valid_until = time_to_live
        self.save(update_fields=["valid_until"])


class AccessTokenAnnotation(OrganizationModel):
    class WatermarkType(models.IntegerChoices):
        STATIC = 0, _("static")
        DYNAMIC = 1, _("dynamic")

    access_token = TenantForeignKey(
        "app.AccessToken", on_delete=models.CASCADE, related_name="annotations"
    )
    type = models.CharField(
        choices=WatermarkType.choices, default=WatermarkType.STATIC, max_length=12
    )
    text = models.CharField(
        help_text=_("Text to display as watermark"), max_length=1024
    )
    color = models.CharField(max_length=12, default="#ff0000")
    size = models.PositiveSmallIntegerField(
        help_text=_("Height of the text, in pixels"), default=15
    )
    opacity = models.DecimalField(
        max_digits=3, decimal_places=2, default=Decimal("0.5")
    )
    x = models.PositiveSmallIntegerField(
        help_text=_("Static text's distance from the left border of video"), default=16
    )
    y = models.PositiveSmallIntegerField(
        help_text=_("Static text's distance from the top border of video"), default=16
    )
    skip = models.PositiveIntegerField(
        null=True,
        help_text=_(
            "Specifies duration(in milliseconds) to not display watermark between two overlays"
        ),
        default=0,
    )
    interval = models.PositiveIntegerField(
        help_text=_("Specifies interval over which the watermark changes position"),
        default=2000,
    )

    def to_dict(self):
        return {
            "uuid": str(self.uuid),
            "access_token_id": self.access_token_id,
            "type": self.type,
            "text": self.text,
            "color": self.color,
            "size": self.size,
            "opacity": str(self.opacity),
            "x": self.x,
            "y": self.y,
            "skip": self.skip,
            "interval": self.interval,
            "organization_id": self.organization_id,
        }

    @classmethod
    def from_dict(cls, data):
        return cls(
            uuid=data["uuid"],
            access_token_id=data["access_token_id"],
            type=data["type"],
            text=data["text"],
            color=data["color"],
            size=data["size"],
            opacity=Decimal(data["opacity"]),
            x=data["x"],
            y=data["y"],
            skip=data["skip"],
            interval=data["interval"],
            organization_id=data["organization_id"],
        )
