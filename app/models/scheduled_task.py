import uuid
from datetime import timedelta

import dill
import requests
from django.db import models, transaction
from django.utils.timezone import now
from django_multitenant.utils import set_current_tenant, unset_current_tenant
from picklefield import Pickled<PERSON><PERSON><PERSON>ield
from safedelete.managers import SafeDeleteManager

from app.db.constraints import DistributeConstraint
from app.models import Organization, OrganizationModel
from app.models.base import NonUUIDModel, OrganizationBaseModel
from config.celery import app as celery_app


class ScheduledTaskReferenceManager(SafeDeleteManager):
    def revoke(self, task_id):
        self.filter(task_id=task_id).delete()

    def process_due_tasks(self):
        now_time = now().replace(second=0, microsecond=0)
        upper_time_limit = now_time + timedelta(seconds=59, microseconds=999999)
        tasks = self.filter(
            run_at__range=[now_time, upper_time_limit],
            status=ScheduledTaskReference.Status.WAITING,
        )
        task_ids = list(map(str, tasks.values_list('task_id', flat=True)))
        requests.post(
            "https://hc-ping.com/f29c82c1-6935-4ef5-9c56-668d268b64a9",
            json={"scheduled_tasks": tasks.count(), "task_ids": task_ids}, 
        )
        for task_reference in tasks:
            task_reference.execute_task()


class ScheduledTaskReference(NonUUIDModel):
    class Status(models.IntegerChoices):
        WAITING = 0
        PROCESSING = 1
        QUEUED = 2

    task_id = models.UUIDField(default=uuid.uuid4, primary_key=True, editable=False)
    organization_uuid = models.CharField(max_length=12, editable=False)
    status = models.PositiveSmallIntegerField(
        choices=Status.choices, default=Status.WAITING
    )
    run_at = models.DateTimeField()

    objects = ScheduledTaskReferenceManager()  # type: ignore

    @property
    def task(self):
        unset_current_tenant()
        organization = Organization.objects.filter(uuid=self.organization_uuid).first()
        set_current_tenant(organization)
        return ScheduledTask.objects.filter(reference_uuid=self.task_id).first()

    @transaction.atomic
    def execute_task(self):
        self.update_status(ScheduledTaskReference.Status.PROCESSING)
        kwargs = dill.loads(self.task.pickled_payload)
        celery_app.send_task(**kwargs)
        self.update_status(ScheduledTaskReference.Status.QUEUED)

    def update_status(self, status):
        self.status = status
        self.save(update_fields=["status"])

    class Meta:
        constraints = [
            DistributeConstraint(
                name="%(app_label)s_%(class)s_distribute", reference=True
            ),
        ]


class ScheduledTask(OrganizationModel):
    # Disable automatic uuid field from OrganizationModel as we don't need it and is redundant
    uuid = None
    reference_uuid = models.UUIDField(db_index=True, editable=False)
    pickled_payload = PickledObjectField(editable=False)

    class Meta:
        ordering = ("-created",)
        # Since we are not using uuid field, we should add unique constraint for organization and reference_uuid
        constraints = OrganizationBaseModel.Meta.constraints + [
            models.UniqueConstraint(
                fields=["organization", "reference_uuid"],
                name="unique_%(class)s_organization_uuid",
            )
        ]
