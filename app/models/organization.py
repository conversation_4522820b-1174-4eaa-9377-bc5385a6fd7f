import uuid

from django.conf import settings
from django.contrib.postgres.fields import Array<PERSON>ield
from django.db import models
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from django_multitenant.mixins import TenantModelMixin

from app.db.constraints import DistributeConstraint
from app.domain.cloud_storage import create_bucket
from app.domain.cloudfront import create_cdn, create_key_group
from app.utils.uuid import populate_uuid

from ..utils.crypto import populate_drm_fields
from .base import Model, OrganizationManager
from .fields import CreatedByField
from .player import BasePlayerPreferences


class Organization(Model, TenantModelMixin, BasePlayerPreferences):
    class StorageProvider(models.IntegerChoices):
        AWS = 1
        WASABI = 2

    class Status(models.IntegerChoices):
        ACTIVE = 0
        SUSPENDED = 1
        BLOCKED = 2

    name = models.CharField(max_length=254)
    uuid = models.CharField(max_length=12, editable=False, null=False, db_index=True)  # type: ignore

    members = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        related_name="organizations",
        through="app.Membership",
        through_fields=("organization", "user"),
    )
    created_by = CreatedByField()
    bucket_name = models.CharField(
        _("Bucket Name"), null=True, blank=True, max_length=2048
    )
    bucket_secret_token = models.CharField(null=True, blank=True, max_length=2048)
    cdn_id = models.CharField(_("CDN ID"), null=True, blank=True, max_length=2048)
    cdn_url = models.URLField(_("CDN URL"), null=True, blank=True)
    cloudfront_key_group_id = models.CharField(
        _("CloudFront Key Group ID"), null=True, blank=True, max_length=256
    )
    drm_aes_signing_key = models.CharField(null=True, blank=True, max_length=64)
    drm_aes_signing_iv = models.CharField(null=True, blank=True, max_length=32)

    storage_vendor = models.PositiveIntegerField(
        _("Storage Vendor"),
        choices=StorageProvider.choices,
        default=StorageProvider.WASABI,
        null=True,
        blank=True,
    )
    storage_region = models.CharField(
        _("Storage Region"), null=True, blank=True, max_length=256
    )
    storage_access_key_id = models.CharField(
        _("Storage Access Key ID"), null=True, blank=True, max_length=256
    )
    storage_secret_access_key = models.CharField(
        _("Storage Secret Access Key"), null=True, blank=True, max_length=256
    )
    cdn_access_key_id = models.CharField(
        _("CDN Access Key ID"), null=True, blank=True, max_length=256
    )
    cdn_secret_access_key = models.CharField(
        _("CDN Secret Access Key"), null=True, blank=True, max_length=256
    )
    cdn_one_year_cache_policy_id = models.CharField(
        _("CDN One-Year Cache Policy ID"), null=True, blank=True, max_length=256
    )
    cdn_expire_in_3_seconds_cache_policy_id = models.CharField(
        _("CDN Cache Policy ID (Expires in 3 Seconds)"),
        null=True,
        blank=True,
        max_length=256,
    )
    cdn_public_key_id = models.CharField(
        _("CDN public key ID"), null=True, blank=True, max_length=256
    )
    cdn_private_key = models.TextField(_("CDN private key ID"), null=True, blank=True)
    allowed_domains_for_embedding = ArrayField(
        base_field=models.CharField(max_length=253),
        default=list,
        null=True,
        blank=True,
        size=50,
    )
    enable_hd_live_streaming = models.BooleanField(
        _("Enable HD resolution for live stream"), default=False
    )
    show_analytics = models.BooleanField(
        _("Show Video Analytics"), default=False, null=True
    )

    limited_access_enabled = models.BooleanField(
        _("Enable Limited Access"), default=True, editable=True, null=True
    )

    status = models.PositiveSmallIntegerField(
        _("Status"), choices=Status.choices, default=Status.ACTIVE, db_index=True
    )
    use_haproxy_for_livestream = models.BooleanField(
        _("Use HAProxy for livestream"), null=True, default=True
    )

    enabled_resolutions = ArrayField(
        models.CharField(
            max_length=10,
            choices=[
                ("4k", "4k"),
                ("1080p", "1080p"),
                ("720p", "720p"),
                ("480p", "480p"),
                ("360p", "360p"),
                ("240p", "240p"),
            ],
        ),
        default=["720p", "480p", "360p", "240p"],
        blank=True,
        null=True,
    )

    tenant_id = "id"
    objects = OrganizationManager()

    @property
    def bucket_url(self):
        if self.uses_s3_storage:
            return "s3://" + self.bucket_name
        return "wasabi://" + self.bucket_name

    @property
    def uses_s3_storage(self):
        return self.storage_vendor == Organization.StorageProvider.AWS

    @property
    def storage_domain(self):
        if self.storage_vendor == Organization.StorageProvider.AWS:
            return f"s3.{self.storage_region}.amazonaws.com"
        return f"s3.{self.storage_region}.wasabisys.com"

    def initialize_bucket(self):
        if not self.bucket_name:
            self.bucket_name = slugify(self) + "-tpstreams.com"
            self.bucket_secret_token = uuid.uuid4().hex
            create_bucket(self)
            self.save()

    def initialize_cdn(self):
        if not self.cdn_id:
            self.cdn_id, self.cdn_url = create_cdn(self)
            self.save()

    def initialize_cloudfront_key_group(self):
        if not self.cloudfront_key_group_id:
            previous_organization = (
                Organization.objects.exclude(cdn_id__isnull=True)
                .filter(cdn_public_key_id=self.cdn_public_key_id)
                .last()
            )

            if previous_organization and previous_organization.cloudfront_key_group_id:
                self.cloudfront_key_group_id = (
                    previous_organization.cloudfront_key_group_id
                )
            else:
                self.cloudfront_key_group_id = "f4200d40-4581-4b36-a257-dd7c3e213e97"

            self.save()

    def refresh_cloudfront_key_group(self):
        self.cloudfront_key_group_id = create_key_group(self)
        self.save()

    def is_member(self, user):
        return (
            user.is_authenticated
            and user.memberships.filter(organization=self).exists()
        )

    def __str__(self):
        return f"{self.name}-{self.uuid}"  # type: ignore

    class Meta:
        constraints = [
            DistributeConstraint(
                name="%(app_label)s_%(class)s_distribute", distribution_column="id"
            ),
        ]


@receiver(pre_save, sender=Organization)
def populate_organization_fields(sender, instance, **kwargs):
    populate_uuid(instance, length=6)
    populate_drm_fields(instance)


@receiver(post_save)
def initialize_bucket_and_cdn(sender, instance, *args, **kwargs):
    if isinstance(instance, Organization) and not settings.DEBUG:
        instance.initialize_cloudfront_key_group()
        instance.initialize_bucket()
        instance.initialize_cdn()
