from django.contrib.postgres.fields import ArrayField
from django.db import models
from django.utils.translation import gettext_lazy as _

from .base import OrganizationModel


class VideoProcessingOptions(OrganizationModel):
    class Format(models.IntegerChoices):
        ABR = 0, _("abr")
        MP4 = 1, _("mp4")

    class VideoCodec(models.IntegerChoices):
        H264 = 0, _("h264")
        H265 = 1, _("h265")

    class AudioCodec(models.IntegerChoices):
        AAC = 0, _("aac")

    class ThumbnailFormat(models.IntegerChoices):
        PNG = 0, _("png")
        JPG = 1, _("jpg")
        JPEG = 2, _("jpeg")
        WEBP = 3, _("webp")

    class Resolutions(models.IntegerChoices):
        _240p = 0, _("240p")
        _360p = 1, _("360p")
        _480p = 2, _("480p")
        _720p = 3, _("720p")
        _1080p = 4, _("1080p")
        _4k = 5, _("4k")

    class ContentProtectionType(models.IntegerChoices):
        DISABLED = 0, _("Disabled")
        DRM = 1, _("DRM (Widevine & Fairplay)")
        AES = 2, _("AES (Advanced Encryption Standard)")
        AES_WITH_SIGNED_URL = 3, _("AES with Signed URL")

    format = models.PositiveSmallIntegerField(
        choices=Format.choices, default=Format.ABR
    )
    resolutions = ArrayField(
        models.PositiveSmallIntegerField(choices=Resolutions.choices), default=list
    )
    video_codec = models.PositiveSmallIntegerField(
        choices=VideoCodec.choices, default=VideoCodec.H264
    )
    audio_codec = models.PositiveSmallIntegerField(
        choices=AudioCodec.choices, default=AudioCodec.AAC
    )
    video_codecs = ArrayField(
        models.PositiveSmallIntegerField(choices=VideoCodec.choices),
        default=list,
        null=True,
    )
    enable_drm = models.BooleanField(default=False)
    thumbnail_duration = models.PositiveBigIntegerField(null=True)
    thumbnail_format = models.PositiveSmallIntegerField(
        choices=ThumbnailFormat.choices, default=ThumbnailFormat.PNG
    )
    padding_top = models.PositiveSmallIntegerField(null=True, blank=True)
    padding_bottom = models.PositiveSmallIntegerField(null=True, blank=True)
    padding_left = models.PositiveSmallIntegerField(null=True, blank=True)
    padding_right = models.PositiveSmallIntegerField(null=True, blank=True)
    padding_color = models.CharField(max_length=255, null=True, blank=True)
    content_protection_type = models.PositiveSmallIntegerField(
        choices=ContentProtectionType.choices,
        default=ContentProtectionType.DISABLED,
        null=True,
    )

    def save(self, *args, **kwargs):
        if self.content_protection_type == self.ContentProtectionType.DRM:
            self.enable_drm = True
        super().save(*args, **kwargs)

    class Meta(OrganizationModel.Meta):
        abstract = True
