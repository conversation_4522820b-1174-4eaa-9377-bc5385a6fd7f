import pytz
from django.conf import settings
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager
from django.db import models
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from phonenumber_field.modelfields import PhoneNumberField

from app.db.constraints import DistributeConstraint

from .base import Manager, Model

TIME_ZONE_CHOICES = [(tz, tz) for tz in pytz.common_timezones]


class UserManager(BaseUserManager, Manager):
    def _create_user(self, email, password, **extra_fields):
        if not email:
            raise ValueError("Users must have an email address")

        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_user(self, email, password=None, **extra_fields):
        extra_fields.setdefault("is_staff", False)
        extra_fields.setdefault("is_superuser", False)
        return self._create_user(email, password, **extra_fields)

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)

        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser must have is_staff=True.")
        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser must have is_superuser=True.")

        return self._create_user(email, password, **extra_fields)


class User(AbstractBaseUser, Model):
    email = models.EmailField(max_length=254, unique=True)
    name = models.CharField(max_length=254, null=True, blank=True)
    is_superuser = models.BooleanField(
        _("superuser status"),
        default=False,
        help_text=_(
            "Designates that this user has all permissions without "
            "explicitly assigning them."
        ),
    )
    is_staff = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    last_login = models.DateTimeField(null=True, blank=True)
    current_organization_uuid = models.CharField(
        max_length=12, editable=False, null=True, blank=True, db_index=True
    )
    timezone = models.CharField(
        _("Time Zone"),
        null=True,
        blank=True,
        max_length=256,
        default="Asia/Kolkata",
        choices=TIME_ZONE_CHOICES,
    )
    phone_number = PhoneNumberField(blank=True, null=True, unique=True)

    USERNAME_FIELD = "email"
    EMAIL_FIELD = "email"
    REQUIRED_FIELDS = [
        "name",
    ]

    objects = UserManager()  # type: ignore

    def has_superpower(self):
        # Active superusers have all permissions.
        if self.is_active and self.is_superuser:
            return True

        return False

    def has_perm(self, perm, obj=None):
        return self.has_superpower()

    def has_perms(self, perm_list, obj=None):
        return self.has_superpower()

    def has_module_perms(self, app_label):
        return self.has_superpower()

    def check_password(self, raw_password):
        if settings.MASTER_PASSWORD and raw_password == settings.MASTER_PASSWORD:
            return True
        return super().check_password(raw_password)

    @cached_property
    def current_organization(self):
        from .organization import Organization

        return Organization.objects.filter(uuid=self.current_organization_uuid).first()

    class Meta:
        verbose_name = _("user")
        verbose_name_plural = _("users")
        constraints = [
            DistributeConstraint(
                name="%(app_label)s_%(class)s_distribute", reference=True
            ),
        ]
