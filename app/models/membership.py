from django.conf import settings
from django.db import models
from django.db.models import Q

from .base import OrganizationModel


class Membership(OrganizationModel):
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="memberships",
        on_delete=models.CASCADE,
    )
    email = models.EmailField(null=True, blank=True, max_length=75)

    def __str__(self):
        if self.user:
            return f"{self.user.name} in {self.organization}"
        return f"{self.email} in {self.organization}"

    class Meta(OrganizationModel.Meta):
        constraints = OrganizationModel.Meta.constraints + [
            models.UniqueConstraint(
                fields=["organization", "user", "email"],
                name="%(class)s_org_user_email_uniq",
            ),
            models.UniqueConstraint(
                fields=["organization", "user"],
                condition=Q(email__isnull=True),
                name="%(class)s_org_user_uniq",
            ),
            models.UniqueConstraint(
                fields=["organization", "email"],
                condition=Q(user__isnull=True),
                name="%(class)s_org_email_uniq",
            ),
            models.CheckConstraint(
                check=Q(email__isnull=False) | Q(user__isnull=False),
                name="%(class)s_check_email_and_user",
            ),
        ]
