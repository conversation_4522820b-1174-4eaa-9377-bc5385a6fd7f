from django.conf import settings
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_multitenant.fields import TenantForeignKey

from app.models import Asset, OrganizationModel, TenantOneToOneField


class EventType(models.IntegerChoices):
    RECORDING_STARTED = 1, _("Recording Started")
    RECORDING_STOPPED = 2, _("Recording Stopped")
    RECORDING_PAUSED = 3, _("Recording Paused")
    RECORDING_RESUMED = 4, _("Recording Resumed")
    RECORDING_COMPLETED = 5, _("Recording Completed")


class WebhookStatus(models.IntegerChoices):
    PENDING = 1, _("Pending Processing")
    SKIPPED = 2, _("Skipped Processing")
    FAILED = 3, _("Processing Failed")
    SUCCESS = 4, _("Processed Successfully")


class ZoomWebhookLog(OrganizationModel):
    event_type = models.PositiveSmallIntegerField(
        choices=EventType.choices,
        help_text=_("Type of Zoom event received (e.g., recording completed)."),
    )
    payload = models.JSONField(
        help_text=_("Raw JSON payload received from the Zoom webhook.")
    )
    received_at = models.DateTimeField(
        auto_now_add=True, help_text=_("Timestamp when the webhook was received.")
    )
    status = models.PositiveSmallIntegerField(
        choices=WebhookStatus.choices,
        default=WebhookStatus.PENDING,
        help_text=_("Processing status of the webhook event."),
    )
    error_message = models.TextField(
        null=True,
        blank=True,
        help_text=_("Error message if processing failed, else blank."),
    )

    class Meta(OrganizationModel.Meta):
        ordering = ["-received_at"]
        verbose_name = _("Zoom Webhook Log")
        verbose_name_plural = _("Zoom Webhook Logs")

    def __str__(self):
        return f"{self.get_event_type_display()} at {self.received_at}"

    def update_status(self, status):
        self.status = status
        self.save(update_fields=["status"])


class ImportStatus(models.IntegerChoices):
    PENDING = 1, _("Pending")
    IMPORTED = 2, _("Imported")
    FAILED = 3, _("Failed")


class ZoomRecording(OrganizationModel):
    asset = TenantOneToOneField(
        Asset,
        on_delete=models.CASCADE,
        related_name="zoom_recording",
        help_text=_("The Asset created from this Zoom recording."),
    )
    meeting_uuid = models.CharField(
        max_length=255,
        db_index=True,
        help_text=_("Zoom meeting UUID for this recording."),
    )
    recording_uuid = models.CharField(
        max_length=255,
        help_text=_("Unique Zoom recording UUID. Used for idempotency."),
    )
    zoom_user_id = models.CharField(
        max_length=255, db_index=True, help_text=_("Zoom user ID of the meeting host.")
    )
    topic = models.CharField(
        max_length=1024,
        null=True,
        blank=True,
        help_text=_("Meeting topic/title from Zoom."),
    )
    start_time = models.DateTimeField(
        null=True, blank=True, help_text=_("Start time of the Zoom meeting")
    )
    duration = models.PositiveIntegerField(
        null=True, blank=True, help_text=_("Duration of the meeting")
    )
    imported_at = models.DateTimeField(
        auto_now_add=True, help_text=_("Timestamp when the recording was imported.")
    )
    download_url = models.URLField(
        max_length=2048,
        null=True,
        blank=True,
        help_text=_("Zoom download URL for the recording file."),
    )
    status = models.PositiveSmallIntegerField(
        choices=ImportStatus.choices,
        default=ImportStatus.PENDING,
        help_text=_("Import status: pending, imported, or failed."),
    )
    error_message = models.TextField(
        null=True,
        blank=True,
        help_text=_("Error message if import failed, else blank."),
    )

    class Meta(OrganizationModel.Meta):
        ordering = ["-created"]
        verbose_name = _("Zoom Recording")
        verbose_name_plural = _("Zoom Recordings")
        constraints = OrganizationModel.Meta.constraints + [
            models.UniqueConstraint(
                fields=["organization", "recording_uuid"],
                name="unique_%(class)s_organization_recording_uuid",
            )
        ]

    def __str__(self):
        return f"Recording {self.recording_uuid} imported to Asset {getattr(self.asset, 'uuid', 'N/A')}"

    def mark_as_imported(self):
        self.status = ImportStatus.IMPORTED
        self.save(update_fields=["status"])

    def mark_as_failed(self, error_message=None):
        self.status = ImportStatus.FAILED
        self.error_message = str(error_message) if error_message is not None else None
        self.save(update_fields=["status", "error_message"])


class ConnectionStatus(models.IntegerChoices):
    CONNECTED = 1, _("Connected")
    DISCONNECTED = 2, _("Disconnected")
    REFRESH_TOKEN_EXPIRED = 3, _("Refresh Token Expired")


class ZoomAccount(OrganizationModel):
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="zoom_accounts",
        help_text=_("The user who authorized this Zoom account."),
    )
    zoom_user_id = models.CharField(
        max_length=255,
        db_index=True,
        help_text=_("Unique Zoom user ID for this account."),
    )
    email = models.EmailField(
        null=True, blank=True, help_text=_("Email address of the Zoom account.")
    )
    access_token = models.TextField(
        null=True, blank=True, help_text=_("Current Zoom OAuth access token.")
    )
    refresh_token = models.TextField(
        null=True, blank=True, help_text=_("Current Zoom OAuth refresh token.")
    )
    expires_at = models.DateTimeField(
        null=True, blank=True, help_text=_("Datetime when the access token expires.")
    )
    status = models.PositiveSmallIntegerField(
        choices=ConnectionStatus.choices,
        default=ConnectionStatus.CONNECTED,
        help_text=_("Current status of the Zoom connection."),
        db_index=True,
    )
    disconnected_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_("When the account was last disconnected or refresh token expired."),
    )
    import_destination = TenantForeignKey(
        Asset,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        limit_choices_to={"type": Asset.Type.FOLDER},
        help_text=_("The folder where Zoom recordings will be imported by default."),
    )
    enable_drm = models.BooleanField(
        default=False,
        help_text=_(
            "If enabled, all imported Zoom recordings will be protected with DRM."
        ),
    )

    class Meta(OrganizationModel.Meta):
        ordering = ["-created"]
        verbose_name = _("Zoom Account")
        verbose_name_plural = _("Zoom Accounts")
        constraints = OrganizationModel.Meta.constraints + [
            models.UniqueConstraint(
                fields=["organization", "zoom_user_id"],
                name="unique_%(class)s_organization_zoom_user_id",
            ),
            models.UniqueConstraint(
                fields=["organization", "user"],
                name="unique_%(class)s_organization_user",
            ),
        ]

    def __str__(self):
        return f"{self.user} - {self.zoom_user_id} - {self.get_status_display()}"

    @property
    def is_token_expired(self):
        return self.expires_at is not None and self.expires_at <= timezone.now()

    @property
    def is_connected(self):
        return self.status == ConnectionStatus.CONNECTED

    def disconnect(self):
        self.access_token = None
        self.refresh_token = None
        self.expires_at = None
        self.status = ConnectionStatus.DISCONNECTED
        self.disconnected_at = timezone.now()
        self.save(
            update_fields=[
                "access_token",
                "refresh_token",
                "expires_at",
                "status",
                "disconnected_at",
            ]
        )

    def mark_refresh_token_expired(self):
        self.access_token = None
        self.refresh_token = None
        self.expires_at = None
        self.status = ConnectionStatus.REFRESH_TOKEN_EXPIRED
        self.disconnected_at = timezone.now()
        self.save(
            update_fields=[
                "access_token",
                "refresh_token",
                "expires_at",
                "status",
                "disconnected_at",
            ]
        )

    def update_tokens(self, access_token, refresh_token, expires_in_seconds):
        self.access_token = access_token
        self.refresh_token = refresh_token
        self.expires_at = timezone.now() + timezone.timedelta(
            seconds=expires_in_seconds
        )
        self.status = ConnectionStatus.CONNECTED
        self.disconnected_at = None
        self.save(
            update_fields=[
                "access_token",
                "refresh_token",
                "expires_at",
                "status",
                "disconnected_at",
            ]
        )
