from django.db import models
from django.utils.translation import gettext_lazy as _

from .base import OrganizationModel


class AssetUsage(OrganizationModel):
    class TimeFrames(models.IntegerChoices):
        DAILY = 1
        MONTHLY = 2

    time_frame = models.PositiveSmallIntegerField(
        choices=TimeFrames.choices, default=TimeFrames.DAILY, db_index=True
    )
    date = models.DateField()
    bandwidth_used = models.PositiveBigIntegerField(
        _("Bandwidth used in bytes"), null=True, default=0
    )
    active_storage_bytes = models.PositiveBigIntegerField(
        _("Active storage used in bytes"), null=True, default=0
    )
    deleted_storage_bytes = models.PositiveBigIntegerField(
        _("Deleted storage used in bytes"), null=True, default=0
    )

    live_stream_usage = models.PositiveBigIntegerField(
        _("Live stream usage in seconds"), null=True, default=0
    )
    subtitle_generation_minutes = models.PositiveBigIntegerField(
        _("Minutes of subtitles generated"), null=True, default=0
    )
    subtitle_generation_cost = models.PositiveBigIntegerField(
        _("Subtitles generation cost in dollars"), null=True, default=0
    )

    @property
    def total_storage_bytes(self):
        return self.active_storage_bytes + self.deleted_storage_bytes

    class Meta(OrganizationModel.Meta):
        constraints = OrganizationModel.Meta.constraints + [
            models.UniqueConstraint(
                fields=["organization", "date", "time_frame"],
                name="unique_%(class)s_organization_bandwidth_used",
            )
        ]
