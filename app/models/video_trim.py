from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator
from django.db import models
from django.db.models import UniqueConstraint
from django.utils.translation import gettext_lazy as _
from django_multitenant.fields import TenantForeignKey

from app.models import CreatedByField
from app.models.video import Video

from .base import OrganizationModel


class TrimStatus(models.IntegerChoices):
    PENDING = 1, _("Pending")
    PROCESSING = 2, _("Processing")
    COMPLETED = 3, _("Completed")
    FAILED = 4, _("Failed")
    REVERTED = 5, _("Reverted")


class VideoTrim(OrganizationModel):
    video = TenantForeignKey(
        Video,
        on_delete=models.CASCADE,
        related_name="trims",
        help_text=_("Trimmed video asset reference"),
    )
    start_time = models.PositiveIntegerField(
        default=0,
        validators=[MinValueValidator(0)],
        help_text=_("Trim start time in seconds"),
    )
    end_time = models.PositiveIntegerField(
        blank=True,
        null=True,
        validators=[MinValueValidator(1)],
        help_text=_("Trim end time in seconds"),
    )
    created_by = CreatedByField(
        help_text=_("User who initiated the trim operation"),
    )
    status = models.PositiveSmallIntegerField(
        choices=TrimStatus.choices,
        default=TrimStatus.PENDING,
        help_text=_("Current status of the trimming job"),
    )
    background_task_id = models.UUIDField(
        null=True,
        blank=True,
        help_text=_("Async task ID associated with the background processing job"),
    )

    class Meta(OrganizationModel.Meta):
        ordering = ["-created"]
        verbose_name = _("Video Trim")
        verbose_name_plural = _("Video Trims")

    def __str__(self) -> str:
        return f"Trim[{self.video.asset.uuid}] ({self.start_time}-{self.end_time}s)"

    def clean(self):
        super().clean()
        if self.start_time is not None and self.end_time is not None:
            if self.end_time <= self.start_time:
                raise ValidationError(
                    {"end_time": _("End time must be greater than start time")}
                )


class OutputType(models.IntegerChoices):
    HLS = 1, _("HLS")
    DASH = 2, _("DASH")
    TRIMMED_HLS = 3, _("Trimmed HLS")
    TRIMMED_DASH = 4, _("Trimmed DASH")


class VideoOutput(OrganizationModel):
    video = TenantForeignKey(
        Video,
        on_delete=models.CASCADE,
        related_name="outputs",
        help_text=_("Original video associated with this output"),
    )
    output_type = models.PositiveSmallIntegerField(
        choices=OutputType.choices,
        help_text=_("Type of output (e.g., HLS, DASH)"),
    )
    codec = models.CharField(
        max_length=16,
        help_text=_("Video codec used, e.g., h264 or h265"),
    )
    duration = models.PositiveIntegerField(
        help_text=_("Duration of the video output in seconds")
    )
    url = models.URLField(
        max_length=1024,
        help_text=_("URL to access this video output"),
    )
    is_active = models.BooleanField(
        default=True,
        help_text=_("Indicates whether this output is currently active"),
    )

    class Meta(OrganizationModel.Meta):
        constraints = OrganizationModel.Meta.constraints + [
            UniqueConstraint(
                fields=["organization", "video", "output_type", "codec"],
                name="unique_video_output_per_type_codec",
            )
        ]
        ordering = ["-created"]
        verbose_name = _("Video Output")
        verbose_name_plural = _("Video Outputs")

    def __str__(self) -> str:
        return (
            f"Output[{self.video_id}] {self.get_output_type_display()} - {self.codec}"
        )
