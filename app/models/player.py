from django.conf.locale import LANG_INFO
from django.db import models
from django.utils.translation import gettext_lazy as _

from .base import OrganizationModel


class BasePlayerPreferences(models.Model):
    LANGUAGE_CODES = tuple(
        (i["code"], i["name"]) for i in LANG_INFO.values() if i.get("code")
    )

    class ResolutionChoices(models.IntegerChoices):
        AUTO = 0, "Auto"
        P_240 = 240, "240p"
        P_360 = 360, "360p"
        P_480 = 480, "480p"
        P_720 = 720, "720p"
        P_1080 = 1080, "1080p"
        P_4K = 2160, "4k"

    class PlayButtonPosition(models.IntegerChoices):
        AUTO = 0, "Auto"
        BOTTOM = 1, "Bottom"
        CENTER = 2, "Center"

    autoplay_enabled = models.BooleanField(
        _("Enable autoplay"), default=False, null=True, blank=True
    )
    background_mode_enabled = models.BooleanField(
        _("Enable background mode"), default=False, null=True, blank=True
    )
    loop_enabled = models.BooleanField(
        _("Enable loop playback"), default=False, null=True, blank=True
    )
    muted_on_start = models.BooleanField(
        _("Mute video on start"), default=False, null=True, blank=True
    )

    default_quality = models.PositiveSmallIntegerField(
        _("Default video quality"),
        choices=ResolutionChoices.choices,
        default=ResolutionChoices.AUTO,
        null=True,
        blank=True,
    )
    play_button_position = models.PositiveSmallIntegerField(
        _("Play button position"),
        choices=PlayButtonPosition.choices,
        default=PlayButtonPosition.AUTO,
        null=True,
        blank=True,
    )
    primary_color = models.CharField(
        _("Primary color"), max_length=12, default="#03a4eb", null=True, blank=True
    )
    icons_color = models.CharField(
        _("Icons color"), max_length=12, default="#fff", null=True, blank=True
    )
    accent_color = models.CharField(
        _("Accent color"), max_length=12, default="#03a4eb", null=True, blank=True
    )
    background_color = models.CharField(
        _("Background color"), max_length=12, default="#0F0F0F", null=True, blank=True
    )
    show_player_controls = models.BooleanField(
        _("Show player controls"), default=True, null=True, blank=True
    )
    show_video_title = models.BooleanField(
        _("Show video title"), default=False, null=True, blank=True
    )
    show_subtitles = models.BooleanField(
        _("Show subtitles"), default=True, null=True, blank=True
    )
    show_play_button = models.BooleanField(
        _("Show play button"), default=True, null=True, blank=True
    )
    show_progress_bar = models.BooleanField(
        _("Show progress bar"), default=True, null=True, blank=True
    )

    show_volume_bar = models.BooleanField(
        _("Show volume"), default=True, null=True, blank=True
    )
    show_transcript = models.BooleanField(
        _("Show transcript"), default=True, null=True, blank=True
    )
    show_video_quality_control = models.BooleanField(
        _("Show video quality controls"), default=True, null=True, blank=True
    )
    show_speed_control = models.BooleanField(
        _("Show speed controls"), default=True, null=True, blank=True
    )
    show_picture_in_picture_control = models.BooleanField(
        _("Show picture in picture control "), default=True, null=True, blank=True
    )
    show_full_screen_control = models.BooleanField(
        _("Show full controls"), default=True, null=True, blank=True
    )
    default_subtitle_language = models.CharField(
        _("Default subtitle language"),
        max_length=10,
        null=True,
        blank=True,
        choices=LANGUAGE_CODES,
        default="en",
    )

    class Meta:
        abstract = True


class EmbedPreset(BasePlayerPreferences, OrganizationModel):
    title = models.CharField(_("Preset title"), max_length=255)

    class Meta(OrganizationModel.Meta):
        verbose_name = "Embed Preset"
        verbose_name_plural = "Embed Presets"
        constraints = OrganizationModel.Meta.constraints + [
            models.UniqueConstraint(
                fields=["organization", "title"],
                name="unique_%(class)s_organization_embed_preset",
            )
        ]
