from typing import Op<PERSON>, Tu<PERSON>, Type  # noqa

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils.translation import pgettext_lazy
from django_multitenant.fields import TenantForeignKey
from safedelete.managers import SafeDeleteManager, SafeDeleteQueryset  # noqa
from taggit.models import ItemBase, TagBase

from .base import OrganizationModel, OrganizationThroughModel


class Tag(TagBase, OrganizationModel):
    name = models.CharField(
        verbose_name=pgettext_lazy("A tag name", "name"), max_length=100
    )
    slug = models.SlugField(
        verbose_name=pgettext_lazy("A tag slug", "slug"),
        max_length=100,
        allow_unicode=True,
    )

    class Meta(OrganizationModel.Meta):
        verbose_name = _("Tag")
        verbose_name_plural = _("Tags")
        constraints = OrganizationModel.Meta.constraints + [
            models.UniqueConstraint(
                fields=["organization", "name"],
                name="unique_%(class)s_organization_name",
            ),
            models.UniqueConstraint(
                fields=["organization", "slug"],
                name="unique_%(class)s_organization_slug",
            ),
        ]


class TaggedAsset(ItemBase, OrganizationThroughModel):
    content_object = TenantForeignKey("app.Asset", on_delete=models.CASCADE)
    tag = TenantForeignKey(
        "app.Tag",
        related_name="%(app_label)s_%(class)s_items",
        on_delete=models.CASCADE,
    )

    class Meta(OrganizationThroughModel.Meta):
        constraints = OrganizationThroughModel.Meta.constraints
