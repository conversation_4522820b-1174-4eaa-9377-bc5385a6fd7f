from django.conf.locale import LANG_INFO
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_multitenant.fields import TenantForeignKey

from .base import OrganizationModel
from .fields import TenantOneToOneField
from .preview_thumbnail import PreviewThumbnail


class Track(OrganizationModel):
    LANGUAGE_CODES = tuple(
        (i["code"], i["name"]) for i in LANG_INFO.values() if i.get("code")
    )

    class Type(models.IntegerChoices):
        VIDEO = 1
        AUDIO = 2
        PLAYLIST = 3
        THUMBNAIL = 4
        SUBTITLE = 5
        PREVIEW_THUMBNAIL = 6

    class SubtitleType(models.IntegerChoices):
        AUTO_GENERATED = 0, _("Auto Generated")
        UPLOADED = 1, _("Uploaded")

    type = models.PositiveSmallIntegerField(choices=Type.choices)
    name = models.CharField(max_length=255)
    width = models.PositiveSmallIntegerField(null=True)
    height = models.PositiveSmallIntegerField(null=True)
    bytes = models.PositiveBigIntegerField(null=True)
    duration = models.PositiveBigIntegerField(null=True)
    video = TenantForeignKey(
        "app.Video", on_delete=models.CASCADE, related_name="tracks"
    )
    language = models.CharField(
        max_length=20, null=True, blank=True, choices=LANGUAGE_CODES, default="en"
    )
    url = models.CharField(_("Track Url"), null=True, blank=True, max_length=4096)
    is_active = models.BooleanField(default=True)
    subtitle_type = models.PositiveSmallIntegerField(
        choices=SubtitleType.choices, default=SubtitleType.UPLOADED
    )
    subtitle_data = models.JSONField(
        _("Subtitle generation related data"), null=True, blank=True
    )
    preview_thumbnail = TenantOneToOneField(
        PreviewThumbnail, on_delete=models.SET_NULL, null=True, related_name="track"
    )
