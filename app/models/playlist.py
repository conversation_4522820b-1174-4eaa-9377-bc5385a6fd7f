from django.db import models
from django.utils.translation import gettext_lazy as _
from django_multitenant.fields import Tenant<PERSON><PERSON><PERSON><PERSON><PERSON>

from .base import OrganizationModel


class Playlist(OrganizationModel):
    name = models.CharField(max_length=255)
    track = TenantForeignKey(
        "app.Track", on_delete=models.CASCADE, related_name="playlists"
    )
    bytes = models.PositiveBigIntegerField(null=True)
    width = models.PositiveSmallIntegerField(_("Width"), null=True)
    height = models.PositiveSmallIntegerField(_("Height"), null=True)
    path = models.CharField(_("Playlist Path"), null=True, max_length=4096)

    class Meta(OrganizationModel.Meta):
        constraints = OrganizationModel.Meta.constraints + [
            models.UniqueConstraint(
                fields=["organization", "track", "name"],
                name="unique_%(class)s_organization_playlist",
            )
        ]
