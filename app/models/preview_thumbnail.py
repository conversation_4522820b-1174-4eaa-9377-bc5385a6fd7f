from django.db import models
from django.utils.translation import gettext_lazy as _

from .base import OrganizationModel


class PreviewThumbnail(OrganizationModel):
    width = models.PositiveSmallIntegerField(_("Width"), null=True)
    height = models.PositiveSmallIntegerField(_("Height"), null=True)
    rows = models.PositiveSmallIntegerField(_("Rows"), null=True, default=0)
    columns = models.PositiveSmallIntegerField(_("Columns"), null=True, default=0)
    interval = models.PositiveSmallIntegerField(
        _("Sprite Interval"), null=True, default=0
    )
    url = models.Char<PERSON>ield(
        _("PreviewThumbnail Url"), null=True, blank=True, max_length=4096
    )
