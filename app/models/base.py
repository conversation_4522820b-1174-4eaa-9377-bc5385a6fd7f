import uuid
from typing import Op<PERSON>, <PERSON>ple, Type  # noqa

from django.db import models
from django_multitenant.mixins import TenantManagerMixin, TenantModelMixin
from django_multitenant.utils import get_current_tenant, get_tenant_filters
from model_utils.models import TimeStampedModel
from mptt.managers import TreeManager
from mptt.models import MPTTModel
from safedelete.managers import SafeDeleteManager, SafeDeleteQueryset  # noqa
from safedelete.models import SOFT_DELETE_CASCADE, SafeDeleteModel

from app.db.constraints import CompositePrimaryKeyConstraint, DistributeConstraint


class Manager(SafeDeleteManager):
    pass


class NonUUIDModel(TimeStampedModel, SafeDeleteModel):
    _safedelete_policy = SOFT_DELETE_CASCADE

    class Meta:
        abstract = True


class Model(NonUUIDModel):
    uuid = models.UUIDField(default=uuid.uuid4, editable=False, null=False)

    objects = Manager()  # type: ignore

    class Meta:
        abstract = True


class OrganizationManager(SafeDeleteManager, TenantManagerMixin):
    def get_queryset(self):
        queryset = SafeDeleteManager.get_queryset(self)

        # Injecting tenant_id filters in the get_queryset.
        # Injects tenant_id filter on the current model for all the non-join/join queries.
        current_tenant = get_current_tenant()
        if current_tenant:
            kwargs = get_tenant_filters(self.model)
            return queryset.filter(**kwargs)
        return queryset


class OrganizationTreeManager(OrganizationManager, TreeManager):
    def get_queryset(self):
        return OrganizationManager.get_queryset(self).order_by(
            self.tree_id_attr, self.left_attr
        )


class OrganizationBaseModel(models.Model, TenantModelMixin):
    organization = models.ForeignKey(
        "app.Organization",
        on_delete=models.CASCADE,
        related_name="%(class)s",
        related_query_name="%(class)ss",
    )

    tenant_id = "organization_id"
    objects = OrganizationManager()

    class Meta:
        abstract = True
        constraints: list[object] = [
            CompositePrimaryKeyConstraint(
                fields=["organization", "id"],
                name="%(app_label)s_%(class)s_pkey",
            ),
            DistributeConstraint(
                name="%(app_label)s_%(class)s_distribute",
                distribution_column="organization_id",
            ),
        ]

    def save(self, *args, **kwargs):
        if self.is_update():
            update_fields = kwargs.pop("update_fields", None)
            if update_fields:
                update_fields = self.remove_organization_from_update_fields(
                    update_fields
                )
            else:
                update_fields = self.get_all_fields_without_organization()
            kwargs["update_fields"] = update_fields
        super().save(*args, **kwargs)

    def is_update(self):
        return getattr(self, "id", None)

    def remove_organization_from_update_fields(self, update_fields):
        return [field for field in update_fields if field != "organization"]

    def get_all_fields_without_organization(self):
        return [
            field.name
            for field in self._meta.concrete_fields
            if field.name != "organization"
            and not field.primary_key
            and not hasattr(field, "through")
        ]


# We don't need UUID for through tables
class OrganizationThroughModel(OrganizationBaseModel, NonUUIDModel):
    class Meta(OrganizationBaseModel.Meta):
        abstract = True


class OrganizationModel(OrganizationBaseModel, Model):
    class Meta(OrganizationBaseModel.Meta):
        abstract = True
        constraints = OrganizationBaseModel.Meta.constraints + [
            models.UniqueConstraint(
                fields=["organization", "uuid"],
                name="unique_%(class)s_organization_uuid",
            )
        ]


class OrganizationTreeModel(MPTTModel, OrganizationModel):
    objects = OrganizationTreeManager()

    class Meta(OrganizationModel.Meta):
        abstract = True
