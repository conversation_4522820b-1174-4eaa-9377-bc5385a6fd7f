import os
from typing import Op<PERSON>, Tuple, Type  # noqa

from django.contrib.postgres.fields import Array<PERSON>ield
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_multitenant.fields import Tenant<PERSON><PERSON>ign<PERSON>ey
from safedelete.managers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SafeDeleteQueryset  # noqa

from app.domain.cloud_storage import generate_presigned_url

from ..domain import cloudfront
from ..domain.access_token import get_or_create_access_token_without_validity
from .asset import Asset
from .base import OrganizationThroughModel
from .fields import TenantOneToOneField
from .player import BasePlayerPreferences, EmbedPreset
from .videoprocessingoptions import VideoProcessingOptions


class Video(VideoProcessingOptions, BasePlayerPreferences):
    class Status(models.IntegerChoices):
        NOT_STARTED = 0
        TRANSCODING = 1
        COMPLETED = 2
        ERROR = 3
        UPLOADING = 4
        QUEUED = 5
        INPUT_READ_ERROR = 6
        UPLOADED = 7
        DELETING = 8

    asset = TenantOneToOneField(Asset, on_delete=models.CASCADE)

    status = models.PositiveSmallIntegerField(
        choices=Status.choices, default=Status.NOT_STARTED, db_index=True
    )
    progress = models.PositiveSmallIntegerField(default=0)
    playback_url = models.CharField(null=True, blank=True, max_length=1024)
    dash_url = models.CharField(null=True, blank=True, max_length=1024)
    thumbnails = ArrayField(
        models.CharField(max_length=1024), default=list, blank=True, null=True
    )
    preview_thumbnail_url = models.CharField(max_length=1024, null=True, blank=True)
    cover_thumbnail_url = models.CharField(max_length=1024, null=True, blank=True)
    job_id = models.UUIDField(null=True, blank=True)
    transmux_only = models.BooleanField(null=True, blank=True)
    duration = models.DurationField(null=True, blank=True)
    transcoding_submission_time = models.DateTimeField(null=True, blank=True)
    transcoding_start_time = models.DateTimeField(null=True, blank=True)
    transcoding_end_time = models.DateTimeField(null=True, blank=True)
    meta_data = models.JSONField(
        help_text=_(
            "Additional data will be stored.For ex: transcoded with backup asset path, status"
        ),
        null=True,
        blank=True,
    )
    generate_subtitle = models.BooleanField(
        default=False,
        null=True,
        blank=True,
        help_text=_("Enable to generate subtitle for the video."),
    )
    use_global_player_preferences = models.BooleanField(default=True)
    embed_preset = TenantForeignKey(
        EmbedPreset,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="videos",
    )

    start_time_seconds = models.PositiveSmallIntegerField(
        _("Start time in seconds"), default=0, null=True, blank=True
    )
    output_urls = models.JSONField(
        null=True,
        blank=True,
        default=dict,
        help_text=_("Stores HLS and DASH URLs for different codecs"),
    )

    def __str__(self):
        return self.asset.title

    def update_status(self, status):
        self.status = status
        self.save(update_fields=["status"])

    def get_playback_url(self):
        return f"{self.organization.cdn_url}{self.playback_url}"

    def get_dash_url(self):
        return f"{self.organization.cdn_url}{self.dash_url}"

    def get_cover_thumbnail_url(self):
        if self.cover_thumbnail_url:
            return f"{self.organization.cdn_url}{self.cover_thumbnail_url}"
        if self.thumbnails:
            return f"{self.organization.cdn_url}{self.thumbnails[0]}"
        return

    def get_thumbnail_urls(self):
        if self.thumbnails:
            return [
                f"{self.organization.cdn_url}{thumbnail}"
                for thumbnail in self.thumbnails
            ]

    def get_embed_url(self):
        access_token = get_or_create_access_token_without_validity(asset=self.asset)
        return f"/embed/{self.organization.uuid}/{self.asset.uuid}/?access_token={access_token.uuid}"  # type: ignore

    def should_generate_subtitle(self):
        from app.models.track import Track

        return (
            self.generate_subtitle
            and not self.tracks.filter(
                subtitle_type=Track.SubtitleType.AUTO_GENERATED
            ).exists()
        )

    def enable_subtitle_generation(self):
        if self.generate_subtitle:
            return
        self.generate_subtitle = True
        self.save(update_fields=["generate_subtitle"])

    def enable_drm_protection(self):
        self.content_protection_type = Video.ContentProtectionType.DRM
        self.enable_drm = True
        self.save(update_fields=["content_protection_type", "enable_drm"])

    @property
    def is_aes_encrypted(self):
        return self.content_protection_type in [
            Video.ContentProtectionType.AES,
            Video.ContentProtectionType.AES_WITH_SIGNED_URL,
        ]

    @property
    def is_drm_encrypted(self):
        return self.content_protection_type == Video.ContentProtectionType.DRM

    def save(self, *args, **kwargs):
        return super().save(*args, **kwargs)

    def set_output_url(self, codec, hls_url, dash_url):
        if not self.output_urls:
            self.output_urls = {}

        self.output_urls[codec] = {
            "hls_url": hls_url,
            "dash_url": dash_url,
        }

    def update_output_urls(self, video_outputs):
        from app.models.video_trim import OutputType

        self.output_urls = {}

        outputs_by_codec = {}
        for output in video_outputs:
            codec = output.codec
            if codec not in outputs_by_codec:
                outputs_by_codec[codec] = {}

            relative_url = output.url

            if output.output_type in [OutputType.HLS, OutputType.TRIMMED_HLS]:
                if (
                    output.output_type == OutputType.TRIMMED_HLS
                    or "hls_url" not in outputs_by_codec[codec]
                ):
                    outputs_by_codec[codec]["hls_url"] = relative_url

            elif output.output_type in [OutputType.DASH, OutputType.TRIMMED_DASH]:
                if (
                    output.output_type == OutputType.TRIMMED_DASH
                    or "dash_url" not in outputs_by_codec[codec]
                ):
                    outputs_by_codec[codec]["dash_url"] = relative_url

        for codec, urls in outputs_by_codec.items():
            self.output_urls[codec] = urls

    def update_primary_playback_urls(self, video_outputs):
        from app.models.video_trim import OutputType

        h264_hls_url = None
        h264_dash_url = None

        for output in video_outputs:
            if output.codec == "h264":
                if output.output_type in [OutputType.HLS, OutputType.TRIMMED_HLS]:
                    if output.output_type == OutputType.TRIMMED_HLS or not h264_hls_url:
                        h264_hls_url = output.url
                elif output.output_type in [OutputType.DASH, OutputType.TRIMMED_DASH]:
                    if (
                        output.output_type == OutputType.TRIMMED_DASH
                        or not h264_dash_url
                    ):
                        h264_dash_url = output.url

        if h264_hls_url:
            self.playback_url = h264_hls_url
        if h264_dash_url:
            self.dash_url = h264_dash_url

    class Meta(VideoProcessingOptions.Meta):
        constraints = VideoProcessingOptions.Meta.constraints + [
            models.UniqueConstraint(
                fields=["organization", "asset"],
                name="unique_%(class)s_organization_asset",
            )
        ]


class VideoInput(OrganizationThroughModel):
    url = models.CharField(max_length=4096)
    video = TenantForeignKey(Video, on_delete=models.CASCADE, related_name="inputs")

    def get_input_url(self):
        if self.url.startswith("http"):
            return self.url

        if self.organization.uses_s3_storage:
            return cloudfront.generate_presigned_url(self.organization, self.url)
        return generate_presigned_url(self.organization, self.url)

    def change_url(self, url):
        self.url = url
        self.save(update_fields=["url"])

    def is_video_ready_to_download(self):
        return (
            self.video.status != Video.Status.UPLOADING
            and self.video.status != Video.Status.INPUT_READ_ERROR
        )

    def get_download_filename(self):
        file_extension = os.path.splitext(self.url)[1]
        download_as = self.video.asset.title

        if not download_as.lower().endswith(file_extension.lower()):
            download_as += file_extension

        return download_as


class VideoChapter(OrganizationThroughModel):
    label = models.CharField(
        _("Chapter Label"),
        max_length=50,
        null=True,
        blank=True,
    )
    start_time = models.DurationField(
        _("Chapter Starting time"),
        null=True,
        blank=True,
    )
    video = TenantForeignKey(Video, on_delete=models.CASCADE, related_name="chapters")

    def get_start_time_in_seconds(self):
        return self.start_time.seconds
