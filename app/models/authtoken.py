from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from knox import crypto
from knox.models import AbstractAuthToken
from knox.settings import CONSTANTS, knox_settings

from app.db.constraints import CompositePrimaryKeyConstraint, DistributeConstraint

from .base import OrganizationManager, OrganizationModel


class AuthTokenManager(OrganizationManager):
    def create(
        self,
        user,
        organization,
        expiry=knox_settings.TOKEN_TTL,
        prefix=knox_settings.TOKEN_PREFIX,
    ):
        token = prefix + crypto.create_token_string()
        digest = crypto.hash_token(token)
        if expiry is not None:
            expiry = timezone.now() + expiry
        instance = super().create(
            token_key=token[: CONSTANTS.TOKEN_KEY_LENGTH],
            digest=digest,
            organization=organization,
            user=user,
            expiry=expiry,
        )
        return instance, token


class AuthToken(OrganizationModel, AbstractAuthToken):
    objects = AuthTokenManager()

    class Meta(OrganizationModel.Meta):
        verbose_name = _("AuthToken")
        verbose_name_plural = _("AuthTokens")
        constraints: list[object] = [
            CompositePrimaryKeyConstraint(
                fields=["organization", "digest"],
                name="%(app_label)s_%(class)s_pkey",
            ),
            DistributeConstraint(
                name="%(app_label)s_%(class)s_distribute",
                distribution_column="organization_id",
            ),
        ]
