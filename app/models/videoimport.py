from django.db import models
from django_multitenant.fields import Tenant<PERSON><PERSON><PERSON><PERSON><PERSON>
from mptt.models import MPTTModel

from . import TenantTreeForeignKey, Video
from .base import OrganizationModel


class ImportedFolder(MPTTModel, OrganizationModel):
    name = models.CharField(max_length=256)
    parent = TenantTreeForeignKey(
        "self", null=True, blank=True, on_delete=models.CASCADE, related_name="children"
    )
    details = models.JSONField(null=True)

    class Meta(OrganizationModel.Meta):
        constraints = OrganizationModel.Meta.constraints

    class MPTTMeta:
        order_insertion_by = ["name"]

    @property
    def path(self):
        if self.parent:
            return self.parent.path + "/" + self.name
        return self.name


class ImportedVideo(OrganizationModel):
    class Source(models.IntegerChoices):
        VIMEO = 0
        MUX = 1
        GUMLET = 2
        VDOCIPHER = 3
        APPSQUADZ = 4
        JWPLAYER = 5
        TEACHABLE = 6

    folder = TenantForeignKey(ImportedFolder, null=True, on_delete=models.SET_NULL)
    source = models.PositiveSmallIntegerField(
        choices=Source.choices, default=Source.VIMEO, db_index=True
    )
    name = models.CharField(max_length=1024)
    uri = models.URLField(max_length=4096)
    details = models.JSONField(null=True)
    video = TenantForeignKey(Video, null=True, on_delete=models.SET_NULL)

    @property
    def folder_path(self):
        if self.folder:
            return self.folder.path + "/" + self.name
        return self.name

    @property
    def owner(self):
        return self.details["user"]["name"]


class ImportedResolution(OrganizationModel):
    class Status(models.IntegerChoices):
        QUEUED = 0
        UPLOADING = 1
        COMPLETED = 2
        ERROR = 3

    video = TenantForeignKey(
        ImportedVideo, on_delete=models.CASCADE, related_name="resolutions"
    )
    status = models.PositiveSmallIntegerField(
        choices=Status.choices, default=Status.QUEUED, db_index=True
    )
    resolution = models.CharField(max_length=12)

    def update_status(self, status):
        self.status = status
        self.save(update_fields=["status"])

    @property
    def output_path(self):
        if self.video.get_source_display == "Vimeo":
            return f"video-import-data/{self.video.owner}/{self.video.folder_path}/{self.resolution}.mp4"

        return f"video-import-data/{self.video.id}/{self.resolution}.mp4"
