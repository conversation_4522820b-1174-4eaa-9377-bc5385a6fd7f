from django.contrib.postgres.fields import Array<PERSON>ield
from django.core.validators import URLValidator
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_multitenant.fields import Tenant<PERSON>oreignKey

from app.models import (
    Asset,
    OrganizationModel,
    OrganizationThroughModel,
    TenantOneToOneField,
    Video,
)

from .player import BasePlayerPreferences, EmbedPreset


class LiveStream(OrganizationModel, BasePlayerPreferences):
    class Status(models.IntegerChoices):
        NOT_STARTED = 0
        STREAMING = 1
        RECORDING = 2
        COMPLETED = 3
        ERROR = 4
        DISCONNECTED = 5
        STOPPED = 6

    class ServerStatus(models.IntegerChoices):
        NOT_CREATED = 0
        CREATING = 1
        CREATED = 2
        PROXY_SETUP = 3
        AVAILABLE = 4
        DESTROYED = 5
        DELETING = 6

    class Resolutions(models.IntegerChoices):
        _240p = 0, _("240p")
        _360p = 1, _("360p")
        _480p = 2, _("480p")
        _720p = 3, _("720p")
        _1080p = 4, _("1080p")

    class Latency(models.IntegerChoices):
        NORMAL_LATENCY = 0
        LOW_LATENCY = 1

    class TerminationCause(models.IntegerChoices):
        USER_INITIATED = 0
        SCHEDULED_TERMINATION = 1

    rtmp_url = models.URLField(
        _("RTMP URL"), validators=[URLValidator(schemes=["rtmp", "rtmps"])]
    )
    stream_key = models.CharField(_("Stream Key"), max_length=64, null=True, blank=True)
    hls_url_path = models.CharField(_("HLS URL path"), max_length=2048)
    status = models.PositiveSmallIntegerField(
        _("Status"), choices=Status.choices, default=Status.NOT_STARTED, db_index=True
    )
    server_ip = models.GenericIPAddressField(
        _("Streaming Server IP Address"), null=True, blank=True
    )
    server_private_ip = models.GenericIPAddressField(
        _("Streaming Server's Private IP Address"), null=True, blank=True
    )
    server_status = models.PositiveSmallIntegerField(
        _("Status of Live stream server"),
        choices=ServerStatus.choices,
        default=ServerStatus.NOT_CREATED,
    )
    server_id = models.CharField(
        _("Unique identifier of the server"), null=True, blank=True, max_length=125
    )
    server_data = models.JSONField(_("Streaming Server details"), null=True, blank=True)
    start = models.DateTimeField(_("Start Date"), null=True, blank=True)
    end = models.DateTimeField(_("End time"), null=True, blank=True)
    asset = TenantOneToOneField(
        Asset, on_delete=models.CASCADE, related_name="live_stream"
    )
    enable_drm = models.BooleanField(_("Enable DRM for live stream"), default=False)
    transcode_recorded_video = models.BooleanField(default=True)
    chat_room_id = models.UUIDField(_("Chat Room Id"), null=True, blank=True)
    chat_transcript_url = models.CharField(
        _("Chat Transcript Url"), null=True, blank=True, max_length=4096
    )
    resolutions = ArrayField(
        models.PositiveSmallIntegerField(choices=Resolutions.choices), default=list
    )
    server_termination_task_id = models.UUIDField(
        _("Server Termination Task Id"), null=True, blank=True
    )

    termination_cause = models.PositiveSmallIntegerField(
        _("Termination Cause"), choices=TerminationCause.choices, null=True, blank=True
    )

    user_details = models.JSONField(
        _("User Details"), default=dict, null=True, blank=True
    )

    upload_recording_task_id = models.UUIDField(
        _("Upload Recording Task Id"), null=True, blank=True
    )
    enable_drm_for_recording = models.BooleanField(
        _("Enable DRM for live stream recording"), default=True
    )

    store_recorded_video = models.BooleanField(
        _("Store recording in bucket"),
        default=True,
        help_text=_(
            "If enabled, the live stream recording will be stored in the bucket for later use"
        ),
        null=True,
        blank=True,
    )

    has_moved = models.BooleanField(_("Moved to Asset List"), default=False)
    enable_llhls = models.BooleanField(
        _("Enable LLHLS for live stream"), default=False, null=True, blank=True
    )
    latency = models.PositiveSmallIntegerField(
        _("Streaming Latency"),
        choices=Latency.choices,
        default=Latency.NORMAL_LATENCY,
        null=True,
        blank=True,
    )

    use_global_player_preferences = models.BooleanField(default=True)
    embed_preset = TenantForeignKey(
        EmbedPreset,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="live_streams",
    )
    meta_data = models.JSONField(
        help_text=_(
            "Additional data will be stored.For ex: upload recording task server ip"
        ),
        null=True,
        blank=True,
    )
    scheduled_trim_data = models.JSONField(
        null=True,
        blank=True,
        help_text="Stores scheduled trim details such as start_time, end_time, and user info",
    )

    @property
    def get_hls_url(self):
        if (
            hasattr(self, "live_stream_usage")
            and self.live_stream_usage.server_provider
            == LiveStreamUsage.ServerProvider.DIGITALOCEAN
        ):
            hls_path = self.hls_url_path.replace("live", "live2")
            return f"{self.organization.cdn_url}{hls_path}"
        return f"{self.organization.cdn_url}{self.hls_url_path}"

    @property
    def get_hls_url_using_openresty(self):
        hls_path = self.hls_url_path.replace("live", "live2")
        return f"{self.organization.cdn_url}{hls_path}"

    @property
    def get_dash_url(self):
        if (
            hasattr(self, "live_stream_usage")
            and self.live_stream_usage.server_provider
            == LiveStreamUsage.ServerProvider.DIGITALOCEAN
        ):
            dash_path = self.hls_url_path.replace("m3u8", "mpd").replace(
                "live", "live2"
            )
            return f"{self.organization.cdn_url}{dash_path}"
        dash_path = self.hls_url_path.replace("m3u8", "mpd")
        return f"{self.organization.cdn_url}{dash_path}"

    @property
    def get_dash_url_using_openresty(self):
        dash_path = self.hls_url_path.replace("m3u8", "mpd").replace("live", "live2")
        return f"{self.organization.cdn_url}{dash_path}"

    def update_chat_room_id(self, chat_room_id):
        self.chat_room_id = chat_room_id
        self.save()

    @property
    def is_recording_transcoded(self):
        return (
            hasattr(self.asset, "video")
            and self.asset.video.status == Video.Status.COMPLETED
        )

    @property
    def is_active(self):
        return self.status in [
            self.Status.STREAMING,
            self.Status.RECORDING,
        ]


class LiveStreamEvent(OrganizationThroughModel):
    class Type(models.IntegerChoices):
        CREATED = 0
        ON_PUBISH = 1
        ON_PUBISH_DONE = 2
        STOPPED = 3
        RECORDING = 4
        COMPLETED = 5
        ERROR = 6

    type = models.PositiveSmallIntegerField(_("Event Type"), choices=Type.choices)
    live_stream = TenantForeignKey(
        LiveStream, on_delete=models.CASCADE, related_name="events"
    )
    data = models.JSONField(_("Event data"), null=True, blank=True)


class LiveStreamUsage(OrganizationModel):
    class ServerProvider(models.IntegerChoices):
        DIGITALOCEAN = 1
        AWS = 2
        LINODE = 3

    server_ip = models.GenericIPAddressField(
        _("Streaming Server IP Address"), null=True
    )
    server_provider = models.PositiveSmallIntegerField(
        _("Status"),
        choices=ServerProvider.choices,
        default=ServerProvider.DIGITALOCEAN,
        db_index=True,
    )
    start_time = models.DateTimeField(_("Start time"), null=True, blank=True)
    end_time = models.DateTimeField(_("End time"), null=True, blank=True)
    cost_per_hour = models.DecimalField(
        _("Server cost (per hour in rupees)"),
        max_digits=3,
        decimal_places=2,
        null=True,
        blank=True,
    )
    live_stream = TenantOneToOneField(
        LiveStream,
        on_delete=models.SET_NULL,
        related_name="live_stream_usage",
        null=True,
    )
