from django.contrib.postgres.fields import ArrayField
from django.db import models
from django.db.models.signals import pre_save
from django.dispatch import receiver
from django.utils.translation import gettext_lazy as _

from app.models import CreatedByField, OrganizationModel
from app.tasks import send_data_to_webhook_task
from app.utils.uuid import populate_uuid


class TranscodingJob(OrganizationModel):
    class Resolutions(models.IntegerChoices):
        _240p = 0, _("240p")
        _360p = 1, _("360p")
        _480p = 2, _("480p")
        _720p = 3, _("720p")
        _1080p = 4, _("1080p")

    class Status(models.IntegerChoices):
        QUEUED = 1
        TRANSCODING = 2
        COMPLETED = 3
        ERROR = 4

    uuid = models.CharField(max_length=36, editable=False, null=False)  # type: ignore
    resolutions = ArrayField(
        models.PositiveSmallIntegerField(choices=Resolutions.choices), default=list
    )
    video_duration = models.DurationField(null=True, blank=True)
    transcoding_duration = models.DurationField(null=True, blank=True)
    status = models.PositiveSmallIntegerField(
        choices=Status.choices, default=Status.QUEUED, db_index=True
    )
    output_path = models.CharField(max_length=4096)
    input_url = models.CharField(max_length=4096)
    job_id = models.UUIDField(null=True, blank=True)
    created_by = CreatedByField()
    start_time = models.DateTimeField(_("start"), null=True, blank=True)
    end_time = models.DateTimeField(_("end"), null=True, blank=True)
    error_message = models.TextField(_("Error message"), null=True, blank=True)

    def notify_webhook(self):
        from app.models import Webhook

        if not Webhook.objects.exists():
            return

        for webhook in Webhook.objects.all():
            send_data_to_webhook_task.delay(
                webhook.url,
                self.organization.uuid,
                job_uuid=self.uuid,
                token=webhook.secret_token,
            )


@receiver(pre_save, sender=TranscodingJob)
def populate_transcoding_job_uuid(sender, instance, **kwargs):
    populate_uuid(
        instance,
        length=11,
        alphabet="ABCDEFGHJKMNPQRSTUXYZabcdefghjkmnpqrstuxyz23456789",
    )
