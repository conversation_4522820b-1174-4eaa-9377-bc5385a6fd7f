from django.db import models
from django.utils.translation import gettext_lazy as _

from .base import OrganizationModel


class BandwidthUsage(OrganizationModel):
    class TimeFrames(models.IntegerChoices):
        DAILY = 1
        MONTHLY = 2

    time_frame = models.PositiveSmallIntegerField(
        choices=TimeFrames.choices, default=TimeFrames.DAILY, db_index=True
    )
    date = models.DateField()
    bandwidth_used = models.PositiveBigIntegerField(
        _("Bandwidth used in bytes"), null=True, default=0
    )

    class Meta(OrganizationModel.Meta):
        constraints = OrganizationModel.Meta.constraints + [
            models.UniqueConstraint(
                fields=["organization", "date", "time_frame"],
                name="unique_%(class)s_organization_bandwidth_used",
            )
        ]
