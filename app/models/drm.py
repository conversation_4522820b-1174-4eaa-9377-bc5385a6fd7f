import base64
import json

from django.db import models
from django.utils.translation import gettext_lazy as _
from django_multitenant.fields import TenantForeignKey

from app.models import Asset, OrganizationBaseModel, OrganizationModel

from .fields import TenantOneToOneField


class EncryptionKey(OrganizationModel):
    # Disable automatic uuid field from OrganizationModel as we don't need it and is redundant
    uuid = None
    content_id = models.UUIDField(db_index=True, editable=False)
    asset = TenantOneToOneField(
        Asset, on_delete=models.SET_NULL, null=True, related_name="encryption_key"
    )
    aes_encryption_key = models.UUIDField(null=True)
    widevine_encryption_keys = models.JSONField(null=True)
    fairplay_encryption_key_data = models.JSONField(null=True)

    class Meta:
        ordering = ("-created",)
        constraints = OrganizationBaseModel.Meta.constraints + [
            models.UniqueConstraint(
                fields=["organization", "asset"],
                name="unique_%(class)s_organization_asset",
            )
        ]

    @property
    def widevine_content_keys(self):
        if self.widevine_encryption_keys:
            return [
                {
                    "track_type": data["type"],
                    "key_id": data["key_id"],
                    "key": data["key"],
                }
                for data in json.loads(self.widevine_encryption_keys)["tracks"]
            ]
        return []

    @property
    def widevine_content_key_response(self):
        # This is the format which will be used by Shaka Packager
        if self.widevine_encryption_keys:
            return {
                "response": base64.b64encode(
                    self.widevine_encryption_keys.encode()
                ).decode()
            }
        return None


class DRMLicenseLog(OrganizationModel):
    class DRMType(models.IntegerChoices):
        WIDEVINE = 1
        FAIRPLAY = 2

    class Device(models.IntegerChoices):
        PC = 1
        MOBILE_BROWSER = 2
        MOBILE_SDK = 3
        OTHER = 4

    class DRMProvider(models.IntegerChoices):
        TESTPRESS = 1
        VDOCIPHER = 2

    class LicenseType(models.IntegerChoices):
        STREAMING = 1
        DOWNLOAD = 2

    # Disable automatic uuid field from OrganizationModel as we don't need it and is redundant
    uuid = None
    drm_type = models.PositiveSmallIntegerField(choices=DRMType.choices, db_index=True)
    device = models.PositiveSmallIntegerField(choices=Device.choices)
    license_type = models.PositiveSmallIntegerField(
        choices=LicenseType.choices, default=LicenseType.STREAMING
    )
    ip_address = models.GenericIPAddressField(null=True, unpack_ipv4=True)
    user_agent = models.CharField(max_length=1024, editable=False, null=True)
    asset = TenantForeignKey(Asset, on_delete=models.CASCADE, null=True)
    content_id = models.UUIDField(db_index=True, editable=False)
    provider = models.PositiveSmallIntegerField(choices=DRMProvider.choices)
    meta_data = models.JSONField(
        help_text=_("Additional data will be stored. For ex: Asset path, uuid"),
        null=True,
    )

    class Meta:
        ordering = ("-created",)
        constraints = OrganizationBaseModel.Meta.constraints
