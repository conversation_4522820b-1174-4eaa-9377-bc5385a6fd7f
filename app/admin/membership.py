from django.contrib import admin
from safedelete.admin import SafeDeleteAdmin, highlight_deleted

from app.models import Membership


class MembershipAdmin(SafeDeleteAdmin):
    list_display = (
        highlight_deleted,
        "highlight_deleted_field",
    ) + SafeDeleteAdmin.list_display  # type: ignore

    field_to_highlight = "id"  # type: ignore
    search_fields = ("email", "organization__uuid")


# MembershipAdmin.highlight_deleted_field.short_description = (
#    MembershipAdmin.field_to_highlight
# )

admin.site.register(Membership, MembershipAdmin)
