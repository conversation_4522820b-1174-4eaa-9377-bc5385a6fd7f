from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from utm_tracker.models import LeadSource

from app.models import Organization, Video


class OrganizationAdmin(admin.ModelAdmin):
    exclude = [
        "storage_vendor",
        "storage_access_key_id",
        "storage_secret_access_key",
        "cdn_secret_access_key",
        "cdn_private_key",
        "cdn_access_key_id",
    ]
    list_display = (
        "uuid",
        "name",
        "user_name",
        "video_count",
        "lead_count",
        "created_by",
        "phone_number",
        "created",
        "bucket_name",
    )
    search_fields = ("name", "uuid")
    readonly_fields = ("uuid", "video_count", "lead_count", "created_by")
    list_display_links = ("video_count", "uuid")

    @admin.display(description="Phone Number")  # type: ignore
    def phone_number(self, obj):
        return obj.created_by.phone_number

    @admin.display(description="User Name")  # type: ignore
    def user_name(self, obj):
        return obj.created_by.name

    @admin.display(
        description="Video Count",
    )  # type: ignore
    def video_count(self, obj):
        count = Video.objects.filter(organization=obj).count()
        url = reverse("admin:app_video_changelist") + f"?q={obj.uuid}"
        return format_html('<a href="{}">{}</a>', url, count)

    @admin.display(
        description="Leads",
    )  # type: ignore
    def lead_count(self, obj):
        count = LeadSource.objects.filter(
            user__current_organization_uuid=obj.uuid
        ).count()
        url = reverse("admin:utm_tracker_leadsource_changelist") + f"?q={obj.uuid}"
        return format_html('<a href="{}">{}</a>', url, count)


admin.site.register(Organization, OrganizationAdmin)
