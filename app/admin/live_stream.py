from django.contrib import admin

from app.filters import Custom<PERSON>onthFilter, CustomYearFilter
from app.models import LiveStreamUsage


class LiveStreamUsageAdmin(admin.ModelAdmin):
    list_display = (
        "server_ip_address",
        "organization",
        "start_time",
        "end_time",
        "time_used",
    )
    list_filter = (
        <PERSON><PERSON><PERSON><PERSON>ilter,
        CustomMonthFilter,
        "organization",
    )

    search_fields = ("organization__name", "organization__uuid")

    @admin.display(description="Server Ip")  # type: ignore
    def server_ip_address(self, obj):
        if obj.live_stream:
            return obj.live_stream.server_ip

    @admin.display(description="Time Used")  # type: ignore
    def time_used(self, obj):
        if obj.start_time and obj.end_time:
            time_used = obj.end_time - obj.start_time
            return str(time_used)


admin.site.register(LiveStreamUsage, LiveStreamUsageAdmin)
