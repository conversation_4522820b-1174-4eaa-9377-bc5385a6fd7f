from django.contrib import admin

from app.models import Track


class TrackAdmin(admin.ModelAdmin):
    list_display = (
        "type",
        "organization",
        "language",
        "url",
        "bytes",
    )
    search_fields = (
        "language",
        "name",
        "video__asset__uuid",
        "organization__name",
        "organization__uuid",
    )
    readonly_fields = (
        "video",
        "organization",
    )


admin.site.register(Track, TrackAdmin)
