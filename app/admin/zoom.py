from django.contrib import admin

from app.filters import Custom<PERSON>onthFilter, CustomYearFilter
from app.models.zoom import ZoomAccount, ZoomRecording, ZoomWebhookLog


@admin.register(ZoomWebhookLog)
class ZoomWebhookLogAdmin(admin.ModelAdmin):
    list_display = (
        "event_type",
        "organization",
        "received_at",
        "status",
    )
    list_filter = (
        CustomYearFilter,
        CustomMonthFilter,
        "event_type",
        "status",
        "organization",
    )
    search_fields = ("organization__name", "error_message")
    readonly_fields = (
        "organization",
        "received_at",
    )
    ordering = ("-received_at",)
    list_select_related = ("organization",)


@admin.register(ZoomRecording)
class ZoomRecordingAdmin(admin.ModelAdmin):
    list_display = (
        "topic",
        "organization",
        "meeting_uuid",
        "recording_uuid",
        "start_time",
        "status",
    )
    list_filter = (CustomYearFilter, CustomMonthFilter, "status", "organization")
    search_fields = (
        "organization__name",
        "meeting_uuid",
        "recording_uuid",
        "topic",
        "zoom_user_id",
    )
    readonly_fields = (
        "organization",
        "imported_at",
    )
    ordering = ("-imported_at",)
    list_select_related = ("organization",)


@admin.register(ZoomAccount)
class ZoomAccountAdmin(admin.ModelAdmin):
    list_display = (
        "user",
        "organization",
        "zoom_user_id",
        "status",
        "disconnected_at",
    )
    list_filter = (
        CustomYearFilter,
        CustomMonthFilter,
        "status",
        "enable_drm",
        "organization",
    )
    search_fields = (
        "organization__name",
        "user__email",
        "zoom_user_id",
    )
    readonly_fields = ("organization", "disconnected_at", "import_destination_folder")
    ordering = ("-created",)
    list_select_related = ("organization", "user")
    exclude = ["import_destination"]

    def import_destination_folder(self, obj):
        if obj.import_destination:
            return f"{obj.import_destination.title} ({obj.import_destination.uuid})"
        return "-"

    import_destination_folder.short_description = "Import Destination"
