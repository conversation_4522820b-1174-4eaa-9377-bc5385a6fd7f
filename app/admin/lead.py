from django.contrib import admin
from utm_tracker.admin import LeadSourceAdmin
from utm_tracker.models import LeadSource

from app.filters.admin import KeywordFilter
from app.models import Organization


class LeadSourceAdmin(LeadSourceAdmin):
    raw_id_fields = ("user",)
    list_display = ("user", "organization", "keyword", "referrer", "timestamp")
    search_fields = ("term", "content", "user__current_organization_uuid")
    list_filter = (KeywordFilter, "timestamp")
    readonly_fields = ("created_at", "timestamp")

    @admin.display(description="Organization")  # type: ignore
    def organization(self, obj):
        return Organization.objects.get(created_by=obj.user)

    @admin.display(description="Keyword")  # type: ignore
    def keyword(self, obj):
        if obj.custom_tags.get("keyword"):
            return obj.custom_tags.get("keyword")

    @admin.display(description="campaign")  # type: ignore
    def referrer(self, obj):
        if obj.custom_tags.get("referrer"):
            return obj.campaign + " - " + obj.custom_tags.get("referrer")
        return obj.campaign


admin.site.unregister(LeadSource)
admin.site.register(LeadSource, LeadSourceAdmin)
