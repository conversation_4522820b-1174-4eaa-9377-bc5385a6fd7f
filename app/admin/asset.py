from django.contrib import admin

from app.filters import Custom<PERSON><PERSON>h<PERSON>ilter, CustomYearFilter
from app.models import Asset


class AssetAdmin(admin.ModelAdmin):
    list_display = (
        "title",
        "organization",
        "uuid",
        "created",
        "modified",
    )
    list_filter = (Custom<PERSON>earFilter, CustomMonthFilter, "organization")
    search_fields = ("organization__name", "title", "created__date", "uuid")
    readonly_fields = ("organization", "created_by", "modified")

    def save_related(self, request, form, formsets, change):
        obj = form.instance
        defaults = {"organization_id": obj.organization_id}
        obj.tags.set(
            form.cleaned_data["tags"], tag_kwargs=defaults, through_defaults=defaults
        )
        for formset in formsets:
            self.save_formset(request, form, formset, change=change)

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "parent":
            current_asset_id = request.resolver_match.kwargs["object_id"]
            current_asset = Asset.objects.get(pk=current_asset_id)
            kwargs["queryset"] = Asset.objects.filter(
                type=Asset.Type.FOLDER, organization=current_asset.organization
            )
        return super().formfield_for_foreignkey(db_field, request, **kwargs)


admin.site.register(Asset, AssetAdmin)
