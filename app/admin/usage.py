from django.contrib import admin
from import_export import resources
from import_export.admin import ExportActionModelAdmin
from import_export.fields import Field

from app.filters import Custom<PERSON>onthFilter, CustomYearFilter
from app.models import AssetUsage
from app.utils.datetime import format_seconds_to_ms
from app.utils.parsers import bytes_to_human_readable


class UsageResource(resources.ModelResource):
    total_usage = Field(attribute="total_usage", column_name="Total Usage")

    def dehydrate_time_frame(self, instance):
        return instance.get_time_frame_display()

    def dehydrate_total_usage(self, instance):
        return bytes_to_human_readable(
            instance.active_storage_bytes + instance.deleted_storage_bytes
        )

    def dehydrate_active_storage_bytes(self, instance):
        return bytes_to_human_readable(instance.active_storage_bytes)

    def dehydrate_deleted_storage_bytes(self, instance):
        return bytes_to_human_readable(instance.deleted_storage_bytes)

    def dehydrate_bandwidth_used(self, instance):
        return bytes_to_human_readable(instance.bandwidth_used)

    def dehydrate_live_stream_usage(self, instance):
        return format_seconds_to_ms(instance.live_stream_usage)

    class Meta:
        model = AssetUsage
        fields = (
            "total_usage",
            "organization__name",
            "time_frame",
            "date",
            "bandwidth_used",
            "live_stream_usage",
            "active_storage_bytes",
            "deleted_storage_bytes",
            "subtitle_generation_minutes",
        )


@admin.register(AssetUsage)
class AssetUsageAdmin(ExportActionModelAdmin):
    resource_class = UsageResource
    list_display = (
        "organization",
        "time_frame",
        "date",
        "bandwidth_used_human_readable",
        "live_stream_usage_human_readable",
        "active_storage_used_human_readable",
        "deleted_storage_used_human_readable",
        "total_usage",
        "subtitle_generation_minutes",
    )
    readonly_fields = (
        "organization",
        "time_frame",
        "date",
        "bandwidth_used_human_readable",
        "bandwidth_used",
        "live_stream_usage",
        "active_storage_used_human_readable",
        "deleted_storage_used_human_readable",
        "total_usage",
        "subtitle_generation_minutes",
    )
    list_filter = (
        CustomYearFilter,
        CustomMonthFilter,
        "time_frame",
        "organization",
    )

    search_fields = ("organization__name",)
    ordering = ("organization__name", "time_frame", "date")

    @admin.display(description="Active Storage", ordering="active_storage_bytes")  # type: ignore
    def active_storage_used_human_readable(self, obj):
        return bytes_to_human_readable(obj.active_storage_bytes)

    @admin.display(description="Deleted Storage", ordering="deleted_storage_bytes")  # type: ignore
    def deleted_storage_used_human_readable(self, obj):
        return bytes_to_human_readable(obj.deleted_storage_bytes)

    @admin.display(description="Total Storage Used", ordering="active_storage_bytes")  # type: ignore
    def total_usage(self, obj):
        return bytes_to_human_readable(
            obj.active_storage_bytes + obj.deleted_storage_bytes
        )

    @admin.display(description="Bandwidth Used", ordering="bandwidth_used")  # type: ignore
    def bandwidth_used_human_readable(self, obj):
        return bytes_to_human_readable(obj.bandwidth_used)

    @admin.display(description="Live Stream Usage", ordering="live_stream_usage")  # type: ignore
    def live_stream_usage_human_readable(self, obj):
        if obj.organization.enable_hd_live_streaming:
            return format_seconds_to_ms(obj.live_stream_usage) + " HD"
        return format_seconds_to_ms(obj.live_stream_usage)
