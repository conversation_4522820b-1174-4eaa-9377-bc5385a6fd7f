from django.conf import settings
from django.contrib import admin
from django.utils.html import format_html

from app.filters import Custom<PERSON><PERSON>h<PERSON>ilter, CustomYearFilter
from app.models import Video, VideoInput


class VideoAdmin(admin.ModelAdmin):
    list_display = (
        "asset",
        "preview",
        "organization",
        "created",
        "duration",
        "job_id",
    )
    list_filter = (<PERSON><PERSON><PERSON><PERSON>ilter, CustomMonthFilter, "organization")
    search_fields = (
        "organization__name",
        "organization__uuid",
        "asset__title",
        "created__date",
        "asset__uuid",
        "job_id",
    )

    readonly_fields = ("organization", "asset")

    @admin.display(
        description="Action",
    )  # type: ignore
    def preview(self, obj):
        video_url = f"{settings.SITE_URL}{obj.get_embed_url()}"
        return format_html('<a href="{}" target="_blank">{}</a>', video_url, "Preview")


admin.site.register(Video, VideoAdmin)
admin.site.register(VideoInput)
