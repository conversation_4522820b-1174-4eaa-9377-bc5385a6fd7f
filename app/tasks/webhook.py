import json

import requests
import sentry_sdk
from celery import shared_task
from django.core.serializers.json import DjangoJSONEncoder
from django.shortcuts import get_object_or_404
from django_multitenant.utils import set_current_tenant, unset_current_tenant


@shared_task(
    name="send_asset_data_to_webhook",
    bind=True,
    acks_late=True,
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=20,
    retry_jitter=True,
    queue="webhook",
)
def send_data_to_webhook_task(
    self, url, org_uuid, asset_uuid=None, job_uuid=None, token=None
):
    from app.api.v1.serializers.asset import AssetSerializer
    from app.api.v1.serializers.transcoding_job import TranscodingJobSerializer
    from app.models import Asset, Organization
    from app.models.transcoding_job import TranscodingJob

    unset_current_tenant()
    organization = get_object_or_404(Organization, uuid=org_uuid)
    set_current_tenant(organization)
    if asset_uuid:
        asset = Asset.objects.get(uuid=asset_uuid)
        data = AssetSerializer(instance=asset).data
    if job_uuid:
        job = TranscodingJob.objects.get(uuid=job_uuid)
        data = TranscodingJobSerializer(instance=job).data

    headers = {"Content-Type": "application/json"}
    if token:
        headers["x-streams-token"] = token
    response = requests.request(
        "POST",
        url,
        data=json.dumps(data, cls=DjangoJSONEncoder),
        headers=headers,
    )

    if "eduonixhelp" in url:
        with sentry_sdk.push_scope() as scope:
            scope.set_extra("url", url)
            scope.set_extra("data", json.dumps(data, cls=DjangoJSONEncoder))
            scope.set_extra("response", response.status_code)
            scope.set_tag("id", data.get("id"))
            scope.set_tag("status", data.get("status"))

    sentry_sdk.capture_message("Log Eduronix Web Hook", "debug")
    response.raise_for_status()
