import sentry_sdk
from django.shortcuts import get_object_or_404

from app.domain.video import start_transcoding
from app.tasks.base import TpStreamsTask
from config.celery import app

PREFERRED_RECORDING_TYPES = [
    "shared_screen_with_speaker_view(CC)",
    "shared_screen_with_speaker_view",
    "shared_screen",
    "active_speaker",
]


class ImportZoomRecordingTask(TpStreamsTask):
    def do_run(self, *args, **kwargs):
        from app.domain.zoom import ZoomImportError, create_or_update_zoom_recording

        try:
            self.initialize(kwargs)
            recording_data = self.get_recording_data()
            if not recording_data:
                raise ZoomImportError("No recording data found")

            recording_file = self.get_preferred_recording_file(
                recording_data.get("recording_files")
            )
            if not recording_file:
                raise ZoomImportError("No recording file found")

            zoom_recording = create_or_update_zoom_recording(
                recording_data, recording_file, self.zoom_account, self.organization
            )
            self.upload_recording_to_storage(zoom_recording)
            self.transcode_zoom_recording(zoom_recording)
        except Exception as e:
            sentry_sdk.capture_exception(e)
            self.mark_as_failure(webhook_log=self.webhook_log, error_message=str(e))
            raise e

    def initialize(self, kwargs):
        self.webhook_log_id = kwargs.get("webhook_log_id", None)
        self.meeting_uuid = kwargs.get("meeting_uuid", None)
        self.zoom_user_id = kwargs.get("zoom_user_id", None)
        self.webhook_log = None

        if not any([self.webhook_log_id, self.meeting_uuid]):
            raise ValueError("Either webhook_log_id or meeting_uuid is required")

        if self.webhook_log_id:
            self._set_webhook_log()

        self.zoom_account = self._set_zoom_account()
        if not self.zoom_account:
            raise ValueError("Failed to get Zoom account")

    def _set_webhook_log(self):
        from app.models.zoom import ZoomWebhookLog

        self.webhook_log = get_object_or_404(
            ZoomWebhookLog, id=self.webhook_log_id, organization=self.organization
        )

    def _set_zoom_account(self):
        from app.domain.zoom import refresh_zoom_token
        from app.models.zoom import ZoomAccount

        self.zoom_account = get_object_or_404(
            ZoomAccount, zoom_user_id=self.zoom_user_id, organization=self.organization
        )
        if self.zoom_account.is_token_expired:
            refresh_zoom_token(self.zoom_account)

        return self.zoom_account

    def get_recording_data(self):
        from app.domain.zoom import (
            extract_recording_data_from_webhook_payload,
            fetch_zoom_recording_data_from_api,
        )

        if self.webhook_log:
            return extract_recording_data_from_webhook_payload(self.webhook_log)
        return fetch_zoom_recording_data_from_api(self.zoom_account, self.meeting_uuid)

    def get_preferred_recording_file(self, recording_files):
        for recording_type in PREFERRED_RECORDING_TYPES:
            recording_file = next(
                (
                    file
                    for file in recording_files
                    if file.get("file_type", "") == "MP4"
                    and file.get("recording_type") == recording_type
                ),
                None,
            )
            if recording_file:
                return recording_file
        return None

    def upload_recording_to_storage(self, zoom_recording):
        from app.domain.s3 import get_s3_config_for_rclone
        from app.models import Organization
        from app.utils.s3 import upload_from_url_to_s3
        from app.utils.wasabi import get_wasabi_config, upload_from_url_to_wasabi

        output_path = f"{self.organization.bucket_name}/private/{zoom_recording.asset.uuid}/video.mp4"

        try:
            if self.organization.storage_vendor == Organization.StorageProvider.AWS:
                config = get_s3_config_for_rclone(self.organization)
                upload_from_url_to_s3(zoom_recording.download_url, output_path, config)
            else:
                config = get_wasabi_config(self.organization)
                upload_from_url_to_wasabi(
                    zoom_recording.download_url, output_path, config, make_public=True
                )
        except Exception as e:
            sentry_sdk.capture_exception(e)
            self.mark_as_failure(zoom_recording, self.webhook_log, str(e))
            raise

    def transcode_zoom_recording(self, zoom_recording):
        from app.domain.video import create_video_input
        from app.domain.zoom import create_video_for_zoom_recording

        try:
            video = getattr(zoom_recording.asset, "video", None)
            if not video:
                video = create_video_for_zoom_recording(
                    zoom_recording, self.zoom_account
                )
            if not video.inputs.exists():
                create_video_input(video)
            start_transcoding(video)
            self.mark_as_success(zoom_recording, self.webhook_log)

        except Exception as e:
            sentry_sdk.capture_exception(e)
            self.mark_as_failure(zoom_recording, self.webhook_log, str(e))
            raise

    def mark_as_success(self, zoom_recording=None, webhook_log=None):
        from app.models.zoom import WebhookStatus

        if webhook_log:
            webhook_log.update_status(status=WebhookStatus.SUCCESS)

        if zoom_recording:
            zoom_recording.mark_as_imported()

    def mark_as_failure(
        self, zoom_recording=None, webhook_log=None, error_message=None
    ):
        from app.models.zoom import WebhookStatus

        if webhook_log:
            webhook_log.update_status(status=WebhookStatus.FAILED)
            webhook_log.error_message = error_message
            webhook_log.save(update_fields=["error_message"])

        if zoom_recording:
            zoom_recording.mark_as_failed(error_message=error_message)


ImportZoomRecordingTask = app.register_task(ImportZoomRecordingTask())
