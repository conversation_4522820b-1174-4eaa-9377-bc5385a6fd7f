import logging
import os
import subprocess

import requests
import sentry_sdk

from app.domain.video import start_transcoding
from app.tasks.base import TpStreamsTask
from app.utils.ffmpeg import remux_hls_stream_with_headers
from app.utils.migrator_interface import MigratorInterface
from app.utils.wasabi import copy_files_to_wasabi, get_wasabi_config
from config.celery import app

logger = logging.getLogger(__name__)


class MigrateTeachableVideoTask(MigratorInterface, TpStreamsTask):
    def do_run(self, *args, **kwargs):
        try:
            logger.info(
                "Starting Teachable video migration task",
                extra={
                    "imported_video_id": kwargs.get("imported_video_id"),
                    "content_protection_type": kwargs.get("content_protection_type"),
                },
            )

            self.initialize(kwargs)
            self._migrate_video()
            self._migrate_thumbnail()

            logger.info(
                "Successfully completed Teachable video migration",
                extra={"imported_video_id": self.imported_video.id},
            )
        except Exception as e:
            logger.error(
                "Failed to migrate Teachable video",
                extra={
                    "imported_video_id": kwargs.get("imported_video_id"),
                    "error": str(e),
                },
                exc_info=True,
            )
            sentry_sdk.capture_exception(e)
            raise

    def initialize(self, kwargs):
        try:
            from app.models import ImportedVideo, Video

            self.imported_video = ImportedVideo.objects.get(
                id=kwargs.get("imported_video_id")
            )
            self.content_protection_type = kwargs.get(
                "content_protection_type", Video.ContentProtectionType.DISABLED
            )
            self.transcoding_queue_name = kwargs.get("transcoding_queue_name")
            self.video_data = self.imported_video.details
            self.access_token = kwargs.get("access_token")

            logger.info(
                "Initialized migration task",
                extra={
                    "imported_video_id": self.imported_video.id,
                    "video_name": self.imported_video.name,
                },
            )
        except Exception as e:
            logger.error(
                "Failed to initialize migration task",
                extra={
                    "imported_video_id": kwargs.get("imported_video_id"),
                    "error": str(e),
                },
                exc_info=True,
            )
            sentry_sdk.capture_exception(e)
            raise

    def _migrate_video(self):
        try:
            logger.info(
                "Starting video migration",
                extra={"imported_video_id": self.imported_video.id},
            )

            video_path = self._extract_video_from_hls()
            output_path = self._upload_video_to_storage(video_path)
            asset = self._create_video_asset(output_path)

            logger.info(
                "Starting video transcoding",
                extra={
                    "imported_video_id": self.imported_video.id,
                    "asset_id": asset.id,
                },
            )

            start_transcoding(
                asset.video,
                extra_settings={
                    "inputs": [
                        {
                            "url": f"{self.organization.bucket_name}/{output_path}",
                            "name": self.imported_video.name,
                            "bandwidth": 0,
                        }
                    ]
                },
                transcoding_queue_name=self.transcoding_queue_name,
            )

            logger.info(
                "Successfully started video transcoding",
                extra={
                    "imported_video_id": self.imported_video.id,
                    "asset_id": asset.id,
                },
            )
        except Exception as e:
            logger.error(
                "Failed to migrate video",
                extra={"imported_video_id": self.imported_video.id, "error": str(e)},
                exc_info=True,
            )
            sentry_sdk.capture_exception(e)
            raise

    def _extract_video_from_hls(self):
        temp_dir = os.path.abspath(f"temp/{self.imported_video.id}")
        os.makedirs(temp_dir, exist_ok=True)
        output_file = os.path.join(temp_dir, "video.mp4")

        try:
            logger.info(
                "Fetching video URL",
                extra={"imported_video_id": self.imported_video.id},
            )

            new_playback_url = self._fetch_video_url()
            remux_hls_stream_with_headers(
                new_playback_url,
                output_file,
                user_agent="Mozilla/5.0",
                referer="https://play.hotmart.com/",
            )

            logger.info(
                "Successfully extracted video from HLS",
                extra={"imported_video_id": self.imported_video.id},
            )

            return output_file
        except subprocess.CalledProcessError as e:
            logger.error(
                "FFmpeg conversion failed",
                extra={"imported_video_id": self.imported_video.id, "error": str(e)},
                exc_info=True,
            )
            if os.path.exists(output_file):
                os.remove(output_file)
            sentry_sdk.capture_exception(e)
            raise Exception(f"FFmpeg conversion failed: {str(e)}")
        except Exception as e:
            logger.error(
                "Failed to download and convert HLS video",
                extra={"imported_video_id": self.imported_video.id, "error": str(e)},
                exc_info=True,
            )
            if os.path.exists(output_file):
                os.remove(output_file)
            sentry_sdk.capture_exception(e)
            raise Exception(f"Failed to download and convert HLS video: {str(e)}")

    def _fetch_video_url(self):
        from app.domain.teachable_to_wasabi_migrator import TeachableAPI

        try:
            if not hasattr(self, "access_token") or not self.access_token:
                return self.imported_video.uri

            teachable = TeachableAPI(self.access_token)
            video = teachable.get_video(
                self.video_data["course_id"],
                self.video_data["lecture_id"],
                self.video_data["video_id"],
            )
            return video.url
        except Exception:
            return self.imported_video.uri

    def _upload_video_to_storage(self, video_path):
        output_path = f"video-import-data/{self.imported_video.id}/"
        try:
            config = get_wasabi_config(self.organization)
            copy_files_to_wasabi(
                video_path,
                f"{self.organization.bucket_name}/{output_path}",
                config,
                make_public=False,
            )
            return output_path
        finally:
            if os.path.exists(video_path):
                os.remove(video_path)

    def _create_video_asset(self, input_url_path):
        return self.create_asset(
            folder_name="Imported Videos",
            input_url_path=f"{input_url_path}video.mp4",
            transmux_only=False,
        )

    def _migrate_thumbnail(self):
        if not self.video_data.get("thumbnail_url"):
            logger.info(
                "No thumbnail URL provided, skipping thumbnail migration",
                extra={"imported_video_id": self.imported_video.id},
            )
            return

        try:
            logger.info(
                "Starting thumbnail migration",
                extra={"imported_video_id": self.imported_video.id},
            )

            thumbnail_path = self._download_thumbnail()
            if thumbnail_path and self._upload_thumbnail_to_wasabi(thumbnail_path):
                self._update_video_data(thumbnail_path)

            logger.info(
                "Successfully migrated thumbnail",
                extra={"imported_video_id": self.imported_video.id},
            )
        except Exception as e:
            logger.error(
                "Failed to migrate thumbnail",
                extra={"imported_video_id": self.imported_video.id, "error": str(e)},
                exc_info=True,
            )
            sentry_sdk.capture_exception(e)
            # Don't raise here as thumbnail failure shouldn't fail the whole task

    def _download_thumbnail(self):
        try:
            logger.info(
                "Downloading thumbnail",
                extra={
                    "imported_video_id": self.imported_video.id,
                    "thumbnail_url": self.video_data["thumbnail_url"],
                },
            )

            headers = {
                "User-Agent": "Mozilla/5.0",
                "Referer": "https://play.hotmart.com/",
            }
            response = requests.get(
                self.video_data["thumbnail_url"], headers=headers, verify=True
            )
            response.raise_for_status()
            temp_dir = os.path.abspath(f"temp/{self.imported_video.id}")
            os.makedirs(temp_dir, exist_ok=True)

            thumbnail_path = os.path.join(
                temp_dir, f"{self.imported_video.name}_thumb.jpg"
            )
            with open(thumbnail_path, "wb") as f:
                f.write(response.content)

            logger.info(
                "Successfully downloaded thumbnail",
                extra={"imported_video_id": self.imported_video.id},
            )

            return thumbnail_path
        except Exception as e:
            logger.error(
                "Error downloading thumbnail",
                extra={"imported_video_id": self.imported_video.id, "error": str(e)},
                exc_info=True,
            )
            sentry_sdk.capture_exception(e)
            return None

    def _upload_thumbnail_to_wasabi(self, thumbnail_path):
        try:
            logger.info(
                "Uploading thumbnail to Wasabi",
                extra={"imported_video_id": self.imported_video.id},
            )

            config = get_wasabi_config(self.organization)
            output_path = f"{self.organization.bucket_name}/video-import-data/{self.imported_video.id}/thumbnail.jpg"
            copy_files_to_wasabi(thumbnail_path, output_path, config, make_public=False)

            logger.info(
                "Successfully uploaded thumbnail to Wasabi",
                extra={"imported_video_id": self.imported_video.id},
            )

            return True
        except Exception as e:
            logger.error(
                "Failed to upload thumbnail to Wasabi",
                extra={"imported_video_id": self.imported_video.id, "error": str(e)},
                exc_info=True,
            )
            sentry_sdk.capture_exception(e)
            return False
        finally:
            if os.path.exists(thumbnail_path):
                os.remove(thumbnail_path)

    def _update_video_data(self, thumbnail_path):
        try:
            logger.info(
                "Updating video data with thumbnail path",
                extra={"imported_video_id": self.imported_video.id},
            )

            self.video_data[
                "thumbnail_path"
            ] = f"video-import-data/{self.imported_video.id}/thumbnail.jpg"
            self.imported_video.details = self.video_data
            self.imported_video.save(update_fields=["details"])

            logger.info(
                "Successfully updated video data",
                extra={"imported_video_id": self.imported_video.id},
            )

            return True
        except Exception as e:
            logger.error(
                "Failed to update video data with thumbnail path",
                extra={"imported_video_id": self.imported_video.id, "error": str(e)},
                exc_info=True,
            )
            sentry_sdk.capture_exception(e)
            return False


MigrateTeachableVideoTask = app.register_task(MigrateTeachableVideoTask())  # type: ignore
