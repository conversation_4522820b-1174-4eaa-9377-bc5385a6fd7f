import sentry_sdk
from celery import shared_task
from django.db import transaction
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.domain.purge_asset import delete_asset, purge_assets_marked_for_deletion
from app.domain.video import stop_transcoding


@shared_task(
    name="purge_assets_task",
    autoretry_for=(Exception,),
    max_retries=5,
    retry_backoff=True,
    retry_backoff_max=500,
    retry_jitter=True,
    queue="purge_assets",
)
def purge_assets_task(org_code, days):
    from app.models import Organization

    organization = get_object_or_404(Organization, uuid=org_code)
    set_current_tenant(organization)
    purge_assets_marked_for_deletion(days=days)
    unset_current_tenant()


@shared_task(
    name="stop_transcoding_task",
    autoretry_for=(Exception,),
    max_retries=5,
    retry_backoff=True,
    retry_backoff_max=500,
    retry_jitter=True,
)
def stop_transcoding_task(asset_id):
    from app.models import Asset

    asset = get_object_or_404(Asset, uuid=asset_id)
    stop_transcoding(asset)


@shared_task(
    name="delete_asset_task",
    autoretry_for=(Exception,),
    max_retries=5,
    retry_backoff=True,
    retry_backoff_max=500,
    retry_jitter=True,
    queue="purge_assets",
)
def delete_asset_task(asset_id):
    from app.models import Asset

    asset = Asset.deleted_objects.get(uuid=asset_id)
    delete_asset(asset)


@shared_task(
    name="empty_trash_task",
    autoretry_for=(Exception,),
    max_retries=5,
    retry_backoff=True,
    retry_backoff_max=500,
    retry_jitter=True,
    queue="purge_assets",
)
def empty_trash_task(org_code):
    from app.models import Asset, Organization

    organization = get_object_or_404(Organization, uuid=org_code)
    set_current_tenant(organization)
    assets_to_delete = Asset.objects.deleted_only()
    for asset in assets_to_delete:
        delete_asset(asset)
    unset_current_tenant()


@shared_task(
    name="soft_delete_asset_task",
    autoretry_for=(Exception,),
    max_retries=5,
    retry_backoff=True,
    retry_backoff_max=500,
    retry_jitter=True,
    queue="purge_assets",
)
def soft_delete_asset_task(asset_uuid, org_uuid):
    from app.domain.asset import soft_delete_folder
    from app.models import Asset, Organization

    organization = get_object_or_404(Organization, uuid=org_uuid)
    set_current_tenant(organization)

    try:
        with transaction.atomic():
            asset = Asset.objects.select_related("parent").get(uuid=asset_uuid)
            parent = asset.parent

            if asset.type == Asset.Type.FOLDER:
                soft_delete_folder(asset)
            else:
                Asset.objects.filter(uuid=asset_uuid).update(
                    deleted=timezone.now(),
                    deleted_by_cascade=False,
                    modified=timezone.now(),
                )

        if parent:
            parent.update_children_count()

    except Exception as e:
        with sentry_sdk.push_scope() as scope:
            scope.set_extra("asset_uuid", asset_uuid)
            scope.set_extra("org_uuid", org_uuid)
            sentry_sdk.capture_exception(
                f"Asset deletion task failed for asset ID {asset_uuid}: {e}"
            )
        raise
    finally:
        unset_current_tenant()


@shared_task(
    name="restore_asset_task",
    autoretry_for=(Exception,),
    max_retries=5,
    retry_backoff=True,
    retry_backoff_max=500,
    retry_jitter=True,
    queue="purge_assets",
)
def restore_asset_task(asset_uuid, org_uuid):
    from app.domain.asset import restore_folder
    from app.models import Asset, Organization

    organization = get_object_or_404(Organization, uuid=org_uuid)
    set_current_tenant(organization)
    try:
        with transaction.atomic():
            asset = Asset.all_objects.select_related("parent").get(uuid=asset_uuid)
            parent = asset.parent

            if asset.type == Asset.Type.FOLDER:
                restore_folder(asset)
            else:
                Asset.all_objects.filter(uuid=asset_uuid).update(
                    deleted=None,
                    deleted_by_cascade=False,
                    modified=timezone.now(),
                )

        if parent:
            parent.update_children_count()
            Asset.objects.partial_rebuild(tree_id=parent.tree_id)

    except Exception as e:
        with sentry_sdk.push_scope() as scope:
            scope.set_extra("asset_uuid", asset_uuid)
            scope.set_extra("org_uuid", org_uuid)
            sentry_sdk.capture_exception(
                f"Asset restoration task failed for asset ID {asset_uuid}: {e}"
            )
        raise
    finally:
        unset_current_tenant()
