from celery import shared_task

from app.utils.email import send_email


@shared_task(queue="subtitle_generation")
def send_email_task(
    subject,
    message_text,
    from_email,
    to_emails,
    bcc_emails=[],
    cc_emails=[],
    headers=None,
    message_html=None,
    attachments=[],
):
    send_email(
        subject,
        message_text,
        from_email,
        to_emails,
        bcc_emails,
        cc_emails,
        headers,
        message_html,
        attachments,
    )
