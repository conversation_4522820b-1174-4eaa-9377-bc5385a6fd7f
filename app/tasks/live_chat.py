import json

from django.shortcuts import get_object_or_404

from app.domain.cloud_storage import get_client
from app.domain.live_chat import (
    create_chat_room_for_asset,
    get_chat_messages,
    update_chat_room_as_exported,
)
from app.tasks.base import TpStreamsTask
from config.celery import app


class CreateChatRoomTask(TpStreamsTask):
    def do_run(self, *args, **kwargs):
        from app.models import Asset

        asset_id = kwargs.get("asset_id")
        asset = get_object_or_404(Asset, uuid=asset_id)
        if not asset.live_stream.chat_room_id:
            create_chat_room_for_asset(asset)


CreateChatRoomTask = app.register_task(CreateChatRoomTask())  # type: ignore


class ExportLiveChatTask(TpStreamsTask):
    def do_run(self, *args, **kwargs):
        from app.models import Asset

        asset_id = kwargs.get("asset_id")
        asset = get_object_or_404(Asset, uuid=asset_id)

        messages = self.get_messages(asset)
        self.upload(asset, messages)
        self.update_status_in_chatroom(asset)

    def get_messages(self, asset):
        messages = []
        offset = 0
        page_size = 250

        while True:
            results = get_chat_messages(
                asset.live_stream.chat_room_id, offset, page_size
            )
            if not len(results):
                break

            offset += page_size
            messages.extend(results)
        return messages

    def upload(self, asset, messages):
        s3 = get_client(asset.organization)

        data = json.dumps(messages, indent=2)
        path = f"transcoded/{asset.uuid}/chat.json"

        s3.put_object(
            Bucket=asset.organization.bucket_name,
            Key=path,
            Body=data,
            ACL="public-read",
        )
        asset.live_stream.chat_transcript_url = path
        asset.live_stream.save(update_fields=["chat_transcript_url"])

    def update_status_in_chatroom(self, asset):
        update_chat_room_as_exported(asset.live_stream.chat_room_id)


ExportLiveChatTask = app.register_task(ExportLiveChatTask())  # type: ignore
