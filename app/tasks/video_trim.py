import sentry_sdk
from django.shortcuts import get_object_or_404

from app.tasks.base import TpStreamsTask
from config.celery import app


class VideoTrimTask(TpStreamsTask):
    def do_run(self, *args, **kwargs):
        from app.domain.video_trim.core import mark_trim_completed, mark_trim_status
        from app.domain.video_trim.exceptions import (
            ManifestHandlingError,
            VideoTrimValidationError,
        )
        from app.models.video_trim import TrimStatus, VideoTrim

        trim_job_id = kwargs.get("trim_job_id")

        try:
            trim_job = get_object_or_404(VideoTrim, id=trim_job_id)
            video = trim_job.video
            mark_trim_status(trim_job, TrimStatus.PROCESSING)
            trimmed_outputs = self.trim_video_manifests(
                video, trim_job.start_time, trim_job.end_time
            )
            mark_trim_completed(video, trim_job, trimmed_outputs)
            video.asset.notify_webhook()

        except VideoTrimValidationError as e:
            self.handle_trim_failure(trim_job_id, f"Validation error: {str(e)}")
        except ManifestHandlingError as e:
            self.handle_trim_failure(trim_job_id, f"Manifest handling error: {str(e)}")
        except Exception as e:
            self.handle_trim_failure(trim_job_id, f"Unexpected error: {str(e)}")
            sentry_sdk.capture_exception(e)

    def trim_video_manifests(self, video, start_time, end_time):
        from app.domain.video_trim.exceptions import ManifestHandlingError
        from app.domain.video_trim.manifest_domain.dash import trim_dash_manifests
        from app.domain.video_trim.manifest_domain.hls import trim_hls_manifests

        trimmed_outputs = {}

        try:
            if video.is_drm_encrypted:
                trimmed_outputs = {
                    "h264": {
                        "dash": trim_dash_manifests(video, start_time, end_time)[
                            "master"
                        ]
                    }
                }
            else:
                trimmed_outputs = {
                    "h264": {
                        "hls": trim_hls_manifests(video, start_time, end_time)["master"]
                    }
                }
        except ManifestHandlingError as e:
            sentry_sdk.capture_message(f"Manifest trimming failed: {str(e)}")

        if not trimmed_outputs:
            raise ManifestHandlingError("No manifests could be trimmed successfully")

        return trimmed_outputs

    def get_available_codecs(self, video):
        from app.models.video_trim import OutputType

        original_output_types = [OutputType.HLS, OutputType.DASH]
        codecs = (
            video.outputs.filter(output_type__in=original_output_types, is_active=True)
            .values_list("codec", flat=True)
            .distinct()
        )

        return list(codecs) if codecs else ["h264"]

    def handle_trim_failure(self, trim_job_id, error_message):
        from app.domain.video_trim.core import mark_trim_status
        from app.models.video_trim import TrimStatus, VideoTrim

        try:
            trim_job = VideoTrim.objects.get(id=trim_job_id)
            mark_trim_status(trim_job, TrimStatus.FAILED)
        except VideoTrim.DoesNotExist:
            pass

        sentry_sdk.capture_message(
            f"Video trim failed for job {trim_job_id}: {error_message}"
        )


VideoTrimTask = app.register_task(VideoTrimTask())  # type: ignore


class VideoTrimRevertTask(TpStreamsTask):
    def do_run(self, *args, **kwargs):
        from app.domain.video_trim.core import revert_to_original
        from app.domain.video_trim.exceptions import VideoTrimValidationError
        from app.models.video import Video

        video_id = kwargs.get("video_id")

        try:
            video = get_object_or_404(Video, id=video_id)
            revert_to_original(video)

        except VideoTrimValidationError as e:
            sentry_sdk.capture_message(
                f"Video trim revert failed for video {video_id}: {str(e)}"
            )
        except Exception as e:
            sentry_sdk.capture_exception(e)


VideoTrimRevertTask = app.register_task(VideoTrimRevertTask())  # type: ignore
