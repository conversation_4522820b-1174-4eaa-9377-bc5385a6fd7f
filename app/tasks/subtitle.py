import datetime

from django.conf import settings
from django.shortcuts import get_object_or_404

from app.domain.aws import AWSSubtitleServer
from app.domain.cloud_storage import generate_presigned_url, has_object
from app.domain.local_file_to_storage_uploader import upload_local_files_to_storage
from app.tasks.base import TpStreamsTask
from app.utils.audio import get_audio
from app.utils.browser import is_valid_url
from app.utils.datetime import convert_date_string_to_datetime_obj
from app.utils.duration import get_duration
from app.utils.file import delete_folder, mkdir
from app.utils.storage import download_file as download_video
from config.celery import app


class GenerateSubtitleTask(TpStreamsTask):
    def do_run(self, *args, **kwargs):
        from app.domain.subtitle import (
            create_subtitle_track,
            store_subtitle_generation_details,
        )
        from app.models import Asset, Track

        self.asset = get_object_or_404(Asset, uuid=kwargs.get("asset_id"))
        local_audio_path = self.download_video_and_extract_audio(self.asset.video)
        self.upload_audio_to_storage(local_audio_path)
        subtitle_data = self.generate_subtitle_data(local_audio_path)
        delete_folder(f"{settings.BASE_DIR}/subtitle_videos/{self.asset.uuid}")
        track, created = create_subtitle_track(
            self.asset,
            name="auto-generated",
            language="en",
            subtitle_type=Track.SubtitleType.AUTO_GENERATED,
            subtitle_data=subtitle_data,
        )
        subtitle_server = AWSSubtitleServer(track)
        server_id = subtitle_server.create_server()
        server_details = subtitle_server.get_server_details()
        if server_id:
            store_subtitle_generation_details(
                track,
                "SERVER_STARTED",
                server_id=server_id,
                server_details=server_details,
            )

    def download_video_and_extract_audio(self, video):
        local_folder_path = f"{settings.BASE_DIR}/subtitle_videos/{video.asset.uuid}/"
        mkdir(local_folder_path)
        download_video(
            video.inputs.first().get_input_url(),
            f"{local_folder_path}/{video.asset.uuid}.mp4",
        )
        audio_path = get_audio(
            f"{local_folder_path}/{video.asset.uuid}.mp4",
            f"{local_folder_path}/{video.asset.uuid}.mp3",
        )
        return f"{local_folder_path}/{video.asset.uuid}.mp3" if audio_path else None

    def generate_subtitle_data(self, local_audio_path):
        return {
            "audio_duration": get_duration(local_audio_path),
            "audio_input_url": self.get_audio_input_url(),
        }

    def upload_audio_to_storage(self, local_audio_path):
        if not local_audio_path:
            """"""
        upload_local_files_to_storage(
            [
                local_audio_path,
            ],
            f"/{self.asset.organization.bucket_name}/private/",
            self.asset.organization,
        )

    def get_audio_input_url(self):
        if has_object(self.asset.organization, f"private/{self.asset.uuid}.mp3"):
            url = generate_presigned_url(
                self.asset.organization, f"private/{self.asset.uuid}.mp3"
            )
            audio_input_url = url if is_valid_url(url) else None
            return audio_input_url
        return ""


GenerateSubtitleTask = app.register_task(GenerateSubtitleTask())  # type: ignore


class UpdateSubtitlesGenerationMinutesTask(TpStreamsTask):
    def do_run(self, *args, **kwargs):
        from app.domain.subtitle import update_subtitles_generation_minutes

        if kwargs.get("date"):
            date = convert_date_string_to_datetime_obj(kwargs.get("date"))
        else:
            date = datetime.date.today() - datetime.timedelta(days=1)
        update_subtitles_generation_minutes(self.organization, date)


UpdateSubtitlesGenerationMinutesTask = app.register_task(UpdateSubtitlesGenerationMinutesTask())  # type: ignore
