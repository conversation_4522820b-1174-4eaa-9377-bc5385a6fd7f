from app.domain.video import start_transcoding
from app.tasks.base import TpStreamsTask
from app.utils.migrator_interface import MigratorInterface
from app.utils.wasabi import get_wasabi_config, upload_from_url_to_wasabi
from config.celery import app


class MigrateImportedVideoTask(MigratorInterface, TpStreamsTask):
    def do_run(self, *args, **kwargs):
        self.intialize(kwargs)
        self.copy_highest_resolution_to_wasabi(self._get_video())
        asset = self.create_asset(folder_name=self.site_id)
        self.process_video_based_on_protection_type(asset)
        thumbnail_url = self.upload_thumbnail_to_wasabi(
            output_path=f"transcoded/{asset.uuid}/thumbnail.jpg"
        )
        self.save_thumbnail_url_in_video(asset.video, thumbnail_url)

    def intialize(self, kwargs):
        from app.models import ImportedVideo, Video
        from app.utils.jwplayer import JwplayerInterface as Jwplayer

        self.imported_video = ImportedVideo.objects.get(
            id=kwargs.get("imported_video_id")
        )
        self.site_id = kwargs.get("site_id")
        self.api_key = kwargs.get("api_key")
        self.content_protection_type = kwargs.get(
            "content_protection_type", Video.ContentProtectionType.DISABLED
        )
        self.jwplayer = Jwplayer(self.site_id, self.api_key)

    def _get_video(self):
        return self.jwplayer.get_video(self.imported_video.details["id"])

    def process_video_based_on_protection_type(self, asset):
        start_transcoding(
            asset.video,
            extra_settings={
                "inputs": self.get_transcoding_job_inputs(self._get_video())
            },
        )

    def upload_thumbnail_to_wasabi(self, output_path):
        thumbnail_url = self.jwplayer.get_thumbnail_url(
            self.imported_video.details["id"]
        )
        config = get_wasabi_config(self.organization)
        upload_from_url_to_wasabi(
            thumbnail_url,
            f"{self.organization.bucket_name}/" + output_path,
            config,
            make_public=True,
        )
        return output_path

    def save_thumbnail_url_in_video(self, video, thumbnail_url):
        video.thumbnails = [thumbnail_url]
        video.cover_thumbnail_url = thumbnail_url
        video.save(update_fields=["thumbnails", "cover_thumbnail_url"])


MigrateImportedVideoTask = app.register_task(MigrateImportedVideoTask())  # type: ignore
