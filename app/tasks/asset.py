import sentry_sdk
from celery import shared_task
from django.shortcuts import get_object_or_404


@shared_task(
    name="tree_rebuild_task",
    autoretry_for=(Exception,),
    max_retries=5,
    retry_backoff=True,
    retry_backoff_max=500,
    retry_jitter=True,
)
def rebuild_asset_tree(asset_id):
    from app.models import Asset

    try:
        asset = get_object_or_404(Asset, uuid=asset_id)
        Asset.objects.partial_rebuild(tree_id=asset.tree_id)

    except Exception as error:
        with sentry_sdk.push_scope() as scope:
            scope.set_extra("asset_id", asset_id)
            sentry_sdk.capture_message("Failed rebuilding the asset tree")
