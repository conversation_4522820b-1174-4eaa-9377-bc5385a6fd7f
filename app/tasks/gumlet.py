from app.domain.video import start_transcoding
from app.tasks.base import TpStreamsTask
from app.utils.migrator_interface import MigratorInterface
from app.utils.wasabi import get_wasabi_config, upload_from_url_to_wasabi
from config.celery import app


class MigrateImportedVideoTask(MigratorInterface, TpStreamsTask):
    def do_run(self, *args, **kwargs):
        self.intialize(kwargs)
        self.video = self._get_video()
        input_url_path = self.copy_source_to_wasabi(self.video)
        asset = self.create_asset(
            folder_name=self.video.playlist_name,
            input_url_path=input_url_path,
            transmux_only=False,
        )
        self.process_video_based_on_protection_type(asset)
        thumbnail_url = self.upload_thumbnail_to_wasabi(
            output_path=f"transcoded/{asset.uuid}/thumbnail.jpg"
        )
        self.save_thumbnail_url_in_video(asset.video, thumbnail_url)

    def intialize(self, kwargs):
        from app.models import ImportedVideo, Video
        from app.utils.gumlet import GumletInterface as Gumlet

        self.imported_video = ImportedVideo.objects.get(
            id=kwargs.get("imported_video_id")
        )
        self.playlist_id = kwargs.get("playlist_id")
        self.api_key = kwargs.get("api_key")
        self.content_protection_type = kwargs.get(
            "content_protection_type", Video.ContentProtectionType.DISABLED
        )
        self.gumlet = Gumlet(self.api_key)

    def _get_video(self):
        return self.gumlet.get_asset(self.imported_video.details["id"])

    def copy_source_to_wasabi(self, video):
        output_path = f"private/{video.id}/main.mp4"
        config = get_wasabi_config(self.organization)
        upload_from_url_to_wasabi(
            video.playback_url,
            f"{self.organization.bucket_name}/{output_path}",
            config,
            make_public=True,
        )
        return output_path

    def process_video_based_on_protection_type(self, asset):
        start_transcoding(
            asset.video,
        )

    def upload_thumbnail_to_wasabi(self, output_path):
        thumbnail_url = self.video.thumbnail_url
        config = get_wasabi_config(self.organization)
        upload_from_url_to_wasabi(
            thumbnail_url,
            f"{self.organization.bucket_name}/" + output_path,
            config,
            make_public=True,
        )
        return output_path

    def save_thumbnail_url_in_video(self, video, thumbnail_url):
        video.thumbnails = [thumbnail_url]
        video.cover_thumbnail_url = thumbnail_url
        video.save(update_fields=["thumbnails", "cover_thumbnail_url"])


MigrateImportedVideoTask = app.register_task(MigrateImportedVideoTask())  # type: ignore
