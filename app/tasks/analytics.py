import datetime

from app.domain.analytics import store_video_analytics_to_db
from app.tasks.base import TpStreamsTask
from config.celery import app


class StoreVideoAnalyticsTask(TpStreamsTask):
    def do_run(self, *args, **kwargs):
        date = self.parse_date(kwargs.get("date"))
        store_video_analytics_to_db(self.organization, date)

    def parse_date(self, date_from_kwargs):
        if date_from_kwargs:
            try:
                return datetime.datetime.strptime(date_from_kwargs, "%Y-%m-%d").date()
            except ValueError:
                pass
        return datetime.date.today() - datetime.timedelta(days=1)


StoreVideoAnalyticsTask = app.register_task(StoreVideoAnalyticsTask())  # type: ignore
