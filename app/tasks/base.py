import dill
from django.shortcuts import get_object_or_404
from django_multitenant.utils import set_current_tenant, unset_current_tenant

import celery


class TpStreamsTask(celery.Task):
    def schedule(self, run_at, kwargs, queue=None):
        from app.models import Organization, ScheduledTask, ScheduledTaskReference

        assert kwargs.get("organization_uuid") is not None
        organization_uuid = kwargs.get("organization_uuid")
        task_reference = ScheduledTaskReference.objects.create(
            organization_uuid=organization_uuid, run_at=run_at
        )

        data = {
            "name": self.name,
            "task_id": str(task_reference.task_id),
            "kwargs": kwargs,
        }

        if queue:
            data["queue"] = queue

        ScheduledTask.objects.create(
            organization=get_object_or_404(Organization, uuid=organization_uuid),
            pickled_payload=dill.dumps(data, protocol=2, recurse=True),
            reference_uuid=task_reference.task_id,
        )

        return self.AsyncResult(task_reference.task_id)

    def run(self, *args, **kwargs):
        assert kwargs.get("organization_uuid") is not None
        self._set_organization(kwargs.get("organization_uuid"))
        self.do_run(*args, **kwargs)

    def _set_organization(self, organization_uuid):
        from app.models import Organization

        unset_current_tenant()
        self.organization = get_object_or_404(Organization, uuid=organization_uuid)
        set_current_tenant(self.organization)

    def do_run(self, *args, **kwargs):
        """The body of the task executed by workers."""
        raise NotImplementedError("do_run must be defined in the child class")
