from app.domain.video import start_transcoding
from app.tasks import TpStreamsTask
from app.utils.migrator_interface import MigratorInterface
from config.celery import app


class MigrateImportedVideoTask(MigratorInterface, TpStreamsTask):
    def do_run(self, *args, **kwargs):
        self.intialize(kwargs)
        self.copy_highest_resolution_to_wasabi(self._get_video())
        asset = self.create_asset(folder_name=self.imported_video.owner)
        self._upload_subtitles(asset)
        start_transcoding(
            asset.video,
            extra_settings={
                "inputs": self.get_transcoding_job_inputs(self._get_video())
            },
        )

    def intialize(self, kwargs):
        from app.models import Video
        from app.models.videoimport import ImportedVideo
        from app.utils.vimeo import Interface as Vimeo

        self.imported_video = ImportedVideo.objects.get(
            id=kwargs.get("imported_video_id")
        )
        self.access_token = kwargs.get("access_token")
        self.content_protection_type = kwargs.get(
            "content_protection_type", Video.ContentProtectionType.DISABLED
        )
        self.vimeo = Vimeo(self.access_token)

    def _get_video(self):
        return self.vimeo.get_video(self.imported_video.uri)

    def _upload_subtitles(self, asset):
        from app.domain.subtitle import upload_subtitle_from_url

        subtitles = self.vimeo.get_video_subtitles(self.imported_video.uri)
        for subtitle in subtitles:
            upload_subtitle_from_url(
                asset, subtitle.name, subtitle.language, subtitle.link
            )


MigrateImportedVideoTask = app.register_task(MigrateImportedVideoTask())  # type: ignore
