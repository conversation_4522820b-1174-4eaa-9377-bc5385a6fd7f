import datetime

import celery
from django.shortcuts import get_object_or_404

from app.domain.storages import update_storage_usage
from app.tasks.base import TpStreamsTask
from config.celery import app


class UpdateAssetSizeTask(TpStreamsTask):
    def run(self, *args, **kwargs):
        from app.models import Asset

        asset = get_object_or_404(Asset, uuid=kwargs.get("asset_id"))
        asset.update_size()


UpdateAssetSizeTask = app.register_task(UpdateAssetSizeTask())  # type: ignore


class UpdateStorageUsageTask(celery.Task):
    def run(self, *args, **kwargs):
        date = datetime.date.today() - datetime.timedelta(days=1)
        update_storage_usage(date)


UpdateStorageUsageTask = app.register_task(UpdateStorageUsageTask())  # type: ignore
