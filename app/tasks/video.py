import base64
import binascii
import json
import os
import re
import shutil
import subprocess
import uuid
from urllib.parse import urljoin, urlparse

import m3u8
import sentry_sdk
from django.shortcuts import get_object_or_404

from app.domain.cloud_storage import delete_file
from app.domain.local_file_to_storage_uploader import upload_local_files_to_storage
from app.domain.s3 import get_s3_config_for_rclone
from app.domain.video import start_transcoding
from app.tasks.base import TpStreamsTask
from app.utils.mpd import get_highest_resolution_folder_path
from app.utils.s3 import upload_from_url_to_s3
from app.utils.wasabi import (
    FileCopyError,
    copy_files_to_local,
    delete_files,
    get_wasabi_config,
    upload_from_url_to_wasabi,
)
from config.celery import app


def base64_to_hex(base64_string):
    decoded_bytes = base64.b64decode(base64_string)
    hex_result = binascii.hexlify(decoded_bytes).decode("utf-8")

    return hex_result


class DownloadAndTranscodeVideoTask(TpStreamsTask):
    def do_run(self, *args, **kwargs):
        from app.domain.subtitle import generate_subtitle
        from app.models import Organization, Video

        video = get_object_or_404(Video, id=kwargs.get("video_id"))
        video_input = video.inputs.first()
        output_path = self.get_output_path(video_input)
        video.update_status(Video.Status.UPLOADING)
        try:
            if self.organization.storage_vendor == Organization.StorageProvider.AWS:
                config = get_s3_config_for_rclone(self.organization)
                upload_from_url_to_s3(
                    video_input.url,
                    f"/{self.organization.bucket_name}/{output_path}",
                    config,
                )
            else:
                config = get_wasabi_config(self.organization)
                upload_from_url_to_wasabi(
                    video_input.url,
                    f"/{self.organization.bucket_name}/{output_path}",
                    config=config,
                    make_public=True,
                )
        except FileCopyError:
            video.update_status(Video.Status.INPUT_READ_ERROR)
            return

        video_input.change_url(output_path)
        video.update_status(Video.Status.UPLOADED)
        start_transcoding(video)
        if video.should_generate_subtitle():
            generate_subtitle(video.asset)

    def get_output_path(self, video_input):
        parsed_url = urlparse(video_input.url)
        file_extension = os.path.splitext(parsed_url.path)[1]
        return f"private/{video_input.video.asset.uuid}{file_extension}"


DownloadAndTranscodeVideoTask = app.register_task(DownloadAndTranscodeVideoTask())  # type: ignore


class DeleteMigratedVideoFilesTask(TpStreamsTask):
    def do_run(self, *args, **kwargs):
        from app.models import Asset

        asset = get_object_or_404(Asset, uuid=kwargs.get("asset_id"))
        config = get_wasabi_config(asset.organization)
        delete_files(
            f"{asset.organization.bucket_name}/transcoded/{asset.uuid}",
            config,
            exclude_folder="new",
        )


DeleteMigratedVideoFilesTask = app.register_task(DeleteMigratedVideoFilesTask())  # type: ignore


class UpdateVideoPlaylistTask(TpStreamsTask):
    def do_run(self, *args, **kwargs):
        from app.domain.video import store_video_playlist_info

        try:
            videos = self.get_videos(self.organization, asset_id=kwargs.get("asset_id"))
            for video in videos:
                store_video_playlist_info(video)
        except Exception as e:
            sentry_sdk.capture_exception(f"Error in update video playlist info : {e}")

    def get_videos(self, organization, asset_id):
        if asset_id:
            return self.get_video(asset_id)
        return self.get_all_videos(organization)

    def get_video(self, asset_id):
        from app.models.asset import Asset

        try:
            asset = Asset.objects.get(uuid=asset_id)
            return [asset.video] if asset else []
        except Asset.DoesNotExist as e:
            sentry_sdk.capture_exception(f"Error in update video playlist info : {e}")
            return []

    def get_all_videos(self, organization):
        from app.models.video import Video

        return Video.objects.filter(
            organization=organization,
            status=Video.Status.COMPLETED,
        )


UpdateVideoPlaylistTask = app.register_task(UpdateVideoPlaylistTask())  # type: ignore


class GenerateSourceFromHLSVideo(TpStreamsTask):
    def do_run(self, *args, **kwargs):
        from app.models import Asset

        try:
            asset_id = kwargs.get("asset_id")
            asset = Asset.objects.get(uuid=asset_id)
            output_folder = self.create_output_folder(asset.uuid)
            m3u8_url = self.get_highest_resolution_m3u8_url(
                asset.organization.cdn_url + asset.video.playback_url
            )
            m3u8_file_path = self.download_m3u8_segments(m3u8_url, output_folder)

            video_output_path = os.path.join(output_folder, "video.mp4")
            self.convert_m3u8_to_video(m3u8_file_path, video_output_path, output_folder)

            upload_local_files_to_storage(
                [video_output_path],
                self.construct_storage_path(asset),
                asset.organization,
            )
            self.update_asset_source_path(asset)

        except Exception as e:
            sentry_sdk.capture_exception(
                f"Error occurred while processing asset ID: {asset_id}. Details: {str(e)}"
            )

        finally:
            if os.path.exists(output_folder):
                shutil.rmtree(output_folder)

    def create_output_folder(self, uuid):
        output_folder = str(uuid)
        os.makedirs(output_folder, exist_ok=True)
        return output_folder

    def get_highest_resolution_m3u8_url(self, url):
        master_m3u8 = m3u8.load(url)
        highest_resolution = max(
            master_m3u8.playlists, key=lambda x: x.stream_info.resolution
        )
        return urljoin(url, highest_resolution.uri)

    def download_m3u8_segments(self, m3u8_url, output_folder):
        playlist_filename = self.download_file_with_rclone(m3u8_url, output_folder)
        playlist = m3u8.load(playlist_filename)
        segments_urls_file = os.path.join(output_folder, "all_the_urls.txt")
        with open(segments_urls_file, "w") as f:
            for segment in playlist.segments:
                segment_url = urljoin(m3u8_url, segment.uri)
                f.write(segment_url + "\n")

        command = [
            "aria2c",
            "-i",
            segments_urls_file,
            "-d",
            output_folder,
            "-j",
            "16",
            "--file-allocation=none",
        ]
        try:
            subprocess.check_call(command)
        except subprocess.CalledProcessError as e:
            raise Exception(
                f"Failed to download .ts files using aria2. Error: {str(e)}"
            )

        return playlist_filename

    def download_file_with_rclone(self, url, output_folder):
        filename = os.path.basename(url)
        destination = os.path.join(output_folder, filename)
        command = ["rclone", "copyurl", url, destination]

        try:
            subprocess.check_call(command)
            return destination
        except subprocess.CalledProcessError as e:
            raise Exception(
                f"Failed to download file from {url} using rclone. Error: {str(e)}"
            )

    def convert_m3u8_to_video(self, m3u8_file, output_file, output_folder):
        playlist = m3u8.load(m3u8_file)
        segments_list_path = os.path.join(output_folder, "segments.txt")

        with open(segments_list_path, "w") as f:
            for segment in playlist.segments:
                f.write(f"file '{os.path.basename(segment.uri)}'\n")

        command = [
            "ffmpeg",
            "-f",
            "concat",
            "-safe",
            "0",
            "-i",
            segments_list_path,
            "-c",
            "copy",
            output_file,
        ]
        if subprocess.call(command) != 0:
            raise Exception("FFmpeg concatenation of .ts files to mp4 failed")

    def construct_storage_path(self, asset):
        return f"{asset.organization.bucket_name}/private/{asset.uuid}/"

    def update_asset_source_path(self, asset):
        storage_path = f"private/{asset.uuid}/video.mp4"
        input = asset.video.inputs.first()
        input.url = storage_path
        input.save()


GenerateSourceFromHLSVideo = app.register_task(GenerateSourceFromHLSVideo())  # type: ignore


class DeleteSourceVideoTask(TpStreamsTask):
    def do_run(self, *args, **kwargs):
        from app.models import Video

        if kwargs.get("video_id"):
            try:
                video = get_object_or_404(Video, id=kwargs.get("video_id"))
                if not video.meta_data:
                    video.meta_data = {}
                video_input = video.inputs.first()
                delete_file(video.organization, video_input.url)
                video.meta_data["is_source_deleted"] = True
                video.save(update_fields=["meta_data"])
            except DeleteSourceError:
                return


DeleteSourceVideoTask = app.register_task(DeleteSourceVideoTask())  # type: ignore


class DeleteSourceError(Exception):
    pass


class UploadSourceVideoFromLink(TpStreamsTask):
    def do_run(self, *args, **kwargs):
        from app.models import Asset

        try:
            asset_id = kwargs.get("asset_id")
            asset = Asset.objects.get(uuid=asset_id)
            destination_file_path = f"private/{asset.uuid}/video.mp4"
            video_url = kwargs.get("video_url")
            config = get_wasabi_config(asset.organization)
            upload_from_url_to_wasabi(
                str(video_url),
                f"{asset.organization.bucket_name}/" + destination_file_path,
                config,
                make_public=True,
            )
            input = asset.video.inputs.first()
            input.url = destination_file_path
            input.save()

        except Exception as e:
            print(
                f"Error occurred while processing asset ID: {asset_id}. Details: {str(e)}"
            )
            sentry_sdk.capture_exception(
                f"Error occurred while processing asset ID: {asset_id}. Details: {str(e)}"
            )


UploadSourceVideoFromLink = app.register_task(UploadSourceVideoFromLink())  # type: ignore


class GenerateSourceFromDRMVideo(TpStreamsTask):
    def do_run(self, *args, **kwargs):
        from app.models import Asset

        asset_id = kwargs.get("asset_id")
        asset = Asset.objects.get(uuid=asset_id)
        downloaded_path = self.download_video_and_audio_segments(asset)
        try:
            audio_path, video_path = self.concatenate_video_and_audio_segments(
                downloaded_path
            )
            keys = json.loads(asset.encryption_key.widevine_encryption_keys)
            decrypted_audio_path, decrypted_video_path = self.decrypt_files(
                audio_path, video_path, keys
            )
            merged_video_path = self.merge_video_audio(
                decrypted_audio_path, decrypted_video_path
            )
            output_path = f"/{asset.organization.bucket_name}/private/{asset.uuid}/"
            upload_local_files_to_storage(
                [merged_video_path], output_path, asset.organization
            )
            self.update_source_data_in_asset(
                asset, f"private/{asset.uuid}/source_video.mp4"
            )

        except Exception as e:
            sentry_sdk.capture_exception(e)

        finally:
            self.delete_folder(downloaded_path)

    def download_video_and_audio_segments(self, asset):
        highest_resolution_folder_path = get_highest_resolution_folder_path(
            self.get_dash_url(asset)
        )
        relative_folder_path = highest_resolution_folder_path.replace(
            asset.organization.cdn_url, asset.organization.bucket_name + "/"
        )
        local_destination_path = os.path.join(os.getcwd(), uuid.uuid4().hex)
        config = get_wasabi_config(self.organization)
        copy_files_to_local(relative_folder_path, local_destination_path, config=config)
        return local_destination_path

    def get_dash_url(self, asset):
        return f"{asset.organization.cdn_url}{asset.video.dash_url}"

    def concatenate_video_and_audio_segments(self, downloaded_path):
        audio_output_path = self.concatenate_files_into_mp4(
            files_path=downloaded_path,
            file_prefix="audio",
            output_filename="output_audio.mp4",
        )
        video_output_path = self.concatenate_files_into_mp4(
            files_path=downloaded_path,
            file_prefix="video",
            output_filename="output_video.mp4",
        )
        return audio_output_path, video_output_path

    def concatenate_files_into_mp4(self, files_path, file_prefix, output_filename):
        files = [
            file
            for file in os.listdir(files_path)
            if file.startswith(file_prefix) and file.endswith(".mp4")
        ]
        files.sort(key=self.file_numerical_priority)
        output_path = os.path.join(files_path, output_filename)
        with open(output_path, "wb") as output_file:
            for file_name in files:
                file_path = os.path.join(files_path, file_name)
                with open(file_path, "rb") as input_file:
                    shutil.copyfileobj(input_file, output_file)
        return output_path

    def file_numerical_priority(self, file_name):
        if file_name.endswith("_init.mp4"):
            return 0
        match = re.search(r"\d+\b", file_name)
        if match:
            return int(match.group())
        return float("inf")

    def decrypt_files(self, audio_path, video_path, keys):
        folder_path = os.path.dirname(audio_path)
        audio_output = os.path.join(folder_path, "decrypted_audio.mp4")
        video_output = os.path.join(folder_path, "decrypted_video.mp4")

        keys_string = ",".join(
            [
                f"label={track['type']}:key_id={base64_to_hex(track['key_id'])}:key={base64_to_hex(track['key'])}"
                for track in keys["tracks"]
            ]
        )

        command = [
            "packager",
            f"in={audio_path},stream=audio,output={audio_output}",
            f"in={video_path},stream=video,output={video_output}",
            "--enable_raw_key_decryption",
            "--keys",
            keys_string,
        ]

        process = subprocess.run(command, capture_output=True, text=True)

        if process.returncode != 0:
            raise Exception("Decryption process failed.")

        return audio_output, video_output

    def merge_video_audio(self, input_video_path, input_audio_path):
        folder_path, _ = os.path.split(input_video_path)
        video_source_path = os.path.join(folder_path, "source_video.mp4")
        command = [
            "ffmpeg",
            "-i",
            input_video_path,
            "-i",
            input_audio_path,
            "-c:v",
            "copy",
            "-c:a",
            "aac",
            video_source_path,
        ]
        process = subprocess.Popen(
            command, stderr=subprocess.PIPE, stdout=subprocess.PIPE
        )
        output, error = process.communicate()
        if process.returncode != 0:
            raise Exception(f"Command failed: {error}")
        return video_source_path

    def update_source_data_in_asset(self, asset, source_path):
        input = asset.video.inputs.first()
        input.url = source_path
        input.save(update_fields=["url"])
        if not asset.video.meta_data:
            asset.video.meta_data = {}
        asset.video.meta_data["is_source_regenerated"] = True
        asset.video.save(update_fields=["meta_data"])

    def delete_folder(self, local_path):
        try:
            shutil.rmtree(local_path)
        except OSError as e:
            print(f"Error: {e}")


GenerateSourceFromDRMVideo = app.register_task(GenerateSourceFromDRMVideo())  # type: ignore
