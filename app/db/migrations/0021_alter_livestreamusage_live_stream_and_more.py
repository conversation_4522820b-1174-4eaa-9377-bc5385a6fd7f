# Generated by Django 4.0.8 on 2023-06-13 03:59

import app.models.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0020_livestream_end_livestream_server_id_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="livestreamusage",
            name="live_stream",
            field=app.models.fields.TenantOneToOneField(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="live_stream_usage",
                to="app.livestream",
            ),
        ),
        migrations.AlterField(
            model_name="livestreamusage",
            name="server_provider",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "Digitalocean"), (2, "Aws")],
                db_index=True,
                default=1,
                verbose_name="Status",
            ),
        ),
    ]
