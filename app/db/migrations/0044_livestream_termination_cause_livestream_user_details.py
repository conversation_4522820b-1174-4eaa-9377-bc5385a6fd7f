# Generated by Django 4.0.8 on 2023-09-13 11:31

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0043_transcodingjob_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="livestream",
            name="termination_cause",
            field=models.IntegerField(
                blank=True,
                choices=[(0, "User Initiated"), (1, "Scheduled Termination")],
                null=True,
                verbose_name="Termination Cause",
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="user_details",
            field=models.JSONField(
                blank=True, default=dict, null=True, verbose_name="User Details"
            ),
        ),
        migrations.AlterField(
            model_name="livestream",
            name="termination_cause",
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[(0, "User Initiated"), (1, "Scheduled Termination")],
                null=True,
                verbose_name="Termination Cause",
            ),
        ),
    ]
