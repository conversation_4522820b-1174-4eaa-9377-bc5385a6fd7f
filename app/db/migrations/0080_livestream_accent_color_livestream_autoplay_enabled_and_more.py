# Generated by Django 4.0.8 on 2024-07-26 05:29

import app.db.constraints.composite_key
import app.db.constraints.distribute
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_multitenant.fields
import django_multitenant.mixins
import model_utils.fields
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0079_videochapter_videochapter_app_videochapter_pkey_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="livestream",
            name="accent_color",
            field=models.CharField(
                blank=True,
                default="#03a4eb",
                max_length=12,
                null=True,
                verbose_name="Accent color",
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="autoplay_enabled",
            field=models.BooleanField(
                blank=True, default=False, null=True, verbose_name="Enable autoplay"
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="background_color",
            field=models.CharField(
                blank=True,
                default="#0F0F0F",
                max_length=12,
                null=True,
                verbose_name="Background color",
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="background_mode_enabled",
            field=models.BooleanField(
                blank=True,
                default=False,
                null=True,
                verbose_name="Enable background mode",
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="default_quality",
            field=models.IntegerField(
                blank=True,
                choices=[
                    (0, "Auto"),
                    (240, "240p"),
                    (360, "360p"),
                    (480, "480p"),
                    (720, "720p"),
                    (1080, "1080p"),
                    (2160, "4k"),
                ],
                default=0,
                null=True,
                verbose_name="Default video quality",
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="default_subtitle_language",
            field=models.CharField(
                blank=True,
                choices=[
                    ("af", "Afrikaans"),
                    ("ar", "Arabic"),
                    ("ar-dz", "Algerian Arabic"),
                    ("ast", "Asturian"),
                    ("az", "Azerbaijani"),
                    ("be", "Belarusian"),
                    ("bg", "Bulgarian"),
                    ("bn", "Bengali"),
                    ("br", "Breton"),
                    ("bs", "Bosnian"),
                    ("ca", "Catalan"),
                    ("cs", "Czech"),
                    ("cy", "Welsh"),
                    ("da", "Danish"),
                    ("de", "German"),
                    ("dsb", "Lower Sorbian"),
                    ("el", "Greek"),
                    ("en", "English"),
                    ("en-au", "Australian English"),
                    ("en-gb", "British English"),
                    ("eo", "Esperanto"),
                    ("es", "Spanish"),
                    ("es-ar", "Argentinian Spanish"),
                    ("es-co", "Colombian Spanish"),
                    ("es-mx", "Mexican Spanish"),
                    ("es-ni", "Nicaraguan Spanish"),
                    ("es-ve", "Venezuelan Spanish"),
                    ("et", "Estonian"),
                    ("eu", "Basque"),
                    ("fa", "Persian"),
                    ("fi", "Finnish"),
                    ("fr", "French"),
                    ("fy", "Frisian"),
                    ("ga", "Irish"),
                    ("gd", "Scottish Gaelic"),
                    ("gl", "Galician"),
                    ("he", "Hebrew"),
                    ("hi", "Hindi"),
                    ("hr", "Croatian"),
                    ("hsb", "Upper Sorbian"),
                    ("hu", "Hungarian"),
                    ("hy", "Armenian"),
                    ("ia", "Interlingua"),
                    ("io", "Ido"),
                    ("id", "Indonesian"),
                    ("ig", "Igbo"),
                    ("is", "Icelandic"),
                    ("it", "Italian"),
                    ("ja", "Japanese"),
                    ("ka", "Georgian"),
                    ("kab", "Kabyle"),
                    ("kk", "Kazakh"),
                    ("km", "Khmer"),
                    ("kn", "Kannada"),
                    ("ko", "Korean"),
                    ("ky", "Kyrgyz"),
                    ("lb", "Luxembourgish"),
                    ("lt", "Lithuanian"),
                    ("lv", "Latvian"),
                    ("mk", "Macedonian"),
                    ("ml", "Malayalam"),
                    ("mn", "Mongolian"),
                    ("mr", "Marathi"),
                    ("ms", "Malay"),
                    ("my", "Burmese"),
                    ("nb", "Norwegian Bokmal"),
                    ("ne", "Nepali"),
                    ("nl", "Dutch"),
                    ("nn", "Norwegian Nynorsk"),
                    ("no", "Norwegian"),
                    ("os", "Ossetic"),
                    ("pa", "Punjabi"),
                    ("pl", "Polish"),
                    ("pt", "Portuguese"),
                    ("pt-br", "Brazilian Portuguese"),
                    ("ro", "Romanian"),
                    ("ru", "Russian"),
                    ("sk", "Slovak"),
                    ("sl", "Slovenian"),
                    ("sq", "Albanian"),
                    ("sr", "Serbian"),
                    ("sr-latn", "Serbian Latin"),
                    ("sv", "Swedish"),
                    ("sw", "Swahili"),
                    ("ta", "Tamil"),
                    ("te", "Telugu"),
                    ("tg", "Tajik"),
                    ("th", "Thai"),
                    ("tk", "Turkmen"),
                    ("tr", "Turkish"),
                    ("tt", "Tatar"),
                    ("udm", "Udmurt"),
                    ("uk", "Ukrainian"),
                    ("ur", "Urdu"),
                    ("uz", "Uzbek"),
                    ("vi", "Vietnamese"),
                    ("zh-hans", "Simplified Chinese"),
                    ("zh-hant", "Traditional Chinese"),
                ],
                default="en",
                max_length=10,
                null=True,
                verbose_name="Default subtitle language",
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="icons_color",
            field=models.CharField(
                blank=True,
                default="#fff",
                max_length=12,
                null=True,
                verbose_name="Icons color",
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="loop_enabled",
            field=models.BooleanField(
                blank=True,
                default=False,
                null=True,
                verbose_name="Enable loop playback",
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="muted_on_start",
            field=models.BooleanField(
                blank=True, default=False, null=True, verbose_name="Mute video on start"
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="play_button_position",
            field=models.IntegerField(
                blank=True,
                choices=[(0, "Auto"), (1, "Bottom"), (2, "Center")],
                default=0,
                null=True,
                verbose_name="Play button position",
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="primary_color",
            field=models.CharField(
                blank=True,
                default="#03a4eb",
                max_length=12,
                null=True,
                verbose_name="Primary color",
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="show_full_screen_control",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show full controls"
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="show_picture_in_picture_control",
            field=models.BooleanField(
                blank=True,
                default=True,
                null=True,
                verbose_name="Show picture in picture control ",
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="show_play_button",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show play button"
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="show_player_controls",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show player controls"
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="show_progress_bar",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show progress bar"
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="show_speed_control",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show speed controls"
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="show_subtitles",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show subtitles"
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="show_transcript",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show transcript"
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="show_video_quality_control",
            field=models.BooleanField(
                blank=True,
                default=True,
                null=True,
                verbose_name="Show video quality controls",
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="show_video_title",
            field=models.BooleanField(
                blank=True, default=False, null=True, verbose_name="Show video title"
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="show_volume_bar",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show volume"
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="use_global_player_preferences",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="organization",
            name="accent_color",
            field=models.CharField(
                blank=True,
                default="#03a4eb",
                max_length=12,
                null=True,
                verbose_name="Accent color",
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="autoplay_enabled",
            field=models.BooleanField(
                blank=True, default=False, null=True, verbose_name="Enable autoplay"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="background_color",
            field=models.CharField(
                blank=True,
                default="#0F0F0F",
                max_length=12,
                null=True,
                verbose_name="Background color",
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="background_mode_enabled",
            field=models.BooleanField(
                blank=True,
                default=False,
                null=True,
                verbose_name="Enable background mode",
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="default_quality",
            field=models.IntegerField(
                blank=True,
                choices=[
                    (0, "Auto"),
                    (240, "240p"),
                    (360, "360p"),
                    (480, "480p"),
                    (720, "720p"),
                    (1080, "1080p"),
                    (2160, "4k"),
                ],
                default=0,
                null=True,
                verbose_name="Default video quality",
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="default_subtitle_language",
            field=models.CharField(
                blank=True,
                choices=[
                    ("af", "Afrikaans"),
                    ("ar", "Arabic"),
                    ("ar-dz", "Algerian Arabic"),
                    ("ast", "Asturian"),
                    ("az", "Azerbaijani"),
                    ("be", "Belarusian"),
                    ("bg", "Bulgarian"),
                    ("bn", "Bengali"),
                    ("br", "Breton"),
                    ("bs", "Bosnian"),
                    ("ca", "Catalan"),
                    ("cs", "Czech"),
                    ("cy", "Welsh"),
                    ("da", "Danish"),
                    ("de", "German"),
                    ("dsb", "Lower Sorbian"),
                    ("el", "Greek"),
                    ("en", "English"),
                    ("en-au", "Australian English"),
                    ("en-gb", "British English"),
                    ("eo", "Esperanto"),
                    ("es", "Spanish"),
                    ("es-ar", "Argentinian Spanish"),
                    ("es-co", "Colombian Spanish"),
                    ("es-mx", "Mexican Spanish"),
                    ("es-ni", "Nicaraguan Spanish"),
                    ("es-ve", "Venezuelan Spanish"),
                    ("et", "Estonian"),
                    ("eu", "Basque"),
                    ("fa", "Persian"),
                    ("fi", "Finnish"),
                    ("fr", "French"),
                    ("fy", "Frisian"),
                    ("ga", "Irish"),
                    ("gd", "Scottish Gaelic"),
                    ("gl", "Galician"),
                    ("he", "Hebrew"),
                    ("hi", "Hindi"),
                    ("hr", "Croatian"),
                    ("hsb", "Upper Sorbian"),
                    ("hu", "Hungarian"),
                    ("hy", "Armenian"),
                    ("ia", "Interlingua"),
                    ("io", "Ido"),
                    ("id", "Indonesian"),
                    ("ig", "Igbo"),
                    ("is", "Icelandic"),
                    ("it", "Italian"),
                    ("ja", "Japanese"),
                    ("ka", "Georgian"),
                    ("kab", "Kabyle"),
                    ("kk", "Kazakh"),
                    ("km", "Khmer"),
                    ("kn", "Kannada"),
                    ("ko", "Korean"),
                    ("ky", "Kyrgyz"),
                    ("lb", "Luxembourgish"),
                    ("lt", "Lithuanian"),
                    ("lv", "Latvian"),
                    ("mk", "Macedonian"),
                    ("ml", "Malayalam"),
                    ("mn", "Mongolian"),
                    ("mr", "Marathi"),
                    ("ms", "Malay"),
                    ("my", "Burmese"),
                    ("nb", "Norwegian Bokmal"),
                    ("ne", "Nepali"),
                    ("nl", "Dutch"),
                    ("nn", "Norwegian Nynorsk"),
                    ("no", "Norwegian"),
                    ("os", "Ossetic"),
                    ("pa", "Punjabi"),
                    ("pl", "Polish"),
                    ("pt", "Portuguese"),
                    ("pt-br", "Brazilian Portuguese"),
                    ("ro", "Romanian"),
                    ("ru", "Russian"),
                    ("sk", "Slovak"),
                    ("sl", "Slovenian"),
                    ("sq", "Albanian"),
                    ("sr", "Serbian"),
                    ("sr-latn", "Serbian Latin"),
                    ("sv", "Swedish"),
                    ("sw", "Swahili"),
                    ("ta", "Tamil"),
                    ("te", "Telugu"),
                    ("tg", "Tajik"),
                    ("th", "Thai"),
                    ("tk", "Turkmen"),
                    ("tr", "Turkish"),
                    ("tt", "Tatar"),
                    ("udm", "Udmurt"),
                    ("uk", "Ukrainian"),
                    ("ur", "Urdu"),
                    ("uz", "Uzbek"),
                    ("vi", "Vietnamese"),
                    ("zh-hans", "Simplified Chinese"),
                    ("zh-hant", "Traditional Chinese"),
                ],
                default="en",
                max_length=10,
                null=True,
                verbose_name="Default subtitle language",
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="icons_color",
            field=models.CharField(
                blank=True,
                default="#fff",
                max_length=12,
                null=True,
                verbose_name="Icons color",
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="loop_enabled",
            field=models.BooleanField(
                blank=True,
                default=False,
                null=True,
                verbose_name="Enable loop playback",
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="muted_on_start",
            field=models.BooleanField(
                blank=True, default=False, null=True, verbose_name="Mute video on start"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="play_button_position",
            field=models.IntegerField(
                blank=True,
                choices=[(0, "Auto"), (1, "Bottom"), (2, "Center")],
                default=0,
                null=True,
                verbose_name="Play button position",
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="primary_color",
            field=models.CharField(
                blank=True,
                default="#03a4eb",
                max_length=12,
                null=True,
                verbose_name="Primary color",
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="show_full_screen_control",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show full controls"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="show_picture_in_picture_control",
            field=models.BooleanField(
                blank=True,
                default=True,
                null=True,
                verbose_name="Show picture in picture control ",
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="show_play_button",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show play button"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="show_player_controls",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show player controls"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="show_progress_bar",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show progress bar"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="show_speed_control",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show speed controls"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="show_subtitles",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show subtitles"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="show_transcript",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show transcript"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="show_video_quality_control",
            field=models.BooleanField(
                blank=True,
                default=True,
                null=True,
                verbose_name="Show video quality controls",
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="show_video_title",
            field=models.BooleanField(
                blank=True, default=False, null=True, verbose_name="Show video title"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="show_volume_bar",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show volume"
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="accent_color",
            field=models.CharField(
                blank=True,
                default="#03a4eb",
                max_length=12,
                null=True,
                verbose_name="Accent color",
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="autoplay_enabled",
            field=models.BooleanField(
                blank=True, default=False, null=True, verbose_name="Enable autoplay"
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="background_color",
            field=models.CharField(
                blank=True,
                default="#0F0F0F",
                max_length=12,
                null=True,
                verbose_name="Background color",
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="background_mode_enabled",
            field=models.BooleanField(
                blank=True,
                default=False,
                null=True,
                verbose_name="Enable background mode",
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="default_quality",
            field=models.IntegerField(
                blank=True,
                choices=[
                    (0, "Auto"),
                    (240, "240p"),
                    (360, "360p"),
                    (480, "480p"),
                    (720, "720p"),
                    (1080, "1080p"),
                    (2160, "4k"),
                ],
                default=0,
                null=True,
                verbose_name="Default video quality",
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="default_subtitle_language",
            field=models.CharField(
                blank=True,
                choices=[
                    ("af", "Afrikaans"),
                    ("ar", "Arabic"),
                    ("ar-dz", "Algerian Arabic"),
                    ("ast", "Asturian"),
                    ("az", "Azerbaijani"),
                    ("be", "Belarusian"),
                    ("bg", "Bulgarian"),
                    ("bn", "Bengali"),
                    ("br", "Breton"),
                    ("bs", "Bosnian"),
                    ("ca", "Catalan"),
                    ("cs", "Czech"),
                    ("cy", "Welsh"),
                    ("da", "Danish"),
                    ("de", "German"),
                    ("dsb", "Lower Sorbian"),
                    ("el", "Greek"),
                    ("en", "English"),
                    ("en-au", "Australian English"),
                    ("en-gb", "British English"),
                    ("eo", "Esperanto"),
                    ("es", "Spanish"),
                    ("es-ar", "Argentinian Spanish"),
                    ("es-co", "Colombian Spanish"),
                    ("es-mx", "Mexican Spanish"),
                    ("es-ni", "Nicaraguan Spanish"),
                    ("es-ve", "Venezuelan Spanish"),
                    ("et", "Estonian"),
                    ("eu", "Basque"),
                    ("fa", "Persian"),
                    ("fi", "Finnish"),
                    ("fr", "French"),
                    ("fy", "Frisian"),
                    ("ga", "Irish"),
                    ("gd", "Scottish Gaelic"),
                    ("gl", "Galician"),
                    ("he", "Hebrew"),
                    ("hi", "Hindi"),
                    ("hr", "Croatian"),
                    ("hsb", "Upper Sorbian"),
                    ("hu", "Hungarian"),
                    ("hy", "Armenian"),
                    ("ia", "Interlingua"),
                    ("io", "Ido"),
                    ("id", "Indonesian"),
                    ("ig", "Igbo"),
                    ("is", "Icelandic"),
                    ("it", "Italian"),
                    ("ja", "Japanese"),
                    ("ka", "Georgian"),
                    ("kab", "Kabyle"),
                    ("kk", "Kazakh"),
                    ("km", "Khmer"),
                    ("kn", "Kannada"),
                    ("ko", "Korean"),
                    ("ky", "Kyrgyz"),
                    ("lb", "Luxembourgish"),
                    ("lt", "Lithuanian"),
                    ("lv", "Latvian"),
                    ("mk", "Macedonian"),
                    ("ml", "Malayalam"),
                    ("mn", "Mongolian"),
                    ("mr", "Marathi"),
                    ("ms", "Malay"),
                    ("my", "Burmese"),
                    ("nb", "Norwegian Bokmal"),
                    ("ne", "Nepali"),
                    ("nl", "Dutch"),
                    ("nn", "Norwegian Nynorsk"),
                    ("no", "Norwegian"),
                    ("os", "Ossetic"),
                    ("pa", "Punjabi"),
                    ("pl", "Polish"),
                    ("pt", "Portuguese"),
                    ("pt-br", "Brazilian Portuguese"),
                    ("ro", "Romanian"),
                    ("ru", "Russian"),
                    ("sk", "Slovak"),
                    ("sl", "Slovenian"),
                    ("sq", "Albanian"),
                    ("sr", "Serbian"),
                    ("sr-latn", "Serbian Latin"),
                    ("sv", "Swedish"),
                    ("sw", "Swahili"),
                    ("ta", "Tamil"),
                    ("te", "Telugu"),
                    ("tg", "Tajik"),
                    ("th", "Thai"),
                    ("tk", "Turkmen"),
                    ("tr", "Turkish"),
                    ("tt", "Tatar"),
                    ("udm", "Udmurt"),
                    ("uk", "Ukrainian"),
                    ("ur", "Urdu"),
                    ("uz", "Uzbek"),
                    ("vi", "Vietnamese"),
                    ("zh-hans", "Simplified Chinese"),
                    ("zh-hant", "Traditional Chinese"),
                ],
                default="en",
                max_length=10,
                null=True,
                verbose_name="Default subtitle language",
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="icons_color",
            field=models.CharField(
                blank=True,
                default="#fff",
                max_length=12,
                null=True,
                verbose_name="Icons color",
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="loop_enabled",
            field=models.BooleanField(
                blank=True,
                default=False,
                null=True,
                verbose_name="Enable loop playback",
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="muted_on_start",
            field=models.BooleanField(
                blank=True, default=False, null=True, verbose_name="Mute video on start"
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="play_button_position",
            field=models.IntegerField(
                blank=True,
                choices=[(0, "Auto"), (1, "Bottom"), (2, "Center")],
                default=0,
                null=True,
                verbose_name="Play button position",
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="primary_color",
            field=models.CharField(
                blank=True,
                default="#03a4eb",
                max_length=12,
                null=True,
                verbose_name="Primary color",
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="show_full_screen_control",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show full controls"
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="show_picture_in_picture_control",
            field=models.BooleanField(
                blank=True,
                default=True,
                null=True,
                verbose_name="Show picture in picture control ",
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="show_play_button",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show play button"
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="show_player_controls",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show player controls"
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="show_progress_bar",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show progress bar"
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="show_speed_control",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show speed controls"
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="show_subtitles",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show subtitles"
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="show_transcript",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show transcript"
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="show_video_quality_control",
            field=models.BooleanField(
                blank=True,
                default=True,
                null=True,
                verbose_name="Show video quality controls",
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="show_video_title",
            field=models.BooleanField(
                blank=True, default=False, null=True, verbose_name="Show video title"
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="show_volume_bar",
            field=models.BooleanField(
                blank=True, default=True, null=True, verbose_name="Show volume"
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="start_time_seconds",
            field=models.IntegerField(
                blank=True, default=0, null=True, verbose_name="Start time in seconds"
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="use_global_player_preferences",
            field=models.BooleanField(default=True),
        ),
        migrations.CreateModel(
            name="EmbedPreset",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "autoplay_enabled",
                    models.BooleanField(
                        blank=True,
                        default=False,
                        null=True,
                        verbose_name="Enable autoplay",
                    ),
                ),
                (
                    "background_mode_enabled",
                    models.BooleanField(
                        blank=True,
                        default=False,
                        null=True,
                        verbose_name="Enable background mode",
                    ),
                ),
                (
                    "loop_enabled",
                    models.BooleanField(
                        blank=True,
                        default=False,
                        null=True,
                        verbose_name="Enable loop playback",
                    ),
                ),
                (
                    "muted_on_start",
                    models.BooleanField(
                        blank=True,
                        default=False,
                        null=True,
                        verbose_name="Mute video on start",
                    ),
                ),
                (
                    "default_quality",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (0, "Auto"),
                            (240, "240p"),
                            (360, "360p"),
                            (480, "480p"),
                            (720, "720p"),
                            (1080, "1080p"),
                            (2160, "4k"),
                        ],
                        default=0,
                        null=True,
                        verbose_name="Default video quality",
                    ),
                ),
                (
                    "play_button_position",
                    models.IntegerField(
                        blank=True,
                        choices=[(0, "Auto"), (1, "Bottom"), (2, "Center")],
                        default=0,
                        null=True,
                        verbose_name="Play button position",
                    ),
                ),
                (
                    "primary_color",
                    models.CharField(
                        blank=True,
                        default="#03a4eb",
                        max_length=12,
                        null=True,
                        verbose_name="Primary color",
                    ),
                ),
                (
                    "icons_color",
                    models.CharField(
                        blank=True,
                        default="#fff",
                        max_length=12,
                        null=True,
                        verbose_name="Icons color",
                    ),
                ),
                (
                    "accent_color",
                    models.CharField(
                        blank=True,
                        default="#03a4eb",
                        max_length=12,
                        null=True,
                        verbose_name="Accent color",
                    ),
                ),
                (
                    "background_color",
                    models.CharField(
                        blank=True,
                        default="#0F0F0F",
                        max_length=12,
                        null=True,
                        verbose_name="Background color",
                    ),
                ),
                (
                    "show_player_controls",
                    models.BooleanField(
                        blank=True,
                        default=True,
                        null=True,
                        verbose_name="Show player controls",
                    ),
                ),
                (
                    "show_video_title",
                    models.BooleanField(
                        blank=True,
                        default=False,
                        null=True,
                        verbose_name="Show video title",
                    ),
                ),
                (
                    "show_subtitles",
                    models.BooleanField(
                        blank=True,
                        default=True,
                        null=True,
                        verbose_name="Show subtitles",
                    ),
                ),
                (
                    "show_play_button",
                    models.BooleanField(
                        blank=True,
                        default=True,
                        null=True,
                        verbose_name="Show play button",
                    ),
                ),
                (
                    "show_progress_bar",
                    models.BooleanField(
                        blank=True,
                        default=True,
                        null=True,
                        verbose_name="Show progress bar",
                    ),
                ),
                (
                    "show_volume_bar",
                    models.BooleanField(
                        blank=True, default=True, null=True, verbose_name="Show volume"
                    ),
                ),
                (
                    "show_transcript",
                    models.BooleanField(
                        blank=True,
                        default=True,
                        null=True,
                        verbose_name="Show transcript",
                    ),
                ),
                (
                    "show_video_quality_control",
                    models.BooleanField(
                        blank=True,
                        default=True,
                        null=True,
                        verbose_name="Show video quality controls",
                    ),
                ),
                (
                    "show_speed_control",
                    models.BooleanField(
                        blank=True,
                        default=True,
                        null=True,
                        verbose_name="Show speed controls",
                    ),
                ),
                (
                    "show_picture_in_picture_control",
                    models.BooleanField(
                        blank=True,
                        default=True,
                        null=True,
                        verbose_name="Show picture in picture control ",
                    ),
                ),
                (
                    "show_full_screen_control",
                    models.BooleanField(
                        blank=True,
                        default=True,
                        null=True,
                        verbose_name="Show full controls",
                    ),
                ),
                (
                    "default_subtitle_language",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("af", "Afrikaans"),
                            ("ar", "Arabic"),
                            ("ar-dz", "Algerian Arabic"),
                            ("ast", "Asturian"),
                            ("az", "Azerbaijani"),
                            ("be", "Belarusian"),
                            ("bg", "Bulgarian"),
                            ("bn", "Bengali"),
                            ("br", "Breton"),
                            ("bs", "Bosnian"),
                            ("ca", "Catalan"),
                            ("cs", "Czech"),
                            ("cy", "Welsh"),
                            ("da", "Danish"),
                            ("de", "German"),
                            ("dsb", "Lower Sorbian"),
                            ("el", "Greek"),
                            ("en", "English"),
                            ("en-au", "Australian English"),
                            ("en-gb", "British English"),
                            ("eo", "Esperanto"),
                            ("es", "Spanish"),
                            ("es-ar", "Argentinian Spanish"),
                            ("es-co", "Colombian Spanish"),
                            ("es-mx", "Mexican Spanish"),
                            ("es-ni", "Nicaraguan Spanish"),
                            ("es-ve", "Venezuelan Spanish"),
                            ("et", "Estonian"),
                            ("eu", "Basque"),
                            ("fa", "Persian"),
                            ("fi", "Finnish"),
                            ("fr", "French"),
                            ("fy", "Frisian"),
                            ("ga", "Irish"),
                            ("gd", "Scottish Gaelic"),
                            ("gl", "Galician"),
                            ("he", "Hebrew"),
                            ("hi", "Hindi"),
                            ("hr", "Croatian"),
                            ("hsb", "Upper Sorbian"),
                            ("hu", "Hungarian"),
                            ("hy", "Armenian"),
                            ("ia", "Interlingua"),
                            ("io", "Ido"),
                            ("id", "Indonesian"),
                            ("ig", "Igbo"),
                            ("is", "Icelandic"),
                            ("it", "Italian"),
                            ("ja", "Japanese"),
                            ("ka", "Georgian"),
                            ("kab", "Kabyle"),
                            ("kk", "Kazakh"),
                            ("km", "Khmer"),
                            ("kn", "Kannada"),
                            ("ko", "Korean"),
                            ("ky", "Kyrgyz"),
                            ("lb", "Luxembourgish"),
                            ("lt", "Lithuanian"),
                            ("lv", "Latvian"),
                            ("mk", "Macedonian"),
                            ("ml", "Malayalam"),
                            ("mn", "Mongolian"),
                            ("mr", "Marathi"),
                            ("ms", "Malay"),
                            ("my", "Burmese"),
                            ("nb", "Norwegian Bokmal"),
                            ("ne", "Nepali"),
                            ("nl", "Dutch"),
                            ("nn", "Norwegian Nynorsk"),
                            ("no", "Norwegian"),
                            ("os", "Ossetic"),
                            ("pa", "Punjabi"),
                            ("pl", "Polish"),
                            ("pt", "Portuguese"),
                            ("pt-br", "Brazilian Portuguese"),
                            ("ro", "Romanian"),
                            ("ru", "Russian"),
                            ("sk", "Slovak"),
                            ("sl", "Slovenian"),
                            ("sq", "Albanian"),
                            ("sr", "Serbian"),
                            ("sr-latn", "Serbian Latin"),
                            ("sv", "Swedish"),
                            ("sw", "Swahili"),
                            ("ta", "Tamil"),
                            ("te", "Telugu"),
                            ("tg", "Tajik"),
                            ("th", "Thai"),
                            ("tk", "Turkmen"),
                            ("tr", "Turkish"),
                            ("tt", "Tatar"),
                            ("udm", "Udmurt"),
                            ("uk", "Ukrainian"),
                            ("ur", "Urdu"),
                            ("uz", "Uzbek"),
                            ("vi", "Vietnamese"),
                            ("zh-hans", "Simplified Chinese"),
                            ("zh-hant", "Traditional Chinese"),
                        ],
                        default="en",
                        max_length=10,
                        null=True,
                        verbose_name="Default subtitle language",
                    ),
                ),
                (
                    "title",
                    models.CharField(max_length=255, verbose_name="Preset title"),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "Embed Preset",
                "verbose_name_plural": "Embed Presets",
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddField(
            model_name="livestream",
            name="embed_preset",
            field=django_multitenant.fields.TenantForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="live_streams",
                to="app.embedpreset",
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="embed_preset",
            field=django_multitenant.fields.TenantForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="videos",
                to="app.embedpreset",
            ),
        ),
        migrations.AddConstraint(
            model_name="embedpreset",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_embedpreset_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="embedpreset",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_embedpreset_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="embedpreset",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_embedpreset_organization_uuid",
            ),
        ),
        migrations.AddConstraint(
            model_name="embedpreset",
            constraint=models.UniqueConstraint(
                fields=("organization", "title"),
                name="unique_embedpreset_organization_embed_preset",
            ),
        ),
        migrations.AlterField(
            model_name="embedpreset",
            name="default_quality",
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[
                    (0, "Auto"),
                    (240, "240p"),
                    (360, "360p"),
                    (480, "480p"),
                    (720, "720p"),
                    (1080, "1080p"),
                    (2160, "4k"),
                ],
                default=0,
                null=True,
                verbose_name="Default video quality",
            ),
        ),
        migrations.AlterField(
            model_name="embedpreset",
            name="play_button_position",
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[(0, "Auto"), (1, "Bottom"), (2, "Center")],
                default=0,
                null=True,
                verbose_name="Play button position",
            ),
        ),
        migrations.AlterField(
            model_name="livestream",
            name="default_quality",
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[
                    (0, "Auto"),
                    (240, "240p"),
                    (360, "360p"),
                    (480, "480p"),
                    (720, "720p"),
                    (1080, "1080p"),
                    (2160, "4k"),
                ],
                default=0,
                null=True,
                verbose_name="Default video quality",
            ),
        ),
        migrations.AlterField(
            model_name="livestream",
            name="play_button_position",
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[(0, "Auto"), (1, "Bottom"), (2, "Center")],
                default=0,
                null=True,
                verbose_name="Play button position",
            ),
        ),
        migrations.AlterField(
            model_name="organization",
            name="default_quality",
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[
                    (0, "Auto"),
                    (240, "240p"),
                    (360, "360p"),
                    (480, "480p"),
                    (720, "720p"),
                    (1080, "1080p"),
                    (2160, "4k"),
                ],
                default=0,
                null=True,
                verbose_name="Default video quality",
            ),
        ),
        migrations.AlterField(
            model_name="organization",
            name="play_button_position",
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[(0, "Auto"), (1, "Bottom"), (2, "Center")],
                default=0,
                null=True,
                verbose_name="Play button position",
            ),
        ),
        migrations.AlterField(
            model_name="video",
            name="default_quality",
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[
                    (0, "Auto"),
                    (240, "240p"),
                    (360, "360p"),
                    (480, "480p"),
                    (720, "720p"),
                    (1080, "1080p"),
                    (2160, "4k"),
                ],
                default=0,
                null=True,
                verbose_name="Default video quality",
            ),
        ),
        migrations.AlterField(
            model_name="video",
            name="play_button_position",
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[(0, "Auto"), (1, "Bottom"), (2, "Center")],
                default=0,
                null=True,
                verbose_name="Play button position",
            ),
        ),
        migrations.AlterField(
            model_name="video",
            name="start_time_seconds",
            field=models.PositiveSmallIntegerField(
                blank=True, default=0, null=True, verbose_name="Start time in seconds"
            ),
        ),
    ]
