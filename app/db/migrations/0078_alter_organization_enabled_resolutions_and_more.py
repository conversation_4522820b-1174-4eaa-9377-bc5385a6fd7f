# Generated by Django 4.0.8 on 2024-07-04 12:39

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0077_organization_enabled_resolutions"),
    ]

    operations = [
        migrations.AlterField(
            model_name="organization",
            name="enabled_resolutions",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    choices=[
                        ("4k", "4k"),
                        ("1080p", "1080p"),
                        ("720p", "720p"),
                        ("480p", "480p"),
                        ("360p", "360p"),
                        ("240p", "240p"),
                    ],
                    max_length=10,
                ),
                blank=True,
                default=["720p", "480p", "360p", "240p"],
                null=True,
                size=None,
            ),
        ),
        migrations.AlterField(
            model_name="template",
            name="resolutions",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.PositiveSmallIntegerField(
                    choices=[
                        (0, "240p"),
                        (1, "360p"),
                        (2, "480p"),
                        (3, "720p"),
                        (4, "1080p"),
                        (5, "4k"),
                    ]
                ),
                default=list,
                size=None,
            ),
        ),
        migrations.AlterField(
            model_name="video",
            name="resolutions",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.PositiveSmallIntegerField(
                    choices=[
                        (0, "240p"),
                        (1, "360p"),
                        (2, "480p"),
                        (3, "720p"),
                        (4, "1080p"),
                        (5, "4k"),
                    ]
                ),
                default=list,
                size=None,
            ),
        ),
    ]
