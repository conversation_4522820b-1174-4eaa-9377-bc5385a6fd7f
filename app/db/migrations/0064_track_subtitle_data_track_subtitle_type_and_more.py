# Generated by Django 4.0.8 on 2024-02-13 03:57

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0063_livestream_has_moved"),
    ]

    operations = [
        migrations.AddField(
            model_name="track",
            name="subtitle_data",
            field=models.JSONField(
                blank=True, null=True, verbose_name="Subtitle generation related data"
            ),
        ),
        migrations.AddField(
            model_name="track",
            name="subtitle_type",
            field=models.IntegerField(
                choices=[(0, "Auto Generated"), (1, "Uploaded")], default=1
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="generate_subtitle",
            field=models.BooleanField(
                blank=True,
                default=False,
                help_text="Enable to generate subtitle for the video.",
                null=True,
            ),
        ),
    ]
