# Generated by Django 4.0.8 on 2025-06-02 12:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0088_livestream_store_recorded_video'),
    ]

    operations = [
        migrations.AlterField(
            model_name='importedvideo',
            name='source',
            field=models.PositiveSmallIntegerField(choices=[(0, 'Vimeo'), (1, 'Mux'), (2, 'Gumlet'), (3, 'Vdocipher'), (4, 'Appsquadz'), (5, 'Jwplayer'), (6, 'Teachable')], db_index=True, default=0),
        ),
        migrations.AlterField(
            model_name='livestreamevent',
            name='type',
            field=models.PositiveSmallIntegerField(choices=[(0, 'Created'), (1, 'On Pubish'), (2, 'On Pubish Done'), (3, 'Stopped'), (4, 'Recording'), (5, 'Completed'), (6, 'Error')], verbose_name='Event Type'),
        ),
        migrations.AlterField(
            model_name='livestreamusage',
            name='server_provider',
            field=models.PositiveSmallIntegerField(choices=[(1, 'Digitalocean'), (2, 'Aws'), (3, 'Linode')], db_index=True, default=1, verbose_name='Status'),
        ),
    ]
