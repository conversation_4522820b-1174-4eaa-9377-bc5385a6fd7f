# Generated by Django 4.0.8 on 2024-03-14 13:06

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0073_livestream_latency"),
    ]

    operations = [
        migrations.AddField(
            model_name="assetusage",
            name="subtitle_generation_cost",
            field=models.IntegerField(
                default=0,
                null=True,
                verbose_name="Subtitles generation cost in dollars",
            ),
        ),
        migrations.AlterField(
            model_name="assetusage",
            name="subtitle_generation_cost",
            field=models.PositiveBigIntegerField(
                default=0,
                null=True,
                verbose_name="Subtitles generation cost in dollars",
            ),
        ),
    ]
