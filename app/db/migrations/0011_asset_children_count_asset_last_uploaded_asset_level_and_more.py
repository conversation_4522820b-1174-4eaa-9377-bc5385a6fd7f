# Generated by Django 4.0.8 on 2023-03-02 13:04

import app.models.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0010_importedfolder_video_transmux_only_importedvideo_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="asset",
            name="children_count",
            field=models.BigIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="asset",
            name="last_uploaded",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="asset",
            name="level",
            field=models.IntegerField(default=0, editable=False),
        ),
        migrations.AddField(
            model_name="asset",
            name="lft",
            field=models.IntegerField(default=0, editable=False),
        ),
        migrations.AddField(
            model_name="asset",
            name="parent",
            field=app.models.fields.TenantTreeForeign<PERSON>ey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="children",
                to="app.asset",
            ),
        ),
        migrations.AddField(
            model_name="asset",
            name="rght",
            field=models.IntegerField(default=0, editable=False),
        ),
        migrations.AddField(
            model_name="asset",
            name="tree_id",
            field=models.IntegerField(db_index=True, default=0, editable=False),
        ),
        migrations.AlterField(
            model_name="asset",
            name="type",
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, "video"),
                    (1, "image"),
                    (2, "livestream"),
                    (3, "audio"),
                    (4, "folder"),
                ],
                db_index=True,
                default=0,
            ),
        ),
    ]
