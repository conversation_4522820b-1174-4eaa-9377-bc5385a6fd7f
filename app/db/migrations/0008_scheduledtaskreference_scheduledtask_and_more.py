# Generated by Django 4.0.8 on 2023-01-18 16:35

import app.db.constraints.composite_key
import app.db.constraints.distribute
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_multitenant.mixins
import model_utils.fields
import picklefield.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("app", "0007_livestream_livestreamevent_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ScheduledTaskReference",
            fields=[
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "task_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("organization_uuid", models.CharField(editable=False, max_length=12)),
                (
                    "status",
                    models.PositiveSmallIntegerField(
                        choices=[(0, "Waiting"), (1, "Processing"), (2, "Queued")],
                        default=0,
                    ),
                ),
                ("run_at", models.DateTimeField()),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="ScheduledTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("reference_uuid", models.UUIDField(db_index=True, editable=False)),
                (
                    "pickled_payload",
                    picklefield.fields.PickledObjectField(editable=False),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "ordering": ("-created",),
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddConstraint(
            model_name="scheduledtask",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_scheduledtask_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="scheduledtask",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_scheduledtask_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="scheduledtask",
            constraint=models.UniqueConstraint(
                fields=("organization", "reference_uuid"),
                name="unique_scheduledtask_organization_uuid",
            ),
        ),
    ]
