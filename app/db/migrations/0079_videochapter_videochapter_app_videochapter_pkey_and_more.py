# Generated by Django 4.0.8 on 2024-07-06 04:40

import app.db.constraints.composite_key
import app.db.constraints.distribute
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_multitenant.fields
import django_multitenant.mixins
import model_utils.fields


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0078_alter_organization_enabled_resolutions_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="VideoChapter",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "label",
                    models.CharField(
                        blank=True,
                        max_length=50,
                        null=True,
                        verbose_name="Chapter Label",
                    ),
                ),
                (
                    "start_time",
                    models.DurationField(
                        blank=True, null=True, verbose_name="Chapter Starting time"
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
                (
                    "video",
                    django_multitenant.fields.TenantForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chapters",
                        to="app.video",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddConstraint(
            model_name="videochapter",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_videochapter_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="videochapter",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_videochapter_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
    ]
