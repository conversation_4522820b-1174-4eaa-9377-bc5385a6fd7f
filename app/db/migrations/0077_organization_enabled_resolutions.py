# Generated by Django 4.0.8 on 2024-06-24 11:29

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0076_alter_organization_uuid_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="organization",
            name="enabled_resolutions",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    choices=[
                        ("1080p", "1080p"),
                        ("720p", "720p"),
                        ("480p", "480p"),
                        ("360p", "360p"),
                        ("240p", "240p"),
                    ],
                    max_length=10,
                ),
                blank=True,
                default=["720p", "480p", "360p", "240p"],
                null=True,
                size=None,
            ),
        ),
    ]
