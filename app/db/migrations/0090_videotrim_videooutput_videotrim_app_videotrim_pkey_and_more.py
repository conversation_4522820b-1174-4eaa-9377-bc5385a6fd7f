# Generated by Django 4.0.8 on 2025-06-09 13:24

import app.db.constraints.composite_key
import app.db.constraints.distribute
import app.models.fields
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_multitenant.fields
import django_multitenant.mixins
import model_utils.fields
import uuid


class Migration(migrations.Migration):
    atomic = False
    dependencies = [
        ("app", "0089_alter_importedvideo_source_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="VideoTrim",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "start_time",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Trim start time in seconds",
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "end_time",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Trim end time in seconds",
                        null=True,
                        validators=[django.core.validators.MinValueValidator(1)],
                    ),
                ),
                (
                    "status",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "Pending"),
                            (2, "Processing"),
                            (3, "Completed"),
                            (4, "Failed"),
                            (5, "Reverted"),
                        ],
                        default=1,
                        help_text="Current status of the trimming job",
                    ),
                ),
                (
                    "background_task_id",
                    models.UUIDField(
                        blank=True,
                        help_text="Async task ID associated with the background processing job",
                        null=True,
                    ),
                ),
                (
                    "created_by",
                    app.models.fields.CreatedByField(
                        blank=True,
                        help_text="User who initiated the trim operation",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
                (
                    "video",
                    django_multitenant.fields.TenantForeignKey(
                        help_text="Trimmed video asset reference",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="trims",
                        to="app.video",
                    ),
                ),
            ],
            options={
                "verbose_name": "Video Trim",
                "verbose_name_plural": "Video Trims",
                "ordering": ["-created"],
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name="VideoOutput",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "output_type",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "HLS"),
                            (2, "DASH"),
                            (3, "Trimmed HLS"),
                            (4, "Trimmed DASH"),
                        ],
                        help_text="Type of output (e.g., HLS, DASH)",
                    ),
                ),
                (
                    "codec",
                    models.CharField(
                        help_text="Video codec used, e.g., h264 or h265", max_length=16
                    ),
                ),
                (
                    "duration",
                    models.PositiveIntegerField(
                        help_text="Duration of the video output in seconds"
                    ),
                ),
                (
                    "url",
                    models.URLField(
                        help_text="URL to access this video output", max_length=1024
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Indicates whether this output is currently active",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
                (
                    "video",
                    django_multitenant.fields.TenantForeignKey(
                        help_text="Original video associated with this output",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="outputs",
                        to="app.video",
                    ),
                ),
            ],
            options={
                "verbose_name": "Video Output",
                "verbose_name_plural": "Video Outputs",
                "ordering": ["-created"],
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddConstraint(
            model_name="videotrim",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_videotrim_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="videotrim",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_videotrim_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="videotrim",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_videotrim_organization_uuid",
            ),
        ),
        migrations.AddConstraint(
            model_name="videooutput",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_videooutput_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="videooutput",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_videooutput_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="videooutput",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_videooutput_organization_uuid",
            ),
        ),
        migrations.AddConstraint(
            model_name="videooutput",
            constraint=models.UniqueConstraint(
                fields=("organization", "video", "output_type", "codec"),
                name="unique_video_output_per_type_codec",
            ),
        ),
    ]
