# Generated by Django 4.0.8 on 2023-06-27 12:56

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0022_livestream_transcode_recorded_video"),
    ]

    operations = [
        migrations.AddField(
            model_name="organization",
            name="cdn_access_key_id",
            field=models.Char<PERSON>ield(
                blank=True, max_length=256, null=True, verbose_name="CDN Access Key ID"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="cdn_expire_in_3_seconds_cache_policy_id",
            field=models.Char<PERSON>ield(
                blank=True,
                max_length=256,
                null=True,
                verbose_name="CDN Cache Policy ID (Expires in 3 Seconds)",
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="cdn_one_year_cache_policy_id",
            field=models.CharField(
                blank=True,
                max_length=256,
                null=True,
                verbose_name="CDN One-Year Cache Policy ID",
            ),
        ),
        migrations.Add<PERSON>ield(
            model_name="organization",
            name="cdn_secret_access_key",
            field=models.Char<PERSON><PERSON>(
                blank=True,
                max_length=256,
                null=True,
                verbose_name="CDN Secret Access Key",
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="storage_access_key_id",
            field=models.CharField(
                blank=True,
                max_length=256,
                null=True,
                verbose_name="Storage Access Key ID",
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="storage_region",
            field=models.CharField(
                blank=True, max_length=256, null=True, verbose_name="Storage Region"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="storage_secret_access_key",
            field=models.CharField(
                blank=True,
                max_length=256,
                null=True,
                verbose_name="Storage Secret Access Key",
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="storage_vendor",
            field=models.IntegerField(
                blank=True, default=2, null=True, verbose_name="Storage Vendor"
            ),
        ),
    ]
