# Generated by Django 4.0.8 on 2022-12-01 14:04

import app.db.constraints.composite_key
import app.db.constraints.distribute
from app.db.operations.citus import CitusExtension
import app.models.fields
from django.conf import settings
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_multitenant.fields
import django_multitenant.mixins
import model_utils.fields
import taggit.managers
import uuid


class Migration(migrations.Migration):
    initial = True

    atomic = False
    dependencies = []

    operations = [
        CitusExtension(),
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                ("email", models.EmailField(max_length=254, unique=True)),
                ("name", models.CharField(blank=True, max_length=254, null=True)),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                ("is_staff", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("last_login", models.DateTimeField(blank=True, null=True)),
                (
                    "current_organization_uuid",
                    models.CharField(
                        blank=True, editable=False, max_length=12, null=True
                    ),
                ),
            ],
            options={
                "verbose_name": "user",
                "verbose_name_plural": "users",
            },
        ),
        migrations.CreateModel(
            name="Asset",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "title",
                    models.CharField(
                        blank=True, db_index=True, max_length=1024, null=True
                    ),
                ),
                (
                    "type",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, "video"),
                            (1, "image"),
                            (2, "livestream"),
                            (3, "audio"),
                        ],
                        db_index=True,
                        default=0,
                    ),
                ),
                ("bytes", models.PositiveBigIntegerField(blank=True, null=True)),
                (
                    "created_by",
                    app.models.fields.CreatedByField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name="Membership",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("email", models.EmailField(blank=True, max_length=75, null=True)),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name="Organization",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("name", models.CharField(max_length=254)),
                ("uuid", models.CharField(editable=False, max_length=12)),
                (
                    "created_by",
                    app.models.fields.CreatedByField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "members",
                    models.ManyToManyField(
                        related_name="organizations",
                        through="app.Membership",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name="Tag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("name", models.CharField(max_length=100, verbose_name="name")),
                (
                    "slug",
                    models.SlugField(
                        allow_unicode=True, max_length=100, verbose_name="slug"
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "Tag",
                "verbose_name_plural": "Tags",
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name="Video",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "format",
                    models.PositiveSmallIntegerField(
                        choices=[(0, "abr"), (1, "mp4")], default=0
                    ),
                ),
                (
                    "resolutions",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.PositiveSmallIntegerField(
                            choices=[
                                (0, "240p"),
                                (1, "360p"),
                                (2, "480p"),
                                (3, "720p"),
                                (4, "1080p"),
                            ]
                        ),
                        default=list,
                        size=None,
                    ),
                ),
                (
                    "video_codec",
                    models.PositiveSmallIntegerField(
                        choices=[(0, "h264"), (1, "h265")], default=0
                    ),
                ),
                (
                    "audio_codec",
                    models.PositiveSmallIntegerField(choices=[(0, "aac")], default=0),
                ),
                ("enable_drm", models.BooleanField(default=False)),
                ("thumbnail_duration", models.PositiveBigIntegerField(null=True)),
                (
                    "thumbnail_format",
                    models.PositiveSmallIntegerField(
                        choices=[(0, "png"), (1, "jpg"), (2, "jpeg"), (3, "webp")],
                        default=0,
                    ),
                ),
                (
                    "padding_top",
                    models.PositiveSmallIntegerField(blank=True, null=True),
                ),
                (
                    "padding_bottom",
                    models.PositiveSmallIntegerField(blank=True, null=True),
                ),
                (
                    "padding_left",
                    models.PositiveSmallIntegerField(blank=True, null=True),
                ),
                (
                    "padding_right",
                    models.PositiveSmallIntegerField(blank=True, null=True),
                ),
                (
                    "padding_color",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "status",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, "Not Started"),
                            (1, "Transcoding"),
                            (2, "Completed"),
                            (3, "Error"),
                        ],
                        db_index=True,
                        default=0,
                    ),
                ),
                ("progress", models.PositiveSmallIntegerField(default=0)),
                (
                    "playback_url",
                    models.CharField(blank=True, max_length=1024, null=True),
                ),
                ("dash_url", models.CharField(blank=True, max_length=1024, null=True)),
                (
                    "thumbnails",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=1024),
                        blank=True,
                        default=list,
                        null=True,
                        size=None,
                    ),
                ),
                (
                    "preview_thumbnail_url",
                    models.CharField(blank=True, max_length=1024, null=True),
                ),
                (
                    "asset",
                    app.models.fields.TenantOneToOneField(
                        on_delete=django.db.models.deletion.CASCADE, to="app.asset"
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name="Webhook",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("url", models.URLField()),
                ("secret_token", models.TextField(blank=True, null=True)),
                (
                    "created_by",
                    app.models.fields.CreatedByField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name="VideoInput",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("url", models.URLField(max_length=1024)),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
                (
                    "video",
                    django_multitenant.fields.TenantForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="inputs",
                        to="app.video",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name="Track",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "type",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "Video"),
                            (2, "Audio"),
                            (3, "Playlist"),
                            (4, "Thumbnail"),
                            (5, "Subtitle"),
                            (6, "Preview Thumbnail"),
                        ]
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("width", models.PositiveSmallIntegerField(null=True)),
                ("height", models.PositiveSmallIntegerField(null=True)),
                ("bytes", models.PositiveBigIntegerField(null=True)),
                ("duration", models.PositiveBigIntegerField(null=True)),
                (
                    "language",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("af", "Afrikaans"),
                            ("ar", "Arabic"),
                            ("ar-dz", "Algerian Arabic"),
                            ("ast", "Asturian"),
                            ("az", "Azerbaijani"),
                            ("be", "Belarusian"),
                            ("bg", "Bulgarian"),
                            ("bn", "Bengali"),
                            ("br", "Breton"),
                            ("bs", "Bosnian"),
                            ("ca", "Catalan"),
                            ("cs", "Czech"),
                            ("cy", "Welsh"),
                            ("da", "Danish"),
                            ("de", "German"),
                            ("dsb", "Lower Sorbian"),
                            ("el", "Greek"),
                            ("en", "English"),
                            ("en-au", "Australian English"),
                            ("en-gb", "British English"),
                            ("eo", "Esperanto"),
                            ("es", "Spanish"),
                            ("es-ar", "Argentinian Spanish"),
                            ("es-co", "Colombian Spanish"),
                            ("es-mx", "Mexican Spanish"),
                            ("es-ni", "Nicaraguan Spanish"),
                            ("es-ve", "Venezuelan Spanish"),
                            ("et", "Estonian"),
                            ("eu", "Basque"),
                            ("fa", "Persian"),
                            ("fi", "Finnish"),
                            ("fr", "French"),
                            ("fy", "Frisian"),
                            ("ga", "Irish"),
                            ("gd", "Scottish Gaelic"),
                            ("gl", "Galician"),
                            ("he", "Hebrew"),
                            ("hi", "Hindi"),
                            ("hr", "Croatian"),
                            ("hsb", "Upper Sorbian"),
                            ("hu", "Hungarian"),
                            ("hy", "Armenian"),
                            ("ia", "Interlingua"),
                            ("io", "Ido"),
                            ("id", "Indonesian"),
                            ("ig", "Igbo"),
                            ("is", "Icelandic"),
                            ("it", "Italian"),
                            ("ja", "Japanese"),
                            ("ka", "Georgian"),
                            ("kab", "Kabyle"),
                            ("kk", "Kazakh"),
                            ("km", "Khmer"),
                            ("kn", "Kannada"),
                            ("ko", "Korean"),
                            ("ky", "Kyrgyz"),
                            ("lb", "Luxembourgish"),
                            ("lt", "Lithuanian"),
                            ("lv", "Latvian"),
                            ("mk", "Macedonian"),
                            ("ml", "Malayalam"),
                            ("mn", "Mongolian"),
                            ("mr", "Marathi"),
                            ("ms", "Malay"),
                            ("my", "Burmese"),
                            ("nb", "Norwegian Bokmal"),
                            ("ne", "Nepali"),
                            ("nl", "Dutch"),
                            ("nn", "Norwegian Nynorsk"),
                            ("no", "Norwegian"),
                            ("os", "Ossetic"),
                            ("pa", "Punjabi"),
                            ("pl", "Polish"),
                            ("pt", "Portuguese"),
                            ("pt-br", "Brazilian Portuguese"),
                            ("ro", "Romanian"),
                            ("ru", "Russian"),
                            ("sk", "Slovak"),
                            ("sl", "Slovenian"),
                            ("sq", "Albanian"),
                            ("sr", "Serbian"),
                            ("sr-latn", "Serbian Latin"),
                            ("sv", "Swedish"),
                            ("sw", "Swahili"),
                            ("ta", "Tamil"),
                            ("te", "Telugu"),
                            ("tg", "Tajik"),
                            ("th", "Thai"),
                            ("tk", "Turkmen"),
                            ("tr", "Turkish"),
                            ("tt", "Tatar"),
                            ("udm", "Udmurt"),
                            ("uk", "Ukrainian"),
                            ("ur", "Urdu"),
                            ("uz", "Uzbek"),
                            ("vi", "Vietnamese"),
                            ("zh-hans", "Simplified Chinese"),
                            ("zh-hant", "Traditional Chinese"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
                (
                    "video",
                    django_multitenant.fields.TenantForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tracks",
                        to="app.video",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name="Template",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "format",
                    models.PositiveSmallIntegerField(
                        choices=[(0, "abr"), (1, "mp4")], default=0
                    ),
                ),
                (
                    "resolutions",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.PositiveSmallIntegerField(
                            choices=[
                                (0, "240p"),
                                (1, "360p"),
                                (2, "480p"),
                                (3, "720p"),
                                (4, "1080p"),
                            ]
                        ),
                        default=list,
                        size=None,
                    ),
                ),
                (
                    "video_codec",
                    models.PositiveSmallIntegerField(
                        choices=[(0, "h264"), (1, "h265")], default=0
                    ),
                ),
                (
                    "audio_codec",
                    models.PositiveSmallIntegerField(choices=[(0, "aac")], default=0),
                ),
                ("enable_drm", models.BooleanField(default=False)),
                ("thumbnail_duration", models.PositiveBigIntegerField(null=True)),
                (
                    "thumbnail_format",
                    models.PositiveSmallIntegerField(
                        choices=[(0, "png"), (1, "jpg"), (2, "jpeg"), (3, "webp")],
                        default=0,
                    ),
                ),
                (
                    "padding_top",
                    models.PositiveSmallIntegerField(blank=True, null=True),
                ),
                (
                    "padding_bottom",
                    models.PositiveSmallIntegerField(blank=True, null=True),
                ),
                (
                    "padding_left",
                    models.PositiveSmallIntegerField(blank=True, null=True),
                ),
                (
                    "padding_right",
                    models.PositiveSmallIntegerField(blank=True, null=True),
                ),
                (
                    "padding_color",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("name", models.CharField(max_length=255)),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name="TaggedAsset",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "content_object",
                    django_multitenant.fields.TenantForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="app.asset"
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
                (
                    "tag",
                    django_multitenant.fields.TenantForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(app_label)s_%(class)s_items",
                        to="app.tag",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddField(
            model_name="membership",
            name="organization",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s",
                related_query_name="%(class)ss",
                to="app.organization",
            ),
        ),
        migrations.AddField(
            model_name="membership",
            name="user",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="memberships",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.CreateModel(
            name="Folder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("name", models.CharField(max_length=256)),
                ("size", models.PositiveBigIntegerField(default=0)),
                ("assets_count", models.PositiveBigIntegerField(default=0)),
                ("last_uploaded", models.DateTimeField(blank=True, null=True)),
                ("lft", models.PositiveIntegerField(editable=False)),
                ("rght", models.PositiveIntegerField(editable=False)),
                ("tree_id", models.PositiveIntegerField(db_index=True, editable=False)),
                ("level", models.PositiveIntegerField(editable=False)),
                (
                    "created_by",
                    app.models.fields.CreatedByField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
                (
                    "parent",
                    app.models.fields.TenantTreeForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="children",
                        to="app.folder",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name="AuthToken",
            fields=[
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "digest",
                    models.CharField(max_length=128, primary_key=True, serialize=False),
                ),
                ("token_key", models.CharField(db_index=True, max_length=25)),
                ("expiry", models.DateTimeField(blank=True, null=True)),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="auth_token_set",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "AuthToken",
                "verbose_name_plural": "AuthTokens",
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddField(
            model_name="asset",
            name="folder",
            field=django_multitenant.fields.TenantForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="assets",
                to="app.folder",
            ),
        ),
        migrations.AddField(
            model_name="asset",
            name="organization",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s",
                related_query_name="%(class)ss",
                to="app.organization",
            ),
        ),
        migrations.AddField(
            model_name="asset",
            name="tags",
            field=taggit.managers.TaggableManager(
                blank=True,
                help_text="A comma-separated list of tags.",
                through="app.TaggedAsset",
                to="app.Tag",
                verbose_name="Tags",
            ),
        ),
        migrations.AddConstraint(
            model_name="user",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                name="app_user_distribute", reference=True, reverse_ignore=False
            ),
        ),
        migrations.AddConstraint(
            model_name="webhook",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_webhook_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="webhook",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                name="app_webhook_distribute",
                distribution_column="organization_id",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="webhook",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"), name="unique_webhook_organization_uuid"
            ),
        ),
        migrations.AddConstraint(
            model_name="videoinput",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_videoinput_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="videoinput",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                name="app_videoinput_distribute",
                distribution_column="organization_id",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="video",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_video_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="video",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                name="app_video_distribute",
                distribution_column="organization_id",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="video",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"), name="unique_video_organization_uuid"
            ),
        ),
        migrations.AddConstraint(
            model_name="video",
            constraint=models.UniqueConstraint(
                fields=("organization", "asset"), name="unique_video_organization_asset"
            ),
        ),
        migrations.AddConstraint(
            model_name="track",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_track_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="track",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                name="app_track_distribute",
                distribution_column="organization_id",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="track",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"), name="unique_track_organization_uuid"
            ),
        ),
        migrations.AddConstraint(
            model_name="template",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_template_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="template",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                name="app_template_distribute",
                distribution_column="organization_id",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="template",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_template_organization_uuid",
            ),
        ),
        migrations.AddConstraint(
            model_name="taggedasset",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_taggedasset_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="taggedasset",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                name="app_taggedasset_distribute",
                distribution_column="organization_id",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="tag",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_tag_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="tag",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                name="app_tag_distribute",
                distribution_column="organization_id",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="tag",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"), name="unique_tag_organization_uuid"
            ),
        ),
        migrations.AddConstraint(
            model_name="tag",
            constraint=models.UniqueConstraint(
                fields=("organization", "name"), name="unique_tag_organization_name"
            ),
        ),
        migrations.AddConstraint(
            model_name="tag",
            constraint=models.UniqueConstraint(
                fields=("organization", "slug"), name="unique_tag_organization_slug"
            ),
        ),
        migrations.AddConstraint(
            model_name="organization",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                name="app_organization_distribute",
                distribution_column="id",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="membership",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_membership_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="membership",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                name="app_membership_distribute",
                distribution_column="organization_id",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="membership",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_membership_organization_uuid",
            ),
        ),
        migrations.AddConstraint(
            model_name="membership",
            constraint=models.UniqueConstraint(
                fields=("organization", "user", "email"),
                name="membership_org_user_email_uniq",
            ),
        ),
        migrations.AddConstraint(
            model_name="membership",
            constraint=models.UniqueConstraint(
                condition=models.Q(("email__isnull", True)),
                fields=("organization", "user"),
                name="membership_org_user_uniq",
            ),
        ),
        migrations.AddConstraint(
            model_name="membership",
            constraint=models.UniqueConstraint(
                condition=models.Q(("user__isnull", True)),
                fields=("organization", "email"),
                name="membership_org_email_uniq",
            ),
        ),
        migrations.AddConstraint(
            model_name="membership",
            constraint=models.CheckConstraint(
                check=models.Q(
                    ("email__isnull", False), ("user__isnull", False), _connector="OR"
                ),
                name="membership_check_email_and_user",
            ),
        ),
        migrations.AddConstraint(
            model_name="folder",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_folder_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="folder",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                name="app_folder_distribute",
                distribution_column="organization_id",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="folder",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"), name="unique_folder_organization_uuid"
            ),
        ),
        migrations.AddConstraint(
            model_name="authtoken",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "digest"), name="app_authtoken_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="authtoken",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                name="app_authtoken_distribute",
                distribution_column="organization_id",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="asset",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_asset_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="asset",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                name="app_asset_distribute",
                distribution_column="organization_id",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="asset",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"), name="unique_asset_organization_uuid"
            ),
        ),
    ]
