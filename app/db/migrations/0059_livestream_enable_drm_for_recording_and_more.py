# Generated by Django 4.0.8 on 2023-12-19 11:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0058_livestream_upload_recording_task_id'),
    ]

    operations = [
        migrations.AddField(
            model_name='livestream',
            name='enable_drm_for_recording',
            field=models.BooleanField(default=True, verbose_name='Enable DRM for live stream recording'),
        ),
        migrations.AlterField(
            model_name='livestream',
            name='enable_drm',
            field=models.BooleanField(default=False, verbose_name='Enable DRM for live stream'),
        ),
    ]
