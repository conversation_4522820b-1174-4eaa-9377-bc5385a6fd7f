# Generated by Django 4.0.8 on 2023-09-11 04:22

import app.db.constraints.composite_key
import app.db.constraints.distribute
import app.models.fields
from django.conf import settings
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_multitenant.mixins
import model_utils.fields


class Migration(migrations.Migration):
    atomic = False
    dependencies = [
        ("app", "0042_livestream_resolutions_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="TranscodingJob",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.CharField(editable=False, max_length=36)),
                (
                    "resolutions",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.PositiveSmallIntegerField(
                            choices=[
                                (0, "240p"),
                                (1, "360p"),
                                (2, "480p"),
                                (3, "720p"),
                                (4, "1080p"),
                            ]
                        ),
                        default=list,
                        size=None,
                    ),
                ),
                ("video_duration", models.DurationField(blank=True, null=True)),
                ("transcoding_duration", models.DurationField(blank=True, null=True)),
                (
                    "status",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "Queued"),
                            (2, "Transcoding"),
                            (3, "Completed"),
                            (4, "Error"),
                        ],
                        db_index=True,
                        default=1,
                    ),
                ),
                ("output_path", models.CharField(max_length=4096)),
                ("input_url", models.CharField(max_length=4096)),
                ("job_id", models.UUIDField(blank=True, null=True)),
                (
                    "start_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="start"),
                ),
                (
                    "end_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="end"),
                ),
                (
                    "created_by",
                    app.models.fields.CreatedByField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddConstraint(
            model_name="transcodingjob",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_transcodingjob_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="transcodingjob",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_transcodingjob_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="transcodingjob",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_transcodingjob_organization_uuid",
            ),
        ),
    ]
