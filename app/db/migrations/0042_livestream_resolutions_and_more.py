# Generated by Django 4.0.8 on 2023-08-17 05:38

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0041_encryptionkey_aes_encryption_key"),
    ]

    operations = [
        migrations.AddField(
            model_name="livestream",
            name="resolutions",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.PositiveSmallIntegerField(
                    choices=[
                        (0, "240p"),
                        (1, "360p"),
                        (2, "480p"),
                        (3, "720p"),
                        (4, "1080p"),
                    ]
                ),
                default=list,
                size=None,
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="enable_hd_live_streaming",
            field=models.BooleanField(
                default=False, verbose_name="Enable HD resolution for live stream"
            ),
        ),
    ]
