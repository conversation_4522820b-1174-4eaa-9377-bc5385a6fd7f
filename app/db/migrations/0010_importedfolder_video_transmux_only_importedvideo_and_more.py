# Generated by Django 4.0.8 on 2023-02-19 15:47

import app.db.constraints.composite_key
import app.db.constraints.distribute
import app.models.fields
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_multitenant.fields
import django_multitenant.mixins
import model_utils.fields
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0009_alter_videoinput_url"),
    ]

    operations = [
        migrations.CreateModel(
            name="ImportedFolder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("name", models.CharField(max_length=256)),
                ("details", models.JSONField(null=True)),
                ("lft", models.PositiveIntegerField(editable=False)),
                ("rght", models.PositiveIntegerField(editable=False)),
                ("tree_id", models.PositiveIntegerField(db_index=True, editable=False)),
                ("level", models.PositiveIntegerField(editable=False)),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
                (
                    "parent",
                    app.models.fields.TenantTreeForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="children",
                        to="app.importedfolder",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddField(
            model_name="video",
            name="transmux_only",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name="ImportedVideo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "source",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, "Vimeo"),
                            (1, "Mux"),
                            (2, "Gumlet"),
                            (3, "Vdocipher"),
                            (4, "Appsquadz"),
                        ],
                        db_index=True,
                        default=0,
                    ),
                ),
                ("name", models.CharField(max_length=1024)),
                ("uri", models.URLField(max_length=4096)),
                ("details", models.JSONField(null=True)),
                (
                    "folder",
                    django_multitenant.fields.TenantForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="app.importedfolder",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name="ImportedResolution",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "status",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, "Queued"),
                            (1, "Uploading"),
                            (2, "Completed"),
                            (3, "Error"),
                        ],
                        db_index=True,
                        default=0,
                    ),
                ),
                ("resolution", models.CharField(max_length=12)),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
                (
                    "video",
                    django_multitenant.fields.TenantForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="resolutions",
                        to="app.importedvideo",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddConstraint(
            model_name="importedvideo",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_importedvideo_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="importedvideo",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_importedvideo_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="importedvideo",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_importedvideo_organization_uuid",
            ),
        ),
        migrations.AddConstraint(
            model_name="importedresolution",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_importedresolution_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="importedresolution",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_importedresolution_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="importedresolution",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_importedresolution_organization_uuid",
            ),
        ),
        migrations.AddConstraint(
            model_name="importedfolder",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_importedfolder_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="importedfolder",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_importedfolder_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="importedfolder",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_importedfolder_organization_uuid",
            ),
        ),
    ]
