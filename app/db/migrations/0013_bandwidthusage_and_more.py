# Generated by Django 4.0.8 on 2023-03-03 10:03

import app.db.constraints.composite_key
import app.db.constraints.distribute
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_multitenant.mixins
import model_utils.fields
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0012_alter_asset_items_count_alter_asset_level_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="BandwidthUsage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "time_frame",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "Daily"), (2, "Monthly")], db_index=True, default=1
                    ),
                ),
                ("date", models.DateField()),
                (
                    "bandwidth_used",
                    models.PositiveBigIntegerField(
                        default=0, null=True, verbose_name="Bandwidth used in bytes"
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddConstraint(
            model_name="bandwidthusage",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_bandwidthusage_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="bandwidthusage",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_bandwidthusage_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="bandwidthusage",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_bandwidthusage_organization_uuid",
            ),
        ),
        migrations.AddConstraint(
            model_name="bandwidthusage",
            constraint=models.UniqueConstraint(
                fields=("organization", "date", "time_frame"),
                name="unique_bandwidthusage_organization_bandwidth_used",
            ),
        ),
    ]
