# Generated by Django 4.0.8 on 2023-07-31 11:24

import app.db.constraints.composite_key
import app.db.constraints.distribute
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_multitenant.fields
import django_multitenant.mixins
import model_utils.fields
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0035_video_duration"),
    ]

    operations = [
        migrations.AddField(
            model_name="asset",
            name="average_watched_time",
            field=models.IntegerField(
                default=0, help_text="The average watched time in seconds.", null=True
            ),
        ),
        migrations.AddField(
            model_name="asset",
            name="views_count",
            field=models.IntegerField(db_index=True, default=0, null=True),
        ),
        migrations.CreateModel(
            name="AssetViewerLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("visitor_id", models.CharField(db_index=True, max_length=32)),
                ("session_id", models.CharField(max_length=40)),
                (
                    "duration",
                    models.IntegerField(
                        default=0,
                        help_text="Duration taken to view the video in seconds.",
                    ),
                ),
                ("location", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "device",
                    models.IntegerField(
                        choices=[
                            (0, "Mobile"),
                            (1, "System"),
                            (2, "Tablet"),
                            (3, "Other"),
                        ],
                        null=True,
                    ),
                ),
                (
                    "platform",
                    models.IntegerField(
                        choices=[(0, "Ios"), (1, "Android"), (2, "Web"), (3, "Other")],
                        null=True,
                    ),
                ),
                (
                    "client",
                    models.IntegerField(
                        choices=[
                            (0, "Ios"),
                            (1, "Android"),
                            (2, "Flutter"),
                            (3, "Browser"),
                            (4, "Other"),
                        ],
                        null=True,
                    ),
                ),
                (
                    "user_agent",
                    models.CharField(blank=True, max_length=1024, null=True),
                ),
                (
                    "asset",
                    django_multitenant.fields.TenantForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="views",
                        to="app.asset",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddConstraint(
            model_name="assetviewerlog",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_assetviewerlog_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="assetviewerlog",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_assetviewerlog_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="assetviewerlog",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_assetviewerlog_organization_uuid",
            ),
        ),
    ]
