# Generated by Django 4.0.8 on 2024-03-14 06:25

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0072_alter_livestream_status"),
    ]

    operations = [
        migrations.AddField(
            model_name="livestream",
            name="latency",
            field=models.IntegerField(
                blank=True,
                choices=[(0, "Normal Latency"), (1, "Low Latency")],
                default=0,
                null=True,
                verbose_name="Streaming Latency",
            ),
        ),
        migrations.AlterField(
            model_name="livestream",
            name="latency",
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[(0, "Normal Latency"), (1, "Low Latency")],
                default=0,
                null=True,
                verbose_name="Streaming Latency",
            ),
        ),
    ]
