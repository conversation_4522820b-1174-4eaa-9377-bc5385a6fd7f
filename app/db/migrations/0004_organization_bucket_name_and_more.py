# Generated by Django 4.0.8 on 2022-12-21 12:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("app", "0003_accesstoken_accesstokenannotation_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="organization",
            name="bucket_name",
            field=models.Char<PERSON>ield(
                blank=True, max_length=2048, null=True, verbose_name="Bucket Name"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="bucket_secret_token",
            field=models.CharField(blank=True, max_length=2048, null=True),
        ),
        migrations.AddField(
            model_name="organization",
            name="cdn_id",
            field=models.CharField(
                blank=True, max_length=2048, null=True, verbose_name="CDN ID"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="cdn_url",
            field=models.URLField(blank=True, null=True, verbose_name="CDN URL"),
        ),
        migrations.Add<PERSON><PERSON>(
            model_name="organization",
            name="cloudfront_key_group_id",
            field=models.Char<PERSON>ield(
                blank=True,
                max_length=256,
                null=True,
                verbose_name="CloudFront Key Group ID",
            ),
        ),
    ]
