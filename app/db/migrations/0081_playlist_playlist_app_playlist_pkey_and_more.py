# Generated by Django 4.0.8 on 2024-08-27 12:31

import app.db.constraints.composite_key
import app.db.constraints.distribute
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_multitenant.fields
import django_multitenant.mixins
import model_utils.fields
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0080_livestream_accent_color_livestream_autoplay_enabled_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Playlist",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("name", models.CharField(max_length=255)),
                ("bytes", models.IntegerField(null=True)),
                ("width", models.IntegerField(null=True, verbose_name="Width")),
                ("height", models.IntegerField(null=True, verbose_name="Height")),
                (
                    "path",
                    models.CharField(
                        max_length=4096, null=True, verbose_name="Playlist Path"
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
                (
                    "track",
                    django_multitenant.fields.TenantForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="playlists",
                        to="app.track",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddConstraint(
            model_name="playlist",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_playlist_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="playlist",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_playlist_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="playlist",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_playlist_organization_uuid",
            ),
        ),
        migrations.AddConstraint(
            model_name="playlist",
            constraint=models.UniqueConstraint(
                fields=("organization", "track", "name"),
                name="unique_playlist_organization_playlist",
            ),
        ),
        migrations.AlterField(
            model_name="playlist",
            name="bytes",
            field=models.PositiveBigIntegerField(null=True),
        ),
        migrations.AlterField(
            model_name="playlist",
            name="height",
            field=models.PositiveSmallIntegerField(null=True, verbose_name="Height"),
        ),
        migrations.AlterField(
            model_name="playlist",
            name="width",
            field=models.PositiveSmallIntegerField(null=True, verbose_name="Width"),
        ),
    ]
