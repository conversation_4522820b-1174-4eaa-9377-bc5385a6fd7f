# Generated by Django 4.0.8 on 2023-04-25 13:01

import app.db.constraints.composite_key
import app.db.constraints.distribute
import app.models.fields
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_multitenant.fields
import django_multitenant.mixins
import model_utils.fields


class Migration(migrations.Migration):
    dependencies = [
        (
            "app",
            "0017_rename_fairplay_key_data_video_fairplay_encryption_key_data_and_more",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="organization",
            name="drm_aes_signing_iv",
            field=models.Char<PERSON>ield(blank=True, max_length=32, null=True),
        ),
        migrations.AddField(
            model_name="organization",
            name="drm_aes_signing_key",
            field=models.CharField(blank=True, max_length=64, null=True),
        ),
        migrations.CreateModel(
            name="EncryptionKey",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("content_id", models.UUIDField(db_index=True, editable=False)),
                ("widevine_encryption_keys", models.JSONField(null=True)),
                ("fairplay_encryption_key_data", models.JSONField(null=True)),
                (
                    "asset",
                    app.models.fields.TenantOneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="encryption_key",
                        to="app.asset",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "ordering": ("-created",),
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name="DRMLicenseLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "drm_type",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "Widevine"), (2, "Fairplay")], db_index=True
                    ),
                ),
                (
                    "device",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "Pc"),
                            (2, "Mobile Browser"),
                            (3, "Mobile Sdk"),
                            (4, "Other"),
                        ]
                    ),
                ),
                (
                    "license_type",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "Streaming"), (2, "Download")], default=1
                    ),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(null=True, unpack_ipv4=True),
                ),
                (
                    "user_agent",
                    models.CharField(editable=False, max_length=1024, null=True),
                ),
                ("content_id", models.UUIDField(db_index=True, editable=False)),
                (
                    "provider",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "Testpress"), (2, "Vdocipher")]
                    ),
                ),
                (
                    "meta_data",
                    models.JSONField(
                        help_text="Additional data will be stored. For ex: Asset path, uuid",
                        null=True,
                    ),
                ),
                (
                    "asset",
                    django_multitenant.fields.TenantForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="app.asset",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "ordering": ("-created",),
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddConstraint(
            model_name="encryptionkey",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_encryptionkey_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="encryptionkey",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_encryptionkey_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="encryptionkey",
            constraint=models.UniqueConstraint(
                fields=("organization", "asset"),
                name="unique_encryptionkey_organization_asset",
            ),
        ),
        migrations.AddConstraint(
            model_name="drmlicenselog",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_drmlicenselog_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="drmlicenselog",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_drmlicenselog_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
    ]
