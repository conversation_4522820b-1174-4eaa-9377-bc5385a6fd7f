# Generated by Django 4.0.8 on 2024-10-15 11:05

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0085_alter_embedpreset_default_subtitle_language_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="template",
            name="video_codecs",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.PositiveSmallIntegerField(
                    choices=[(0, "h264"), (1, "h265")]
                ),
                default=list,
                null=True,
                size=None,
            ),
        ),
        migrations.AlterField(
            model_name="video",
            name="video_codecs",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.PositiveSmallIntegerField(
                    choices=[(0, "h264"), (1, "h265")]
                ),
                default=list,
                null=True,
                size=None,
            ),
        ),
    ]
