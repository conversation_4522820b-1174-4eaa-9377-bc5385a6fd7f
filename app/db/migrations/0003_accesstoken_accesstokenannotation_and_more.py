# Generated by Django 4.0.8 on 2022-12-14 11:34

import app.db.constraints.composite_key
import app.db.constraints.distribute
from decimal import Decimal
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_multitenant.fields
import django_multitenant.mixins
import model_utils.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("app", "0002_videoaccesstoken_videoaccesstokenannotation_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="AccessToken",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("valid_until", models.DateTimeField(blank=True, null=True)),
                (
                    "expires_after_first_usage",
                    models.BooleanField(
                        default=False,
                        help_text="If this enabled, token will get expired after one use",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name="AccessTokenAnnotation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "type",
                    models.CharField(
                        choices=[(0, "static"), (1, "dynamic")],
                        default=0,
                        max_length=12,
                    ),
                ),
                (
                    "text",
                    models.CharField(
                        help_text="Text to display as watermark", max_length=1024
                    ),
                ),
                ("color", models.CharField(default="#ff0000", max_length=12)),
                (
                    "size",
                    models.PositiveSmallIntegerField(
                        default=15, help_text="Height of the text, in pixels"
                    ),
                ),
                (
                    "opacity",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.5"), max_digits=3
                    ),
                ),
                (
                    "x",
                    models.PositiveSmallIntegerField(
                        default=16,
                        help_text="Static text's distance from the left border of video",
                    ),
                ),
                (
                    "y",
                    models.PositiveSmallIntegerField(
                        default=16,
                        help_text="Static text's distance from the top border of video",
                    ),
                ),
                (
                    "skip",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Specifies duration(in milliseconds) to not display watermark between two overlays",
                        null=True,
                    ),
                ),
                (
                    "interval",
                    models.PositiveIntegerField(
                        default=2000,
                        help_text="Specifies interval over which the watermark changes position",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.DeleteModel(
            name="VideoAccessToken",
        ),
        migrations.DeleteModel(
            name="VideoAccessTokenAnnotation",
        ),
        migrations.AddField(
            model_name="accesstokenannotation",
            name="access_token",
            field=django_multitenant.fields.TenantForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="annotations",
                to="app.accesstoken",
            ),
        ),
        migrations.AddField(
            model_name="accesstokenannotation",
            name="organization",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s",
                related_query_name="%(class)ss",
                to="app.organization",
            ),
        ),
        migrations.AddField(
            model_name="accesstoken",
            name="asset",
            field=django_multitenant.fields.TenantForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="access_tokens",
                to="app.asset",
            ),
        ),
        migrations.AddField(
            model_name="accesstoken",
            name="organization",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s",
                related_query_name="%(class)ss",
                to="app.organization",
            ),
        ),
        migrations.AddConstraint(
            model_name="accesstokenannotation",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_accesstokenannotation_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="accesstokenannotation",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                name="app_accesstokenannotation_distribute",
                distribution_column="organization_id",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="accesstokenannotation",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_accesstokenannotation_organization_uuid",
            ),
        ),
        migrations.AddConstraint(
            model_name="accesstoken",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_accesstoken_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="accesstoken",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                name="app_accesstoken_distribute",
                distribution_column="organization_id",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="accesstoken",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_accesstoken_organization_uuid",
            ),
        ),
    ]
