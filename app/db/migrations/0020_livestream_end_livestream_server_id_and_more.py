# Generated by Django 4.0.8 on 2023-05-29 12:23

import app.db.constraints.composite_key
import app.db.constraints.distribute
import app.models.fields
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_multitenant.mixins
import model_utils.fields
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0019_alter_video_status"),
    ]

    operations = [
        migrations.AddField(
            model_name="livestream",
            name="end",
            field=models.DateTimeField(blank=True, null=True, verbose_name="End time"),
        ),
        migrations.AddField(
            model_name="livestream",
            name="server_id",
            field=models.CharField(
                blank=True,
                max_length=125,
                null=True,
                verbose_name="Unique identifier of the server",
            ),
        ),
        migrations.AddField(
            model_name="livestream",
            name="server_status",
            field=models.IntegerField(
                choices=[
                    (0, "Not Created"),
                    (1, "Creating"),
                    (2, "Created"),
                    (3, "Proxy Setup"),
                    (4, "Available"),
                    (5, "Destroyed"),
                ],
                default=0,
                verbose_name="Status of Live stream server",
            ),
        ),
        migrations.AlterField(
            model_name="livestream",
            name="server_status",
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, "Not Created"),
                    (1, "Creating"),
                    (2, "Created"),
                    (3, "Proxy Setup"),
                    (4, "Available"),
                    (5, "Destroyed"),
                ],
                default=0,
                verbose_name="Status of Live stream server",
            ),
        ),
        migrations.AlterField(
            model_name="livestream",
            name="server_ip",
            field=models.GenericIPAddressField(
                blank=True, null=True, verbose_name="Streaming Server IP Address"
            ),
        ),
        migrations.AlterField(
            model_name="livestream",
            name="stream_key",
            field=models.CharField(
                blank=True, max_length=64, null=True, verbose_name="Stream Key"
            ),
        ),
        migrations.CreateModel(
            name="LiveStreamUsage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "server_ip",
                    models.GenericIPAddressField(
                        null=True, verbose_name="Streaming Server IP Address"
                    ),
                ),
                (
                    "server_provider",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "Digitalocean")],
                        db_index=True,
                        default=1,
                        verbose_name="Status",
                    ),
                ),
                (
                    "start_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Start time"
                    ),
                ),
                (
                    "end_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="End time"
                    ),
                ),
                (
                    "cost_per_hour",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=3,
                        null=True,
                        verbose_name="Server cost (per hour in rupees)",
                    ),
                ),
                (
                    "live_stream",
                    app.models.fields.TenantOneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="live_stream",
                        to="app.livestream",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddConstraint(
            model_name="livestreamusage",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_livestreamusage_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="livestreamusage",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_livestreamusage_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="livestreamusage",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_livestreamusage_organization_uuid",
            ),
        ),
    ]
