# Generated by Django 4.0.8 on 2023-07-31 11:27

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0036_asset_average_watched_time_asset_views_count_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="asset",
            name="average_watched_time",
            field=models.PositiveBigIntegerField(
                default=0, help_text="The average watched time in seconds.", null=True
            ),
        ),
        migrations.AlterField(
            model_name="asset",
            name="views_count",
            field=models.PositiveBigIntegerField(db_index=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name="assetviewerlog",
            name="client",
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, "Ios"),
                    (1, "Android"),
                    (2, "Flutter"),
                    (3, "Browser"),
                    (4, "Other"),
                ],
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="assetviewerlog",
            name="device",
            field=models.PositiveSmallIntegerField(
                choices=[(0, "Mobile"), (1, "System"), (2, "Tablet"), (3, "Other")],
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="assetviewerlog",
            name="duration",
            field=models.PositiveBigIntegerField(
                default=0, help_text="Duration taken to view the video in seconds."
            ),
        ),
        migrations.AlterField(
            model_name="assetviewerlog",
            name="platform",
            field=models.PositiveSmallIntegerField(
                choices=[(0, "Ios"), (1, "Android"), (2, "Web"), (3, "Other")],
                null=True,
            ),
        ),
    ]
