# Generated by Django 4.0.8 on 2023-12-01 04:41

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0057_organization_use_haproxy_for_livestream"),
    ]

    operations = [
        migrations.AddField(
            model_name="livestream",
            name="upload_recording_task_id",
            field=models.UUIDField(
                blank=True, null=True, verbose_name="Upload Recording Task Id"
            ),
        ),
    ]
