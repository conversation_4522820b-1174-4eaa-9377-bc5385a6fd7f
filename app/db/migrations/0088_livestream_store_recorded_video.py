# Generated by Django 4.0.8 on 2025-03-04 09:36

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0087_livestream_meta_data"),
    ]

    operations = [
        migrations.AddField(
            model_name="livestream",
            name="store_recorded_video",
            field=models.BooleanField(
                blank=True,
                default=True,
                help_text="If enabled, the live stream recording will be stored in the bucket for later use",
                null=True,
                verbose_name="Store recording in bucket",
            ),
        ),
    ]
