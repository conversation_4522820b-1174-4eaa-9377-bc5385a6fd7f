# Generated by Django 4.0.8 on 2024-04-24 06:00

import app.db.constraints.composite_key
import app.db.constraints.distribute
import app.models.fields
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_multitenant.mixins
import model_utils.fields
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0074_assetusage_subtitle_generation_cost"),
    ]

    operations = [
        migrations.CreateModel(
            name="PreviewThumbnail",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "width",
                    models.PositiveSmallIntegerField(null=True, verbose_name="Width"),
                ),
                (
                    "height",
                    models.PositiveSmallIntegerField(null=True, verbose_name="Height"),
                ),
                (
                    "rows",
                    models.PositiveSmallIntegerField(
                        default=0, null=True, verbose_name="Rows"
                    ),
                ),
                (
                    "columns",
                    models.PositiveSmallIntegerField(
                        default=0, null=True, verbose_name="Columns"
                    ),
                ),
                (
                    "interval",
                    models.PositiveSmallIntegerField(
                        default=0, null=True, verbose_name="Sprite Interval"
                    ),
                ),
                (
                    "url",
                    models.CharField(
                        blank=True,
                        max_length=4096,
                        null=True,
                        verbose_name="PreviewThumbnail Url",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddField(
            model_name="track",
            name="preview_thumbnail",
            field=app.models.fields.TenantOneToOneField(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="track",
                to="app.previewthumbnail",
            ),
        ),
        migrations.AddConstraint(
            model_name="previewthumbnail",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_previewthumbnail_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="previewthumbnail",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_previewthumbnail_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="previewthumbnail",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_previewthumbnail_organization_uuid",
            ),
        ),
    ]
