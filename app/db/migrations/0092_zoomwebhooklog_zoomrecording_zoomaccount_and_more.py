# Generated by Django 4.0.8 on 2025-07-01 06:17

import app.db.constraints.composite_key
import app.db.constraints.distribute
import app.models.fields
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_multitenant.fields
import django_multitenant.mixins
import model_utils.fields
import uuid


class Migration(migrations.Migration):
    atomic = False
    dependencies = [
        ("app", "0091_livestream_scheduled_trim_data"),
    ]

    operations = [
        migrations.CreateModel(
            name="ZoomWebhookLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "event_type",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "Recording Started"),
                            (2, "Recording Stopped"),
                            (3, "Recording Paused"),
                            (4, "Recording Resumed"),
                            (5, "Recording Completed"),
                        ],
                        help_text="Type of Zoom event received (e.g., recording completed).",
                    ),
                ),
                (
                    "payload",
                    models.JSONField(
                        help_text="Raw JSON payload received from the Zoom webhook."
                    ),
                ),
                (
                    "received_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="Timestamp when the webhook was received.",
                    ),
                ),
                (
                    "status",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "Pending Processing"),
                            (2, "Skipped Processing"),
                            (3, "Processing Failed"),
                            (4, "Processed Successfully"),
                        ],
                        default=1,
                        help_text="Processing status of the webhook event.",
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True,
                        help_text="Error message if processing failed, else blank.",
                        null=True,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "Zoom Webhook Log",
                "verbose_name_plural": "Zoom Webhook Logs",
                "ordering": ["-received_at"],
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name="ZoomRecording",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "meeting_uuid",
                    models.CharField(
                        db_index=True,
                        help_text="Zoom meeting UUID for this recording.",
                        max_length=255,
                    ),
                ),
                (
                    "recording_uuid",
                    models.CharField(
                        help_text="Unique Zoom recording UUID. Used for idempotency.",
                        max_length=255,
                    ),
                ),
                (
                    "zoom_user_id",
                    models.CharField(
                        db_index=True,
                        help_text="Zoom user ID of the meeting host.",
                        max_length=255,
                    ),
                ),
                (
                    "topic",
                    models.CharField(
                        blank=True,
                        help_text="Meeting topic/title from Zoom.",
                        max_length=1024,
                        null=True,
                    ),
                ),
                (
                    "start_time",
                    models.DateTimeField(
                        blank=True,
                        help_text="Start time of the Zoom meeting",
                        null=True,
                    ),
                ),
                (
                    "duration",
                    models.PositiveIntegerField(
                        blank=True, help_text="Duration of the meeting", null=True
                    ),
                ),
                (
                    "imported_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="Timestamp when the recording was imported.",
                    ),
                ),
                (
                    "download_url",
                    models.URLField(
                        blank=True,
                        help_text="Zoom download URL for the recording file.",
                        max_length=2048,
                        null=True,
                    ),
                ),
                (
                    "status",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "Pending"), (2, "Imported"), (3, "Failed")],
                        default=1,
                        help_text="Import status: pending, imported, or failed.",
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True,
                        help_text="Error message if import failed, else blank.",
                        null=True,
                    ),
                ),
                (
                    "asset",
                    app.models.fields.TenantOneToOneField(
                        help_text="The Asset created from this Zoom recording.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="zoom_recording",
                        to="app.asset",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "Zoom Recording",
                "verbose_name_plural": "Zoom Recordings",
                "ordering": ["-created"],
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name="ZoomAccount",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "zoom_user_id",
                    models.CharField(
                        db_index=True,
                        help_text="Unique Zoom user ID for this account.",
                        max_length=255,
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True,
                        help_text="Email address of the Zoom account.",
                        max_length=254,
                        null=True,
                    ),
                ),
                (
                    "access_token",
                    models.TextField(
                        blank=True,
                        help_text="Current Zoom OAuth access token.",
                        null=True,
                    ),
                ),
                (
                    "refresh_token",
                    models.TextField(
                        blank=True,
                        help_text="Current Zoom OAuth refresh token.",
                        null=True,
                    ),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Datetime when the access token expires.",
                        null=True,
                    ),
                ),
                (
                    "status",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "Connected"),
                            (2, "Disconnected"),
                            (3, "Refresh Token Expired"),
                        ],
                        db_index=True,
                        default=1,
                        help_text="Current status of the Zoom connection.",
                    ),
                ),
                (
                    "disconnected_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the account was last disconnected or refresh token expired.",
                        null=True,
                    ),
                ),
                (
                    "enable_drm",
                    models.BooleanField(
                        default=False,
                        help_text="If enabled, all imported Zoom recordings will be protected with DRM.",
                    ),
                ),
                (
                    "import_destination",
                    django_multitenant.fields.TenantForeignKey(
                        blank=True,
                        help_text="The folder where Zoom recordings will be imported by default.",
                        limit_choices_to={"type": 4},
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="app.asset",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="The user who authorized this Zoom account.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="zoom_accounts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Zoom Account",
                "verbose_name_plural": "Zoom Accounts",
                "ordering": ["-created"],
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddConstraint(
            model_name="zoomwebhooklog",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_zoomwebhooklog_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="zoomwebhooklog",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_zoomwebhooklog_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="zoomwebhooklog",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_zoomwebhooklog_organization_uuid",
            ),
        ),
        migrations.AddConstraint(
            model_name="zoomrecording",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_zoomrecording_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="zoomrecording",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_zoomrecording_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="zoomrecording",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_zoomrecording_organization_uuid",
            ),
        ),
        migrations.AddConstraint(
            model_name="zoomrecording",
            constraint=models.UniqueConstraint(
                fields=("organization", "recording_uuid"),
                name="unique_zoomrecording_organization_recording_uuid",
            ),
        ),
        migrations.AddConstraint(
            model_name="zoomaccount",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_zoomaccount_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="zoomaccount",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_zoomaccount_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="zoomaccount",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_zoomaccount_organization_uuid",
            ),
        ),
        migrations.AddConstraint(
            model_name="zoomaccount",
            constraint=models.UniqueConstraint(
                fields=("organization", "zoom_user_id"),
                name="unique_zoomaccount_organization_zoom_user_id",
            ),
        ),
        migrations.AddConstraint(
            model_name="zoomaccount",
            constraint=models.UniqueConstraint(
                fields=("organization", "user"),
                name="unique_zoomaccount_organization_user",
            ),
        ),
    ]
