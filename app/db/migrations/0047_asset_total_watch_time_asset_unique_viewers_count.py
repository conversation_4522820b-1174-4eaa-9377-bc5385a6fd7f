# Generated by Django 4.0.8 on 2023-09-27 10:34

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0046_video_transcoding_end_time_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="asset",
            name="total_watch_time",
            field=models.IntegerField(
                default=0, help_text="The total watched time in seconds.", null=True
            ),
        ),
        migrations.AddField(
            model_name="asset",
            name="unique_viewers_count",
            field=models.IntegerField(
                db_index=True,
                default=0,
                help_text="The number of users who have watched the video for the first time.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="asset",
            name="total_watch_time",
            field=models.PositiveBigIntegerField(
                default=0, help_text="The total watched time in seconds.", null=True
            ),
        ),
        migrations.AlterField(
            model_name="asset",
            name="unique_viewers_count",
            field=models.PositiveBigIntegerField(
                db_index=True,
                default=0,
                help_text="The number of users who have watched the video for the first time.",
                null=True,
            ),
        ),
    ]
