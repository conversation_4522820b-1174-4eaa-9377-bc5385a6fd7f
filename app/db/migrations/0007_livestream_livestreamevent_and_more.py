# Generated by Django 4.0.8 on 2023-01-17 08:48

import app.db.constraints.composite_key
import app.db.constraints.distribute
import app.models.fields
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_multitenant.fields
import django_multitenant.mixins
import model_utils.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("app", "0006_video_job_id_alter_video_status"),
    ]

    operations = [
        migrations.CreateModel(
            name="LiveStream",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "rtmp_url",
                    models.URLField(
                        validators=[
                            django.core.validators.URLValidator(
                                schemes=["rtmp", "rtmps"]
                            )
                        ],
                        verbose_name="RTMP URL",
                    ),
                ),
                (
                    "stream_key",
                    models.CharField(max_length=64, verbose_name="Stream Key"),
                ),
                (
                    "hls_url_path",
                    models.CharField(max_length=2048, verbose_name="HLS URL path"),
                ),
                (
                    "status",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, "Not Started"),
                            (1, "Streaming"),
                            (2, "Recording"),
                            (3, "Completed"),
                            (4, "Error"),
                            (5, "Disconnected"),
                        ],
                        db_index=True,
                        default=0,
                        verbose_name="Status",
                    ),
                ),
                (
                    "server_ip",
                    models.GenericIPAddressField(
                        verbose_name="Streaming Server IP Address"
                    ),
                ),
                (
                    "server_data",
                    models.JSONField(
                        blank=True, null=True, verbose_name="Streaming Server details"
                    ),
                ),
                (
                    "start",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Start Date"
                    ),
                ),
                (
                    "asset",
                    app.models.fields.TenantOneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="live_stream",
                        to="app.asset",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name="LiveStreamEvent",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    "deleted_by_cascade",
                    models.BooleanField(default=False, editable=False),
                ),
                (
                    "type",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, "Created"),
                            (1, "On Pubish"),
                            (2, "On Pubish Done"),
                        ],
                        verbose_name="Event Type",
                    ),
                ),
                (
                    "data",
                    models.JSONField(blank=True, null=True, verbose_name="Event data"),
                ),
                (
                    "live_stream",
                    django_multitenant.fields.TenantForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="events",
                        to="app.livestream",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        related_query_name="%(class)ss",
                        to="app.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddConstraint(
            model_name="livestreamevent",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_livestreamevent_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="livestreamevent",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_livestreamevent_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="livestream",
            constraint=app.db.constraints.composite_key.CompositePrimaryKeyConstraint(
                fields=("organization", "id"), name="app_livestream_pkey"
            ),
        ),
        migrations.AddConstraint(
            model_name="livestream",
            constraint=app.db.constraints.distribute.DistributeConstraint(
                distribution_column="organization_id",
                name="app_livestream_distribute",
                reference=False,
                reverse_ignore=False,
            ),
        ),
        migrations.AddConstraint(
            model_name="livestream",
            constraint=models.UniqueConstraint(
                fields=("organization", "uuid"),
                name="unique_livestream_organization_uuid",
            ),
        ),
    ]
