# Generated by Django 4.0.8 on 2023-08-16 11:47

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0039_remove_video_drm_provider_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="template",
            name="content_protection_type",
            field=models.SmallIntegerField(
                choices=[
                    (0, "Disabled"),
                    (1, "DRM (Widevine & Fairplay)"),
                    (2, "AES (Advanced Encryption Standard)"),
                    (3, "AES with Signed URL"),
                ],
                default=0,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="video",
            name="content_protection_type",
            field=models.SmallIntegerField(
                choices=[
                    (0, "Disabled"),
                    (1, "DRM (Widevine & Fairplay)"),
                    (2, "AES (Advanced Encryption Standard)"),
                    (3, "AES with Signed URL"),
                ],
                default=0,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="template",
            name="content_protection_type",
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, "Disabled"),
                    (1, "DRM (Widevine & Fairplay)"),
                    (2, "AES (Advanced Encryption Standard)"),
                    (3, "AES with Signed URL"),
                ],
                default=0,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="video",
            name="content_protection_type",
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, "Disabled"),
                    (1, "DRM (Widevine & Fairplay)"),
                    (2, "AES (Advanced Encryption Standard)"),
                    (3, "AES with Signed URL"),
                ],
                default=0,
                null=True,
            ),
        ),
    ]
