from django.db.models.constraints import BaseConstraint
from django.db.utils import DEFAULT_DB_ALIAS


class DistributeConstraint(BaseConstraint):
    sql_create_reference_table = "SELECT create_reference_table('%(table)s')"
    sql_create_distributed_table = (
        "SELECT create_distributed_table('%(table)s', '%(column)s'); "  # noqa
    )
    sql_undistribute_table = "SELECT undistribute_table('%(table)s')"

    def __init__(
        self,
        name,
        distribution_column=None,
        reference=False,
        reverse_ignore=False,
        *args,
        **kwargs
    ):
        if not name:
            raise ValueError("A unique constraint must be named.")

        if not reference and not distribution_column:
            raise ValueError("distribution_column is required to distribute a table")

        if not reference and not isinstance(distribution_column, str):
            raise ValueError("DistributeConstraint.distribution_column must be a str.")

        self.reference = reference
        self.distribution_column = distribution_column
        self.reverse_ignore = reverse_ignore
        super().__init__(name, *args, **kwargs)  # type: ignore

    def constraint_sql(self, model, schema_editor):
        return self.create_sql(model, schema_editor)

    def create_sql(self, model, schema_editor):
        if self.reference:
            sql = self.sql_create_reference_table
        else:
            sql = self.sql_create_distributed_table

        parts = {"table": model._meta.db_table}
        if not self.reference:
            parts["column"] = self.distribution_column

        return sql % parts

    def remove_sql(self, model, schema_editor):
        if self.reverse_ignore:
            return

        parts = {
            "table": model._meta.db_table,
        }
        return self.sql_undistribute_table % parts

    def __eq__(self, other):
        if isinstance(other, DistributeConstraint):
            return (
                self.name == other.name
                and self.reference == other.reference
                and self.reverse_ignore == other.reverse_ignore
            )
        return super().__eq__(other)

    def validate(self, model, instance, exclude=None, using=DEFAULT_DB_ALIAS):
        pass

    def deconstruct(self):
        path, args, kwargs = super().deconstruct()
        kwargs["distribution_column"] = self.distribution_column
        kwargs["reference"] = self.reference
        kwargs["reverse_ignore"] = self.reverse_ignore
        return path, args, kwargs
