from django.core.exceptions import ValidationError
from django.db import connections
from django.db.backends.ddl_references import Columns, Statement, Table
from django.db.models.constraints import BaseConstraint
from django.db.utils import DEFAULT_DB_ALIAS


class CompositePrimaryKeyConstraint(BaseConstraint):
    sql_pk_constraint = "PRIMARY KEY (%(columns)s)"
    sql_delete_pk = "ALTER TABLE %(table)s DROP CONSTRAINT %(name)s CASCADE"

    def __init__(self, name, fields=(), *args, **kwargs):
        if not fields:
            raise ValueError(
                "At least one field is required to define a "
                "composite primary key constraint."
            )

        if not isinstance(fields, (list, tuple)):
            raise ValueError(
                "CompositePrimaryKeyConstraint.fields must be a list or tuple."
            )

        self.fields = tuple(fields)
        super().__init__(name, *args, **kwargs)  # type: ignore

    def constraint_sql(self, model, schema_editor):
        fields = [model._meta.get_field(field_name) for field_name in self.fields]
        return self._composite_primary_key_sql(
            schema_editor,
            fields,
            self.name,
        )

    def _composite_primary_key_sql(self, schema_editor, name, fields):
        columns = [field.column for field in fields]
        return schema_editor.sql_constraint % {
            "name": schema_editor.quote_name(name),
            "constraint": self.sql_pk_constraint % {"columns": columns},
        }

    def create_sql(self, model, schema_editor):
        # We first delete the default primary_key created by Django
        self._delete_primary_key(schema_editor, model)

        fields = [model._meta.get_field(field_name) for field_name in self.fields]
        return self._create_composite_primary_key_sql(
            schema_editor,
            model,
            fields,
        )

    def _delete_primary_key(self, schema_editor, model):
        # This adds cascade while deleting primary key as seen in self.sql_delete_pk above
        constraint_names = schema_editor._constraint_names(model, primary_key=True)
        for constraint_name in constraint_names:
            schema_editor.execute(
                schema_editor._delete_constraint_sql(
                    self.sql_delete_pk, model, constraint_name
                )
            )

    def _create_composite_primary_key_sql(self, schema_editor, model, fields):
        columns = [field.column for field in fields]
        return Statement(
            schema_editor.sql_create_pk,
            table=Table(model._meta.db_table, schema_editor.quote_name),
            name=schema_editor.quote_name(
                schema_editor._create_index_name(
                    model._meta.db_table, columns, suffix="_pk"
                )
            ),
            columns=Columns(model._meta.db_table, columns, schema_editor.quote_name),
        )

    def remove_sql(self, model, schema_editor):
        schema_editor._delete_primary_key(model)
        # Attempt to restore the default primary_key
        for field in model._meta.fields:
            if field.primary_key:
                return schema_editor._create_primary_key_sql(model, field)

        # No primary_key found. Ignore in that case
        return None

    def __repr__(self):
        return "<{}: fields={} name={}>".format(
            self.__class__.__qualname__,
            self.fields,
            repr(self.name),
        )

    def __eq__(self, other):
        if isinstance(other, CompositePrimaryKeyConstraint):
            return self.name == other.name and self.fields == other.fields
        return super().__eq__(other)

    def deconstruct(self):
        path, args, kwargs = super().deconstruct()
        kwargs["fields"] = self.fields
        return path, args, kwargs

    def validate(self, model, instance, exclude=None, using=DEFAULT_DB_ALIAS):
        queryset = model._default_manager.using(using)
        if self.fields:
            lookup_kwargs = {}
            for field_name in self.fields:
                if exclude and field_name in exclude:
                    return
                field = model._meta.get_field(field_name)
                lookup_value = getattr(instance, field.attname)
                if lookup_value is None or (
                    lookup_value == ""
                    and connections[using].features.interprets_empty_strings_as_nulls
                ):
                    # A composite constraint containing NULL value cannot cause
                    # a violation since NULL != NULL in SQL.
                    return
                lookup_kwargs[field.name] = lookup_value
            queryset = queryset.filter(**lookup_kwargs)
        model_class_pk = instance._get_pk_val(model._meta)
        if not instance._state.adding and model_class_pk is not None:
            queryset = queryset.exclude(pk=model_class_pk)

        if queryset.exists():
            # When fields are defined, use the unique_error_message() for
            # backward compatibility.
            for model, constraints in instance.get_constraints():
                for constraint in constraints:
                    if constraint is self:
                        raise ValidationError(
                            instance.unique_error_message(model, self.fields)
                        )
