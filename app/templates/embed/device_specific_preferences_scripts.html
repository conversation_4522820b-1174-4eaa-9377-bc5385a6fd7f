<script>
    const PREFERENCES_KEY = 'streams_users_player_preferences';

    function storePlayerPreferencesInLocalStorage(updates) {
        if (!isLocalStorageAvailable()) {
            console.warn('localStorage is not available');
            return;
        }

        let preferences;
        try {
            preferences = JSON.parse(localStorage.getItem(PREFERENCES_KEY) || '{}');
        } catch (error) {
            console.warn('Failed to retrieve preferences:', error);
            preferences = {};
        }

        Object.assign(preferences, updates);

        try {
            localStorage.setItem(PREFERENCES_KEY, JSON.stringify(preferences));
        } catch (error) {
            console.warn('Failed to save preferences:', error);
        }
    }

    function getPlayerPreferencesFromLocalStorage() {
        if (!isLocalStorageAvailable()) {
            console.warn('localStorage is not available');
            return null;
        }

        try {
            return JSON.parse(localStorage.getItem(PREFERENCES_KEY) || '{}');
        } catch (error) {
            console.warn('Failed to retrieve preferences:', error);
            return null;
        }
    }

    function isLocalStorageAvailable() {
        return typeof localStorage !== 'undefined';
    }

</script>

{% include "./volume_preference_script.html" %}
{% include "./caption_preference_script.html" %}
