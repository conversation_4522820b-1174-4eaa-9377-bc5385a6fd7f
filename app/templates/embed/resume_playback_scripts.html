<script>
let lastSaveTime = 0;
let lastPositionFetched = false;
const saveInterval = 2 * 60 *1000; // Save every 2 minutes
let watchedTime = 0;
let lastTimeUpdate = 0;

function hasEnabledResumeFeature() {
  return getPlayerUserId() !== null;
}

async function fetchLastWatchedPosition() {
  if (!hasEnabledResumeFeature()) return 0;

  const response = await fetch('{{ cassandra_server_url }}/api/player/last-watched-position/', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify(getRequestData()),
  });

  if (response.ok) {
    const data = await response.json();
    return data.watched_seconds;
  }
  return 0;
}

function saveWatchedPosition() {
  if (!hasEnabledResumeFeature()) return;

  fetch('{{ cassandra_server_url }}/api/player/update-watched-position/', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify(getRequestData(true)),
  });
}

async function deleteWatchedPosition() {
  if (!hasEnabledResumeFeature()) return;

  const response = await fetch('{{ cassandra_server_url }}/api/player/last-watched-position/', {
    method: 'DELETE',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify(getRequestData()),
  });

  if (response.ok) {
    console.log('Player saved position deleted successfully.');
  } else {
    console.error('Failed to destroy player instance:', response.statusText);
  }
}

function getRequestData(include_time) {
  let data = {
    user_id: getPlayerUserId(),
    organization_id: '{{ object.organization.uuid }}',
    asset_id: '{{ object.uuid }}',
  };
  if (include_time) {
    data["watched_seconds"] = Math.floor(getCurrentTime());
  }
  return data;
}

async function goToLastWatchedPosition() {
  if (lastPositionFetched) return;
  const lastWatchedPosition = await fetchLastWatchedPosition();
  if (!lastWatchedPosition) return;
  player.currentTime(lastWatchedPosition);
  lastPositionFetched = true;
}

player.on('firstplay', async function() {
  {% if object.organization.uuid == "7nnmbq" %}
    await fetchAndUpdateTime();
  {% endif %}
});

player.on('firstplay', function() {
  goToLastWatchedPosition();
});

player.on('loadeddata', function() {
  setTimeout(() => {
    goToLastWatchedPosition();
  }, 500);
});

player.on('pause', async function() {
  {% if object.organization.uuid == "7nnmbq" %}
    await fetchAndUpdateTime();
  {% endif %}
});

player.on('pause', function() {
  saveWatchedPosition();
});

player.on('seeked', async function() {
  {% if object.organization.uuid == "7nnmbq" %}
    await fetchAndUpdateTime();
  {% endif %}
});

player.on('seeked', function() {
  saveWatchedPosition();
});


player.on('timeupdate', function() {
  const currentTimeMilliseconds = Date.now();
  if (currentTimeMilliseconds - lastSaveTime > saveInterval) {
    lastSaveTime = currentTimeMilliseconds;
    saveWatchedPosition();
  }
});

player.on('timeupdate', async function() {
  {% if object.organization.uuid == "7nnmbq" %}
    await fetchAndUpdateTime();
  {% endif %}
});

player.on('ended', function() {
  {% if not object.organization.uuid == "7nnmbq" %}
    deleteWatchedPosition();
  {% endif %}
});

player.on('ended', async function() {
  {% if object.organization.uuid == "7nnmbq" %}
    await fetchAndUpdateTime();
  {% endif %}
});

async function fetchAndUpdateTime() {
  if (Date.now() - lastTimeUpdate > lastTimeUpdate) {
    let lastWatchedPosition = await fetchLastWatchedPosition();
    if (lastWatchedPosition) {
      watchedTime = lastWatchedPosition;
    }
    lastTimeUpdate = Date.now();
  }
}

function getWatchedTime() {
  return watchedTime;
}
</script>
