{% load static %}

<script src="{% static 'js/video.min.js' %}?v=7.21.4"></script>
<script src="{% static 'js/videojs-contrib-eme.js' %}"></script>
<script src="{% static 'js/player.js' %}?v=2.0"></script>
<script src="{% static 'js/sentry.min.js' %}"></script>


<script>
 startVideo();
 function startVideo() {
  const video = document.querySelector('video');
  video.addEventListener('webkitneedkey', onneedkey, false);
  video.addEventListener('error', onerror, false);
  video.src = '{{ object.video.get_playback_url }}';
 }

 function base64EncodeUint8Array(input) {
  return btoa(String.fromCharCode.apply(null, input));
 }

 const playerBuilder = new PlayerBuilder()
  .setCSRFToken("{{ csrf_token }}")
  .setFairplayCertificateURL("{{ fairplay_certificate_url }}")
  .setDRMURL("{% url "api:drm-license" asset_id=object.uuid organization_id=object.organization.uuid %}?{{ request.GET.urlencode }}")

 const options = {
  keySystems: {
   "com.apple.fps.1_0": playerBuilder.getFairplayKeySystemOptions()
  }
 }

 function onneedkey(event) {
  const eventBus = new EventBus();
  window.videojsContribEme.handleWebKitNeedKeyEvent(event, options, eventBus)
 }

 function onerror(event) {
   Sentry.init({
    dsn: "{{ sentry_url }}",
   });
   Sentry.captureMessage(event)
 }


 // This is to listen events sent by videojs contrib eme. event_bus parameter is must, so we give this.
 function EventBus() {
  const subscribers = {};

  this.on = function(eventType, callback) {
   if (!subscribers[eventType]) {
    subscribers[eventType] = [];
   }
   subscribers[eventType].push(callback);
  };

  this.trigger = function(eventType, data) {
   if (subscribers[eventType]) {
    subscribers[eventType].forEach(function(callback) {
     callback(data);
    });
   }
  };

  this.off = function(eventType, callback) {
   if (subscribers[eventType]) {
    var index = subscribers[eventType].indexOf(callback);
    if (index !== -1) {
     subscribers[eventType].splice(index, 1);
    }
   }
  };
 }


 // Video Events
 const video = document.querySelector("video")

 video.addEventListener("loadeddata", ()=>{
  postMessage({ event: "loadeddata", status: "success" }, '*')
 })

 video.addEventListener("loadedmetadata", ()=>{
  postMessage({ event: "loadedmetadata", status: "success" }, '*')
 })

 function postMessage(message) {
  if (isEmbedded()) {
   window.parent.postMessage(message, "*");
  }
 }

 window.onmessage = function (event) {
  if (event.data.methodName) {
   handleMethodCall(event.data.methodName, event.data.argument);
  }
 };

 function handleMethodCall(methodName, argument) {
  const method = window[methodName];
  if (!method) return;

  try {
   const value = method(argument);
   postMessage({method: methodName, value, status: "success"});
  } catch (error) {
   postMessage({method: methodName, error, status: "failure"});
  }
 }

 function isEmbedded() {
  return self !== top
 }

 function play() {
  video.play();
 }

 function pause() {
  video.pause();
 }

 function setCurrentTime(seconds) {
  video.currentTime = seconds;
 }

 function getCurrentTime() {
  return video.currentTime;
 }

 function getPaused() {
  return video.paused;
 }

 function getDuration(){
  return video.duration
 }

 function setPlaybackRate(rate) {
  return video.playbackRate = rate;
 }

 function getPlaybackRate() {
  return video.playbackRate;
 }

 function played() {
  let result = []
  const timeRanges = video.played
  for (let i = 0; i < timeRanges.length; i++) {
   result.push([timeRanges.start(i), timeRanges.end(i)])
  }
  return result
 }

 function addEventListenerToPlayer(eventName) {
  video.addEventListener(eventName, function (e) {
   postMessage({event: eventName})
  })
 }

 function removeEventListenerFromPlayer(eventName) {
  video.removeEventListener(eventName)
 }
</script>
