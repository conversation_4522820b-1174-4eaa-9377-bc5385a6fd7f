{% load static %}


<script src="https://unpkg.com/plyr@3"></script>
<script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>

<script>
 document.addEventListener('DOMContentLoaded', () => {
   const video = document.querySelector('video');
   const defaultOptions = {};

   if (!Hls.isSupported()) {
     video.src = source;
     const player = new Plyr(video, defaultOptions);
   } else {
     const hls = new Hls({
       "maxLiveSyncPlaybackRate": 1.5,
       "debug": true,
       "enableWorker": true,
       "lowLatencyMode": true,
       "backBufferLength": 90,
     });
     hls.loadSource('{{ hls_url |escapejs }}');
     hls.on(Hls.Events.MANIFEST_PARSED, function (event, data) {
        const availableQualities = hls.levels.map((l) => l.height)
        availableQualities.unshift(0)
        defaultOptions.quality = {
         default: 0,
         options: availableQualities,
         forced: true,
         onChange: (e) => updateQuality(e),
        }

        defaultOptions.i18n = {
          qualityLabel: {
            0: 'Auto',
          },
        }

        hls.on(Hls.Events.LEVEL_SWITCHED, function (event, data) {
          const span = document.querySelector(".plyr__menu__container [data-plyr='quality'][value='0'] span")
          if (hls.autoLevelEnabled) {
            span.innerHTML = `AUTO (${hls.levels[data.level].height}p)`
          } else {
            span.innerHTML = `AUTO`
          }
      })

      const player = new Plyr(video, defaultOptions)
     });

     hls.attachMedia(video);
     window.hls = hls;
   }

    function updateQuality(newQuality) {
      if (newQuality === 0) {
        window.hls.currentLevel = -1; //Enable AUTO quality if option.value = 0
      } else {
        window.hls.levels.forEach((level, levelIndex) => {
          if (level.height === newQuality) {
            window.hls.currentLevel = levelIndex;
          }
        });
      }
    }
});
</script>
