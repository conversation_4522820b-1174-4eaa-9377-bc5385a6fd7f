<script>
  let preferencesFetched = false;

  player.on('loadeddata', function() {
    setTimeout(() => {
      fetchAndApplyUserPreferences();
    }, 500)
  });

  player.on('firstplay', function() {
    fetchAndApplyUserPreferences();
  });

  async function fetchAndApplyUserPreferences() {
    if (preferencesFetched) return;

    const preferences = await fetchUserPlayerPreferences();

    if (preferences && Object.keys(preferences).length > 0) {
      applyPreferredPlaybackSpeed(preferences.preferred_playback_speed);
      applyPreferredQuality(preferences.preferred_video_quality);
      setSpeedChangeListener();
      setQualityChangeListener();
      preferencesFetched = true;
    }
  }

  function setSpeedChangeListener() {
    let speedSelector = document.querySelector('#video-setting-menu-child-menu-playbackratemenubutton');

    if (speedSelector) {
      let qualityItems = speedSelector.querySelectorAll('li.vjs-menu-item');
      for (let i = 0; i < qualityItems.length; i++) {
        qualityItems[i].addEventListener('click', function() {
          setTimeout(() => savePlayerPreference(), 50);
        });
      }
    }
  };

  function setQualityChangeListener() {
    let qualitySelector = document.querySelector('.vjs-quality-selector');

    if (qualitySelector) {
      let qualityItems = qualitySelector.querySelectorAll('li.vjs-menu-item');
      for (let i = 0; i < qualityItems.length; i++) {
        qualityItems[i].addEventListener('click', function() {
          setTimeout(() => savePlayerPreference(), 1000);
        });
      }
    }
  };

  function hasEnabledSavedPreferences() {
    return getPlayerUserId() !== null;
  }

  async function fetchUserPlayerPreferences() {
    if (!hasEnabledSavedPreferences()) return {};

    const response = await fetch('{{ cassandra_server_url }}/api/player/player-preference/', {
      method: 'POST',
      headers: {
          'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: getPlayerUserId(),
        organization_id: '{{ object.organization.uuid }}',
      }),
    });

    if (response.ok) {
      const data = await response.json();
      return data;
    }
    return {};
  }

  function applyPreferredPlaybackSpeed(speed) {
    speed = Number(speed);
    if (speed && isValidPlaybackSpeed(speed)) {
      player.playbackRate(speed);

      setTimeout(() => {
        let rateIndicator = document.querySelector('#video-setting-menu-child-span-playbackratemenubutton');
        if (rateIndicator) rateIndicator.innerHTML = `${speed}x`;
      }, 50)
    }
  }

  function isValidPlaybackSpeed(speed) {
    return player.playbackRates().includes(speed);
  }

  function applyPreferredQuality(qualityToSelect) {
    if (!qualityToSelect) return;

    let qualityItems = document.querySelectorAll('.vjs-quality-selector .vjs-menu-item');

    for (let i = 0; i < qualityItems.length; i++) {
      let itemText = qualityItems[i].querySelector('.vjs-menu-item-text').textContent;
      if (itemText === `${qualityToSelect}p`) {
        qualityItems[i].click();
        break;
      }
    }
  }

  function savePlayerPreference() {
    if (!hasEnabledSavedPreferences()) return;

    const currentQuality = player.hlsQualitySelector.getCurrentQuality();

    fetch('{{ cassandra_server_url }}/api/player/update-player-preference/', {
      method: 'POST',
      headers: {
          'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: getPlayerUserId(),
        organization_id: '{{ object.organization.uuid }}',
        preferred_playback_speed: player.playbackRate(),
        preferred_video_quality: currentQuality == "auto" ? null : currentQuality,
      }),
    });
  }
</script>
