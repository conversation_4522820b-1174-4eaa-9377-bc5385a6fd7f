<script>
    document.addEventListener('DOMContentLoaded', () => {
        document.querySelectorAll('video').forEach(video => {
            applyUserPreferences(video);
            setVolumeChangeListener(video);
        });
    });

    function applyUserPreferences(video) {
        if (!isLocalStorageAvailable()) {
            console.warn('localStorage is not available');
            return;
        }

        let preferences = {};
        try {
            preferences = JSON.parse(localStorage.getItem(PREFERENCES_KEY) || '{}');
        } catch (error) {
            console.warn('Failed to retrieve preferences:', error);
            return;
        }

        applyVolumePreference(video, preferences.volume);
        applyMutePreference(video, preferences.isMuted);
        video.dispatchEvent(new Event('volumechange'));
    }

    function applyVolumePreference(video, volume){
        if (volume != null) {
            const parsedVolume = parseFloat(volume);
            if (!isNaN(parsedVolume) && parsedVolume >= 0 && parsedVolume <= 1) {
                video.volume = parsedVolume;
            }
        }
    }

    function applyMutePreference(video, isMuted){
        if (isMuted != null) {
            video.muted = isMuted;
        }
    }

    function setVolumeChangeListener(video) {
        const debouncedSavePreferences = debounce((volume, isMuted) => {
            if (!isLocalStorageAvailable()) {
                console.warn('localStorage is not available');
                return;
            }

            storePlayerPreferencesInLocalStorage({
                volume: volume,
                isMuted: isMuted,
            });
        }, 300);

        video.addEventListener('volumechange', () => {
            debouncedSavePreferences(video.volume, video.muted);
        });
    }

    function debounce(fn, delay) {
        let timer;
        return function (...args) {
            clearTimeout(timer);
            timer = setTimeout(() => fn(...args), delay);
        };
    }
</script>
