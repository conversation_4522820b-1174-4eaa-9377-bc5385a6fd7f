<script>
    let captionsPreferencesFetched = false;

    player.on('loadedmetadata', () => {
        setTimeout(() => {
            getAndApplyCaptionPreferences();
        }, 500);
    });

    player.on('firstplay', () => {
        getAndApplyCaptionPreferences();
    });

    function getAndApplyCaptionPreferences(){
        if (captionsPreferencesFetched) return;

        const preferences = getPlayerPreferencesFromLocalStorage();
        if (preferences) {
            applyCaptionPreferences(preferences);
            applyCaptionTextStyles(preferences);
        }

        setCaptionChangeListener();
        setCaptionStyleChangeListener();

        captionsPreferencesFetched = true;
    }

    function applyCaptionPreferences(preferences) {
        try {
            const tracks = Array.from(player.textTracks()).filter(track =>
                track.kind === 'captions' || track.kind === 'subtitles'
            );

            if (tracks.length === 0) {
                return;
            }

            const savedLabel = preferences.captionLabel;
            const captionsEnabled = preferences.captionsEnabled;

            if (captionsEnabled && savedLabel) {
                for (const track of tracks) {
                    if (track.label.trim().toLowerCase() === savedLabel.trim().toLowerCase()) {
                        track.mode = 'showing';
                        updateCaptionButtonLabel(track.label);
                    } else {
                        track.mode = 'disabled';
                    }
                }
            }
        } catch (error) {
            console.error('Failed to apply caption preferences:', error);
        }
    }

    function updateCaptionButtonLabel(label) {
        const subsCapsButton = player.el().querySelector('#video-setting-menu-child-span-subscapsbutton');
        if (subsCapsButton) {
            subsCapsButton.textContent = label;
        }
    }

    function applyCaptionTextStyles(preferences) {
        const captionStyles = preferences.captionStyles;

        if (!player.textTrackSettings) {
            console.warn('player.textTrackSettings is not available');
            return;
        }

        if (captionStyles) {
            try {
                player.textTrackSettings.setValues(captionStyles);
                player.textTrackSettings.updateDisplay();
            } catch (error) {
                console.warn('Failed to apply caption styles:', error);
            }
        }
    }

    function setCaptionChangeListener() {
        const tracks = player.textTracks();

        tracks.on('change', () => {
            const activeTrack = Array.from(tracks).find(track => track.mode === 'showing');
            const isEnabled = !!activeTrack;

            storePlayerPreferencesInLocalStorage({
                captionLabel: activeTrack?.label || null,
                captionsEnabled: isEnabled
            });
        });
    }

    function setCaptionStyleChangeListener() {
        const textTrackSettings = player.textTrackSettings;

        const debouncedSavePreferences = debounce((values) => {
            storePlayerPreferencesInLocalStorage({
                captionStyles: values
            });
        }, 300);

        textTrackSettings.on('change', () => {
            const currentValues = textTrackSettings.getValues();
            debouncedSavePreferences(currentValues);
        });

        const resetButton = player.el().querySelector('.vjs-default-button');
        if (resetButton) {
            resetButton.addEventListener('click', () => {
                const defaultValues = textTrackSettings.getValues();
                debouncedSavePreferences(defaultValues);
            });
        }
    }
</script>
