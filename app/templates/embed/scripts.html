{% load static %}

{% if "true" in request.GET.debug %}
  <script src="{{ static_url }}{% static 'js/video.js' %}?v=7.21.4"></script>
{% else %}
  <script src="{{ static_url }}{% static 'js/video.min.js' %}?v=7.21.4"></script>
{% endif %}

<script src="{{ static_url }}{% static 'js/videojs-contrib-quality-levels.min.js' %}"></script>
<script src="{{ static_url }}{% static 'js/videojs-hls-quality-selector.min.js' %}"></script>

{% if "true" in request.GET.enable_debug %}
  <script src="{{ static_url }}{% static 'js/videojs-contrib-eme-with-log.js' %}"></script>
{% else %}
  <script src="{{ static_url }}{% static 'js/videojs-contrib-eme.js' %}"></script>
{% endif %}

<script src="{{ static_url }}{% static 'js/player.js' %}?v=2.6"></script>

{% if not request.GET.background or request.GET.background == "0" %}
  <script src="{{ static_url }}{% static 'js/double_tap_to_seek.js' %}"></script>
{% endif %}

<script src="{{ static_url }}{% static 'js/video_analytics.js' %}"></script>
<script src="{{ static_url }}{% static 'js/video_watermark.js' %}?v=2.0"></script>
<script src="{{ static_url }}{% static 'js/videojs-landscape-fullscreen.min.js' %}"></script>
<script src="{{ static_url }}{% static 'js/videojs-hotkeys-min.js' %}"></script>
<script src="{{ static_url }}{% static 'js/sentry.min.js' %}"></script>
<script src="{{ static_url }}{% static 'js/detectIncognito.min.js' %}"></script>
<script src="{{ static_url }}{% static 'js/videojs-sprite-thumbnails.min.js' %}"></script>
<script src="{{ static_url }}{% static 'js/settings.min.js' %}?v=7.21.4"></script>
<script src="{{ static_url }}{% static 'js/chapter-plugin.min.js' %}"></script>

{% if is_low_latency_stream %}
  <script src="{{ static_url }}{% static 'js/amazon-ivs-videojs-tech.min.js' %}"></script>
  <script src="{{ static_url }}{% static 'js/amazon-ivs-quality-plugin.min.js' %}"></script>
{% endif %}

{% if "markers" in request.GET.plugins %}
  <script src="{{ static_url }}{% static 'js/videojs-markers.min.js' %}"></script>
{% endif %}



<script>
  const {src, type} = getVideoOptions()

  {% if is_low_latency_stream %}
   registerIVSTech(videojs);
   registerIVSQualityPlugin(videojs);
  {% endif %}

  let controlsParam = "{{ request.GET.controls|default:'1' }}";
  let enableControls = (controlsParam !== '0');
  let backgroundParam = "{{ request.GET.background|default:'0' }}";
  let enableBackgroundMode = (backgroundParam === '1');
  let muteParam = "{{ request.GET.muted|default:'0' }}";
  let enableMute = (muteParam === '1');
  let autoPlayParam = "{{ request.GET.autoplay|default:'0' }}";
  let enableAutoPlay = (autoPlayParam === '1');
  let pipParam = "{{ request.GET.pip|default:'1' }}";
  let enablePictureInPicture = (pipParam !== '0');
  let loopParam = "{{ request.GET.loop|default:'0' }}";
  let enableLoopMode = (loopParam === '1');
  let isCDNAvailable = {{ is_cdn_available|yesno:"true,false" }};
  let playRates = "{{ request.GET.playRates|default:'0.5, 0.75, 1, 1.25, 1.5, 1.75, 2, 3, 4'|escapejs }}";
  let playRatesArray = playRates.split(',')
   .map(rate => parseFloat(rate.trim()))
   .filter(rate => !isNaN(rate))
   .filter(rate => rate >= 0.5 && rate <= 4);

  const playerBuilder = new PlayerBuilder()
  .setVideoDetails(src, type)
  .useAWSIVFSDK({{ is_low_latency_stream|yesno:"true,false" }})
  .setCSRFToken("{{ csrf_token }}")
  .enableLiveUI(true)
  .enableResponsive(true)
  .enableDoubleClick(false)
  .enableFluidUI(true)
  .enableCachingEncryptionKeys(true)
  .setPlaybackRates(playRatesArray)
  .setFairplayCertificateURL("{{ fairplay_certificate_url }}")
  .setDRMURL("{% url 'api:drm-license' asset_id=object.uuid organization_id=object.organization.uuid %}?{{ request.GET.urlencode|safe }}")
  .usePlayerDimensionsForAdaptiveness(false)
  .setBackgroundMode(enableBackgroundMode)
  .setMute(enableMute)
  .setControls(enableControls)



  let background_mode_enabled = (backgroundParam === '1') ? enableBackgroundMode : {{ player_preference.background_mode_enabled|lower }};
  let mute_mode_enabled = (muteParam === '1') ? enableMute : {{ player_preference.muted_on_start|lower }};
  let autoplay_enabled = (autoPlayParam === '1') ? enableAutoPlay : {{ player_preference.autoplay_enabled|lower }};
  let loop_enabled = (loopParam === '1') ? enableLoopMode : {{ player_preference.loop_enabled|lower }};


  playerBuilder.setBackgroundMode(background_mode_enabled);
  playerBuilder.setMute(mute_mode_enabled);
  playerBuilder.setLoop(loop_enabled);

  if(autoplay_enabled){
    playerBuilder.setMute(autoplay_enabled);

    playerBuilder.setAutoplay(autoplay_enabled);

  }

  {% if player_preference %}

      {% if not player_preference.show_speed_control %}
        playerBuilder.setPlaybackRates([]);
      {% endif %}

      {% if player_preference.show_picture_in_picture_control %}
        playerBuilder.setPictureInPicture({{ player_preference.show_picture_in_picture_control|lower }});
      {% endif %}

  {% endif %}


  const player = playerBuilder.build();

  const debugEnabled = (new URLSearchParams(window.location.search)).get('enable_debug') === 'true';

  function debugLog(message, ...args) {
        if (debugEnabled) {
            console.log(message, ...args);
        }
    }


  var myVideo = document.querySelector('video');
  if (myVideo) {
    myVideo.setAttribute('playsinline', '');

    ['fullscreenchange', 'webkitfullscreenchange', 'mozfullscreenchange', 'MSFullscreenChange'].forEach(event => {
        document.addEventListener(event, () => {
            if (document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement) {
                debugLog('Entered Fullscreen Mode');
            } else {
                debugLog('Exited Fullscreen Mode');
            }
        });
    });

    myVideo.addEventListener('error', (event) => {
        debugLog('Error in Video Playback:', event);
    });

    myVideo.addEventListener('fullscreenerror', (event) => {
        debugLog('Error during Fullscreen:', event);
    });
}

  function handleIpadBug() {
   // For a specific iPad "webkitneedkey" event is not getting triggered. So this will add queryparam 'iosFallback'
   // for that case and reload the page.
   if (window.WebKitMediaKeys) {
    const video = document.querySelector("video")
    var timeoutId = setTimeout(function() {
     openFallbackPage()
    }, 1000);

    video.addEventListener("webkitneedkey", () => {
     clearTimeout(timeoutId);
    })

    function openFallbackPage() {
     const url = new URL(window.location.href);
     if (url.searchParams.get("iosFallback") === true) {
      return
     }

     url.searchParams.set("iosFallback", true);
     window.location.href = url.href;
    }
   }
  }

    {% if "true" in request.GET.enable_touch_toggle or  object.organization.uuid == "jhf4p8"  %}
    player.on('touchstart', function(event) {
      if (!event.target.closest('.vjs-control-bar')) {
        if (player.paused()) {
          player.play();
        } else {
          player.pause();
        }
      }
    });
    {% endif %}

    {% if "true" in request.GET.enable_debug %}
    player.on("waiting", () => {
      const bufferingTime = player.currentTime();

      const hours = Math.floor(bufferingTime / 3600);
      const minutes = Math.floor((bufferingTime % 3600) / 60);
      const seconds = Math.floor(bufferingTime % 60);

      const formattedTime =
        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

      console.log("Buffering at:", formattedTime);
    });
    {% endif %}



    // Enable inline playback on mobile devices.
    var myVideo = document.querySelector('video');
    if (myVideo) {
        myVideo.setAttribute('playsinline', '');
    }

    var bigPlayButton = document.querySelector('.video-js .vjs-big-play-button');
    var playControl = document.querySelector('.video-js .vjs-play-control');
    var progressBar = document.querySelector('.video-js .vjs-progress-holder');
    var remainingTime = document.querySelector('.video-js .vjs-remaining-time-display');
    var currentTime = document.querySelector('.video-js .vjs-current-time-display');
    var volumePanel = document.querySelector('.video-js .vjs-volume-panel');
    const pipButton = document.querySelector('.vjs-picture-in-picture-control');
    const fullScreenButton = document.querySelector('.vjs-fullscreen-control');
    var videoElement = document.querySelector('.video-js');

    player.on("loadedmetadata", () => {


      {% if not player_preference.show_play_button %}

          bigPlayButton.style.display = 'none';
          playControl.style.display = 'none';

      {% endif %}


      {% if player_preference.show_play_button %}
      // Handle play button visibility
      {% if player_preference.get_play_button_position_display == 'Bottom' %}
        bigPlayButton.style.display = 'none';
              player.on('pause', function() {
                videoElement.classList.add('pause')
                player.controlBar.show();
              player.one('play', function() {
                  videoElement.classList.remove('pause')
                });
              });
      {% endif %}


      {% if player_preference.get_play_button_position_display == 'Center' %}
         playControl.style.display = 'none';
          player.on('pause', function() {
            bigPlayButton.style.display = 'block';
            videoElement.classList.add('pause')

            player.controlBar.show();
          player.one('play', function() {
              bigPlayButton.style.display = 'none';
              videoElement.classList.remove('pause')

            });
          });
        {% endif %}



        {% if player_preference.get_play_button_position_display == 'Auto' %}
           player.on('pause', function() {
             bigPlayButton.style.display = 'block';
             playControl.style.display = 'block';
             videoElement.classList.add('pause')
             player.controlBar.show();
           player.one('play', function() {
               bigPlayButton.style.display = 'none';
               videoElement.classList.remove('pause')
             });
           });
       {% endif %}

       {% endif %}

      // Handle progress bar visibility
      {% if not player_preference.show_progress_bar %}
        progressBar.style.display = 'none';
        playControl.style.display = 'none';
        remainingTime.style.display = 'none';
        currentTime.style.display = 'none';
      {% endif %}

      // Handle volume bar visibility
      {% if not player_preference.show_volume_bar %}
        volumePanel.style.setProperty('display', 'none', 'important');
      {% endif %}

      // Set default subtitle language
      {% if player_preference.default_subtitle_language %}
        var tracks = player.textTracks();
        var defaultLanguage = '{{ player_preference.default_subtitle_language }}';
        for (var i = 0; i < tracks.length; i++) {
          var track = tracks[i];
          track.mode = track.language === defaultLanguage ? 'showing' : 'disabled';
        }
      {% endif %}

      // Handle full screen controls visibility
      {% if not player_preference.show_full_screen_control %}
        fullScreenButton.style.display = 'none';
      {% endif %}


            // Handle full screen controls visibility
        {% if not player_preference.show_picture_in_picture_control %}
          pipButton.style.display = 'none';
        {% endif %}


      // Set default quality
      {% if player_preference.default_quality %}
        var qualityLevels = player.qualityLevels();
        for (var i = 0; i < qualityLevels.length; i++) {
          qualityLevels[i].enabled = qualityLevels[i].height === {{ player_preference.default_quality }};
        }
      {% endif %}

      // Show video title
      {% if player_preference.show_video_title %}
        var title = '{{ object.title }}';
        var titleElement = document.createElement('div');
        titleElement.className = 'vjs-title';
        titleElement.textContent = title;
        var videoContainer = player.el();
        videoContainer.appendChild(titleElement);
      {% endif %}


    });


    if (background_mode_enabled==true) {
      console.log(background_mode_enabled);
      pipButton.style.display = 'none';
      pipButton.style.display = 'none';
      volumePanel.style.setProperty('display', 'none', 'important');
      progressBar.style.display = 'none';
      playControl.style.display = 'none';
      remainingTime.style.display = 'none';
      currentTime.style.display = 'none';
      playControl.style.display = 'none';
      bigPlayButton.style.display = 'none';
      fullScreenButton.style.display = 'none';
      videoElement.style.pointerEvents = 'none';

    }


  var remainingTimeElement = document.querySelector('.video-js .vjs-remaining-time');
  var currentTimeElement = document.querySelector('.video-js .vjs-current-time');

  remainingTimeElement.style.display = 'block';
  currentTimeElement.style.display = 'none';

  remainingTimeElement.addEventListener('click', function() {
      remainingTimeElement.style.display = 'none';
      currentTimeElement.style.display = 'block';
  });

  currentTimeElement.addEventListener('click', function() {
      currentTimeElement.style.display = 'none';
      remainingTimeElement.style.display = 'block';
  });

  player.on('volumechange', function() {
    if (player.volume() === 0 || player.muted()) {
      videoElement.classList.add('mute');
    } else {
      videoElement.classList.remove('mute');
    }
  });

  player.on('fullscreenchange', function () {
    if (player.isFullscreen()) {
      videoElement.classList.add('full-screen');
    } else {
      videoElement.classList.remove('full-screen');
    }
  });

  player.on('ratechange', function() {
    const playbackRateSpan = videoElement.querySelector('#' + player.id() + '-setting-menu-child-span-playbackratemenubutton');
    if (playbackRateSpan) {
      playbackRateSpan.textContent = player.playbackRate() + "x";
    }
  });


  {% if is_low_latency_stream %}
    player.enableIVSQualityPlugin();
  {% endif %}


  {% if player_preference.show_subtitles or player_preference.show_speed_control %}
    {% if  not object.organization.uuid == "9mpasc" %}
      player.settingsMenu();
    {% endif %}
  {% endif %}


  {% if preview_thumbnail %}
    player.spriteThumbnails({
      interval:{{ preview_thumbnail.interval }},
      url: '{{ object.organization.cdn_url }}'+'{{ preview_thumbnail.url }}',
      width: {{ preview_thumbnail.width }},
      height: {{ preview_thumbnail.height }},
      rows:{{ preview_thumbnail.rows }},
      columns:{{ preview_thumbnail.columns }},
    });
    {% endif %}

  var chapters = {{ chapters|safe }};
  {% if chapters %}
    player.chapter({
      markers:  chapters,
    });
  {% endif %}


  let hasErrorOccurred = false;
  const clientActionsLogger = []
  showBlankScreenForScreenSharing()

  // Prevent default behavior of mouse wheel over video element
  player.on('ready', function() {
    var videoElement = player.el();
    videoElement.addEventListener('wheel', function(e) {
      e.stopPropagation();
    });
  });

  // Enter fullscreen mode in landscape mode
  player.landscapeFullscreen({
    fullscreen: {
      enterOnRotate: false,
      exitOnRotate: false,
      alwaysInLandscapeMode: true,
      iOS: false
    }
  })

  Sentry.init({
   dsn: "{{ sentry_url }}",
  });

  player.on("error", () => {
   const error = player.error();
   error["playerId"] = [...Array(10)].map(() => Math.random().toString(36)[2]).join('')
   postLoadedDataErrorMessage(error);

   if (!hasErrorOccurred) {
     displayError(error)
     logErrorInSentry(error)
     hasErrorOccurred = true
   }
  })

  function showBlankScreenForScreenSharing() {
    var originalWarn = videojs.log.warn;
    videojs.log.warn = function (message) {
      if (message && message.includes('DRM keystatus changed to "output-restricted."')) {
        player.error({
          code: 3,
          message: "HDMI mirrioring and screen sharing is restricted"
        });
      }
      originalWarn.apply(this, arguments);
    };
  }

  async function displayError() {
    const error = player.error();
    const errorMessage = await getVideoErrorMessage(error)
    document.querySelector(".vjs-modal-dialog-content").innerHTML = `
      <div class="errorModal">
        <div>
          <p>Player Code: ${error.playerId} </p>
          <h3 class="text-sm sm:text-xl">${errorMessage}</h3>
          <p>Error Code: ${error.code}</p>
        </div>
      </div>
    `
  }

  async function getVideoErrorMessage(error) {
    if(videojs.browser.IS_ANY_SAFARI){
      if (!isCDNAvailable) {
        return "Unable to play the video as the CDN is unreachable or there may be a network issue. Please try changing the network.";
      }
      return error.message
    }

    const isWidevineDRMSupported = await isWidevineSupported();
    const isPrivateWindow = await detectIncognito();
    if(isDRMError(error) && !isCookieEnabled()) {
      return this.getCookieEnablingHelpMessageForBrowser()
    }

    if (isBrowserNotSupported(error)) {
      return "Sorry, your browser is not supported. Please use the latest version of Chrome, Firefox, " +
        "or Safari to access our website. Using an outdated browser or a non-supported browser may result " +
        "in unexpected behavior and potential security risks. Thank you for your understanding"
    }
    if (videojs.browser["IS_ANDROID"] && isPrivateWindow.isPrivate) {
      return "Sorry, DRM-protected content wont play in private window try switching to normal window"
    }
    if (isDRMError(error)) {
      return "Sorry, unable to play DRM-protected content.<br>" +
             "Follow these <a href='https://tpstreams.com/help/error-unable-to-play-drm-protected-content/' target='_blank' class='text-blue-500'>instructions</a> to fix"
    }
   if (!isCDNAvailable) {
      return "Unable to play the video as the CDN is unreachable or there may be a network issue. Please try changing the network.";
    }
    return error.message
  }

  function isBrowserNotSupported(error) {
    return error.code === 4 && videojs.browser["IS_CHROME"] && videojs.browser["CHROME_VERSION"] < 100
  }

  function isDRMError(error) {
    return error.code === 5
  }

  async function logErrorInSentry(error) {
    const isWidevineDRMSupported = await isWidevineSupported();
    const isPrivateWindow = await detectIncognito();
    Sentry.captureMessage(error.message, {
      tags: {
        type: "Tpstreams-video",
        playerId: error.playerId,
        organization: "{{ object.organization.uuid }}",
        isWidevineSupported: isWidevineDRMSupported,
        cookieEnabled: isCookieEnabled(),
        errorCode: error.code,
        url: document.referrer,
        assetId: "{{ object.uuid }}",
        is_dev: "{{ debug|yesno:'yes,no' }}",
        isPrivateWindow: isPrivateWindow.isPrivate,
        drmLicenseCalls: window.drmLicenseCallCount,
      },
      extra: {
        videojsLogs: videojs.log.history(),
        error: error,
        isWidevineSupported: isWidevineDRMSupported,
        currentTime: getCurrentTime(),
        clientActionsLog: clientActionsLogger,
        timeRanges: played(),
        browser: videojs.browser
      }
    })
  }

  async function isWidevineSupported() {
    const config = [{
      "initDataTypes": ["cenc"],
      "audioCapabilities": [{
        "contentType": "audio/mp4;codecs=\"mp4a.40.2\""
      }],
      "videoCapabilities": [{
        "contentType": "video/mp4;codecs=\"avc1.42E01E\""
      }]
    }];

    let isSupported;
    try {
      await navigator.requestMediaKeySystemAccess("com.widevine.alpha", config)
      isSupported = true
    } catch (e) {
      isSupported = false;
    }
    return isSupported;
  }

  function isCookieEnabled(){
    var cookieEnabled = navigator.cookieEnabled;
    if (!cookieEnabled){
        document.cookie = "testcookie";
        cookieEnabled = document.cookie.indexOf("testcookie")!==-1;
    }
    return cookieEnabled;
  }

  function getCookieEnablingHelpMessageForBrowser() {
    const {
      IS_ANDROID,
      IS_WINDOWS,
      IS_IPHONE,
      IS_IPAD,
      IS_FIREFOX,
      IS_CHROME,
      IS_EDGE,
      IS_SAFARI
    } = videojs.browser;

    let docLink = "https://tpstreams.com/help/category/video-player-troubleshooting/";

    if (IS_ANDROID) {
      if (IS_FIREFOX) {
        docLink = "https://tpstreams.com/help/how-to-enable-cookies-in-firefox-on-android/";
      } else if (IS_CHROME) {
        docLink = "https://tpstreams.com/help/how-to-enable-cookies-in-chrome-for-android/";
      }
    }

    if (IS_WINDOWS) {
      if (IS_FIREFOX) {
        docLink = "https://tpstreams.com/help/how-to-enable-cookies-in-firefox-on-windows/";
      } else if (IS_CHROME) {
        docLink = "https://tpstreams.com/help/how-to-enable-cookies-in-chrome-on-windows/";
      } else if (IS_EDGE) {
        docLink = "https://tpstreams.com/help/how-to-enable-cookies-in-edge-on-windows/";
      }
    }

    if (IS_SAFARI) {
      docLink = "https://tpstreams.com/help/how-to-enable-cookies-in-safari-on-mac/";
    }

    if (IS_IPHONE && IS_SAFARI) {
      docLink = "https://tpstreams.com/help/how-to-enable-cookies-in-safari-for-iphone/";
    }

    if (IS_IPAD && IS_SAFARI) {
      docLink = "https://tpstreams.com/help/how-to-enable-cookies-in-safari-on-ipad/";
    }

    return `Unable to play protected content, This might because you've disabled cookies.\n` +
            `<a href='${docLink}' target="_blank" class="text-blue-500">Click</a> to learn how to enable them.`;
  }

  function getProtectedContentInstructionsLink(){
    const {
        IS_ANDROID,
        IS_WINDOWS,
        IS_FIREFOX,
        IS_CHROME,
        IS_EDGE
    } = videojs.browser;

    let docLink = "https://tpstreams.com/help/category/enable-proctected-content/";

    if (IS_CHROME){
        docLink = "https://tpstreams.com/help/how-to-enable-chrome-to-play-protected-content-on-macos/"
    }

    if (IS_FIREFOX){
        docLink = "https://tpstreams.com/help/how-to-enable-protected-content-in-firefox-on-macos/"
    }

    if (IS_ANDROID) {
        if (IS_FIREFOX) {
            docLink = "https://tpstreams.com/help/how-to-enable-protected-content-in-firefox-for-android/";
        } else if (IS_CHROME) {
            docLink = "https://tpstreams.com/help/how-to-enable-chrome-to-play-protected-videos-on-android/";
        }
    }

    if (IS_WINDOWS) {
        if (IS_FIREFOX) {
            docLink = "https://tpstreams.com/help/how-to-enable-protected-content-in-windoes-firefox/";
        } else if (IS_CHROME) {
            docLink = "https://tpstreams.com/help/how-to-enable-protected-content-in-chrome-for-windows/";
        } else if (IS_EDGE) {
            docLink = "https://tpstreams.com/help/how-to-enable-protected-content-on-edge-for-windows/";
        }
    }

    return docLink;
}

  player.ready(function () {
    {% if request.GET.focus == "true" %}
       player.focus();
    {% endif %}
    {% if player_preference.show_video_quality_control %}
        player.hlsQualitySelector({
          displayCurrentQuality: false,
        });
    {% endif %}
    postLoadedDataSuccessMessage();
    initHotKeys();
    new DoubleTapToSeekHandler();
    const organizationId = "{{ object.organization.uuid }}";
    const assetId = "{{ object.uuid }}";
    const queryString = "{{ request.GET.urlencode }}";
    const params = new URLSearchParams(queryString);
    const accessToken = params.get("access_token");
    const videoTrackingHandler = new VideoTrackingHandler(organizationId,assetId,accessToken)
    videoTrackingHandler.addVideoTracking(player);
    modifyAESEncryptionKeyRequest(accessToken)
  })

  function postLoadedDataSuccessMessage() {
    postMessage({event: "loadeddata", status: "success"}, '*')
  }

  function modifyAESEncryptionKeyRequest(accessToken){
    if(player.hls){
      player.hls.xhr.beforeRequest = (options) => {
        if (options.uri.includes("aes_key")) {
          options.uri += `?access_token=${accessToken}`;
          return options
        }
        return options
      }
    }
  }

  player.on("loadedmetadata", ()=>{
    postMessage({ event: "loadedmetadata", status: "success" }, '*')
  })

  function postMessage(message) {
    if (isEmbedded()) {
      window.parent.postMessage(message, "*");
    }
  }

  function isEmbedded() {
    return self !== top
  }

  function initHotKeys() {
    player.hotkeys({
      volumeStep: 0.1,
      seekStep: 5,
      enableModifiersForNumbers: false
    });
  }

  function setPlayerUserId(userId) {
    if (userId) {
      sessionStorage.setItem('playerUserId', userId);
    }
  }

  function getPlayerUserId() {
    return sessionStorage.getItem('playerUserId');
  }

  function getVideoOptions() {
    {% if has_drm %}
      if (isRequestFromSafari()) {
        return getVideoOptionsForHLS()
      } else {
        return getVideoOptionsForDASH()
      }
    {% else %}
      return getVideoOptionsForHLS()
    {% endif %}
  }

  function isRequestFromSafari() {
    return (/^((?!chrome|android).)*safari/i.test(navigator.userAgent))
  }

  function getVideoOptionsForHLS() {
    return {"src": `{{ hls_url |escapejs }}`, "type": "application/x-mpegURL"}
  }

  function getVideoOptionsForDASH() {
    return {"src": `{{ dash_url }}`, "type": "application/dash+xml"}
  }

  function postLoadedDataErrorMessage(errorMessage) {
    postMessage({
      event: "loadeddata",
      status: "failed",
      errorMessage: errorMessage,
    });
  }

  window.onmessage = function (event) {
    if (event.data.methodName)
      handleMethodCall(event.data.methodName, event.data.argument);
  };

  function handleMethodCall(methodName, argument) {
    clientActionsLogger.push({
      methodName, argument
    })
    const method = window[methodName];
    if (!method) return;

    try {
      value = method(argument);
      postMessage({method: methodName, value, status: "success"});
    } catch (error) {
      postMessage({method: methodName, error, status: "failure"});
    }
  }


  function play() {
    player.play();
  }

  function pause() {
    player.pause();
  }

  function setCurrentTime(seconds) {
    player.currentTime(seconds.toString());
  }

  function getCurrentTime() {
    return player.currentTime();
  }

  function getLoop() {
    return player.loop();
  }

  function setLoop(value) {
    return player.loop(value);
  }

  function getMuted() {
    return player.muted();
  }

  function setMuted(value) {
    return player.muted(value);
  }

  function getAutoplay() {
    return player.autoplay();
  }

  function setAutoplay(value) {
    return player.autoplay(value);
  }

  function getPlaybackRates() {
    return player.playbackRates();
  }

  function setPlaybackRate(rate) {
    return player.playbackRate(rate);
  }

  function getPlaybackRate() {
    return player.playbackRate();
  }

  function getVolume() {
    return player.volume();
  }

  function setVolume(value) {
    return player.volume(value);
  }

  function getEnded() {
    return player.ended();
  }

  function getPaused() {
    return player.paused();
  }

  function getDuration(){
    return player.duration()
  }

  function setEndScreenImage(url){
    player.on('ended', () => {
      player.poster(url);
      player.posterImage.show();
    });
  }

  function played() {
    let result = []
    const timeRanges = player.played()
    for (let i = 0; i < timeRanges.length; i++) {
      result.push([timeRanges.start(i), timeRanges.end(i)])
    }
    return result
  }

  function getCurrentPlaybackRate() {
    var videoElement = document.querySelector("video")
    if (videoElement) {
        return videoElement.playbackRate;
    }
    return null;
  }

  function addEventListenerToPlayer(eventName) {
    if (isCustomEvent()) return
    player.on(eventName, () => {
      postMessage({event: eventName})
    })
  }

  function removeEventListenerFromPlayer(eventName) {
    if (isCustomEvent()) return
    player.off(eventName)
  }

  function isCustomEvent(eventName) {
    return eventName in ["onMarkerClick", "onMarkerReached"]
  }

  function setMarkers(config) {
    raiseErrorIfMarkersPluginNotEnabled()

    player.markers({
        ...config,
        onMarkerClick: function (marker) {
          postMessage({event: "onMarkerClick", values: [marker]})
        },

        onMarkerReached: function (marker, index) {
          postMessage({event: "onMarkerReached", values: [marker, index]})
        },

        markerTip: {
          display: true,
          text: (marker) => marker.text
        }
      })
  }

  function removeMarkers(indices){
    raiseErrorIfMarkersPluginNotEnabled()

    player.markers.remove(indices)
  }

  function removeAllMarkers(){
    raiseErrorIfMarkersPluginNotEnabled()

    player.markers.removeAll()
  }

  function raiseErrorIfMarkersPluginNotEnabled(){
    let params = getQueryParams()
    if (!params.plugins.includes("markers"))
      throw Error("Video-js markers is not enabled. Please enable by passing enableMarkersPlugin=true via query param")
  }

  function getQueryParams(){
    return new Proxy(new URLSearchParams(window.location.search), {
        get: (searchParams, prop) => searchParams.get(prop),
    });
  }

  function applyWatermark(annotations) {
    if (annotations.length === 0)
      return

    const videoAnnotator = new VideoAnnotator("video", annotations)
    videoAnnotator.run()
  }

  applyWatermark({{ annotations | safe}});

  player.on('fullscreenchange', function () {
    if (isMobileDevice() || isTabletDevice()) {
      moveSettingsMenuToVideoContainer();
    }
  });

  function isMobileDevice() {
    const userAgent = navigator.userAgent.toLowerCase();
    return /iphone|ipod|android.*mobile|blackberry|opera mini|windows phone/.test(userAgent);
  }

  function isTabletDevice() {
    const userAgent = navigator.userAgent.toLowerCase();
    return /ipad|android(?!.*mobile)/.test(userAgent) || (navigator.maxTouchPoints > 1 && /macintosh/.test(userAgent));
  }

  function moveSettingsMenuToVideoContainer() {
    const mobileMenuSettings = document.querySelector('.vjs-settings-menu-mobile');
    const videoContainer = player.el();

    if (mobileMenuSettings && videoContainer && !videoContainer.contains(mobileMenuSettings)) {
      videoContainer.appendChild(mobileMenuSettings);
      const settingMenuHeaders = mobileMenuSettings.querySelectorAll('.setting-menu-header');
      settingMenuHeaders.forEach((header) => {
        header.style.setProperty('background-color', '#24292e', 'important');
        header.style.setProperty('color', '#fff', 'important');
      });
    }
  }

</script>

{% include "./device_specific_preferences_scripts.html" %}

{% if cassandra_server_url %}
  {% include "./resume_playback_scripts.html" %}
  {% include "./player_preferences_scripts.html" %}
{% endif %}

{% if "true" in request.GET.debug %}
  <script src="{% static 'js/debug_listeners.js' %}"></script>
{% endif %}
