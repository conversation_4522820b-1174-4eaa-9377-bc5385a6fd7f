{% extends "layouts/base.html" %}
{% load i18n static %}

{% block html_class %}h-full bg-white{% endblock html_class %}
{% block body_class %}h-full{% endblock body_class %}
{% block head_title %}{% translate "Video Embed" %}{% endblock %}

{% block extra_head %}
 {% if enable_llhls %}
   <link href="https://unpkg.com/plyr@3/dist/plyr.css" rel="stylesheet">
   <style>
    .plyr__controls {
      position: fixed !important;
    }
    video {
     background: black;
    }
   </style>
 {% else %}
  <link href="{% static 'css/video-js.min.css' %}" rel="stylesheet"/>
  <link href="{% static 'css/videojs-controls.min.css' %}" rel="stylesheet"/>
  <link href="{% static 'css/videojs-hls-quality-selector.css' %}" rel="stylesheet"/>
  <link href="{% static 'css/double_tap_seek.css' %}" rel="stylesheet"/>
  <link href="{% static 'css/video-js-playback-rate.min.css' %}" rel="stylesheet"/>
  <link href="{% static 'css/settings.min.css' %}" rel="stylesheet"/>
  <link href="{% static 'css/videojs-tps.min.css' %}" rel="stylesheet"/>
  <link href="{% static 'css/chapter-plugin.min.css' %}" rel="stylesheet"/>

  {% if player_preference %}
  <style>
      :root {
          --vjs-theme-plyr--primary: {{player_preference.primary_color}};
          --vjs-theme-plyr--icons: {{player_preference.icons_color}};
          --vjs-theme-plyr--accent: {{player_preference.accent_color}};
          --vjs-theme-plyr--background: {{player_preference.background_color}};
      }
  </style>
  {% endif %}


  {% if "markers" in request.GET.plugins %}
   <link href="{% static 'css/videojs.markers.min.css' %}" rel="stylesheet" />
  {% endif %}
  {% if "true" in request.GET.enable_debug %}
   <script src="//cdn.jsdelivr.net/npm/eruda"></script>
   <script>eruda.init();</script>
  {% endif %}
  <style>
   .vjs-error .vjs-error-display::before {
    display: none;
   }

   {% if object.live_stream and not object.video %}
    .video-js.vjs-live:not(.vjs-liveui) .vjs-seek-to-live-control,
    .video-js:not(.vjs-live) .vjs-seek-to-live-control {
     display: block !important;
    }
   {% endif %}
  </style>
 {% endif %}
{% endblock extra_head %}

{% block content %}
{% if object.video or object.live_stream %}
 {% if object.live_stream.get_status_display == "Not Started" and object.live_stream.get_server_status_display ==  "Not Created" and object.live_stream.start %}
      <div style="background-color: black; width: 100%; height: 0; padding-top: 56.25%; position: relative;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
            <p style="color: white; font-size: 18px;">Live stream is scheduled at {{ object.live_stream.start}}</p>
        </div>
        </div>
  {% elif object.live_stream.get_status_display == "Not Started" and object.live_stream.get_server_status_display == "Created" and object.live_stream.start %}
        <div style="background-color: black; width: 100%; height: 0; padding-top: 56.25%; position: relative;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
            <p style="color: white; font-size: 18px;">Live stream will begin soon</p>
        </div>
        </div>
  {% elif not is_live_stream_streaming_started %}
        <div style="background-color: black; width: 100%; height: 0; padding-top: 56.25%; position: relative;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
            <p style="color: white; font-size: 18px;">Live stream will begin soon</p>
        </div>
        </div>
 {% else %}
  <video
    id="video"
    class="video-js vjs-big-play-centered vjs-theme-plyr"
    controls
    preload="auto"
    {% if thumbnail_url %}
      poster="{{ thumbnail_url }}"
    {% endif %}
    style="
      position: fixed;
      right: 0;
      bottom: 0;
      min-width: 100%;
      min-height: 100%;
    ">
    <p class="vjs-no-js">
      To view this video please enable JavaScript, and consider upgrading to a web browser that
      <a href="https://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a>
    </p>

    {% if player_preference.show_subtitles %}

      {% for subtitle, url  in subtitles %}
        <track kind="subtitles" src="{{ url }}" srclang="{{subtitle.language}}" label="{{subtitle.name}}">
      {% endfor %}

    {% endif %}


  </video>
  {% include "embed/seek_views.html" %}
  {% endif %}
{% endif %}
{% endblock content %}

{% block extra_body %}
  {% if enable_llhls %}
    {% include "embed/llhls_script.html" %}
  {% elif "true" in request.GET.iosFallback and object.organization.uuid == "dcek2m" %}
    {% include "embed/ios_fallback.html" %}
  {% else %}
    {% include "embed/scripts.html" %}
  {% endif %}
{% endblock extra_body %}

{% block script %}
<script>


  {% if object.organization.uuid == "dcek2m" or object.organization.uuid == "jhf4p8"  %}

  window.addEventListener('load', function () {
      let videoElement = document.querySelector('video');

      if (videoElement) {
          // Apply aspect ratio fix when metadata is loaded
          videoElement.addEventListener('loadedmetadata', function () {
              adjustVideoFitForAspectRatio(videoElement);
          });

          // Fallback: Retry if video metadata isn't available immediately
          if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
              setTimeout(() => adjustVideoFitForAspectRatio(videoElement), 500);
          }
      }

      // Handle dynamically added videos
      observeVideoElements();
  });

  function adjustVideoFitForAspectRatio(videoElement) {
      if (!videoElement.videoWidth || !videoElement.videoHeight) return;

      let aspectRatio = videoElement.videoWidth / videoElement.videoHeight;

      if (aspectRatio >= 1.5 && aspectRatio <= 1.85) {
          videoElement.style.objectFit = 'cover';  // 16:9-like aspect ratio → Cover the container
      } else {
          videoElement.style.objectFit = 'contain';  // Other aspect ratios → Fit within container
      }
  }

function observeVideoElements() {
    let observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            mutation.addedNodes.forEach(node => {
                // If the added node is a video, apply fix
                if (node.tagName === 'VIDEO') {
                    node.addEventListener('loadedmetadata', function () {
                        adjustVideoFitForAspectRatio(node);
                    });
                }

                // If the added node contains video elements, apply fix to all
                let videos = node.querySelectorAll?.('video');
                if (videos) {
                    videos.forEach(video => {
                        video.addEventListener('loadedmetadata', function () {
                            adjustVideoFitForAspectRatio(video);
                        });
                    });
                }
            });
        });
    });

    observer.observe(document.body, { childList: true, subtree: true });
}


{% endif %}

  var is_live_stream_streaming_started = false;

  function checkLiveStreamStatus() {
    if (!is_live_stream_streaming_started) {
      const url = '{{ hls_url }}';

      fetch(url, { method: 'HEAD', })
        .then(response => {
          if (response.status === 200) {
            is_live_stream_streaming_started = true;
            console.log("Live stream is now streaming.");
          } else {
            console.error('Live stream m3u8 file not found, refreshing page.');
            setTimeout(function() {
              window.location.reload();
            }, 5000);
          }
        })
        .catch(error => {
          console.error('Error checking live stream status:', error);
          setTimeout(function() {
            window.location.reload();
          }, 5000);
        });
    }
  }

  {% if is_live_stream %}
    checkLiveStreamStatus();
  {% endif %}


player.on("loadedmetadata", () => {
  const tracks = player.textTracks();
  for (let i = 0; i < tracks.length; i++) {
      if (tracks[i].kind === "subtitles") {
          tracks[i].mode = "disabled";
      }
  }
});

</script>
{% endblock script %}
