{% extends "./base.html" %}

{% load i18n %}
{% load static %}

{% block head_title %}{% translate "Sign Up" %}{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="{% static 'css/intlTelInput.min.css' %}">
 <style>
  .iti {
   width: 100%;
  }
  .iti__selected-dial-code {
   font-size: 14px;
  }
 </style>
{% endblock %}

{% block content %}
<div class="flex min-h-full flex-col justify-center py-12 sm:px-6 lg:px-8">
  {% translate "Sign up for an account" as title %}
  {% translate "log in" as secondary_text %}
  {% include "./includes/logo.html" with title=title secondary_text=secondary_text alt_action_link=login_url %}
  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
    {% if request.GET.create_organization == "true" %}
      <script charset="utf-8" type="text/javascript" src="//js.hsforms.net/forms/embed/v2.js"></script>
        <script>
          hbspt.forms.create({
            region: "na1",
            portalId: "1548190",
            formId: "81b3079f-7816-49f1-aaa0-7559988bacef"
          });
        </script>
    {% else %}
        {% include "./includes/signup_form.html" %}
    {% endif %}
      {% comment %}
        {% include "./includes/social_buttons.html" %}
      {% endcomment %}
    </div>
  </div>
</div>
{% endblock %}


{% block extra_body %}
 <script src="{% static 'js/intlTelInput.min.js' %}"></script>
 <script>
  var phoneInput = document.querySelector("#id_phone_number");
  window.intlTelInput(phoneInput, {
   utilsScript: "{% static 'js/intlTelInputUtils.js' %}",
   hiddenInput: "phone_number",
   initialCountry: "{{ country_code }}",
   nationalMode: false,
   separateDialCode: true
  });
 </script>
{% endblock extra_body %}
