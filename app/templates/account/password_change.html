{% extends "./base.html" %}

{% load i18n %}

{% block head_title %}{% trans "Change Password" %}{% endblock %}

{% block content %}
<div class="flex min-h-full flex-col justify-center py-12 sm:px-6 lg:px-8">
  {% translate "Change Password" as title %}
  {% translate "Forgot Password?" as secondary_text %}
  {% url 'account_reset_password' as alt_action_link %}
  {% include "./includes/logo.html" with title=title secondary_text=secondary_text alt_action_link=alt_action_link %}
  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
      {% include "./includes/password_change_form.html" %}
    </div>
  </div>
</div>
{% endblock %}
