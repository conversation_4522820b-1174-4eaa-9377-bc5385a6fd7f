{% load i18n widget_tweaks %}
<form class="space-y-6" action="{% url 'account_login' %}" method="POST">
  {% csrf_token %}
  {% include "includes/form_errors.html" %}
  {% include "includes/input_field.html" with field=form.login attributes="autocomplete='email'" %}
  {% include "includes/input_field.html" with field=form.password attributes="autocomplete='current-password'" %}

  <div class="flex items-center justify-between">
    <div class="flex items-center">
      {{ form.remember|add_class:"h-4 w-4 rounded border-gray-300 text-blue-700 focus:ring-blue-600"}}
      <label for="{{ form.remember.auto_id}}-me" class="ml-2 block text-sm text-gray-900">{{ form.remember.label }}</label>
    </div>

    <div class="text-sm">
      <a href="{% url 'account_reset_password' %}" class="font-medium text-blue-700 hover:text-blue-600">{% translate "Forgot your password?" %}</a>
    </div>
  </div>

  {% if redirect_field_value %}
    <input type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}" />
  {% endif %}

  {% translate "Sign In" as sign_in_string %}
  {% include "includes/form_button.html" with value=sign_in_string %}
</form>
