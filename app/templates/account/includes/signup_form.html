{% load i18n widget_tweaks %}
<form class="space-y-6" action="{% url 'account_signup' %}?{{ request.GET.urlencode }}" method="POST" x-data="{ formSubmitting: false }" x-on:submit="formSubmitting = true">
  {% csrf_token %}
  {% include "includes/form_errors.html" %}
  {% include "includes/input_field.html" with field=form.name %}
  {% include "includes/input_field.html" with field=form.organization %}
  {% include "includes/input_field.html" with field=form.email %}
  {% include "includes/input_field.html" with field=form.password1 %}
  {% include "./phone_number_field.html" %}

  {% if redirect_field_value %}
    <input type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}" />
  {% endif %}

  {% translate "Sign Up" as sign_up_string %}
  {% include "includes/form_button.html" with value=sign_up_string %}
</form>
