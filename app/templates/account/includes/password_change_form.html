{% load i18n widget_tweaks %}
<form class="space-y-6" action="." method="POST">
  {% csrf_token %}
  {% include "includes/form_errors.html" %}
  {% if form.oldpassword %}
    {% include "includes/input_field.html" with field=form.oldpassword attributes="autocomplete='current-password'" %}
  {% endif %}
  {% include "includes/input_field.html" with field=form.password1 attributes="autocomplete='new-password'" %}
  {% include "includes/input_field.html" with field=form.password2 attributes="autocomplete='new-password'" %}

  {% translate "Change Password" as change_password_string %}
  {% include "includes/form_button.html" with value=change_password_string %}
</form>
