{% extends "./base.html" %}

{% load i18n %}

{% block head_title %}{% trans "Change Password" %}{% endblock %}

{% block content %}
<div class="flex min-h-full flex-col justify-center py-12 sm:px-6 lg:px-8">
  {% translate "Bad Token" as bad_token %}
  {% translate "Change Password" as title %}
  {% translate "Forgot Password?" as secondary_text %}
  {% url 'account_reset_password' as account_reset_link %}
  {% blocktranslate asvar bad_token_secondary_text %}The password reset link was invalid, possibly because it has already been used.  Please request a <a class="font-medium text-blue-700 hover:text-blue-600" href="{{ account_reset_link }}">new password reset</a>.{% endblocktranslate %}

  {% if token_fail %}
    {% include "./includes/logo.html" with title=bad_token secondary_text=bad_token_secondary_text %}
  {% else %}
    {% include "./includes/logo.html" with title=title secondary_text=secondary_text alt_action_link=account_reset_link %}
    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        {% include "./includes/password_change_form.html" %}
      </div>
    </div>
  {% endif %}
</div>
{% endblock %}
