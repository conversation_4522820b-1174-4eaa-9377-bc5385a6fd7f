  {% if  asset.get_status  == 'Streaming' or asset.get_status  == 'Uploading' %}
    <span class="inline-flex items-center rounded-full bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700">{{ asset.get_status }} </span>
  {% elif  'Error' in asset.get_status  %}
    <span class="inline-flex items-center rounded-full bg-red-100 px-1.5 py-0.5 text-xs font-medium text-red-700">{{ asset.get_status }} </span>
  {% elif  asset.get_status  == 'Not Started' %}
    <span class="inline-flex items-center rounded-full bg-gray-100 px-1.5 py-0.5 text-xs font-medium text-gray-600">{{ asset.get_status }} </span>
  {% elif  asset.get_status  == 'Disconnected' %}
    <span class="inline-flex items-center rounded-full bg-pink-100 px-1.5 py-0.5 text-xs font-medium text-pink-700">{{ asset.get_status }} </span>
    {% elif asset.get_status != 'Completed' and asset.get_status != 'Deleting' %}
    <span class="inline-flex items-center rounded-full bg-yellow-100 px-1.5 py-0.5 text-xs font-medium text-yellow-800">{{ asset.get_status }}</span>
  {% endif %}
