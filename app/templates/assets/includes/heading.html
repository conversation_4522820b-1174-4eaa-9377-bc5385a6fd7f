<div class="sm:flex">
  <div class="mt-1  sm:flex-auto">
    {% if folder %}
      {% include "./folder_breadcrumb.html" %}
      <h1 class="text-2xl font-semibold text-gray-900">Contents of {{ folder.title|truncatechars:60 }}</h1>
    {% else %}
      <h1 class="text-2xl font-semibold text-gray-900">{{ title }}</h1>
    {% endif %}
    {% if subtitle %}
      <pre class="mt-1 pl-1 pr-3 py-1 rounded text-sm text-gray-500 bg-gray-100 inline-block">{{ subtitle }}</pre>
    {% endif %}
  </div>
  <div class="flex items-end mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
    {% if request.resolver_match.view_name == 'assets' or request.resolver_match.view_name == 'asset_move' or request.resolver_match.view_name == "live_stream_move" %}
      <a type="button" @click="showAddFolderModal = true;"
        class="inline-flex mr-1 items-center cursor-pointer justify-center rounded-md border bg-gray-100 px-4 py-2 text-sm font-medium text-gray-600 shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-offset-2 sm:w-auto">Add Folder</a>
        {% if cta_text %}
          <a href="{{ cta_link }}" type="button" class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-700 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 sm:w-auto">{{ cta_text }}</a>
        {% endif %}
        {% if request.resolver_match.view_name == "asset_move" and not is_empty %}
          <form  class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-700 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 sm:w-auto" method="post" action="{% url 'asset_move' %}?{% get_updated_query_string name='folder' value=folder.uuid %}">
            {% csrf_token %}
            <button type="submit" >Move Here</button>
          </form>
        {% endif %}
        {% if request.resolver_match.view_name == "live_stream_move" and not is_empty %}
          <form  class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-700 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 sm:w-auto" method="post" action="{% url 'live_stream_move' %}?{% get_updated_query_string name='folder' value=folder.uuid %}">
            {% csrf_token %}
            <button type="submit" >Move Here</button>
          </form>
        {% endif %}
    {% endif %}
  </div>
</div>
