<div class="text-center py-12">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"
      aria-hidden="true">
      <path vector-effect="non-scaling-stroke" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
        d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
    </svg>
    {% if '/live/' in request.path %}
      <h3 class="mt-2 text-sm font-medium text-gray-900">No Live Streams</h3>
      <p class="mt-1 text-sm text-gray-500">Get started by creating a new LiveStream. Pricing starts at just $0.715/hr for 720p or go full HD with 1080p at $1.43/hr.</p>
    {% else %}
      <h3 class="mt-2 text-sm font-medium text-gray-900">No assets</h3>
      <p class="mt-1 text-sm text-gray-500">Get started by creating a new asset.</p>
    {% endif %}
    <div class="mt-6">

      <a type="button" href="{{ cta_link }}"
        class="inline-flex items-center rounded-md border border-transparent bg-blue-700 px-3 py-2 text-sm font-medium leading-4 text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2">
        <!-- Heroicon name: mini/plus -->
        <svg class="-ml-0.5 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
          fill="currentColor" aria-hidden="true">
          <path
            d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
        </svg>
        {% if '/live/' in request.path %}
          Create Live Stream
        {% else %}
          Upload Asset
        {% endif %}
      </a>
    </div>
  </div>
