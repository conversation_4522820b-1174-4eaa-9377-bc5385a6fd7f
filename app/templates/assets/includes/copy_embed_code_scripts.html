<script>
  document.addEventListener('alpine:init', () => {
    window.Alpine.data('Clipboard', () => ({
      copyEmbedCodebuttonText: "Copy Embed Code",
      copyPlaybackURLButtonText: "Copy Playback URL",
      loading: false,
      embedURL: "",
      tooltip: false,

      async copyEmbedCodeToClipboard(asset_id) {
        if (!this.embedURL) {
          this.loading = true
          this.embedURL = await this.getEmbedURL(asset_id)
        }
        await navigator.clipboard.writeText(this.getEmbedCode())
        this.loading = false
        this.copyEmbedCodebuttonText = "Copied"
        setTimeout(() => this.copyEmbedCodebuttonText = "Copy Embed Code", 3000)
      },

      async copy(data) {
        await navigator.clipboard.writeText(data)
        this.copyPlaybackURLButtonText = "Copied"
        setTimeout(() => this.copyPlaybackURLButtonText = "Copy Embed Code", 3000)
      },

      async getEmbedURL(asset_id) {
        let response = await (await fetch(`/api/v1/{{ request.user.current_organization_uuid }}/assets/${asset_id}/embed_url/`, {
          method: "POST",
          credentials: 'same-origin',
          headers: {
            'accept': 'application/json',
            'Content-Type': 'application/json',
            'X-CSRFToken': `{{ csrf_token }}`
          },
        })).json();

        return `${window.location.origin}${response.embed_url}`;
      },

      getEmbedCode() {
        return `<div style="padding-top:56.25%;position:relative;"><iframe src="${this.embedURL}" style="border:0;max-width:100%;position:absolute;top:0;left:0;height:100%;width:100%;" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen="" frameborder="0"></iframe></div>`;
      },
    }))
  })
</script>
