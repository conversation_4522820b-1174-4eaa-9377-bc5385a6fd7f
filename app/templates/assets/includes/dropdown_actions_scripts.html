<script>
  document.addEventListener('alpine:init', () => {
    window.Alpine.data('DeleteModal', () => ({
      deleting: false,

      async deleteAsset(id) {
        this.deleting = true;
        const response = await fetch(`/api/v1/{{request.user.current_organization_uuid}}/assets/${id}/`, {
          method: 'DELETE',
          headers: { 'X-CSRFToken': '{{ csrf_token }}' }
        });
        if (response.ok) {
          this.deleting = false;
          window.location.reload();
        }
      }
    }));

    window.Alpine.data('renameModal', () => ({
      loading: false,
      errorMessage: '',

      async submit() {
        this.updateAsset()
          .then(() => {
            window.location.reload();
          })
          .catch((error) => {
            this.errorMessage = error.message;
          })
      },

      async updateAsset() {
        if (this.loading) return;
        this.loading = true;

        const url = `/api/v1/{{request.user.current_organization_uuid}}/assets/${this.$data.selectedAssetID}/`;
        const response = await fetch(url, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}'
          },
          body: JSON.stringify({
            title: this.$data.selectedAssetName
          })
        });
        const json = await response.json();

        if (!response.ok) {
          this.loading = false;
          throw new Error(json.title);
        }

        return json;
      }
    }))
  });
</script>
