<tr>
  <td colspan="5" class="px-6">
    <nav class="flex items-center justify-between  pb-4 px-4 sm:px-0">
      <div class="-mt-px flex w-0 flex-1">
        {% if page.paginator.num_pages > 1 %}
          {% if page.has_previous %}
            <a href="?{% get_updated_query_string name='page' value=page.previous_page_number %}"
               class="inline-flex items-center border-t-2 border-transparent pt-4 pr-1 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700">
              <svg class="mr-3 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M18 10a.75.75 0 01-.75.75H4.66l2.1 1.95a.75.75 0 11-1.02 1.1l-3.5-3.25a.75.75 0 010-1.1l3.5-3.25a.75.75 0 111.02 1.1l-2.1 1.95h12.59A.75.75 0 0118 10z" clip-rule="evenodd" />
              </svg>
              Previous
            </a>
          {% endif %}
      </div>
      <div class="hidden md:-mt-px md:flex">
        {% for page_number in page.paginator.page_range %}
          {% if page_number == page.number %}
            <span class="inline-flex items-center border-t-2 border-indigo-500 text-indigo-600 px-4 pt-4 text-sm font-medium">{{ page_number }}</span>
          {% elif page_number > page.number|add:'-5' and page_number < page.number|add:'5' %}
            <a href="?{% get_updated_query_string name='page' value=page_number %}"
               class="inline-flex items-center border-t-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-4 pt-4 text-sm font-medium">{{ page_number }}</a>
          {% endif %}
        {% endfor %}
      </div>
      <div class="-mt-px flex w-0 flex-1 justify-end">
        {% if page.has_next %}
          <a href="?{% get_updated_query_string name='page' value=page.next_page_number %}"
             class="inline-flex items-center border-t-2 border-transparent pt-4 pl-1 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700">
            Next
            <svg class="ml-3 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd" d="M2 10a.75.75 0 01.75-.75h12.59l-2.1-1.95a.75.75 0 111.02-1.1l3.5 3.25a.75.75 0 010 1.1l-3.5 3.25a.75.75 0 11-1.02-1.1l2.1-1.95H2.75A.75.75 0 012 10z" clip-rule="evenodd" />
            </svg>
          </a>
        {% endif %}
      {% endif %}
      </div>
    </nav>
  </td>
</tr>
