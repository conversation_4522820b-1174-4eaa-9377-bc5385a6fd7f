<tr x-cloak x-data="{showDropDown: false}">
 <td class="whitespace-nowrap pl-4 sm:pl-6 pr-3 py-4 text-sm font-medium text-gray-900">
   <div class="flex space-x-3 items-center">
     <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none">
       <path fill="#f94f4f" fill-rule="evenodd" d="M10.269 2.187a10.032 10.032 0 0 1 3.462 0 1 1 0 1 0 .345-1.97 12.033 12.033 0 0 0-4.152 0 1 1 0 0 0 .345 1.97Zm7.604-.611a1 1 0 0 0-.983 1.741A9.96 9.96 0 0 1 21.963 12c0 5.502-4.46 9.963-9.963 9.963a9.967 9.967 0 0 1-9.383-6.604 1 1 0 1 0-1.883.675C2.39 20.654 6.807 23.963 12 23.963c6.607 0 11.963-5.356 11.963-11.963a11.96 11.96 0 0 0-6.09-10.424ZM6.921 3.426a1 1 0 1 0-1.02-1.719 12.017 12.017 0 0 0-3.087 2.63A1 1 0 0 0 4.35 5.617a10.017 10.017 0 0 1 2.572-2.191ZM2.617 8.642a1 1 0 0 0-1.883-.675A11.944 11.944 0 0 0 .037 12a1 1 0 1 0 2 0c0-1.18.205-2.311.58-3.36ZM12 19.7A7.701 7.701 0 1 0 12 4.3 7.701 7.701 0 0 0 12 19.7Zm-2.467-4.897V9.196a1 1 0 0 1 1.532-.847l4.464 2.804a1 1 0 0 1 0 1.694l-4.464 2.804a1 1 0 0 1-1.532-.847Z" clip-rule="evenodd"/>
     </svg>
       <a href="{% url 'live_stream_log' uuid=asset.uuid %}">
         <span title="{{ asset.title }}">{{ asset.title|truncatechars:60 }}</span>
       </a>
       <span class="haproxy-status"
             data-live-stream-organization="{{ asset.live_stream.organization.uuid }}"
             data-live-stream-asset="{{ asset.uuid }}"
             data-server-status="{{ asset.live_stream.get_server_status_display }}"
             data-cdn-url="{{ asset.organization.cdn_url }}"
            > &nbsp;
      </span>
   </div>
 </td>
 <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ asset.uuid }}</td>
 <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
  <a href="#" class="organization-filter text-blue-500 hover:text-blue-700" data-key="organization_uuid" data-organization-uuid="{{ asset.organization.uuid }}">
   {{ asset.organization }}
</a>

 </td>
 <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
  {% include "./status_badge.html" %}
</td>
 <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
  {% if asset.live_stream.get_server_status_display == 'Created' %}
  <a href="#" class="server-status-filter inline-flex items-center rounded-full bg-pink-100 px-1.5 py-0.5 text-xs font-medium text-pink-700" data-key="server_status" data-server-status="{{ asset.live_stream.server_status }}">
      Running
  </a>
  {% else %}
   <a href="#" class="server-status-filter text-blue-500 hover:text-blue-700" data-key="server_status" data-server-status="{{ asset.live_stream.server_status }}">
    {{ asset.live_stream.get_server_status_display }}
   </a>
  {% endif %}
</td>
 <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ asset.live_stream.start }}</td>

 <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
  <div class="webhook-status-container"
       data-server-ip="{{ asset.live_stream.server_ip }}"
       data-server-status="{{ asset.live_stream.get_server_status_display }}"
       data-server-id="{{ asset.live_stream.server_id }}"
       data-asset-id="{{ asset.uuid }}">
    {% if asset.live_stream.get_server_status_display == 'Destroyed' %}
      <span class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">Acknowledged</span>
    {% elif asset.live_stream.get_server_status_display == 'Not Created' %}
       <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium">---</span>
    {% else %}
      <div class="flex items-center space-x-2">
        <span class="webhook-status-badge">Loading...</span>
        {% if asset.live_stream.get_server_status_display != 'Not Created' %}
          <div x-data="{ isOpen: false }" class="relative">
            <button type="button"
                   class="pending-events-btn inline-flex items-center space-x-1 px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200"
                   id="pending-events-btn-{{ asset.uuid }}"
                   style="display: none;"
                   @click="
                     isOpen = !isOpen;
                     if(!pendingEventsData) pendingEventsData = {};
                     if(!pendingEventsData['{{ asset.uuid }}']) {
                       pendingEventsData['{{ asset.uuid }}'] = {loading: true, loaded: false, events: [], error: false};
                     };
                     fetchPendingEvents(
                       '{{ asset.uuid }}',
                       '{{ asset.live_stream.server_ip }}',
                       '{{ asset.live_stream.server_id }}',
                       '{{ asset.organization.uuid }}'
                     );
                     $nextTick(() => {
                       const targetEl = document.getElementById('pending-events-{{ asset.uuid }}');
                       if (isOpen) {
                         targetEl.classList.remove('hidden');
                       } else {
                         targetEl.classList.add('hidden');
                       }
                     });
                   ">
              <span class="mr-1">Pending Events</span>
              <svg class="w-3 h-3 transition-transform duration-300"
                   :class="isOpen ? 'rotate-180' : ''"
                   width="12"
                   height="12"
                   viewBox="0 0 16 16"
                   fill="none"
                   xmlns="http://www.w3.org/2000/svg">
                <path d="M2 5L8.16086 10.6869C8.35239 10.8637 8.64761 10.8637 8.83914 10.6869L15 5"
                     stroke="currentColor"
                     stroke-width="2"
                     stroke-linecap="round"/>
              </svg>
            </button>
          </div>
        {% endif %}
      </div>
    {% endif %}
  </div>
</td>
</tr>
<tr id="pending-events-{{ asset.uuid }}" class="hidden w-full overflow-hidden transition-all duration-300">
 <td colspan="7" class="p-4 bg-gray-50">
   <div class="px-4 py-3 rounded-md bg-white shadow">
     <h3 class="text-sm font-medium text-gray-900 mb-2">Pending Events for {{ asset.title|truncatechars:40 }}</h3>

     <div x-show="!pendingEventsData?.['{{ asset.uuid }}'] || pendingEventsData['{{ asset.uuid }}']?.loading" class="text-sm text-gray-500 italic py-2">
       Loading pending events...
     </div>

     <div x-show="pendingEventsData?.['{{ asset.uuid }}']?.error" class="text-sm text-red-500 italic py-2">
       Error loading events
     </div>

     <div x-show="pendingEventsData?.['{{ asset.uuid }}'] && !pendingEventsData['{{ asset.uuid }}']?.loading && !pendingEventsData['{{ asset.uuid }}']?.error && (!pendingEventsData['{{ asset.uuid }}']?.events?.length)" class="text-sm text-gray-500 italic py-2">
       No pending events found
     </div>

     <div x-show="pendingEventsData?.['{{ asset.uuid }}']?.events?.length > 0" class="space-y-2">
       <template x-for="(event, index) in pendingEventsData?.['{{ asset.uuid }}']?.events || []" :key="index">
         <div class="flex items-center justify-between p-2 border-b border-gray-100 last:border-0">
           <div>
             <span class="text-sm" x-text="event.event_type + ' - ' + event.occurred_at"></span>
             <span class="ml-2 px-1.5 py-0.5 bg-gray-100 rounded-full text-xs font-medium text-gray-700" x-text="'Attempts: ' + event.attempt_count"></span>
           </div>
           <button type="button"
                    @click="acknowledgeEvent('{{ asset.uuid }}', '{{ asset.live_stream.organization.uuid }}', event)"
                    class="px-3 py-1 text-xs font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-md">
              Acknowledge
            </button>
         </div>
       </template>
     </div>
   </div>
 </td>
</tr>
