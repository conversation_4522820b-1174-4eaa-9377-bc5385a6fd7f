<script>
  document.addEventListener('alpine:init', function() {
    Alpine.data('DashboardApp', () => ({
      open: false,
      showDeleteModal: false,
      showAcknowledgeModal: false,
      livestreamToAcknowledge: {},
      selectedAssetID: '',
      selectedAssetName: '',
      selectedAssetType: '',
      pendingEventsData: {},

      fetchPendingEvents(assetId, serverIp, serverId, organizationUuid) {
        if (!this.pendingEventsData) {
          this.pendingEventsData = {};
        }

        if (!this.pendingEventsData[assetId]) {
          this.pendingEventsData[assetId] = {
            loading: true,
            events: [],
            error: false
          };
        }

        const url = `/live/pending-events/?server_ip=${serverIp}&server_id=${serverId}`;

        fetch(url, {
          method: 'GET'
        })
        .then(response => {
          if (!response.ok) {
            throw new Error(`Network error: ${response.status}`);
          }
          return response.json();
        })
        .then(data => {
          if (!this.pendingEventsData || !this.pendingEventsData[assetId]) {
            return;
          }

          this.pendingEventsData[assetId].loading = false;
          this.pendingEventsData[assetId].events = data && data.length > 0 ? data : [];
        })
        .catch(error => {
          console.error('Error fetching pending events:', error);

          if (!this.pendingEventsData || !this.pendingEventsData[assetId]) {
            return;
          }

          this.pendingEventsData[assetId].loading = false;
          this.pendingEventsData[assetId].error = true;
          this.pendingEventsData[assetId].events = [];
        });
      },

      acknowledgeEvent(assetId, orgUuid, event) {
        this.livestreamToAcknowledge = {
          name: event.event_type + ' (' + event.occurred_at + ')',
          uuid: assetId,
          orgUuid: orgUuid,
          eventId: event.id,
          eventType: event.event_type
        };

        this.showAcknowledgeModal = true;
      }

    }));
  });

  document.addEventListener('DOMContentLoaded', function() {
    const webhookContainers = document.querySelectorAll('.webhook-status-container');

    webhookContainers.forEach(function(container) {
      const serverStatus = container.getAttribute('data-server-status');
      const assetId = container.getAttribute('data-asset-id');

      if (serverStatus !== 'Destroyed' && serverStatus !== 'Not Created' && assetId) {
        fetchWebhookStatus(container, assetId);
      }
    });
  });

  function fetchWebhookStatus(container, assetId) {
    const statusBadge = container.querySelector('.webhook-status-badge');
    const pendingEventsBtn = container.querySelector('.pending-events-btn');
    const serverIp = container.getAttribute('data-server-ip');
    const serverId = container.getAttribute('data-server-id');

    const url = `/live/stream-status/?server_ip=${serverIp}&server_id=${serverId}`;

    fetch(url, {
      method: 'GET'
    })
      .then(response => {
        if (!response.ok) {
          if (response.status === 404) {
            displayEmptyStatus(statusBadge);
            return null;
          }
          throw new Error(`Network error: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        if (!data) return;

        const latestEvent = data.latest_event;
        let statusColor = '';

        if (!latestEvent) {
          displayEmptyStatus(statusBadge);
          if (pendingEventsBtn) pendingEventsBtn.style.display = 'none';
          return;
        }

        const eventType = latestEvent.event_type || 'None';
        const status = latestEvent.status || 'None';

        if (pendingEventsBtn) {
          pendingEventsBtn.style.display = (status === 'Pending' || status === 'Failed') ? 'inline-flex' : 'none';
        }

        switch(status) {
          case 'Acknowledged':
            statusColor = 'blue';
            break;
          case 'Pending':
            statusColor = 'red';
            break;
          case 'Failed':
            statusColor = 'red';
            break;
          default:
            statusColor = 'gray';
        }

        statusBadge.innerHTML = `
          <span class="inline-flex items-center rounded-full bg-${statusColor}-100 px-2.5 py-0.5 text-xs font-medium text-${statusColor}-800">
            ${eventType}-${status}
          </span>
        `;
      })
      .catch(error => {
        displayEmptyStatus(statusBadge);
        if (pendingEventsBtn) pendingEventsBtn.style.display = 'none';
      });
  }

  function displayEmptyStatus(element) {
    element.innerHTML = `
      <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium">
        ---
      </span>
    `;
  }
</script>
