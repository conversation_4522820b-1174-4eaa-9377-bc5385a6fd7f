{% if asset.live_stream.get_status_display == 'Streaming' %}
<a href="#" class="status-filter no-underline" data-key="status" data-status="{{ asset.live_stream.status }}">
    <span class="inline-flex items-center rounded-full bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700">{{ asset.live_stream.get_status_display }}</span>
</a>
{% elif 'Error' in asset.live_stream.get_status_display %}
<a href="#" class="status-filter no-underline" data-key="status" data-status="{{ asset.live_stream.status }}">
    <span class="inline-flex items-center rounded-full bg-red-100 px-1.5 py-0.5 text-xs font-medium text-red-700">{{ asset.live_stream.get_status_display }}</span>
</a>
{% elif asset.live_stream.get_status_display == 'Not Started' %}
<a href="#" class="status-filter no-underline" data-key="status" data-status="{{ asset.live_stream.status }}">
    <span class="inline-flex items-center rounded-full bg-gray-100 px-1.5 py-0.5 text-xs font-medium text-gray-600">{{ asset.live_stream.get_status_display }}</span>
</a>
{% elif asset.live_stream.get_status_display == 'Disconnected' %}
<a href="#" class="status-filter no-underline" data-key="status" data-status="{{ asset.live_stream.status }}">
    <span class="inline-flex items-center rounded-full bg-pink-100 px-1.5 py-0.5 text-xs font-medium text-pink-700">{{ asset.live_stream.get_status_display }}</span>
</a>
{% elif asset.live_stream.get_status_display == 'Stopped' %}
<a href="#" class="status-filter no-underline" data-key="status" data-status="{{ asset.live_stream.status }}">
    <span class="inline-flex items-center rounded-full bg-pink-100 px-1.5 py-0.5 text-xs font-medium text-pink-700">{{ asset.live_stream.get_status_display }}</span>
</a>
{% elif asset.live_stream.get_status_display != 'Completed' %}
<a href="#" class="status-filter no-underline" data-key="status" data-status="{{ asset.live_stream.status }}">
    <span class="inline-flex items-center rounded-full bg-yellow-100 px-1.5 py-0.5 text-xs font-medium text-yellow-800">{{ asset.live_stream.get_status_display }}</span>
</a>
{% elif asset.live_stream.get_status_display != 'Recording' %}
<a href="#" class="status-filter no-underline" data-key="status" data-status="{{ asset.live_stream.status }}">
    <span class="inline-flex items-center rounded-full bg-yellow-100 px-1.5 py-0.5 text-xs font-medium text-yellow-800">{{ asset.live_stream.get_status_display }}</span>
</a>
{% endif %}
