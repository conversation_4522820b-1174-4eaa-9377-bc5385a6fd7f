{% extends "layouts/base.html" %}
{% load i18n %}
{% load widget_tweaks %}
{% load static %}

{% block extra_head %}
 <link rel="stylesheet" href="{% static 'css/flatpickr.min.css' %}">
 <script src="{% static 'js/flatpickr.min.js' %}"></script>
 <script type="module">
  import preline from 'https://cdn.jsdelivr.net/npm/preline@2.7.0/+esm'
</script>
{% endblock extra_head%}

{% block content %}
<main class="py-10 relative">
  <div class="mx-auto 2xl:max-w-screen-2xl sm:px-6 lg:px-8">
    <div class="flex justify-between">
    <div class="px-4 sm:px-6 lg:px-0">
      <h1 class="text-3xl font-bold tracking-tight text-gray-900">{{ title }} {{date}}</h1>
    </div>
    {% include './filters.html' %}
    </div>
    <div class="px-4 sm:px-6 lg:px-0">
    <div class="py-6">
        <div x-cloak
        x-data="DashboardApp"
        @keydown.window.escape="open = false; showDeleteModal = false; showAcknowledgeModal = false;">

        {% include './table.html' %}
    </div>
    </div>
  </div>
</main>

{% endblock %}
{% block extra_body %}
{% include "./script.html" %}
<script>
 window.organizations = {{ organizations|safe }};
 function updateQueryStringParameter(uri, key, value) {
   var re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
   var separator = uri.indexOf('?') !== -1 ? "&" : "?";
   if (uri.match(re)) {
     return uri.replace(re, '$1' + key + "=" + encodeURIComponent(value) + '$2');
   }
   else {
     return uri + separator + key + "=" + encodeURIComponent(value);
   }
 }

 flatpickr(".flatpickr", {
   enableTime: false,
   dateFormat: "Y-m-d",
   onClose: function(selectedDates, dateStr, instance) {
     handleFilterChange('server_started_date', dateStr);
   }
 });

document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.status-filter, .server-status-filter, .organization-filter').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            var key = this.getAttribute('data-key');
            var value = this.getAttribute('data-status') || this.getAttribute('data-server-status') || this.getAttribute('data-organization-uuid');
            handleFilterChange(key, value);
        });
    });
});

function handleFilterChange(key, value) {
 var newUrl = updateQueryStringParameter(window.location.href, key, value);
 window.location.href = newUrl;
}

document.addEventListener('DOMContentLoaded', function() {
  const haproxyStatusElements = document.querySelectorAll('.haproxy-status');

  haproxyStatusElements.forEach(function(element) {
    const organizationUuid = element.getAttribute('data-live-stream-organization');
    const assetUuid = element.getAttribute('data-live-stream-asset');
    const serverStatus = element.getAttribute('data-server-status');
    const cdnUrl = element.getAttribute('data-cdn-url');
    const url = `${cdnUrl}live/${organizationUuid}/${assetUuid}/video.m3u8`;
    if (serverStatus !== 'Destroyed') {
      is_ha_proxy_error(url, element);
    }
    else{
      element.innerHTML = '';
    }
  });
});

function is_ha_proxy_error(url, element) {
  fetch(url, { method: 'HEAD' })
    .then(response => {
      if (response.status !== 200 ) {
        element.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
          </svg>
        `;
      } else {
        element.innerHTML = '';
      }
    })
    .catch(error => {
      console.error('Error fetching HAProxy status:', error);
      element.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
          </svg>
        `;
    });
}
</script>
{% endblock %}
