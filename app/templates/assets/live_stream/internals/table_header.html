<div class="mt-8 flex flex-col">
 <div class="-my-2 -mx-4 overflow-visible sm:-mx-6 lg:-mx-8">
   <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
     <div class="shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
       {% if is_empty %}
         {% block empty_state %} {% endblock %}
       {% else %}
       <table class="min-w-full divide-y divide-gray-300">
         <thead class="bg-gray-50">
           <tr>
             <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
               Name</th>
             <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">UUID</th>
             <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Organization</th>
             <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Live Status</th>
             <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Server Status</th>
             <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Server Started On</th>
             <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Webhook Status</th>
           </tr>
         </thead>
         <tbody class="divide-y divide-gray-200 bg-white">
           {% block row %} {% endblock %}

           {% if page_obj.has_other_pages %}
             {% include "assets/includes/pagination.html" with page=page_obj %}
           {% endif %}
         </tbody>
       </table>
       {% endif %}
     </div>
   </div>
 </div>
</div>
