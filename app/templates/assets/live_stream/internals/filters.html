<div class="hidden md:ml-4 md:flex md:items-center">
 <!-- Date Filter Dropdown -->
 <div class="relative inline-block text-left px-2" x-data="{showActiondropdown:false}">
   <div>
     <button @click="showActiondropdown=!showActiondropdown" @click.away="showActiondropdown=false" type="button" class="flatpickr inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm text-gray-900 shadow-sm hover:bg-gray-50 border border-gray-300 focus:ring-0 focus:border-gray-300" id="menu-button" aria-expanded="true" aria-haspopup="true">
       Filter by Date
       <svg class="-mr-1 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
         <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd"></path>
       </svg>
     </button>
   </div>
 </div>

 <!-- Status Filter Dropdown -->
 <div class="relative inline-block text-left px-2" x-data="{showActiondropdown:false}">
  <div>
    <button @click="showActiondropdown=!showActiondropdown" @click.away="showActiondropdown=false" type="button" class="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" id="menu-button" aria-expanded="true" aria-haspopup="true">
      Filter by Status
      <svg class="-mr-1 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
        <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd"></path>
      </svg>
    </button>
    <div x-show="showActiondropdown" class="absolute right-0 z-10 mt-2 w-40 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabindex="-1">
      <div class="py-1" role="none">
         <a href="#" class="status-filter text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100" data-status="1" data-key="status">Streaming</a>
         <a href="#" class="status-filter text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100" data-key="status" data-status="0">Not Started </a>
         <a href="#" class="status-filter text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100" data-key="status" data-status="2">Recording</a>
        <a href="#" class="status-filter text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100" data-key="status" data-status="3">Completed</a>
        <a href="#" class="status-filter text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100" data-key="status" data-status="4">Error</a>
        <a href="#" class="status-filter text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100" data-key="status" data-status="5">Disconnected</a>
        <a href="#" class="status-filter text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100" data-key="status" data-status="6">Stopped</a>
       </div>
     </div>
   </div>
 </div>

 <!-- Server Status Filter Dropdown -->
 <div class="relative inline-block text-left px-2" x-data="{showActiondropdown:false}">
   <div>
     <button @click="showActiondropdown=!showActiondropdown" @click.away="showActiondropdown=false" type="button" class="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" id="menu-button" aria-expanded="true" aria-haspopup="true">
       Filter by Server Status
       <svg class="-mr-1 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
         <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd"></path>
       </svg>
     </button>
     <div x-show="showActiondropdown" class="absolute right-0 z-10 mt-2 w-40 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabindex="-1">
       <div class="py-1" role="none">
        <a href="#" class="status-filter text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100" data-server-status="0" data-key="server_status">Not created</a>
        <a href="#" class="status-filter text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100" data-server-status="1" data-key="server_status">Creating</a>
        <a href="#" class="server-status-filter text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100" data-key="server_status" data-server-status="2">Running</a>
         <a href="#" class="server-status-filter text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100" data-key="server_status" data-server-status="3">Proxy Setup</a>
         <a href="#" class="server-status-filter text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100" data-key="server_status" data-server-status="4">Available</a>
         <a href="#" class="server-status-filter text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100" data-key="server_status" data-server-status="5">Destroyed</a>
         <a href="#" class="server-status-filter text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100" data-key="server_status" data-server-status="6">Deleting</a>

       </div>
     </div>
   </div>
 </div>

<div class="relative inline-block text-left px-2"
 x-data="{
    showActiondropdown: false,
    organizations: [],
    searchQuery: '',
    filteredOrganizations: []
 }"
 x-init="
    organizations = [...window.organizations];
    filteredOrganizations = organizations;
 ">
 <div>
  <!-- Dropdown Button -->
  <button @click="showActiondropdown = !showActiondropdown"
          type="button"
          class="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
    Filter by Organization
    <svg class="-mr-1 h-5 w-5 text-gray-400"
         viewBox="0 0 20 20"
         fill="currentColor"
         aria-hidden="true">
      <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938 a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd"></path>
    </svg>
  </button>

  <!-- Dropdown Menu -->
  <div x-show="showActiondropdown"
       @click.away="showActiondropdown = false"
       class="absolute right-0 z-10 mt-2 w-40 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5">
    <!-- Search Input -->
    <input type="text"
           placeholder="Search organizations"
           x-model="searchQuery"
           class="rounded block w-full border-0 focus:ring-0 bg-gray-100 text-sm px-2 py-1"
           @input="filteredOrganizations = organizations.filter(org => org.name.toLowerCase().includes(searchQuery.toLowerCase()))">

    <!-- Filtered List -->
    <div class="py-1">
      <template x-for="organization in filteredOrganizations" :key="organization.uuid">
        <a href="#"
           class="organization-filter text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100" data-key="organization_uuid" :data-organization-uuid="organization.uuid"
           x-text="organization.name"></a>
      </template>
    </div>
  </div>
</div>
</div>
</div>
