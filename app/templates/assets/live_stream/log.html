{% extends "layouts/base.html" %}
{% load static i18n %}

{% block extra_head %}
  <link rel="stylesheet" href="{% static 'css/prism.css' %}">
{% endblock extra_head%}
{% block html_class %}h-full bg-white{% endblock html_class %}
{% block body_class %}h-full{% endblock body_class %}
{% block head_title %}{% translate "Internals" %}{% endblock %}

{% block content %}
  {% translate "Live Stream Activity Log" as title %}
<div x-data="{open: false}" @keydown.window.escape="open = false">

  {% include "includes/sidebar.html" %}
  <!-- Content area -->
  <div class="md:pl-64">
    <div class="flex flex-col max-w-5xl mx-auto sm:px-6 md:px-8">

      <main class="flex-1">
        <div class="relative mx-auto max-w-5xl md:px-8 xl:px-0">
          <div class="pt-10 pb-16">
            <div class="flex flex-col">
              {% include "includes/heading.html" %}
              {% if live_stream.get_server_status_display != "Destroyed" %}
                {% include "./includes/actions.html" %}
              {% endif %}
            </div>
            <div class="px-4 sm:px-6 md:px-0">
                <div class="mt-10 divide-y divide-gray-200">
                    <div class="mt-6">
                      <dl class="divide-y divide-gray-200">
                        {% include "includes/description_list_item.html" with label="Name" value=live_stream.asset.title%}
                        {% include "./includes/description_list_item.html" with label="Asset ID " value=live_stream.asset.uuid action_label="Copy" %}
                        {% include "./includes/description_list_item.html" with label="IP Address" value=live_stream.server_ip action_label="Copy" %}
                        {% include "./includes/description_list_item.html" with label="Server id" value=live_stream.server_id  action_label="Copy" %}
                        {% include "./includes/description_list_item.html" with label="Server Termination Task ID" value=live_stream.server_termination_task_id action_label="Copy"   %}
                        {% if live_stream.chat_room_id %}
                        {% include "./includes/description_list_item.html" with label="Chat room id" value=live_stream.chat_room_id  action_label="Copy" %}
                        {% endif %}
                        {% include "./includes/description_list_item.html" with label="Server Status" value=live_stream.get_server_status_display   %}
                        {% include "./includes/description_list_item.html" with label="Live Stream Status" value=live_stream.get_status_display   %}
                        {% include "./includes/description_list_item.html" with label="Live Stream Duration" value=live_stream_duration   %}
                        {% include "includes/description_list_item.html" with label="Live Stream DRM Enabled" value=live_stream.enable_drm %}
                        {% include "includes/description_list_item.html" with label="Recording DRM Enabled" value=live_stream.enable_drm_for_recording %}
                        {% if live_stream.get_termination_cause_display %}
                        {% include "includes/description_list_item.html" with label="Termination Cause" value=live_stream.get_termination_cause_display %}
                        {% endif %}
                        <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                            <dt class="text-sm font-medium text-gray-500">Streaming Activity Timeline</dt>
                            <dd class="flex mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                                <ul role="list" class="-mb-8 list-disc ">
                                  <li class="mb-2">
                                    <div class="relative pb-4">
                                        <div class="relative flex space-x-3">
                                            <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                                                <div>
                                                      <p class="text-sm text-black-500"><span class="whitespace-normal">EC2 instance created {{live_stream.live_stream_usage.start_time}}</span></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                    {% for event in events %}
                                    <li class="mb-2">
                                        <div class="relative pb-4">
                                            <div class="relative flex space-x-3">
                                                <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                                                    <div>
                                                      {% if event.data.message %}
                                                            <p class="text-sm text-black-500">
                                                                <span class="whitespace-normal">{{ event.data.message }} at {{ event.created }}</span>
                                                            </p>
                                                      {% else %}
                                                        {% if event.type == 1 %}
                                                            <p class="text-sm text-black-500"><span class="whitespace-normal">Streaming started at {{ event.created }}</span></p>
                                                        {% elif event.type == 2 %}
                                                            <p class="text-sm text-black-500"><span class="whitespace-normal">Streaming ended at {{ event.created }}</span></p>
                                                        {% elif event.type == 3 %}
                                                            <p class="text-sm text-black-500"><span class="whitespace-normal">Streaming Stopped at {{ event.created }}</span></p>
                                                        {% elif event.type == 4 %}
                                                            <p class="text-sm text-black-500"><span class="whitespace-normal">Recording started at {{ event.created }}</span></p>
                                                        {% elif event.type == 5 %}
                                                            <p class="text-sm text-black-500"><span class="whitespace-normal">Streaming Completed at {{ event.created }}</span></p>
                                                        {% endif %}
                                                      {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                    {% endfor %}
                                    {% if live_stream.live_stream_usage.end_time %}
                                    <li class="mb-2">
                                      <div class="relative pb-4">
                                          <div class="relative flex space-x-3">
                                              <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                                                  <div>
                                                        <p class="text-sm text-black-500"><span class="whitespace-normal">EC2 instance Deleted {{ live_stream.live_stream_usage.end_time }}</span></p>
                                                  </div>
                                              </div>
                                          </div>
                                      </div>
                                  </li>
                                  {% endif %}
                                </ul>
                            </div>
                        </div>
                        {% if live_stream.live_stream_usage.end_time %}
                            {% include "includes/description_list_item.html" with label="ffmpeg_log" value=ffmpeg_log_url|truncatechars:60 action_link=ffmpeg_log_url action_label="Link" %}
                            {% include "includes/description_list_item.html" with label="transmux_log" value=transmux_log_url|truncatechars:60 action_link=transmux_log_url action_label="Link" %}
                            {% if live_stream.enable_drm  %}
                              {% include "includes/description_list_item.html" with label="widevine_packger_log" value=widevine_packger_log_url|truncatechars:60 action_link=widevine_packger_log_url action_label="Link" %}
                              {% include "includes/description_list_item.html" with label="fairplay_packger_log" value=fairplay_packger_log_url|truncatechars:60 action_link=fairplay_packger_log_url action_label="Link" %}
                            {% endif %}
                            {% include "includes/description_list_item.html" with label="video_url" value=video_input_url|truncatechars:60 action_link=video_input_url action_label="Link" %}
                        {% else %}
                            {% with ffmpeg_log_url=live_stream.organization.cdn_url|add:"live/"|add:live_stream.organization.uuid|add:"/"|add:live_stream.asset.uuid|add:"/ffmpeg_log.txt" %}
                              {% include "includes/description_list_item.html" with label="ffmpeg_log" value=ffmpeg_log_url action_link=ffmpeg_log_url action_label="Link" %}
                            {% endwith %}
                            {% with transmux_log_url=live_stream.organization.cdn_url|add:"live/"|add:live_stream.organization.uuid|add:"/"|add:live_stream.asset.uuid|add:"/transmux_log.txt" %}
                              {% include "includes/description_list_item.html" with label="transmux_log" value=transmux_log_url action_link=transmux_log_url action_label="Link" %}
                            {% endwith %}
                            {% if live_stream.enable_drm %}
                              {% with widevine_packger_log_url=live_stream.organization.cdn_url|add:"live/"|add:live_stream.organization.uuid|add:"/"|add:live_stream.asset.uuid|add:"/packager_widevine.txt" %}
                                {% include "includes/description_list_item.html" with label="widevine_packger_log" value=widevine_packger_log_url action_link=widevine_packger_log_url action_label="Link" %}
                              {% endwith %}
                              {% with fairplay_packger_log_url=live_stream.organization.cdn_url|add:"live/"|add:live_stream.organization.uuid|add:"/"|add:live_stream.asset.uuid|add:"/packager_fairplay.txt" %}
                                {% include "includes/description_list_item.html" with label="fairplay_packger_log" value=fairplay_packger_log_url action_link=fairplay_packger_log_url action_label="Link" %}
                              {% endwith %}
                            {% endif %}
                            {% with video_url=live_stream.organization.cdn_url|add:"live/"|add:live_stream.organization.uuid|add:"/"|add:live_stream.asset.uuid|add:"/video.mp4" %}
                              {% include "includes/description_list_item.html" with label="video_url" value=video_url action_link=video_url action_label="Link" %}
                            {% endwith %}
                        {% endif %}
                        {% if live_stream.server_ip %}
                           {% include "includes/description_list_item.html" with label="srs_url" value="http://"|add:live_stream.server_ip|add:":8080/" action_link="http://"|add:live_stream.server_ip|add:":8080/" action_label="Link" %}
                        {% endif %}
                        {% if live_stream.get_server_status_display != "Destroyed" %}
                        {% include "includes/description_list_item.html" with label="Admin_url" value="http://"|add:live_stream.server_ip|add:"/admin/" action_link="http://"|add:live_stream.server_ip|add:"/admin/" action_label="Link" %}
                          <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                            <dt class="text-sm font-medium text-gray-500">Credentials</dt>
                            <dd class="flex mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                              <div class="space-y-4">
                                <div class="flex items-center w-46">
                                  <span class="text-gray-500 mr-2">Username:</span>
                                  <div class="flex items-center justify-between w-48">
                                    <span class="mr-2">admin</span>
                                    <button onclick="copyToClipboard('admin')" class="font-medium text-blue-700 bg-white rounded-md hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2">Copy</button>
                                  </div>
                                </div>
                                <div class="flex items-center">
                                  <span class="text-gray-500 mr-2">Password:</span>
                                  <div class="flex items-center justify-between w-48">
                                    <span class="mr-2">{{ live_stream.asset.uuid }}{{ live_stream.organization.uuid }}</span>
                                    <button onclick="copyToClipboard('{{ live_stream.asset.uuid }}{{ live_stream.organization.uuid }}')" class="font-medium text-blue-700 bg-white rounded-md hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2">Copy</button>
                                  </div>
                                </div>
                              </div>
                            </dd>
                          </div>
                        {% endif %}
                        {% if live_stream.transcode_recorded_video  and transcoding_url %}
                          {% include "includes/description_list_item.html" with label="transcoding_url" value=transcoding_url|truncatechars:60 action_link=transcoding_url action_label="Link" %}
                        {% endif %}
                        {% if live_stream.get_termination_cause_display %}
                          {% include "./includes/description_list_item.html" with label="Upload Recording Task ID" value=live_stream.upload_recording_task_id action_label="Copy" %}
                          {% include "includes/description_list_item.html" with label="User Name" value=live_stream.user_details.name %}
                          {% include "includes/description_list_item.html" with label="User IP " value=live_stream.user_details.ip_address %}
                          {% include "includes/description_list_item.html" with label="User Agent" value=live_stream.user_details.user_agent %}
                          {% include "includes/description_list_item.html" with label="Location" value=live_stream.user_details.location %}
                        {% endif %}

                        <nav aria-label="Progress">
                          <ol role="list" class="overflow-hidden pt-6">
                            <li class="relative pb-10">
                              {% if check_cdn_status.result %}
                              <div class="absolute left-4 top-4 -ml-px mt-0.5 h-full w-0.5 bg-indigo-600" aria-hidden="true"></div>
                              {% else %}
                              <div class="absolute left-4 top-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-300" aria-hidden="true"></div>
                              {% endif %}
                              <a href="{{ check_cdn_status.url }}" class="group relative flex items-start">
                                {% if check_cdn_status.result %}
                                <span class="flex h-9 items-center">
                                  <span class="relative z-10 flex h-8 w-8 items-center justify-center rounded-full bg-indigo-600 group-hover:bg-indigo-800">
                                    <svg class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                      <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                                    </svg>
                                  </span>
                                </span>
                                {% else %}
                                <span class="flex h-9 items-center" aria-hidden="true">
                                  <span class="relative z-10 flex h-8 w-8 items-center justify-center rounded-full border-2 border-red-600 bg-white">
                                    <span class="h-2.5 w-2.5 rounded-full bg-red-600"></span>
                                  </span>
                                </span>
                                {% endif %}
                                <span class="ml-4 flex min-w-0 flex-col">
                                  <span class="text-sm font-medium">CDN</span>
                                  <span class="text-sm text-gray-500">{{check_cdn_status.url}}</span>
                                </span>
                              </a>
                            </li>
                            <li class="relative pb-10">
                              {% if check_haproxy_status.result %}
                              <div class="absolute left-4 top-4 -ml-px mt-0.5 h-full w-0.5 bg-indigo-600" aria-hidden="true"></div>
                              {% else %}
                                  <div class="absolute left-4 top-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-300" aria-hidden="true"></div>
                              {% endif %}
                              <a href="{{ check_haproxy_status.url }}" class="group relative flex items-start">
                                {% if check_haproxy_status.result %}
                                <span class="flex h-9 items-center">
                                  <span class="relative z-10 flex h-8 w-8 items-center justify-center rounded-full bg-indigo-600 group-hover:bg-indigo-800">
                                    <svg class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                      <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                                    </svg>
                                  </span>
                                </span>
                                {% else %}
                                <span class="flex h-9 items-center" aria-hidden="true">
                                  <span class="relative z-10 flex h-8 w-8 items-center justify-center rounded-full border-2 border-red-600 bg-white">
                                    <span class="h-2.5 w-2.5 rounded-full bg-red-600"></span>
                                  </span>
                                </span>
                                {% endif %}
                                <span class="ml-4 flex min-w-0 flex-col">
                                  <span class="text-sm font-medium">HA proxy</span>
                                  <span class="text-sm text-gray-500">{{check_haproxy_status.url}}</span>
                                </span>
                              </a>
                            </li>
                            <li class="relative pb-10">
                             {% if check_oprenresty_status.result %}
                             <div class="absolute left-4 top-4 -ml-px mt-0.5 h-full w-0.5 bg-indigo-600" aria-hidden="true"></div>
                             {% else %}
                                 <div class="absolute left-4 top-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-300" aria-hidden="true"></div>
                             {% endif %}
                             <a href="{{ check_oprenresty_status.url }}" class="group relative flex items-start">
                               {% if check_oprenresty_status.result %}
                               <span class="flex h-9 items-center">
                                 <span class="relative z-10 flex h-8 w-8 items-center justify-center rounded-full bg-indigo-600 group-hover:bg-indigo-800">
                                   <svg class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                     <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                                   </svg>
                                 </span>
                               </span>
                               {% else %}
                               <span class="flex h-9 items-center" aria-hidden="true">
                                 <span class="relative z-10 flex h-8 w-8 items-center justify-center rounded-full border-2 border-red-600 bg-white">
                                   <span class="h-2.5 w-2.5 rounded-full bg-red-600"></span>
                                 </span>
                               </span>
                               {% endif %}
                               <span class="ml-4 flex min-w-0 flex-col">
                                 <span class="text-sm font-medium">OpenResty</span>
                                 <span class="text-sm text-gray-500">{{check_oprenresty_status.url}}</span>
                               </span>
                             </a>
                           </li>
                            <li class="relative pb-10">
                              <a href="{{ check_live_server_status.url }}" class="group relative flex items-start">
                                {% if check_live_server_status.result %}
                                <span class="flex h-9 items-center">
                                  <span class="relative z-10 flex h-8 w-8 items-center justify-center rounded-full bg-indigo-600 group-hover:bg-indigo-800">
                                    <svg class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                      <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                                    </svg>
                                  </span>
                                </span>
                                {% else %}
                                <span class="flex h-9 items-center" aria-hidden="true">
                                  <span class="relative z-10 flex h-8 w-8 items-center justify-center rounded-full border-2 border-red-600 bg-white">
                                    <span class="h-2.5 w-2.5 rounded-full bg-red-600"></span>
                                  </span>
                                </span>
                                {% endif %}
                                <span class="ml-4 flex min-w-0 flex-col">
                                  <span class="text-sm font-medium">Live Server</span>
                                  <span class="text-sm text-gray-500">{{check_live_server_status.url}}</span>
                                </span>
                              </a>
                            </li>
                     </dl>
                    </div>
                </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</div>

{% endblock content %}


{% block extra_body %}
  {% include "./includes/detail_script.html" %}
{% endblock %}
