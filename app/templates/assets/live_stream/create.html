{% extends "./base.html" %}
{% load i18n widget_tweaks %}

{% block live_streams %}
 {% include "./includes/heading.html" with title="Create Live Stream" %}
 <div class="px-4 sm:px-6 lg:px-0">
  <div class="sm:w-full lg:max-w-md">
   <form x-data="{ latency: '0' }" class="space-y-6" method="post" onsubmit="return submitForm(this);">
    {% csrf_token %}
    {% include "includes/input_field.html"  with field=form.title %}
    <div>
     <label for="{{ form.enable_drm_for_recording.auto_id }}" class="block text-sm font-medium text-gray-700">Enable DRM for recording</label>
     <div class="mt-1 relative">
      {{ form.enable_drm_for_recording|add_class:"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600" }}
     </div>
    </div>
    <div x-show="latency == '0'" >
     {% if not request.user.current_organization.uuid == "f87hk3"%}
     <label for="{{ form.enable_drm.auto_id }}" class="block text-sm font-medium text-gray-700">Enable DRM for livestream
      <span class="inline-flex items-center rounded-full bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700">Beta</span>
     </label>
     <div class="mt-1 relative">
      {{ form.enable_drm|add_class:"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600" }}
     </div>
     {% endif %}
    </div>
    <div>
      {% if not request.user.current_organization.uuid == "9mpasc"%}
        <label for="id_enable_drm" class="block text-sm font-medium text-gray-700">Stream Latency
            <span class="inline-flex items-center rounded-full bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700">Beta</span>
        </label>
        <fieldset class="mt-1">
            <legend class="sr-only">Stream Latency</legend>
            <div class="space-y-4 sm:flex sm:items-center sm:space-x-10 sm:space-y-0">
                {% for choice_key, choice_value in form.latency.field.choices %}
                <div class="flex items-center">
                    <input x-model="latency"  id="{{ choice_key }}-latency" name="latency" type="radio" value="{{ choice_key }}" {% if choice_key == 0  %} checked {% endif %} class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600">
                    <label for="{{ choice_key }}-latency" class="ml-3 block text-sm font-medium leading-6 text-gray-900">{{ choice_value }}</label>
                </div>
                {% endfor %}
            </div>
        </fieldset>
      {% endif %}
    </div>
    {% if request.GET.enable_llhls %}
     <div>
       <label for="{{ form.enable_llhls.auto_id }}" class="block text-sm font-medium text-gray-700">Enable llhls
        <span class="inline-flex items-center rounded-full bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700">Beta</span>
       </label>
       <div class="mt-1 relative">
        {{ form.enable_llhls|add_class:"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600" }}
       </div>
     </div>
    {% endif %}

    <div>
     <button type="submit"
             class="rounded-md bg-indigo-600 px-2.5 py-1.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
      Create
     </button>
    </div>
   </form>
  </div>
 </div>
{% endblock %}

{% block script %}
  <script>
    function submitForm(form) {
      var createButton = form.querySelector('button[type="submit"]');
      createButton.disabled = true;
      createButton.innerHTML = 'Creating...';
      createButton.className = "rounded-md bg-indigo-500 px-2.5 py-1.5 text-sm font-semibold text-white shadow-sm opacity-50 cursor-not-allowed"
      return true;
    }
</script>

{% endblock %}
