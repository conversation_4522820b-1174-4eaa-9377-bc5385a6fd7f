<script>
    document.addEventListener('alpine:init', () => {
      window.Alpine.data('DeleteModal', () => ({
        deleting: false,

        async deleteAsset(id) {
          this.deleting = true;
          const response = await fetch(`/api/v1/{{request.user.current_organization_uuid}}/assets/${id}/`, {
            method: 'DELETE',
            headers: { 'X-CSRFToken': '{{ csrf_token }}' }
          });
          if (response.ok) {
            this.deleting = false;
            window.location.reload();
          }
        }
      }));
    });
  </script>
