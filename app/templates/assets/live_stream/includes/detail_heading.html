<div class="sm:flex space-y-2 sm:space-y-0 items-center px-4 sm:px-0">
    <div class="sm:flex-auto">
      <div class="flex items-center space-x-2">
        <h1 class="text-2xl font-semibold text-gray-900">{{ live_stream.asset.title }}</h1>
        {% include "./status_badge.html" %}
      </div>
    </div>
    <div class="flex items-center space-x-2">
      <form action="{% url 'live_stream_update' uuid=asset.uuid %}" method="get">
        <span class="hidden sm:block">
          <button type="submit" class="inline-flex items-center gap-x-1.5 rounded-md bg-white px-2.5 py-1.5 sm:px-3 sm:py-2 text-sm font-semibold text-blue-600 shadow-sm ring-1 ring-inset ring-blue-300 hover:bg-blue-50">
            <svg class="-ml-0.5 h-5 w-5" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
              <path d="M2.695 14.763l-1.262 3.154a.5.5 0 00.65.65l3.155-1.262a4 4 0 001.343-.885L17.5 5.5a2.121 2.121 0 00-3-3L3.58 13.42a4 4 0 00-.885 1.343z"/>
            </svg>
            Edit
          </button>
        </span>
      </form>
      <span class="hidden sm:block">
        <button type="button" x-data="Clipboard" x-init="()=>{copyPlaybackURLButtonText='Copy Embed Code'}" @click="copy(`{{ embed_code }}`)"
          class="inline-flex items-center gap-x-1.5 rounded-md bg-white px-2.5 py-1.5 sm:px-3 sm:py-2 text-sm font-semibold text-blue-600 shadow-sm ring-1 ring-inset ring-blue-300 hover:bg-blue-50">
          <svg fill="currentColor" class="-ml-0.5 h-5 w-5" viewBox="0 0 24 24">
            <path fill-rule="evenodd"
              d="M7.502 6h7.128A3.375 3.375 0 0 1 18 9.375v9.375a3 3 0 0 0 3-3V6.108c0-1.505-1.125-2.811-2.664-2.94a48.972 48.972 0 0 0-.673-.05A3 3 0 0 0 15 1.5h-1.5a3 3 0 0 0-2.663 1.618c-.225.015-.45.032-.673.05C8.662 3.295 7.554 4.542 7.502 6zM13.5 3A1.5 1.5 0 0 0 12 4.5h4.5A1.5 1.5 0 0 0 15 3h-1.5z"
              clip-rule="evenodd" />
            <path fill-rule="evenodd"
              d="M3 9.375C3 8.339 3.84 7.5 4.875 7.5h9.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-9.75A1.875 1.875 0 0 1 3 20.625V9.375zM6 12a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V12zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75zM6 15a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V15zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75zM6 18a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V18zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75z"
              clip-rule="evenodd" />
          </svg>
          <span x-text="copyPlaybackURLButtonText"></span>
        </button>
      </span>
      {% if live_stream.get_server_status_display ==  "Not Created" %}
        <form action="{% url 'create_live_stream_server' uuid=live_stream.asset.uuid %}" method="post" x-data="{ isSubmitting: false }" x-on:submit="isSubmitting = true">
          {% csrf_token %}
          <button type="submit"
            :disabled="isSubmitting"
            class="inline-flex items-center gap-x-1.5 rounded-md bg-blue-700 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
            x-bind:class="{'cursor-not-allowed opacity-50': isSubmitting}"
            >
              <svg  fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="-ml-0.5 w-5 h-5">
                  <path stroke-linecap="round" d="M15.75 10.5l4.72-4.72a.75.75 0 011.28.53v11.38a.75.75 0 01-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 002.25-2.25v-9a2.25 2.25 0 00-2.25-2.25h-9A2.25 2.25 0 002.25 7.5v9a2.25 2.25 0 002.25 2.25z" />
              </svg>
              Start Live</button>
        </form>
      {% elif live_stream.get_status_display != "Recording" and live_stream.get_status_display != "Completed" and live_stream.get_server_status_display != "Destroyed" %}
        <form action="{% url 'stop_live_stream' uuid=live_stream.asset.uuid %}" method="POST" x-data="{ isSubmitting: false }" x-on:submit="isSubmitting = true">
          {% csrf_token %}
          <button type="submit"
              :disabled="isSubmitting"
              class="inline-flex gap-x-1.5 justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500"
              x-bind:class="{'cursor-not-allowed opacity-50': isSubmitting}">

              <svg fill="currentColor" class="-ml-0.5 h-5 w-5" viewBox="0 0 24 24">
                  <path d="M3.53 2.47a.75.75 0 0 0-1.06 1.06l18 18a.75.75 0 1 0 1.06-1.06l-18-18zM22.5 17.69c0 .471-.202.86-.504 1.124l-4.746-4.746V7.939l2.69-2.689c.944-.945 2.56-.276 2.56 1.06v11.38zM15.75 7.5v5.068L7.682 4.5h5.068a3 3 0 0 1 3 3zM1.5 7.5c0-.782.3-1.494.79-2.028l12.846 12.846A2.995 2.995 0 0 1 12.75 19.5H4.5a3 3 0 0 1-3-3v-9z" />
              </svg>
              Stop Live
          </button>
        </form>
      {% endif %}


      <div x-data="{ open: false }" class="relative ml-3 sm:hidden">
        <button type="button" @click="open = ! open" class="inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:ring-gray-400" id="mobile-menu-button" aria-expanded="false" aria-haspopup="true">
          More
          <svg class="-mr-1 ml-1.5 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
          </svg>
        </button>

        <div x-show="open" class="absolute right-0 z-10 -mr-1 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
         role="menu" aria-orientation="vertical" aria-labelledby="mobile-menu-button" tabindex="-1"
         x-show.transition="open"
         x-transition:enter="transition ease-out duration-200 transform opacity-0 scale-95"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75 transform opacity-100 scale-100"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         >

          <a href="#" x-data="Clipboard" x-init="()=>{copyPlaybackURLButtonText='Copy Embed Code'}" @click="copy(`{{ embed_code }}`)"
            class="group flex items-center px-4 py-2 text-sm text-gray-700">
            <svg fill="currentColor" class="-ml-0.5 mr-1.5 h-5 w-5 text-gray-400" viewBox="0 0 24 24">
              <path fill-rule="evenodd"
                d="M7.502 6h7.128A3.375 3.375 0 0 1 18 9.375v9.375a3 3 0 0 0 3-3V6.108c0-1.505-1.125-2.811-2.664-2.94a48.972 48.972 0 0 0-.673-.05A3 3 0 0 0 15 1.5h-1.5a3 3 0 0 0-2.663 1.618c-.225.015-.45.032-.673.05C8.662 3.295 7.554 4.542 7.502 6zM13.5 3A1.5 1.5 0 0 0 12 4.5h4.5A1.5 1.5 0 0 0 15 3h-1.5z"
                clip-rule="evenodd" />
              <path fill-rule="evenodd"
                d="M3 9.375C3 8.339 3.84 7.5 4.875 7.5h9.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-9.75A1.875 1.875 0 0 1 3 20.625V9.375zM6 12a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V12zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75zM6 15a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V15zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75zM6 18a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V18zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75z"
                clip-rule="evenodd" />
            </svg>
            <span x-text="copyPlaybackURLButtonText"></span>
          </a>
          <a href="{% url 'live_stream_update' uuid=asset.uuid %}"" class="group flex items-center px-4 py-2 text-sm text-gray-700" role="menuitem" tabindex="-1" id="menu-item-0">
            <svg class="-ml-0.5 mr-1.5 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path d="M2.695 14.763l-1.262 3.154a.5.5 0 00.65.65l3.155-1.262a4 4 0 001.343-.885L17.5 5.5a2.121 2.121 0 00-3-3L3.58 13.42a4 4 0 00-.885 1.343z" />
            </svg>

            Edit
          </a>
        </div>
      </div>

    </div>
</div>
