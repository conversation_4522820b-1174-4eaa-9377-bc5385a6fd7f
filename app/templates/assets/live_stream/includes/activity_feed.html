{% load humanize %}

<div class="">
  <!-- Activity feed -->
  <h2 class="text-sm font-semibold leading-6 text-gray-900">Activity</h2>
  <ul role="list" class="mt-6 space-y-6">
  {% if live_stream.start %}
    {% if live_stream.get_server_status_display ==  "Not Created" or live_stream.get_server_status_display ==  "Creating" %}
      <li class="relative flex gap-x-4">
        <div class="absolute top-0 left-0 flex justify-center w-6 h-6">
          <div class="w-px bg-gray-200"></div>
        </div>
        <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white"><svg class="w-6 h-6 text-indigo-600" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd"
            d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
            clip-rule="evenodd"></path>
        </svg>
        </div>
        <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500"><span class="font-medium text-gray-900">{{live_stream.asset.created_by}}</span> scheduled at {{live_stream.start}}</p>
        <time datetime="2023-01-23T10:32" class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ live_stream.start | naturaltime }}</time>
      </li>
    {% else %}
      <li class="relative flex gap-x-4">
        <div class="absolute top-0 left-0 flex justify-center w-6 -bottom-6">
          <div class="w-px bg-gray-200"></div>
        </div>
        <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
          <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300"></div>
        </div>
        <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500"><span class="font-medium text-gray-900">{{live_stream.asset.created_by.name}}</span> scheduled at {{live_stream.start}}</p>
        <time datetime="2023-01-23T10:32" class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ live_stream.start | naturaltime }}</time>
      </li>
    {% endif %}
  {% endif %}


  {% if live_stream.get_server_status_display ==  "Created"  and live_stream.get_status_display == "Not Started" %}
    <li class="relative flex gap-x-4">
      <div class="absolute top-0 left-0 flex justify-center w-6 h-6">
          <div class="w-px bg-gray-200"></div>
      </div>
      <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
        <svg class="w-6 h-6 text-indigo-600" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd"
            d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
            clip-rule="evenodd"></path>
        </svg>
      </div>
        <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">Streaming server created {{live_stream.live_stream_usage.start_time}}</p>
            <time datetime="2023-01-23T11:03" class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ live_stream.live_stream_usage.start_time | naturaltime }}</time>
      </li>
    {% elif live_stream.live_stream_usage.start_time and live_stream.get_status_display != "Not Started" %}
      <li class="relative flex gap-x-4">
        <div class="absolute top-0 left-0 flex justify-center w-6 -bottom-6">
          <div class="w-px bg-gray-200"></div>
        </div>
        <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
          <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300"></div>
        </div>
        <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">Streaming server created {{live_stream.live_stream_usage.start_time}}</p>
          <time datetime="2023-01-23T11:03" class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ live_stream.live_stream_usage.start_time | naturaltime }}</time>
      </li>
  {% endif %}



  {% for event in events %}
    {% if event.get_type_display == "On Pubish" %}
      {% if events.last.get_type_display == "On Pubish" and forloop.last %}
        <li class="relative flex gap-x-4">
          <div class="absolute top-0 left-0 flex justify-center w-6 h-6">
              <div class="w-px bg-gray-200"></div>
          </div>
          <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
            <svg class="w-6 h-6 text-indigo-600" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd"
                d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
                clip-rule="evenodd"></path>
            </svg>
          </div>
      {% else %}
        <li class="relative flex gap-x-4">
          <div class="absolute top-0 left-0 flex justify-center w-6 -bottom-6">
            <div class="w-px bg-gray-200"></div>
          </div>
          <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
          <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300"></div>
          </div>
      {% endif %}
          <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">Streaming Started at {{event.created}}</p>
          <time datetime="2023-01-24T09:12" class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ event.created | naturaltime }}</time>
        </li>
    {% elif event.get_type_display == "On Pubish Done" %}
      {% if events.last.get_type_display == "On Pubish Done" and forloop.last %}
        <li class="relative flex gap-x-4">
          <div class="absolute top-0 left-0 flex justify-center w-6 h-6">
              <div class="w-px bg-gray-200"></div>
          </div>
          <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
            <svg class="w-6 h-6 text-indigo-600" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd"
                d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
                clip-rule="evenodd"></path>
            </svg>
          </div>
      {% else %}
        <li class="relative flex gap-x-4">
          <div class="absolute top-0 left-0 flex justify-center w-6 -bottom-6">
            <div class="w-px bg-gray-200"></div>
          </div>
          <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
          <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300"></div>
          </div>
      {% endif %}
          <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">Streaming ended at {{event.created}}</p>
          <time datetime="2023-01-24T09:12" class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ event.created | naturaltime }}</time>
        </li>
    {% elif event.get_type_display == "Stopped" %}
      {% if not asset.video.transcoding_submission_time %}
        <li class="relative flex gap-x-4">
          <div class="absolute top-0 left-0 flex justify-center w-6 h-6">
              <div class="w-px bg-gray-200"></div>
          </div>
          <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
            <svg class="w-6 h-6 text-indigo-600" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd"
                d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
                clip-rule="evenodd"></path>
            </svg>
          </div>
      {% else %}
        <li class="relative flex gap-x-4">
          <div class="absolute top-0 left-0 flex justify-center w-6 -bottom-6">
            <div class="w-px bg-gray-200"></div>
          </div>
          <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
          <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300"></div>
          </div>
      {% endif %}
          <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">Streaming Stopped at {{event.created}}</p>
          <time datetime="2023-01-24T09:12" class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ event.created | naturaltime }}</time>
        </li>
    {% endif %}
  {% endfor %}


  {% if asset.video.transcoding_submission_time %}
    <li class="relative flex gap-x-4">
      {% if asset.video.status == asset.video.Status.QUEUED and live_stream.get_server_status_display !=  "Destroyed"%}
      <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
          <svg class="w-6 h-6 text-indigo-600" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd"
              d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
              clip-rule="evenodd"></path>
          </svg>
        </div>
      {% else %}
      <div class="absolute top-0 left-0 flex justify-center w-6 -bottom-6">
          <div class="w-px bg-gray-200"></div>
        </div>
      <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
          <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300"></div>
        </div>
      {% endif %}
      <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">Recorded Video uploaded {{asset.video.transcoding_submission_time}}</p>
      <time title="{{ asset.video.transcoding_submission_time | date:'jS F Y g:i A' }}" class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ asset.video.transcoding_submission_time | naturaltime }}</time>
    </li>
    {% endif %}

    {% if live_stream.get_server_status_display ==  "Destroyed" %}
    <li class="relative flex gap-x-4">
      {% if asset.video.transcoding_start_time %}
          <div class="absolute top-0 left-0 flex justify-center w-6 -bottom-6">
            <div class="w-px bg-gray-200"></div>
          </div>
          <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
            <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300"></div>
          </div>
      {% else %}
        <div class="absolute top-0 left-0 flex justify-center w-6 h-6">
          <div class="w-px bg-gray-200"></div>
        </div>
        <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
          <svg class="w-6 h-6 text-indigo-600" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd"
              d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
              clip-rule="evenodd"></path>
          </svg>
        </div>
      {% endif %}
          <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">Streaming server terminated at {{live_stream.live_stream_usage.end_time}}</p>
          <time datetime="2023-01-24T09:20" class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ live_stream.live_stream_usage.end_time | naturaltime }}</time>
        </li>
    {% endif %}

      {% if asset.video.transcoding_start_time %}
        <li class="relative flex gap-x-4">
          {% if asset.video.status == asset.video.Status.TRANSCODING %}
          <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
              <svg class="w-6 h-6 text-indigo-600" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd"
                  d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
                  clip-rule="evenodd"></path>
              </svg>
            </div>
          {% else %}
          <div class="absolute top-0 left-0 flex justify-center w-6 -bottom-6">
              <div class="w-px bg-gray-200"></div>
            </div>
          <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
            <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300"></div>
            </div>
          {% endif %}
          <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">Transcoding started at {{asset.video.transcoding_start_time}}</p>
          <time title="{{ asset.video.transcoding_start_time | date:'jS F Y g:i A' }}" class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ asset.video.transcoding_start_time | naturaltime }}</time>
        </li>
      {% endif %}

      {% if asset.video.transcoding_end_time %}
        <li class="relative flex gap-x-4">
          {% if asset.video.status == asset.video.Status.COMPLETED %}
          <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
              <svg class="w-6 h-6 text-blue-600" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd"
                  d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
                  clip-rule="evenodd" />
              </svg>
            </div>
          <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">Transcoding ended at {{asset.video.transcoding_end_time}}</p>
          {% elif asset.video.status == asset.video.Status.ERROR  %}
          <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
              <svg class="w-6 h-6 text-red-600" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd"
                  d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
                  clip-rule="evenodd" />
              </svg>
            </div>
          <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">Transcoding failed</p>
          {% endif %}
          <time title="{{ asset.video.transcoding_end_time | date:'jS F Y g:i A' }}" class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ asset.video.transcoding_end_time | naturaltime }}</time>
        </li>
      {% endif %}
  </ul>
</div>
