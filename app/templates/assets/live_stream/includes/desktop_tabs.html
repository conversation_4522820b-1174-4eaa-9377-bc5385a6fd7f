{% load i18n %}

<div class="hidden lg:block">
  <div class="border-b border-gray-200">
    <nav class="flex -mb-px space-x-8">

      <a href="{% url 'live_streams' %}" class="px-1 py-4 text-sm font-medium border-b-2
      {% if request.resolver_match.view_name == 'live_streams' %}text-blue-700 border-blue-600 {% else %} border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 {% endif %} whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium">{% translate "Live" %}</a>

      <a href="{% url 'scheduled_live_streams' %}"  class="px-1 py-4 text-sm font-medium border-b-2
      {% if request.resolver_match.view_name == 'scheduled_live_streams' %}text-blue-700 border-blue-600 {% else %} border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 {% endif %} whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium">{% translate "Scheduled" %}</a>

      <a href="{% url 'completed_live_streams' %}"  class="px-1 py-4 text-sm font-medium border-b-2
      {% if request.resolver_match.view_name == 'completed_live_streams' %}text-blue-700 border-blue-600 {% else %} border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 {% endif %} whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium">{% translate "Completed" %}</a>
    </nav>
  </div>
</div>
