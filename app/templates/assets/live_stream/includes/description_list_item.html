<div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
  <dt class="text-sm font-medium text-gray-500">{{ label }}</dt>
  <dd class="flex mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
    <span class="flex-grow">{{ value }}</span>
    <span class="flex-shrink-0 ml-4">
        <button onclick="copyToClipboard('{{ value }}')" target="_blank" class="font-medium text-blue-700 bg-white rounded-md hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2">{{ action_label }}</button>
    </span>
  </dd>
</div>
