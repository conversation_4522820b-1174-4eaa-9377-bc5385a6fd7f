{% load i18n %}

<div x-data="{ open: false }" class="mt-4">
  <button @click="open = !open" type="button" class="text-gray-700 text-lg font-medium flex items-center">
    {% translate "Actions" %}
    <svg class="ml-1 h-5 w-5 inline transition-transform duration-200" :class="{'transform rotate-180': open}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
      <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
    </svg>
  </button>
  <div x-show="open"
       x-transition:enter="transition ease-out duration-200"
       x-transition:enter-start="opacity-0"
       x-transition:enter-end="opacity-100"
       x-transition:leave="transition ease-in duration-150"
       x-transition:leave-start="opacity-100"
       x-transition:leave-end="opacity-0"
       class="mt-6">
    <div class="flex space-x-4">
      <form method="post" class="inline">
        {% csrf_token %}
        <input type="hidden" name="action" value="upload_live_stream_recording">
        <button type="submit" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          {% translate "Upload Recording" %}
        </button>
      </form>
      <form method="post" class="inline">
        {% csrf_token %}
        <input type="hidden" name="action" value="stop_live_stream">
        <button type="submit" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
          {% translate "Stop Live Stream" %}
        </button>
      </form>
    </div>
  </div>
</div>
