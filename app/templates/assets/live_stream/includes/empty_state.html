<div class="mx-auto max-w-lg py-12 ">
  <h2 class="text-base font-semibold leading-6 text-gray-900">No Live Streams Yet</h2>
  <p class="mt-1 text-sm text-gray-500">Begin your broadcasting journey by either starting a live stream immediately or scheduling one for later.
    Pricing starts at just $0.715/hr for 720p or go full HD with 1080p at $1.43/hr.
   </p>
  <ul role="list" class="mt-6 divide-y divide-gray-200 border-b border-t border-gray-200">
    <li>
      <div class="group relative flex items-start space-x-3 py-4">
        <div class="flex-shrink-0">
          <span class="inline-flex h-10 w-10 items-center justify-center rounded-lg bg-blue-500">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6 text-white">
              <path stroke-linecap="round" d="M15.75 10.5l4.72-4.72a.75.75 0 011.28.53v11.38a.75.75 0 01-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 002.25-2.25v-9a2.25 2.25 0 00-2.25-2.25h-9A2.25 2.25 0 002.25 7.5v9a2.25 2.25 0 002.25 2.25z" />
            </svg>
          </span>
        </div>
        <div class="min-w-0 flex-1">
          <div class="text-sm font-medium text-gray-900">
            <a href="{{ cta_link }}">
              <span class="absolute inset-0" aria-hidden="true"></span>
              Go Live Now
            </a>
          </div>
          <p class="text-sm text-gray-500">Dive in and start broadcasting to your audience immediately.</p>
        </div>
        <div class="flex-shrink-0 self-center">
          <svg class="h-5 w-5 text-gray-400 group-hover:text-gray-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>
    </li>
    <li>
      <div class="group relative flex items-start space-x-3 py-4">
        <div class="flex-shrink-0">
          <span class="inline-flex h-10 w-10 items-center justify-center rounded-lg bg-yellow-500">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6 text-white">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5m-9-6h.008v.008H12v-.008zM12 15h.008v.008H12V15zm0 2.25h.008v.008H12v-.008zM9.75 15h.008v.008H9.75V15zm0 2.25h.008v.008H9.75v-.008zM7.5 15h.008v.008H7.5V15zm0 2.25h.008v.008H7.5v-.008zm6.75-4.5h.008v.008h-.008v-.008zm0 2.25h.008v.008h-.008V15zm0 2.25h.008v.008h-.008v-.008zm2.25-4.5h.008v.008H16.5v-.008zm0 2.25h.008v.008H16.5V15z" />
            </svg>
          </span>
        </div>
        <div class="min-w-0 flex-1">
          <div class="text-sm font-medium text-gray-900">
            <a href="{% url 'schedule_live_stream' %}">
              <span class="absolute inset-0" aria-hidden="true"></span>
              Schedule a Live Stream
            </a>
          </div>
          <p class="text-sm text-gray-500">Plan ahead and set a date and time for your broadcast.</p>
        </div>
        <div class="flex-shrink-0 self-center">
          <svg class="h-5 w-5 text-gray-400 group-hover:text-gray-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>
    </li>
  </ul>
</div>
