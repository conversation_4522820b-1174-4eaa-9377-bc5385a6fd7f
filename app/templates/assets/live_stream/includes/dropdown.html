{% if request.resolver_match.view_name == 'scheduled_live_streams' or request.resolver_match.view_name == 'completed_live_streams' %}
    <div x-data="{open:false, showModal: false }" class="relative inline-block text-left">
        <button type="button" @click="selectedAssetName='{{ asset.title|escapejs }}'; selectedAssetID='{{ asset.uuid }}'; selectedAssetType='{{ asset.get_type_display }}'; showModal = !showModal" @click.away="showModal = false;"
            class="flex items-center" id="menu-button" aria-expanded="true" aria-haspopup="true">
            <span class="sr-only">show Dropdown options</span>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                class="w-5 h-5 text-blue-700">
                <path stroke-linecap="round" stroke-linejoin="round"
                d="M12 6.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 12.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 18.75a.75.75 0 110-1.5.75.75 0 010 1.5z" />
            </svg>
        </button>
        <div x-show="showModal" x-transition:enter="transform transition ease-in duration-75"
            x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
            x-transition:leave="transition ease-out duration-75" x-transition:leave-start="opacity-100 scale-100"
            x-transition:leave-end="opacity-0 scale-95"
            class="absolute right-0 z-50 mt-2 w-28 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
            role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabindex="-1">
            <div class="py-1" role="none">
                {% if request.resolver_match.view_name == 'scheduled_live_streams' %}
                    <a @click="showDeleteModal = true"
                    class="cursor-pointer text-gray-700 hover:bg-gray-100 hover:text-gray-900 block px-4 py-2 text-sm"
                    role="menuitem" tabindex="-1" id="menu-item-0">Delete</a>
                {% endif %}
                {% if request.resolver_match.view_name == 'completed_live_streams' %}
                    <a href="{% url 'live_stream_move' %}?asset={{asset.uuid}}"
                    class="cursor-pointer text-gray-700 hover:bg-gray-100 hover:text-gray-900 block px-4 py-2 text-sm"
                    role="menuitem" tabindex="-1" id="menu-item-0">Move</a>
                {% endif %}
            </div>
        </div>
    </div>
{% endif %}
