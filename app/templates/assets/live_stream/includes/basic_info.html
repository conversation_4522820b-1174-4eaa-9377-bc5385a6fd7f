<!-- Basic Info -->
<div class="">
    <dl class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-1 gap-y-4">
      {% if live_stream.rtmp_url %}
        <div class="flex flex-none w-full gap-x-4 ">
          <dt class="flex-none">
            <span class="sr-only">RTMP Url</span>
            <svg fill="none" stroke="currentColor" stroke-width="1.5" class="w-5 h-6 text-gray-400" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244" />
            </svg>
          </dt>
          <dd class="cursor-pointer text-sm font-medium leading-6 text-gray-900" x-data="clipboardComponent()">
            <span @click="copyToClipboard()" x-ref="contentToCopy">{{live_stream.rtmp_url}}</span>
            <span x-show="showCopiedTooltip"
              class="inline-flex items-center rounded-md bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700">Copied!</span>
          </dd>
        </div>
      {% endif %}
      {% if live_stream.stream_key %}
        <div class="flex flex-none w-full gap-x-4">
          <dt class="flex-none">
            <span class="sr-only">Stream Key</span>
            <svg fill="none" stroke="currentColor" stroke-width="1.5" class="w-5 h-6 text-gray-400" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M15.75 5.25a3 3 0 0 1 3 3m3 0a6 6 0 0 1-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1 1 21.75 8.25z" />
            </svg>
          </dt>
          <dd class="cursor-pointer text-sm font-medium leading-6 text-gray-900" x-data="clipboardComponent()">
            <span @click="copyToClipboard()" x-ref="contentToCopy">{{live_stream.stream_key}}</span>
            <span x-show="showCopiedTooltip"
              class="inline-flex items-center rounded-md bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700">Copied!</span>
          </dd>
        </div>
      {% endif %}
      <div class="flex flex-none w-full gap-x-4">
        <dt class="flex-none">
          <span class="sr-only">Current M3U8 Url</span>
          <svg fill="none" stroke="currentColor" stroke-width="1.5" class="w-5 h-6 text-gray-400" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M3.375 19.5h17.25m-17.25 0a1.125 1.125 0 0 1-1.125-1.125M3.375 19.5h1.5C5.496 19.5 6 18.996 6 18.375m-3.75 0V5.625m0 12.75v-1.5c0-.621.504-1.125 1.125-1.125m18.375 2.625V5.625m0 12.75c0 .621-.504 1.125-1.125 1.125m1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125m0 3.75h-1.5A1.125 1.125 0 0 1 18 18.375M20.625 4.5H3.375m17.25 0c.621 0 1.125.504 1.125 1.125M20.625 4.5h-1.5C18.504 4.5 18 5.004 18 5.625m3.75 0v1.5c0 .621-.504 1.125-1.125 1.125M3.375 4.5c-.621 0-1.125.504-1.125 1.125M3.375 4.5h1.5C5.496 4.5 6 5.004 6 5.625m-3.75 0v1.5c0 .621.504 1.125 1.125 1.125m0 0h1.5m-1.5 0c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125m1.5-3.75C5.496 8.25 6 7.746 6 7.125v-1.5M4.875 8.25C5.496 8.25 6 8.754 6 9.375v1.5m0-5.25v5.25m0-5.25C6 5.004 6.504 4.5 7.125 4.5h9.75c.621 0 1.125.504 1.125 1.125m1.125 2.625h1.5m-1.5 0A1.125 1.125 0 0 1 18 7.125v-1.5m1.125 2.625c-.621 0-1.125.504-1.125 1.125v1.5m2.625-2.625c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125M18 5.625v5.25M7.125 12h9.75m-9.75 0A1.125 1.125 0 0 1 6 10.875M7.125 12C6.504 12 6 12.504 6 13.125m0-2.25C6 11.496 5.496 12 4.875 12M18 10.875c0 .621-.504 1.125-1.125 1.125M18 10.875c0 .621.504 1.125 1.125 1.125m-2.25 0c.621 0 1.125.504 1.125 1.125m-12 5.25v-5.25m0 5.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125m-12 0v-1.5c0-.621-.504-1.125-1.125-1.125M18 18.375v-5.25m0 5.25v-1.5c0-.621.504-1.125 1.125-1.125M18 13.125v1.5c0 .621.504 1.125 1.125 1.125M18 13.125c0-.621.504-1.125 1.125-1.125M6 13.125v1.5c0 .621-.504 1.125-1.125 1.125M6 13.125C6 12.504 5.496 12 4.875 12m-1.5 0h1.5m-1.5 0c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125M19.125 12h1.5m0 0c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125m-17.25 0h1.5m14.25 0h1.5" />
          </svg>
        </dt>
        <dd class="cursor-pointer text-sm leading-6 text-gray-500 break-all" x-data="clipboardComponent()">
          <div @click="copyToClipboard()" x-ref="contentToCopy">{{live_stream.get_hls_url}}</div>
          <span x-show="showCopiedTooltip"
            class="inline-flex items-center rounded-md bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700">Copied!</span>
        </dd>
      </div>
      {% if not request.user.current_organization.use_haproxy_for_livestream %}
      <div class="flex flex-none w-full gap-x-4">
       <dt class="flex-none">
         <span class="sr-only">New M3U8 Url</span>
         <svg fill="none" stroke="currentColor" stroke-width="1.5" class="w-5 h-6 text-gray-400" viewBox="0 0 24 24">
           <path stroke-linecap="round" stroke-linejoin="round"
             d="M3.375 19.5h17.25m-17.25 0a1.125 1.125 0 0 1-1.125-1.125M3.375 19.5h1.5C5.496 19.5 6 18.996 6 18.375m-3.75 0V5.625m0 12.75v-1.5c0-.621.504-1.125 1.125-1.125m18.375 2.625V5.625m0 12.75c0 .621-.504 1.125-1.125 1.125m1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125m0 3.75h-1.5A1.125 1.125 0 0 1 18 18.375M20.625 4.5H3.375m17.25 0c.621 0 1.125.504 1.125 1.125M20.625 4.5h-1.5C18.504 4.5 18 5.004 18 5.625m3.75 0v1.5c0 .621-.504 1.125-1.125 1.125M3.375 4.5c-.621 0-1.125.504-1.125 1.125M3.375 4.5h1.5C5.496 4.5 6 5.004 6 5.625m-3.75 0v1.5c0 .621.504 1.125 1.125 1.125m0 0h1.5m-1.5 0c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125m1.5-3.75C5.496 8.25 6 7.746 6 7.125v-1.5M4.875 8.25C5.496 8.25 6 8.754 6 9.375v1.5m0-5.25v5.25m0-5.25C6 5.004 6.504 4.5 7.125 4.5h9.75c.621 0 1.125.504 1.125 1.125m1.125 2.625h1.5m-1.5 0A1.125 1.125 0 0 1 18 7.125v-1.5m1.125 2.625c-.621 0-1.125.504-1.125 1.125v1.5m2.625-2.625c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125M18 5.625v5.25M7.125 12h9.75m-9.75 0A1.125 1.125 0 0 1 6 10.875M7.125 12C6.504 12 6 12.504 6 13.125m0-2.25C6 11.496 5.496 12 4.875 12M18 10.875c0 .621-.504 1.125-1.125 1.125M18 10.875c0 .621.504 1.125 1.125 1.125m-2.25 0c.621 0 1.125.504 1.125 1.125m-12 5.25v-5.25m0 5.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125m-12 0v-1.5c0-.621-.504-1.125-1.125-1.125M18 18.375v-5.25m0 5.25v-1.5c0-.621.504-1.125 1.125-1.125M18 13.125v1.5c0 .621.504 1.125 1.125 1.125M18 13.125c0-.621.504-1.125 1.125-1.125M6 13.125v1.5c0 .621-.504 1.125-1.125 1.125M6 13.125C6 12.504 5.496 12 4.875 12m-1.5 0h1.5m-1.5 0c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125M19.125 12h1.5m0 0c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125m-17.25 0h1.5m14.25 0h1.5" />
         </svg>
       </dt>
       <dd class="cursor-pointer text-sm leading-6 text-gray-500 break-all relative">
        <div>
          <span @click="copyToClipboard()" x-ref="contentToCopy">{{live_stream.get_hls_url_using_openresty}}</span>
          <span class="inline-flex items-center rounded-full bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700 relative">
            Beta
          </span>
        </div>
        <span x-show="showCopiedTooltip"
        class="inline-flex items-center rounded-md bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700">Copied!</span>
    </dd>
  </div>
  {% endif %}
      <div class="flex flex-none w-full gap-x-4 ">
        <dt class="flex-none">
          <span class="sr-only">Asset ID</span>
          <svg fill="none" stroke="currentColor" stroke-width="1.5" class="w-5 h-6 text-gray-400" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M3.75 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 0 1 3.75 9.375v-4.5zm0 9.75c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5a1.125 1.125 0 0 1-1.125-1.125v-4.5zm9.75-9.75c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 0 1 13.5 9.375v-4.5z" />
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M6.75 6.75h.75v.75h-.75v-.75zm0 9.75h.75v.75h-.75v-.75zm9.75-9.75h.75v.75h-.75v-.75zm-3 6.75h.75v.75h-.75v-.75zm0 6h.75v.75h-.75v-.75zm6-6h.75v.75h-.75v-.75zm0 6h.75v.75h-.75v-.75zm-3-3h.75v.75h-.75v-.75z" />
          </svg>
        </dt>
        <dd class="cursor-pointer text-sm leading-6 text-gray-500" x-data="clipboardComponent()">
          <span @click="copyToClipboard()" x-ref="contentToCopy">{{live_stream.asset.uuid}}</span>
          <span x-show="showCopiedTooltip"
            class="inline-flex items-center rounded-md bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700">Copied!</span>
        </dd>
      </div>
      {% if live_stream.chat_room_id %}
        <div class="flex flex-none w-full gap-x-4 ">
          <dt class="flex-none">
            <span class="sr-only">Chat Embed Code</span>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" stroke-width="1.5" class="w-5 h-6 text-gray-400" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3a49.5 49.5 0 0 1-4.02-.163 2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155" />
            </svg>
          </dt>
          <dd class="cursor-pointer text-sm leading-6 text-gray-500" x-data="clipboardComponent()">
            <span @click="copyToClipboard()" x-ref="contentToCopy">{{live_stream.chat_room_id}}</span>
            <span x-show="showCopiedTooltip"
              class="inline-flex items-center rounded-md bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700">Copied!</span>
          </dd>
        </div>

        <div class="relative" x-data="clipboardComponent()">
          <h3 class="mt-4 text-sm font-semibold leading-6 text-gray-900">Chat Embed Code</h3>
        <div class="mt-2 pl-4 pr-8 py-4 bg-gray-50 rounded">
        <pre x-ref="contentToCopy" class="overflow-hidden text-gray-500 font-medium">&lt;div style=&quot;padding-top:56.25%;position:relative;&quot;&gt;
  &lt;iframe src=&quot;https://app.tpstreams.com/live-chat/{{request.user.current_organization.uuid}}/{{asset.uuid}}&quot; style=&quot;border:0;max-width:100%;position:absolute;top:0;left:0;height:100%;width:100%;&quot; allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" &quot; allowfullscreen=&quot;&quot; frameborder=&quot;0&quot;&gt;&lt;/iframe&gt;
&lt;/div&gt;</pre>
          </div>

        <div @click="copyToClipboard()" class="absolute top-0 right-0 cursor-pointer flex">
          <span :class="{ 'invisible opacity-0 h-0': !showCopiedTooltip }"
            class="absolute inset-x-0 bottom-full flex mb-2.5 justify-center scale-100 translate-y-0 opacity-100">
            <span
              class="rounded-md bg-gray-900 px-3 py-1 text-[0.625rem] font-semibold uppercase leading-4 tracking-wide text-white drop-shadow-md filter">
              <svg aria-hidden="true" width="16" height="6" viewBox="0 0 16 6"
                class="absolute left-1/2 top-full -ml-2 -mt-px text-gray-900">
                <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M15 0H1V1.00366V1.00366V1.00371H1.01672C2.72058 1.0147 4.24225 2.74704 5.42685 4.72928C6.42941 6.40691 9.57154 6.4069 10.5741 4.72926C11.7587 2.74703 13.2803 1.0147 14.9841 1.00371H15V0Z"
                  fill="currentColor"></path>
              </svg>
              Copied!
            </span>
          </span>
          <svg :hidden="!showCopiedTooltip" class="h-8 w-8 rotate-[-8deg] stroke-[#06B6D4]" fill="none" viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
            <path
              d="M12.9975 10.7499L11.7475 10.7499C10.6429 10.7499 9.74747 11.6453 9.74747 12.7499L9.74747 21.2499C9.74747 22.3544 10.6429 23.2499 11.7475 23.2499L20.2475 23.2499C21.352 23.2499 22.2475 22.3544 22.2475 21.2499L22.2475 12.7499C22.2475 11.6453 21.352 10.7499 20.2475 10.7499L18.9975 10.7499"
              stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
            <path
              d="M17.9975 12.2499L13.9975 12.2499C13.4452 12.2499 12.9975 11.8022 12.9975 11.2499L12.9975 9.74988C12.9975 9.19759 13.4452 8.74988 13.9975 8.74988L17.9975 8.74988C18.5498 8.74988 18.9975 9.19759 18.9975 9.74988L18.9975 11.2499C18.9975 11.8022 18.5498 12.2499 17.9975 12.2499Z"
              stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M13.7475 16.2499L18.2475 16.2499" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
            </path>
            <path d="M13.7475 19.2499L18.2475 19.2499" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
            </path>
            <g class="">
              <path d="M15.9975 5.99988L15.9975 3.99988" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              </path>
              <path d="M19.9975 5.99988L20.9975 4.99988" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              </path>
              <path d="M11.9975 5.99988L10.9975 4.99988" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              </path>
            </g>
          </svg>
          <svg :hidden="showCopiedTooltip"
            class="hover:rotate-[-4deg] hover:stroke-slate-600 h-8 stroke-slate-400 transition w-8" fill="none"
            viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg" stroke-width="1.5" stroke-linecap="round"
            stroke-linejoin="round">
            <path
              d="M12.9975 10.7499L11.7475 10.7499C10.6429 10.7499 9.74747 11.6453 9.74747 12.7499L9.74747 21.2499C9.74747 22.3544 10.6429 23.2499 11.7475 23.2499L20.2475 23.2499C21.352 23.2499 22.2475 22.3544 22.2475 21.2499L22.2475 12.7499C22.2475 11.6453 21.352 10.7499 20.2475 10.7499L18.9975 10.7499"
              stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
            <path
              d="M17.9975 12.2499L13.9975 12.2499C13.4452 12.2499 12.9975 11.8022 12.9975 11.2499L12.9975 9.74988C12.9975 9.19759 13.4452 8.74988 13.9975 8.74988L17.9975 8.74988C18.5498 8.74988 18.9975 9.19759 18.9975 9.74988L18.9975 11.2499C18.9975 11.8022 18.5498 12.2499 17.9975 12.2499Z"
              stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M13.7475 16.2499L18.2475 16.2499" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
            </path>
            <path d="M13.7475 19.2499L18.2475 19.2499" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
            </path>
            <g class="opacity-0">
              <path d="M15.9975 5.99988L15.9975 3.99988" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              </path>
              <path d="M19.9975 5.99988L20.9975 4.99988" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              </path>
              <path d="M11.9975 5.99988L10.9975 4.99988" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              </path>
            </g>
          </svg>
        </div>
      </div>

      {% endif %}
    </dl>
</div>
