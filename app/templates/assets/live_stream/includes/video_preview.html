{% load i18n %}
<div class="aspect-video">
    {% if live_stream.get_status_display == "Not Started" and live_stream.get_server_status_display ==  "Not Created" and live_stream.start %}
        <div style="background-color: black; width: 100%; height: 0; padding-top: 56.25%; position: relative;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
            <p style="color: white; font-size: 18px;">Live stream is scheduled at {{ live_stream.start}}</p>
        </div>
        </div>
    {% elif live_stream.get_server_status_display == "Creating" %}
        <div style="background-color: black; width: 100%; height: 0; padding-top: 56.25%; position: relative;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
            <p style="color: white; font-size: 18px;">Getting ready to launch live stream</p>
        </div>
        </div>
    {% elif live_stream.get_status_display == "Not Started" and live_stream.get_server_status_display == "Created" and live_stream.start %}
        <div style="background-color: black; width: 100%; height: 0; padding-top: 56.25%; position: relative;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
            <p style="color: white; font-size: 18px;">Live stream will begin soon</p>
        </div>
        </div>
    {% else  %}
      {{ embed_code | safe }}
    {% endif %}
</div>
