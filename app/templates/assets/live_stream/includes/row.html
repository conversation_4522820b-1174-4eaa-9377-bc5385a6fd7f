<tr x-cloak x-data="{showDropDown: false}">
    <td class="whitespace-nowrap pl-4 sm:pl-6 pr-3 py-4 text-sm font-medium text-gray-900">
      <div class="flex space-x-3 items-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none">
          <path fill="#f94f4f" fill-rule="evenodd" d="M10.269 2.187a10.032 10.032 0 0 1 3.462 0 1 1 0 1 0 .345-1.97 12.033 12.033 0 0 0-4.152 0 1 1 0 0 0 .345 1.97Zm7.604-.611a1 1 0 0 0-.983 1.741A9.96 9.96 0 0 1 21.963 12c0 5.502-4.46 9.963-9.963 9.963a9.967 9.967 0 0 1-9.383-6.604 1 1 0 1 0-1.883.675C2.39 20.654 6.807 23.963 12 23.963c6.607 0 11.963-5.356 11.963-11.963a11.96 11.96 0 0 0-6.09-10.424ZM6.921 3.426a1 1 0 1 0-1.02-1.719 12.017 12.017 0 0 0-3.087 2.63A1 1 0 0 0 4.35 5.617a10.017 10.017 0 0 1 2.572-2.191ZM2.617 8.642a1 1 0 0 0-1.883-.675A11.944 11.944 0 0 0 .037 12a1 1 0 1 0 2 0c0-1.18.205-2.311.58-3.36ZM12 19.7A7.701 7.701 0 1 0 12 4.3 7.701 7.701 0 0 0 12 19.7Zm-2.467-4.897V9.196a1 1 0 0 1 1.532-.847l4.464 2.804a1 1 0 0 1 0 1.694l-4.464 2.804a1 1 0 0 1-1.532-.847Z" clip-rule="evenodd"/>
        </svg>
          <a href="{% url 'live_stream_settings' uuid=asset.uuid %}">
            <span title="{{ asset.title }}">{{ asset.title|truncatechars:60 }}</span>
            {% include "assets/includes/asset_status_badge.html" %}
          </a>
      </div>
    </td>
    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ asset.uuid }}</td>
    {% if request.resolver_match.view_name == 'live_streams' or request.resolver_match.view_name == 'scheduled_live_streams' %}
       <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ asset.live_stream.start }}</td>
    {% else  %}
      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
        {% if asset.live_stream.live_stream_usage.start_time %}
            {% with duration_seconds=asset.live_stream.live_stream_usage.start_time|calculate_time_difference_in_seconds:asset.live_stream.live_stream_usage.end_time %}
                {% if duration_seconds is not None %}
                    {{ duration_seconds|seconds_to_hours_minutes }}
                {% else %}
                    Updating...
                {% endif %}
            {% endwith %}
        {% endif %}
    </td>
    {% endif %}
    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
      <div class="inline-flex space-x-2 items-center">
          <div class="flex" x-on:mouseover="tooltip = true" x-on:mouseleave="tooltip = false">
            <div class="relative z-30 inline-flex">
              <div class="relative" x-cloak x-show.transition.origin.top="tooltip">
                <div
                  class="absolute flex justify-center top-0 z-10 w-32 p-2 -mt-1 text-xs leading-tight text-white transform -translate-x-1/2 -translate-y-full bg-gray-900 rounded-lg shadow-lg">
                  <span x-text="copyPlaybackURLButtonText">Copy Playback URL</span>
                </div>

                <svg
                  class="absolute z-10 w-6 h-6 text-gray-900 transform -translate-x-6 -translate-y-3 fill-current stroke-current"
                  width="8" height="8">
                  <rect x="12" y="-10" width="8" height="8" transform="rotate(45)"/>
                </svg>
              </div>
            </div>
          </div>
        </a>
        <a class="cursor-pointer" x-data="Clipboard" @click="copyEmbedCodeToClipboard('{{ asset.uuid }}')">
          <div class="flex" x-on:mouseover="tooltip = true" x-on:mouseleave="tooltip = false">
            <div class="relative z-30 inline-flex">
              <div class="cursor-pointer">
                <svg class="w-5 h-5 text-blue-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                     stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M17.25 6.75L22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3l-4.5 16.5"/>
                </svg>
              </div>
              <div class="relative" x-cloak x-show.transition.origin.top="tooltip">
                <div
                  class="absolute flex justify-center top-0 z-10 w-32 p-2 -mt-1 text-xs leading-tight text-white transform -translate-x-1/2 -translate-y-full bg-gray-900 rounded-lg shadow-lg">
                  <span x-show="!loading" x-text="copyEmbedCodebuttonText">Copy Embed Code</span>
                  <svg x-show="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                       xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
                <svg
                  class="absolute z-10 w-6 h-6 text-gray-900 transform -translate-x-6 -translate-y-3 fill-current stroke-current"
                  width="8" height="8">
                  <rect x="12" y="-10" width="8" height="8" transform="rotate(45)"/>
                </svg>
              </div>
            </div>
          </div>
        </a>
        {% include "./dropdown.html" %}
      </div>
    </td>
  </tr>
