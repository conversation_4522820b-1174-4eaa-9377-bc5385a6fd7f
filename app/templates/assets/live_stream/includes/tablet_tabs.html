{% load i18n %}

<div class="lg:hidden">
  <label for="selected-tab" class="sr-only">Select a tab</label>
  <select id="selected-tab" name="selected-tab"
    class="mt-1 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
    onchange="window.location.href=this.value;">
    <option value="{% url 'live_streams' %}"  {% if request.resolver_match.view_name == 'live_streams' %} selected {% endif %}>
      {% translate "Live" %}</option>
        <option value="{% url 'scheduled_live_streams' %}"  {% if request.resolver_match.view_name == 'scheduled_live_streams' %} selected {% endif %}>
          {% translate "Scheduled" %} </option>
          <option value="{% url 'completed_live_streams' %}"  {% if request.resolver_match.view_name == 'completed_live_streams' %} selected {% endif %}>
            {% translate "Completed" %} </option>
  </select>
</div>
