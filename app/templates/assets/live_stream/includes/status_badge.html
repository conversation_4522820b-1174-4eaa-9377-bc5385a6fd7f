<div class="flex items-center space-x-2">
    {% if live_stream.get_server_status_display == "Creating" %}
        <span
            class="inline-flex items-center gap-x-1.5 rounded-full bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700">
            <svg class="h-1.5 w-1.5 fill-blue-500" viewBox="0 0 6 6" aria-hidden="true">
            <circle cx="3" cy="3" r="3" />
            </svg>
            Creating
        </span>
    {% endif %}

    {% if live_stream.get_server_status_display == "Created" and live_stream.get_status_display == "Not Started"  %}
        <span
            class="flex items-center gap-x-1.5 rounded-full bg-yellow-100 px-1.5 py-0.5 text-xs font-medium text-yellow-800">
            <svg class="h-1.5 w-1.5 fill-yellow-500" viewBox="0 0 6 6" aria-hidden="true">
            <circle cx="3" cy="3" r="3" />
            </svg>
            Ready To Stream
        </span>
    {% endif %}

    {% if live_stream.get_status_display == "Streaming" %}
        <span
            class="inline-flex items-center gap-x-1.5 rounded-full bg-green-100 px-1.5 py-0.5 text-xs font-medium text-green-700">
            <svg class="h-1.5 w-1.5 fill-green-500" viewBox="0 0 6 6" aria-hidden="true">
            <circle cx="3" cy="3" r="3" />
            </svg>
            Live
        </span>
    {% endif %}
    {% if live_stream.get_status_display == "ERROR" %}
        <span
            class="inline-flex items-center gap-x-1.5 rounded-full bg-red-100 px-1.5 py-0.5 text-xs font-medium text-red-700">
            <svg class="h-1.5 w-1.5 fill-red-500" viewBox="0 0 6 6" aria-hidden="true">
            <circle cx="3" cy="3" r="3" />
            </svg>
            Error
        </span>
    {% endif %}
    {% if live_stream.get_status_display == "Disconnected" %}
        <span
            class="inline-flex items-center gap-x-1.5 rounded-full bg-yellow-100 px-1.5 py-0.5 text-xs font-medium text-yellow-800">
            <svg class="h-1.5 w-1.5 fill-yellow-500" viewBox="0 0 6 6" aria-hidden="true">
            <circle cx="3" cy="3" r="3" />
            </svg>
            Awaiting Broadcast
        </span>
    {% endif %}
    {% if live_stream.get_status_display == "Recording" and live_stream.get_server_status_display != "Destroyed" %}
        <span
            class="inline-flex items-center gap-x-1.5 rounded-full bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700">
            <svg class="h-1.5 w-1.5 fill-blue-500" viewBox="0 0 6 6" aria-hidden="true">
            <circle cx="3" cy="3" r="3" />
            </svg>
            Ending
        </span>
    {% endif %}
    {% if live_stream.get_server_status_display == "Destroyed" %}
        <span
            class="inline-flex items-center gap-x-1.5 rounded-full bg-gray-100 px-1.5 py-0.5 text-xs font-medium text-gray-600">
            <svg class="h-1.5 w-1.5 fill-gray-400" viewBox="0 0 6 6" aria-hidden="true">
            <circle cx="3" cy="3" r="3" />
            </svg>
            Ended
        </span>
    {% endif %}
</div>
