<script>
    function copyToClipboard(text) {
        const input = document.createElement('textarea');
        input.value = text;
        document.body.appendChild(input);
        input.select();
        document.execCommand('copy');
        input.remove();

        const copyButton = event.target;
        copyButton.innerText = 'Copied';
        copyButton.classList.add('copied');
        setTimeout(() => {
            copyButton.innerText = 'Copy';
            copyButton.classList.remove('Copied');
        }, 3000);
    }
</script>
