{% load i18n %}
{% load static %}

<!DOCTYPE html>
<html lang="en" style="height: 100%">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>{{asset.title}}</title>
  <link rel="stylesheet" href="{% static 'css/live_chat_v2.css' %}">
  <style>
    .h-full {
      height: 100%;
    }
    .text-2xl {
      font-size: 1.5rem;
    }
  </style>
</head>
<body class="h-full">
{% if asset.live_stream.status  == 2 or  asset.live_stream.status  == 3 %}
  <div class="flex h-full justify-center items-center text-gray-600 font-medium text-2xl">
    Chat is offline
  </div>
{% else %}
  <div id="app">
  </div>

  <script src="{% static 'js/live_chat_v2.umd.cjs' %}?v=2.0"></script>
  <script>
    const config = {
      apiUrl: "{{ chat_url }}",
      apiKey: "{{ api_key }}",
      roomId: "{{ asset.live_stream.chat_room_id }}",
      title: "{{ asset.title }}"
    }
    new TPStreamsChat.load(document.querySelector("#app"), config)
  </script>
{% endif %}

</body>
</html>
