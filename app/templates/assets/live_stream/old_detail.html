{% extends "./base.html" %}
{% load i18n %}
{% load static i18n %}

{% block extra_head %}
  <link rel="stylesheet" href="{% static 'css/prism.css' %}">
{% endblock extra_head%}

{% block live_streams %}
{% include "./includes/heading.html" with title=live_stream.asset.title %}
{% if live_stream.get_status_display == 'Not Started' and live_stream.rtmp_url %}
  <p class="max-w-2xl text-sm text-gray-500">The live stream will automatically terminate if it's not connected within 30 minutes from the time of creation.</p>
{% endif %}

<div class="px-4 sm:px-6 lg:px-0">
<div class="py-6">
<div class="divide-y divide-gray-200">
    <div class="space-y-1">
      <h3 class="text-lg font-medium leading-6 text-gray-900">Stream Settings</h3>
      <p class="max-w-2xl text-sm text-gray-500">Following are the Live Stream details.</p>
    </div>
    <div class="mt-6 flex flex-col lg:flex-row">
      <dl class="divide-y divide-gray-200">
        {% if live_stream.rtmp_url %}
          {% include "./includes/description_list_item.html" with label="Rtmp Url" value=live_stream.rtmp_url  action_label="Copy"  %}
          {% include "./includes/description_list_item.html" with label="Stream Key" value=live_stream.stream_key action_label="Copy" %}
         {% endif %}
         {% if live_stream.asset.uuid %}
          {% include "./includes/description_list_item.html" with label="Asset ID" value=live_stream.asset.uuid action_label="Copy" %}
         {% endif %}
         {% if live_stream.status == 0 %}
         {% include "./includes/description_list_item.html" with label="Status" value=live_stream.get_server_status_display   %}
        {% else %}
         {% include "./includes/description_list_item.html" with label="Status" value=live_stream.get_status_display   %}
        {% endif %}
        {% include "./includes/description_list_item.html" with label="M3U3 URL" value=live_stream.get_hls_url %}
        {% if live_stream.chat_room_id %}
        {% include "./includes/chat_embed.html" %}
        {% endif %}
        {% if live_stream.server_status == 0 %}
        <form action="{% url 'create_live_stream_server' uuid=live_stream.asset.uuid %}" method="post">
         {% csrf_token %}
         <div class="mt-8 flex">
          <button type="submit" class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-600">Start Live</button>
         </div>
        </form>
       {% elif live_stream.status != 2 and live_stream.status != 3 and live_stream.server_status != 5 %}
        <form action="{% url 'stop_live_stream' uuid=live_stream.asset.uuid %}" method="post">
         {% csrf_token %}
         <div class="mt-8 flex">
          <button type="submit" class="rounded-md bg-red-500 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-400">Stop Live</button>
         </div>
        </form>
       {% endif %}
      </dl>
     <div class="mx-auto mt-4 w-full lg:w-1/3">
       <div class="text-right">
        <button x-data="Clipboard" x-init="()=>{copyPlaybackURLButtonText='Copy Embed Code'}" @click="copy(`{{ embed_code }}`)" type="button"
                class="inline-flex items-center gap-x-1.5 rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
         <svg  class="-ml-0.5 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
         </svg>
         <span x-text="copyPlaybackURLButtonText"></span>
        </button>
       </div>
       <div class="mt-2">
        {% if live_stream.status == 0 and live_stream.server_status == 0 and live_stream.start %}
        <div style="background-color: black; width: 100%; height: 0; padding-top: 56.25%; position: relative;">
          <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
              <p style="color: white; font-size: 18px;">Live stream is scheduled at {{ live_stream.start}}</p>
          </div>
        </div>
        {% elif live_stream.status == 0 and live_stream.server_status == 2 and live_stream.start and live_stream.server_status != 5 %}
        <div style="background-color: black; width: 100%; height: 0; padding-top: 56.25%; position: relative;">
          <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
              <p style="color: white; font-size: 18px;">Live stream will begin soon</p>
          </div>
        </div>
        {% elif live_stream.status == 5 or live_stream.server_status == 5 %}
        <div style="background-color: black; width: 100%; height: 0; padding-top: 56.25%; position: relative;">
          <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
              <p style="color: white; font-size: 18px;">Live stream has been ended</p>
          </div>
        </div>
        {% else  %}
          {{ embed_code | safe }}
        {% endif %}
       </div>
     </div>
    </div>
  </div>
</div>
</div>
{% endblock %}


{% block extra_body %}
  {% include "./includes/detail_script.html" %}
  {% include "assets/includes/copy_embed_code_scripts.html" %}
 <script>
  {% if live_stream.server_status == 1 %}
   setTimeout(() => {window.location.reload()}, 15000)
  {% endif %}
 </script>
 <script defer src="{% static 'js/prism.js' %}"></script>
{% endblock %}
