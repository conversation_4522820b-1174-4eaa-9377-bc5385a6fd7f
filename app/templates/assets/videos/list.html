{% extends "layouts/top_searchbar_layout.html" %}
{% load i18n %}

{% block head_title %}{% translate "Assets" %}{% endblock %}
  {% block extra_head %}
    {% if "vimeo alternative TP Streams" in request.GET.utm_campaign %}
    {% include "assets/includes/google_ad_conversion.html" with conversion_lable="-i-4CNHGrbgYEIqQztUD" %}
  {% endif %}
{% endblock extra_head%}
{% block main_content %}
{% translate title  as title %}
  <div x-cloak
  x-data="{open: false, showAddFolderModal: false, showDeleteModal: false, showRenameModal: false, showDeleteFolderModal: false, selectedAssetID: '', selectedAssetName:'', selectedAssetType:'', folderToDelete:''}"
  @keydown.window.escape="open = false; showAddFolderModal = false; showDeleteModal = false; showRenameModal=false; showDeleteFolderModal=false">
    {% include "assets/includes/heading.html" %}
    {% include "./includes/list_table.html" %}
    {% include "./includes/add_folder_modal.html" %}
    {% include "./includes/delete_folder_modal.html" %}
    {% include "./includes/asset_delete_modal.html" %}
    {% include "./includes/asset_rename_modal.html" %}
  </div>
{% endblock main_content %}


{% block extra_body %}
  {% include "assets/includes/copy_embed_code_scripts.html" %}
  {% include "./includes/add_folder_scripts.html" %}
  {% include "assets/includes/dropdown_actions_scripts.html" %}
{% endblock %}
