{% extends "layouts/sidebar_layout.html" %}
{% load i18n %}
{% block head_title %}{% translate "Upload Video" %}{% endblock %}

{% block main_content %}
{% translate "Upload Video" as title %}
{% include "assets/includes/heading.html" %}

<div class="" x-data="app" x-init="initializeUppy">
  {% include "./includes/upload_progress_nav.html" %}
  {% include "./includes/selected_files.html" %}
  {% include "./includes/video_settings.html" %}
  {% include "./includes/transcoding_options.html" %}
  {% include "./includes/upload_files_preview.html" %}
  {% include "./includes/prev_next.html" %}
</div>


{% endblock main_content %}

{% block extra_body %}

<script type="application/javascript" src="https://releases.transloadit.com/uppy/v3.3.1/uppy.min.js"></script>
<script>
const limitedAccessEnabled = {% if request.user.current_organization.limited_access_enabled %}true{% else %}false{% endif %};
class UppyFileUploader {
  constructor() {
    this.initialize()
  }

  initialize() {
    this.uppy = new Uppy.Uppy({
      autoProceed: true,
      allowMultipleUploadBatches: false,
      restrictions: {
        maxNumberOfFiles: null,
        allowedFileTypes: [".mp4",".avi", ".m4v", ".mov", ".mkv"]
      },
    })

    this.uppy.use(Uppy.AwsS3Multipart, {
      companionUrl: '/api/v1/{{ request.user.current_organization_uuid }}/',
    })
  }

  on(event, operation) {
    this.uppy.on(event, operation)
  }

  addFile(attachment) {
    return this.uppy.addFile({
      data: attachment.file,
      type: attachment.file.type,
      name: attachment.name,
      meta: {id: attachment.id},
    })
  }

  addFiles(files) {
    const filesToAdd = files.map(attachment => {
      return {
        data: attachment.file,
        type: attachment.file.type,
        name: attachment.name,
        meta: {id: attachment.id, asset_id: attachment.asset_id},
      }
    })
    return this.uppy.addFiles(filesToAdd)
  }
}

function app() {
  return {
    step: 1,
    assets: [],
    isSelectAllChecked: false,
    resolutions: [],
    contentProtectionType: "disabled",
    generateSubtitle: false,
    error: null,
    uppy: null,
    showLoading: false,
    uploadStarted: false,
    isAllVideosUploaded: false,
    codecs: ['h264'],

    humanizeBytes: function(numberOfBytes) {
      // Approximate to the closest prefixed unit
      const units = [
        "B",
        "KiB",
        "MiB",
        "GiB",
        "TiB",
        "PiB",
        "EiB",
        "ZiB",
        "YiB",
      ];
      const exponent = Math.min(
        Math.floor(Math.log(numberOfBytes) / Math.log(1024)),
        units.length - 1
      );
      const approx = numberOfBytes / 1024 ** exponent;
      const output =
        exponent === 0
          ? `${numberOfBytes} bytes`
          : `${approx.toFixed(3)} ${
              units[exponent]
            } (${numberOfBytes} bytes)`;

      return output;
    },

    initializeUppy() {
      this.uppy = new UppyFileUploader()
      this.uppy.on('upload-progress', this.uploadProgress.bind(this));
      this.uppy.on('upload-success', this.uploadSuccess.bind(this));
      this.uppy.on('upload-error', this.uploadError.bind(this));
    },

    uploadProgress(file, progress) {
      let percentage = progress.bytesUploaded / progress.bytesTotal * 100
      const asset = this.assets.find(asset => asset.asset_id === file.meta.asset_id)
      asset.progress = Math.round(percentage)
    },

    uploadSuccess(file, response) {
      fetch(`/api/v1/{{ request.user.current_organization_uuid }}/assets/${file.meta.asset_id}/start_transcoding/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          "X-CSRFToken": "{{ csrf_token }}"
        }
      })
        .then((response) => response.json())
        .then(data => {
          const asset = this.assets.find(asset => asset.asset_id === data.id)
          asset.status = data.video.status
          this.isAllVideosUploaded = this.assets.filter(asset => asset.status !== "Queued").length === 0
        })
        .catch(error => {
          this.error = `Error in starting transcoding for the video ${file.meta.name}`
        })
        window.onbeforeunload = null
      if (this.generateSubtitle) {
          this.generateSubtitles(file);
        }
    },

    generateSubtitles(file) {
      fetch(`/api/v1/{{ request.user.current_organization_uuid }}/assets/${file.meta.asset_id}/generate_subtitle/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          "X-CSRFToken": "{{ csrf_token }}"
        }
      })
        .catch(error => {
          console.error(`Error in generating subtitle for the video ${file.meta.name}: ${error.message}`);
        });
    },

    uploadError(file, error, response) {
      const asset = this.assets.find(asset => asset.asset_id === file.meta.asset_id);
      asset.status = "Error";

      if (this.assets.length === 1) {
        this.error = `Failed to upload video: ${file.name}`;
      } else {
        this.error = `Failed to upload videos`;
      }
      this.showLoading = false;
      
      fetch(`/api/v1/{{ request.user.current_organization_uuid }}/assets/${file.meta.asset_id}/update_video_status/`, {
        method: 'POST',
        body: JSON.stringify({ status: "error" }),
        headers: {
          'Content-Type': 'application/json',
          "X-CSRFToken": "{{ csrf_token }}"
        }
      })
    },

    selectFile: function(evt) {
      evt.preventDefault();
      const files = evt.dataTransfer ? [...evt.dataTransfer.files] : [...evt.target.files]

      if (files.length === 0) return

      const validFiles = this.filterValidFiles(files)
      const objects = validFiles.map(file => {
        return {
          id: crypto.randomUUID(),
          name: file.name,
          file: file,
          isSelected: false,
          progress: 0,
          status: "Uploading"
        }
      })
      this.assets = this.assets.concat([...objects])
    },

    filterValidFiles: function(files) {
      const validFileTypes = ["video/mp4", "video/avi", "video/quicktime", "video/x-matroska"];
      const validFiles = [];

      for (const file of files) {
        try {
          if (validFileTypes.includes(file.type)) {
            if (limitedAccessEnabled) {
              this.checkFileSize(file);
              this.checkVideoDuration(file);
            }
            validFiles.push(file);
          }
        } catch (error) {
          this.error = error.message;
        }
      }
      return validFiles;
    },

    checkFileSize: function(file) {
      const maxSize = 50000000;
      if (file.size > maxSize) {
        throw new Error(`File size exceeds the allowed limit. Maximum allowed size is 50 MB. To update your plan, <NAME_EMAIL>.`);
        }
      },


    checkVideoDuration: function(file) {
      this.getVideoDuration(file)
        .then(duration => {
          const videoDuration = duration;
          const maxDuration = 600; // 10 minutes in seconds
          if (videoDuration > maxDuration) {
            this.selectAll()
            this.removeSelected()
            throw new Error(
              `Video duration exceeds the allowed limit. Maximum allowed duration is 10 minutes. To update your plan, <NAME_EMAIL>.`
            );
          }
        })
        .catch(error => {
          this.error = error.message;
        });
      },

    getVideoDuration: function(file) {
      return new Promise((resolve, reject) => {
        const video = document.createElement("video");
        video.preload = "metadata";

        video.onloadedmetadata = function() {
          window.URL.revokeObjectURL(video.src);
          const duration = video.duration;
          resolve(duration);
        };

        video.onerror = function() {
          reject(new Error("Unable to get video duration."));
        };

        video.src = URL.createObjectURL(file);
      });
    },

    removeFile: function(id) {
      this.$refs.assets.value = null;
      this.assets = this.assets.filter((asset) => asset.id !== id);
    },

    selectAll() {
      this.assets = this.assets.map(asset => {
        asset.isSelected = true
        return asset
      })
    },

    unSelectAll() {
      this.assets = this.assets.map(asset => {
        asset.isSelected = false;
        return asset
      })
    },

    removeSelected() {
      this.assets = this.assets.filter((asset) => !asset.isSelected);
      this.isSelectAllChecked = false;
    },

    selectAllResolutions() {
      this.resolutions = {{ enabled_resolutions|safe }};


    },

    unSelectAllResolutions() {
      this.resolutions = [];
    },

    preventPageNavigation(){
      window.onbeforeunload = function(event) {
        event.returnValue = "An upload is in progress, are you sure you want to leave this page?";
      };
    },

    handleNextButtonClick() {
      if (this.step === 4) {
        this.preventPageNavigation()
        this.createAssetsAndStartUpload()
      } else if (this.step === 3) {
        if (!this.resolutions.length) {
          this.error = "Please select atleast one resolution"
          return;
        }
        this.error = null
        this.gotoNextStep()
      } else if(this.step === 1 || this.step === 2) {
        this.gotoNextStep()
      }
    },

    createAssetsAndStartUpload() {
      const data = this.assets.map(asset => {
        return {
          title: asset.name,
          resolutions: this.resolutions,
          content_protection_type: this.contentProtectionType,
          generate_subtitle: this.generateSubtitle,
          folder: "{{ folder_uuid }}",
          video_codecs: this.codecs,
        }
      })
      this.showLoading = true
      fetch(`/api/v1/{{ request.user.current_organization_uuid }}/assets/bulk_create_videos/`, {
        method: 'POST',
        body: JSON.stringify(data),
        headers: {
          'Content-Type': 'application/json',
          "X-CSRFToken": "{{ csrf_token }}"
        }
      })
        .then((response) => {
            if (response.status === 201) {
              return response.json();
            } else if (response.status === 400){
                return response.json().then((data) => {
                throw new Error(data[0]["non_field_errors"][0]);
            });
            }
          })
        .then(data => {
          this.showLoading = false
          this.uploadStarted = true
          this.assets.forEach((asset, index) => {
            asset.asset_id = data[index].id
          })
          this.uppy.addFiles(this.assets)
        })
        .catch(error => {
          this.showLoading = false
          this.uploadStarted = false
          this.error = error.message || "Error in creating assets. Please try again"
        })
    },

    gotoNextStep() {
      this.step += 1
    }
  }
}
</script>
{% endblock extra_body %}
