{% extends "layouts/top_searchbar_layout.html" %}

{% block main_content %}

<div class="confirmation-container flex flex-col space-y-6">
  <h2 class="confirmation-title text-2xl font-semibold text-center">Confirm Actions</h2>

  <form method="POST" x-data="{ buttonDisabled: false }" x-on:submit="buttonDisabled = true">
    {% csrf_token %}
    <div class="confirmation-group flex flex-col py-4 border-b border-gray-200">
        <div class="confirmation-message flex items-center space-x-2">
            <span class="text-gray-700 text-base">Retranscode video?</span>
        </div>
        <div class="confirmation-actions flex justify-end mt-2">
            <input type="hidden" name="action" value="retranscode">
            <button type="submit" class="retranscode-button inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded" x-bind:disabled="buttonDisabled"  x-bind:class="{ 'cursor-not-allowed': buttonDisabled }">
              <span x-show="buttonDisabled">Retranscoding...</span>
              <span x-show="!buttonDisabled">Retranscode Video</span>
            </button>
            <a href="{% url 'asset_detail' asset_uuid=asset_uuid %}" class="inline-block bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-2 rounded ml-2">Cancel</a>
        </div>
    </div>
  </form>

  <div class="confirmation-group flex flex-col py-4 border-b border-gray-200">
    <div class="confirmation-message flex items-center space-x-2">
        <span class="text-gray-700 text-base">Regenerate subtitles?</span>
    </div>
    <div class="confirmation-actions flex justify-end mt-2">
        <form method="POST" x-data="{ buttonDisabled: false }" x-on:submit="buttonDisabled = true">
            {% csrf_token %}
            <input type="hidden" name="action" value="regenerate_subtitles">
            <button type="submit" class="regenerate-button inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded" x-bind:disabled="buttonDisabled"  x-bind:class="{ 'cursor-not-allowed': buttonDisabled }">
              <span x-show="buttonDisabled">Regenerating...</span>
              <span x-show="!buttonDisabled">Regenerate subtitles</span>
            </button>
        </form>
        <a href="{% url 'asset_detail' asset_uuid=asset_uuid %}" class="inline-block bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-2 rounded ml-2">Cancel</a>
      </div>
  </div>

  <div class="confirmation-group flex flex-col py-4">
      <div class="confirmation-message flex items-center space-x-2">
          <span class="text-gray-700 text-base">Retranscode video with Backup?</span>
      </div>
      <div class="confirmation-actions flex justify-end mt-2">
          <form method="POST" x-data="{ buttonDisabled: false }" x-on:submit="buttonDisabled = true">
              {% csrf_token %}
            <input type="hidden" name="action" value="transcode_video_with_backup">
              <button type="submit" class="transcode-backup-button inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded" name="transcode_video_with_backup" x-bind:disabled="buttonDisabled"  x-bind:class="{ 'cursor-not-allowed': buttonDisabled }">
                <span x-show="buttonDisabled">Transcoding...</span>
                <span x-show="!buttonDisabled">Transcode Video with Backup</span>
              </button>
          </form>
          <a href="{% url 'asset_detail' asset_uuid=asset_uuid %}" class="inline-block bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-2 rounded ml-2">Cancel</a>
      </div>
  </div>


</div>

{% endblock main_content %}
