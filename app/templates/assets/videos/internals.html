{% extends "layouts/base.html" %}
{% load static i18n %}

{% block extra_head %}
  <link rel="stylesheet" href="{% static 'css/prism.css' %}">
{% endblock extra_head%}
{% block html_class %}h-full bg-white{% endblock html_class %}
{% block body_class %}h-full{% endblock body_class %}

{% block content %}
  {% translate "Asset Internals" as title %}
<div x-data="{open: false}" @keydown.window.escape="open = false">

  {% include "includes/sidebar.html" %}
  <!-- Content area -->
  <div class="md:pl-64">
    <div class="flex flex-col max-w-5xl mx-auto sm:px-6 md:px-8">

      <main class="flex-1">
        <div class="relative mx-auto max-w-5xl md:px-8 xl:px-0">
          <div class="pt-10 pb-16">
            {% include "includes/heading.html" %}
            <div class="px-4 sm:px-6 md:px-0">
                <div class="mt-10 divide-y divide-gray-200">
                    <div class="space-y-1">
                      <h3 class="text-lg font-medium leading-6 text-gray-900">Asset ID : {{asset.uuid}}</h3>
                    </div>
                    <div class="mt-6">
                      <dl class="divide-y divide-gray-200">
                        {% include "includes/description_list_item.html" with label="Content Protection Type" value=asset.video.get_content_protection_type_display %}
                        {% include "includes/description_list_item.html" with label="Job Id" value=asset.video.job_id action_link=job_url action_label="Link" %}
                      </dl>
                    </div>
                </div>
            </div>
            <div class="px-4 sm:px-6 md:px-0">
              <div class="mt-10 divide-y divide-gray-200">
                  <div class="space-y-1">
                    <h3 class="text-lg font-medium leading-6 text-gray-900">Subtitle Details</h3>
                  </div>
                  <div class="mt-6">
                    <dl class="divide-y divide-gray-200">
                      {% if asset.video.generate_subtitle %}
                        {% include "includes/description_list_item.html" with label="Auto Generate Subtitle" value="Enabled" %}
                        {% include "includes/description_list_item.html" with label="Subtitle Server ID" value=auto_genrated_subtitle.subtitle_data.server_id %}
                        {% include "includes/description_list_item.html" with label="Server Started Time" value=server_started_time %}
                        {% include "includes/description_list_item.html" with label="Subtitle Generated Time" value=subtitle_generated_time %}
                        {% include "includes/description_list_item.html" with label="Server Deleted Time" value=server_deleted_time %}
                        {% include "includes/description_list_item.html" with label="Audio Duration" value=auto_genrated_subtitle.subtitle_data.audio_duration %}
                        {% include "includes/description_list_item.html" with label="Subtitle Server Volume Size" value=auto_genrated_subtitle.subtitle_data.volume_size|stringformat:"d"|add:" GB" %}
                        {% include "includes/description_list_item.html" with label="Subtitle Generation Cost (USD)" value=auto_genrated_subtitle.subtitle_data.subtitle_generation_cost_dollar %}
                        {% include "includes/description_list_item.html" with label="Subtitle Server Type Cost per Hour (USD)" value=auto_genrated_subtitle.subtitle_data.server_type_cost_per_hour_dollar %}
                        {% include "includes/description_list_item.html" with label="Volume Cost per GB per Hour (USD)" value=auto_genrated_subtitle.subtitle_data.volume_cost_per_gb_per_hour_dollar %}
                      {% else %}
                        {% include "includes/description_list_item.html" with label="Auto Generate Subtitle" value="Disabled" %}
                      {% endif %}
                    </dl>
                  </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</div>

{% endblock content %}
