
{% include "utils/alpine_select2.html" %}

<script type="application/javascript" src="https://releases.transloadit.com/uppy/v3.3.1/uppy.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/uuid/8.3.2/uuid.min.js"></script>
<script>
  const limitedAccessEnabled = {% if organization.limited_access_enabled %}true{% else %}false{% endif %};

  let authToken = null;
  window.addEventListener('message', function(event) {
    authToken = event.data.authToken;
  });

  function getAuthHeader() {
    return {
      'Authorization': `Token ${authToken}`,
    }
  }


  class UppyFileUploader {
    initializeWhenReady() {
      return new Promise((resolve) => {
        const checkTokenAndInitialize = () => {
          if (authToken) {
            console.log("Initializing uploader with token.");
            this.initialize();
            resolve(this);
          } else {
            console.log("Token not available, waiting...");
            setTimeout(checkTokenAndInitialize, 1000);
          }
        };
        checkTokenAndInitialize();
      });
    }

    initialize() {
      this.uppy = new Uppy.Uppy({
        autoProceed: true,
        allowMultipleUploadBatches: false,
        restrictions: {
          maxNumberOfFiles: null,
          allowedFileTypes: [".mp4",".avi", ".m4v", ".mov", ".mkv"]
        },
      })

      this.uppy.use(Uppy.AwsS3Multipart, {
        companionUrl: '/api/v1/{{ organization.uuid }}/',
        companionHeaders: getAuthHeader(),
      })
    }

    on(event, operation) {
      this.uppy.on(event, operation)
    }

    addFile(attachment) {
      return this.uppy.addFile({
        data: attachment.file,
        type: attachment.file.type,
        name: attachment.name,
        meta: {id: attachment.id},
      })
    }

    addFiles(files) {
      const filesToAdd = files.map(attachment => {
        return {
          data: attachment.file,
          type: attachment.file.type,
          name: attachment.name,
          meta: {id: attachment.id, asset_id: attachment.asset_id},
        }
      })
      return this.uppy.addFiles(filesToAdd)
    }

    reset() {
      console.log("RESET")
      this.uppy.getFiles().forEach(file => uppy.removeFile(file.id));
    }
  }

  function app() {
    return {
      step: {% if disable_folder_selection %} 1 {% else %} 0 {% endif %},
      availableSteps: [
        {% if not disable_folder_selection %}
          {step: 0, title: "Select Folder"},
        {% endif %}

        {step: 1, title: "Select Files"},

        {% if not hide_video_settings %}
          {step: 2, title: "Video Settings"},
        {% endif %}

        {% if not hide_transcode_settings %}
          {step: 3, title: "Transcode Settings"},
        {% endif %}

        {step: 4, title: "Preview"},
      ],
      assets: [],
      isSelectAllChecked: false,
      resolutions: {% if hide_transcode_settings %} {{ enabled_resolutions|safe }} {% else %} [] {% endif %},
      contentProtectionType: {% if hide_transcode_settings %} "drm" {% else %} "disabled" {% endif %},
      generateSubtitle: false,
      error: null,
      uppy: null,
      showLoading: false,
      uploadStarted: false,
      isAllVideosUploaded: false,
      isSelectAllCodecChecked: false,
      codecs: [],
      ... foldersData(),

      humanizeBytes: function(numberOfBytes) {
        // Approximate to the closest prefixed unit
        const units = [
          "B",
          "KiB",
          "MiB",
          "GiB",
          "TiB",
          "PiB",
          "EiB",
          "ZiB",
          "YiB",
        ];
        const exponent = Math.min(
          Math.floor(Math.log(numberOfBytes) / Math.log(1024)),
          units.length - 1
        );
        const approx = numberOfBytes / 1024 ** exponent;
        const output =
          exponent === 0
            ? `${numberOfBytes} bytes`
            : `${approx.toFixed(3)} ${
                units[exponent]
              } (${numberOfBytes} bytes)`;

        return output;
      },

      async initializeUppy() {
        this.uppy = await new UppyFileUploader().initializeWhenReady();
        this.uppy.on('upload-progress', this.uploadProgress.bind(this));
        this.uppy.on('upload-success', this.uploadSuccess.bind(this));
        this.uppy.on('upload-error', this.uploadError.bind(this));

        {% if not disable_folder_selection %}
          this.initializeFolders();
        {% endif %}
      },

      uploadProgress(file, progress) {
        let percentage = progress.bytesUploaded / progress.bytesTotal * 100
        const asset = this.assets.find(asset => asset.asset_id === file.meta.asset_id)
        asset.progress = Math.round(percentage)
      },

      uploadSuccess(file, response) {
        this.postMessageToIframe("fileUploaded", { name: file.meta.name, assetId: file.meta.asset_id })

        fetch(`/api/v1/{{ organization.uuid }}/assets/${file.meta.asset_id}/start_transcoding/`, {
          method: 'POST',
          headers: {
            ...getAuthHeader(),
            'Content-Type': 'application/json',
            "X-CSRFToken": "{{ csrf_token }}"
          }
        })
          .then((response) => response.json())
          .then(data => {
            const asset = this.assets.find(asset => asset.asset_id === data.id)
            asset.status = data.video.status
            this.isAllVideosUploaded = this.assets.filter(asset => asset.status !== "Queued").length === 0
          })
          .catch(error => {
            this.error = `Error in starting transcoding for the video ${file.meta.name}`
          })
          window.onbeforeunload = null
        if (this.generateSubtitle) {
            this.generateSubtitles(file);
          }
      },

      postMessageToIframe(eventName, data) {
        window.parent.postMessage({ event: eventName, data: data}, '*');
      },

      generateSubtitles(file) {
        fetch(`/api/v1/{{ organization.uuid }}/assets/${file.meta.asset_id}/generate_subtitle/`, {
          method: 'POST',
          headers: {
            ...getAuthHeader(),
            'Content-Type': 'application/json',
            "X-CSRFToken": "{{ csrf_token }}"
          }
        })
          .catch(error => {
            console.error(`Error in generating subtitle for the video ${file.meta.name}: ${error.message}`);
          });
      },

      uploadError(file, error, response) {
        const asset = this.assets.find(asset => asset.asset_id === file.meta.asset_id);
        asset.status = "Error";

        fetch(`/api/v1/{{ organization.uuid }}/assets/${file.meta.asset_id}/update_video_status/`, {
          method: 'POST',
          body: JSON.stringify({ status: "error" }),
          headers: {
            ...getAuthHeader(),
            'Content-Type': 'application/json',
            "X-CSRFToken": "{{ csrf_token }}"
          }
        })
      },

      selectFile: function(evt) {
        evt.preventDefault();
        const files = evt.dataTransfer ? [...evt.dataTransfer.files] : [...evt.target.files]

        if (files.length === 0) return

        const validFiles = this.filterValidFiles(files)
        const objects = validFiles.map(file => {
          return {
            id: (crypto.randomUUID ? crypto.randomUUID() : uuidv4()),
            name: file.name,
            file: file,
            isSelected: false,
            progress: 0,
            status: "Uploading"
          }
        })
        this.assets = this.assets.concat([...objects])
      },

      filterValidFiles: function(files) {
        const validFileTypes = ["video/mp4", "video/avi", "video/quicktime", "video/x-matroska"];
        const validFiles = [];

        for (const file of files) {
          try {
            if (validFileTypes.includes(file.type)) {
              if (limitedAccessEnabled) {
                this.checkFileSize(file);
                this.checkVideoDuration(file);
              }
              validFiles.push(file);
            }
          } catch (error) {
            this.error = error.message;
          }
        }
        return validFiles;
      },

      checkFileSize: function(file) {
        const maxSize = 50000000;
        if (file.size > maxSize) {
          throw new Error(`File size exceeds the allowed limit. Maximum allowed size is 50 MB. To update your plan, <NAME_EMAIL>.`);
          }
        },


      checkVideoDuration: function(file) {
        this.getVideoDuration(file)
          .then(duration => {
            const videoDuration = duration;
            const maxDuration = 600; // 10 minutes in seconds
            if (videoDuration > maxDuration) {
              this.selectAll()
              this.removeSelected()
              throw new Error(
                `Video duration exceeds the allowed limit. Maximum allowed duration is 10 minutes. To update your plan, <NAME_EMAIL>.`
              );
            }
          })
          .catch(error => {
            this.error = error.message;
          });
        },

      getVideoDuration: function(file) {
        return new Promise((resolve, reject) => {
          const video = document.createElement("video");
          video.preload = "metadata";

          video.onloadedmetadata = function() {
            window.URL.revokeObjectURL(video.src);
            const duration = video.duration;
            resolve(duration);
          };

          video.onerror = function() {
            reject(new Error("Unable to get video duration."));
          };

          video.src = URL.createObjectURL(file);
        });
      },

      removeFile: function(id) {
        this.$refs.assets.value = null;
        this.assets = this.assets.filter((asset) => asset.id !== id);
      },

      selectAll() {
        this.assets = this.assets.map(asset => {
          asset.isSelected = true
          return asset
        })
      },

      unSelectAll() {
        this.assets = this.assets.map(asset => {
          asset.isSelected = false;
          return asset
        })
      },

      removeSelected() {
        this.assets = this.assets.filter((asset) => !asset.isSelected);
        this.isSelectAllChecked = false;
      },

      selectAllResolutions() {
        this.resolutions = {{ enabled_resolutions|safe }};


      },

      unSelectAllResolutions() {
        this.resolutions = [];
      },

      selectAllCodecs() {
      this.codecs.push("h264", "h265");
      },

      unSelectAllCodecs() {
          this.codecs = ["h264"];
      },

      preventPageNavigation(){
        window.onbeforeunload = function(event) {
          event.returnValue = "An upload is in progress, are you sure you want to leave this page?";
        };
      },

      handleNextButtonClick() {
        if (this.step === 4) {
          this.preventPageNavigation()
          this.createAssetsAndStartUpload()
        } else if (this.step === 3) {
          if (!this.resolutions.length) {
            this.error = "Please select atleast one resolution"
            return;
          }
          this.error = null
          this.gotoNextStep()
        } else if(this.step === 0 || this.step === 1 || this.step === 2) {
          this.gotoNextStep()
        }
      },

      createAssetsAndStartUpload() {
        const data = this.assets.map(asset => {
          return {
            title: asset.name,
            resolutions: this.resolutions,
            content_protection_type: this.contentProtectionType,
            generate_subtitle: this.generateSubtitle,
            folder: this.selectedFolder.uuid,
            video_codecs: this.codecs,
          }
        })
        this.showLoading = true
        fetch(`/api/v1/{{ organization.uuid }}/assets/bulk_create_videos/`, {
          method: 'POST',
          body: JSON.stringify(data),
          headers: {
            ...getAuthHeader(),
            'Content-Type': 'application/json',
            "X-CSRFToken": "{{ csrf_token }}"
          }
        })
          .then((response) => {
              if (response.status === 201) {
                return response.json();
              } else if (response.status === 400){
                  return response.json().then((data) => {
                  throw new Error(data[0]["non_field_errors"][0]);
              });
              }
            })
          .then(data => {
            this.showLoading = false
            this.uploadStarted = true
            this.assets.forEach((asset, index) => {
              asset.asset_id = data[index].id
            })
            this.uppy.addFiles(this.assets)
          })
          .catch(error => {
            this.showLoading = false
            this.uploadStarted = false
            this.error = error.message || "Error in creating assets. Please try again"
          })
      },

      nextStepExists() {
        const currentStepIndex = this.availableSteps.findIndex(s => s.step === this.step);
        return (currentStepIndex >= 0 && currentStepIndex < this.availableSteps.length - 1);
      },

      gotoNextStep() {
        const currentStepIndex = this.availableSteps.findIndex(s => s.step === this.step);

        if (currentStepIndex >= 0 && currentStepIndex < this.availableSteps.length - 1) {
          this.step = this.availableSteps[currentStepIndex + 1].step;
        }
      },

      previousStepExists() {
        const currentStepIndex = this.availableSteps.findIndex(s => s.step === this.step);
        return currentStepIndex > 0;
      },

      goToPreviousStep() {
        const currentStepIndex = this.availableSteps.findIndex(s => s.step === this.step);

        if (currentStepIndex > 0) {
          this.step = this.availableSteps[currentStepIndex - 1].step;
        }
      },

      resetUploader() {
        this.assets = []
        this.error = null
        this.uploadStarted = false
        this.isAllVideosUploaded = false
        this.initializeUppy();
        this.$nextTick(() => {
          this.step = this.availableSteps[0]["step"];
        });
      },

      shouldShowNextButton() {
        if (this.uploadStarted) return false;
        if (this.step == 1 || this.step == 4) return this.assets.length > 0;
        return this.nextStepExists();
      },

      getNextButtonText() {
        if (this.step==4) return 'Upload';
        else if (this.step == 0 && !this.hasSelectedAFolder()) return 'Upload to root'
        return 'Next'
      }
    }
  }


  function foldersData() {
    return {
      scrolledbottom: false,
      inputSearch: false,
      selectedFolder: {},
      breadcrumbFoldersUUIDStack: [],
      folders: {},

      initializeFolders: function() {
        this.folders = { ...select2('/api/v1/{{ organization.uuid }}/assets/folders/', false), extraHeaders: getAuthHeader(), search_param: "q"};
        this.goToFolder(null)
      },

      hasFolders() {
        let items = (this.inputSearch ? this.folders.items : this.folders.allFetchedItems);
        if (items) return items.length > 0;
        return false;
      },

      hasSelectedAFolder() {
        return this.selectedFolder && this.selectedFolder.uuid;
      },

      isFolderSelected(folder) {
        return this.selectedFolder && this.selectedFolder.uuid == folder.uuid;
      },

      onFolderSelect(selectedFolder, goInsideFolder=false) {
        if (this.isFolderSelected(selectedFolder)) return this.onFolderUnselect(selectedFolder);

        this.selectedFolder = selectedFolder;
        if (goInsideFolder) {
          this.breadcrumbFoldersUUIDStack.push(selectedFolder.uuid);
          this.goToFolder(selectedFolder.uuid)
        }
      },

      onFolderUnselect(selectedFolder) {
        this.selectedFolder = {};
      },

      goToFolder(uuid) {
        this.folders.allFetchedItems = [];
        this.folders.items = [];
        if (uuid) {
          this.folders.fetchItems({"parent": uuid});
        }
        else {
          this.folders.fetchItems({"has_parent": false});
        }
      },

      goToPreviousFolder() {
        this.breadcrumbFoldersUUIDStack.pop();
        this.goToFolder(this.breadcrumbFoldersUUIDStack.slice(-1)[0])
      },

      fetchFoldersNextPage(url) {
        const urlObj = new URL(url);
        this.folders.fetchItems(Object.fromEntries(urlObj.searchParams));
      },
    }
  }
</script>
