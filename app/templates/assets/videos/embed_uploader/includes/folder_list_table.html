{% load i18n %}

<nav x-cloak x-show="breadcrumbFoldersUUIDStack.length > 0" aria-label="Back" class="mt-4">
  <a href="#" @click="goToPreviousFolder" class="flex items-center text-sm font-medium text-gray-500 hover:text-gray-700">
    <svg class="-ml-1 mr-1 h-5 w-5 flex-shrink-0 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
      <path fill-rule="evenodd" d="M11.78 5.22a.75.75 0 0 1 0 1.06L8.06 10l3.72 3.72a.75.75 0 1 1-1.06 1.06l-4.25-4.25a.75.75 0 0 1 0-1.06l4.25-4.25a.75.75 0 0 1 1.06 0Z" clip-rule="evenodd" />
    </svg>
    Back
  </a>
</nav>

<div class="mt-4 flex flex-col"
  x-init="$watch('scrolledbottom', (value) => { if (value && folders.nextPage) fetchFoldersNextPage(folders.nextPage) })">
  <div class="-my-2 -mx-4 overflow-visible sm:-mx-6 lg:-mx-8">
    <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
      <div class="shadow ring-1 ring-black ring-opacity-5 md:rounded-lg"
        @scroll="scrolledbottom = ($el.scrollTop + $el.clientHeight >= $el.scrollHeight)"
        style="max-height: 400px; overflow-y: auto">
        <table class="min-w-full divide-y divide-gray-300">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="relative w-12 px-6 sm:w-16 sm:px-8 sticky top-0 z-10 border-b border-gray-300 bg-gray-50 bg-opacity-75 py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 backdrop-blur backdrop-filter sm:pl-6 lg:pl-8">
              </th>
              <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                Name</th>
              <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">ID</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 bg-white">
            <template hidden x-if="!hasFolders()">
              <tr>
                <td x-cloak x-show="!uppy" colspan="3" class="px-6 py-4 text-center text-sm text-gray-500">
                  Initializing uploader...
                </td>
                <td x-cloak x-show="uppy && showLoading" colspan="3" class="px-6 py-4 text-center text-sm text-gray-500">
                  Fetching folders...
                </td>
                <td x-cloak x-show="uppy && !showLoading" colspan="3" class="px-6 py-4 text-center text-sm text-gray-500">
                  No folders found.
                </td>
              </tr>
            </template>

            <template hidden x-for="folder in (inputSearch ? folders.items : folders.allFetchedItems)">
              <tr>
                <td class="relative px-7 sm:w-12 sm:px-6">
                  <!-- Selected row marker, only show when row is selected. -->
                  <div x-cloak x-show="isFolderSelected(folder)" class="absolute inset-y-0 left-0 w-0.5 bg-indigo-600"></div>
                  <input @click="onFolderSelect(folder)" :checked="isFolderSelected(folder)" type="checkbox" class="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600">
                </td>
                <td class="whitespace-nowrap pl-4 sm:pl-6 pr-3 py-4 text-sm font-medium text-gray-900">
                  <a href="#" @click="onFolderSelect(folder, true)">
                    <div class="flex space-x-3 items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-8 h-8 text-yellow-300">
                        <path
                          d="M19.5 21a3 3 0 003-3v-4.5a3 3 0 00-3-3h-15a3 3 0 00-3 3V18a3 3 0 003 3h15zM1.5 10.146V6a3 3 0 013-3h5.379a2.25 2.25 0 011.59.659l2.122 2.121c.14.141.331.22.53.22H19.5a3 3 0 013 3v1.146A4.483 4.483 0 0019.5 9h-15a4.483 4.483 0 00-3 1.146z" />
                      </svg>

                      <div class="flex space-x-2">
                        <span class="truncate" x-title="folder.title" x-text="folder.title"></span>
                      </div>
                    </div>
                  </a>
                </td>
                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500" x-text="folder.uuid"></td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
