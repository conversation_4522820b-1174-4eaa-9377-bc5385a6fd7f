<div class="mt-8" x-cloak x-show="step==0">
  <div x-cloak x-show="error" class="rounded-md bg-red-50 p-4 mt-6">
    <div class="flex">
      <div class="flex-shrink-0">
        <!-- Heroicon name: mini/x-circle -->
        <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium text-red-800" x-text="error"></h3>
      </div>
    </div>
  </div>

  <div class="-mt-4 flex flex-wrap items-center justify-between sm:flex-nowrap">
    <div class="mt-4">
      <h1 class="text-xl font-semibold text-gray-900">Folders</h1>
      <p class="mt-1 text-sm text-gray-500">Select a folder where you want to upload the videos.</p>
    </div>
    <div class="mt-4 flex-shrink-0">
      <input type="text" name="name" id="name"
        class="rounded block w-full border-0 border-transparent focus:ring-0 bg-gray-100 text-sm"
        placeholder="Search for folders"
        @input.debounce="folders.fetchItems({q: $event.target.value}); inputSearch = $event.target.value.trim() !== ''">
    </div>
  </div>

  <div>
    {% include "./folder_list_table.html" %}
    <p x-cloak x-show="hasSelectedAFolder()" class="mt-4 text-sm text-gray-500">Selected folder: <span x-text="selectedFolder.title"></span></p>
  </div>
</div>
