<nav aria-label="Progress" class="mt-6">
  <ol role="list" class="space-y-4 md:flex md:space-y-0">
    <template x-for="(stepTab, index) in availableSteps" :key="index">
      <li class="md:flex-1" :style="index !== 0 ? 'margin-left: 2rem !important;' : ''">
        <a href="#"
           :class="{'border-blue-700 hover:border-blue-800': step == stepTab.step, 'hover:border-gray-300': stepTab.step}"
           class="group flex flex-col border-l-4 py-2 pl-4 md:border-l-0 md:border-t-4 md:pl-0 md:pt-4 md:pb-0">
          <span class="text-sm font-medium"
                :class="{'text-blue-700 group-hover:text-blue-800': step == stepTab.step, 'text-gray-500 group-hover:text-gray-700': step != stepTab.step}">
            Step
            <span x-text="index + 1"></span>
          </span>
          <span class="text-sm font-medium" x-text="stepTab.title"></span>
        </a>
      </li>
    </template>
  </ol>
</nav>
