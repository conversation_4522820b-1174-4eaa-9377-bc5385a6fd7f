{% extends "layouts/base.html" %}
{% load i18n %}

{% block html_class %}h-full bg-white{% endblock html_class %}
{% block body_class %}h-full{% endblock body_class %}

{% block content %}
  <div>
      <div>
        <div class="mx-auto flex flex-col max-w-5xl 2xl:max-w-screen-2xl">
          <main class="py-5 sm:py-10 relative">
            <div class="mx-auto max-w-7xl 2xl:max-w-screen-2xl px-4 sm:px-6 lg:px-8">
              {% translate "Upload Video" as title %}
              {% include "assets/includes/heading.html" %}

              <div class="" x-data="app" x-init="initializeUppy">
                {% include "./includes/upload_progress_nav.html" %}
                {% include "./includes/select_folder_step.html" %}
                <div x-cloak x-show="step==1">
                  {% include "../includes/selected_files.html" %}
                </div>
                {% include "../includes/video_settings.html" %}
                {% include "../includes/transcoding_options.html" %}
                {% include "../includes/upload_files_preview.html" %}
                {% include "./includes/prev_next.html" %}
              </div>
            </div>
          </main>
        </div>
    </div>
  </div>
{% endblock content %}

{% block extra_body %}
  {% include "./includes/scripts.html" %}
{% endblock extra_body %}
