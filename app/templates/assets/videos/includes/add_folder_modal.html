<div x-show="showAddFolderModal" x-data="addFolder" class="relative z-50" role="dialog" aria-modal="true">
  <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

  <div class="fixed z-10 inset-0 overflow-y-auto">
    <div class="flex items-end sm:items-start justify-center min-h-full p-4 text-center sm:p-0">
      <div @click.away="showAddFolderModal = false;"
        class="relative bg-white rounded-lg px-4 pt-5 pb-6 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-800" id="modal-title">Add Folder</h3>
        <div class="mt-4">
          <div class="col-span-4 sm:col-span-2 relative">
            <input placeholder="Folder Name"
                  class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-gray-200 focus:border-gray-300 sm:text-sm"
              maxlength="1024" name="app_name" type="text" value="" autocomplete="off" x-model="folderName" @keydown.enter.prevent="submit()" @input="errorMessage = ''">
            <div class="mt-2 text-sm text-red-600" x-text="errorMessage"></div>
          </div>
        </div>

        <div class="mt-6 flex justify-end">
          <button type="button" @click="reset(); showAddFolderModal=false"
            class="inline-flex justify-center rounded-md border shadow-sm px-4 py-2 bg-gray-100 text-base font-medium text-gray-600 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-200 sm:col-start-2 sm:text-sm">Close</button>
          <button type="button" @click="submit()" x-bind:disabled="folderName.length === 0"
            class="ml-2 w-20 inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white text-white shadow-sm hover:bg-blue-700 focus:outline-none sm:col-start-2 sm:text-sm disabled:opacity-75 disabled:cursor-not-allowed disabled:hover:bg-blue-600">
            <svg x-show="loading" class="animate-spin h-5 w-5 text-white"
                 xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span x-show="!loading">Save</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
