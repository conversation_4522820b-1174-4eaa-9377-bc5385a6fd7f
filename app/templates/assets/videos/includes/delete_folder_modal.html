<div x-show="showDeleteFolderModal" class="relative z-50" role="dialog" aria-modal="true">
  <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

  <div class="fixed z-10 inset-0 overflow-y-auto">
    <div class="flex items-end sm:items-start justify-center min-h-full p-4 text-center sm:p-0">
      <div @click.away="showDeleteFolderModal = false;"
        class="relative bg-white rounded-lg px-4 pt-5 pb-6 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-800" id="modal-title">Delete Folder</h3>
        <div class="mt-4">
          <p class="text-sm text-gray-500">
            Are you sure you want to delete this folder. All contents within the folder will also be deleted.
          </p>
        </div>

        <div class="mt-6 flex justify-end">
          <button type="button" @click="showDeleteFolderModal=false"
            class="inline-flex justify-center rounded-md border shadow-sm px-4 py-2 bg-gray-100 text-base font-medium text-gray-600 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-200 sm:col-start-2 sm:text-sm">Cancel</button>
          <form method="post" :action="folderToDelete.deleteUrl" class="inline">
            {% csrf_token %}
            <button type="submit"
              class="ml-2 w-20 inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white text-white shadow-sm hover:bg-red-700 focus:outline-none sm:col-start-2 sm:text-sm">
              Delete
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
