<div class="mt-8" x-show="step==1">
  <div class="px-4 sm:px-0 sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-xl font-semibold text-gray-900">Files</h1>
      <p class="text-sm text-gray-500">All files in this table will be uploaded.</p>
    </div>
    <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex space-x-2">
      <button @click="removeSelected" type="button" class="inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2">Remove</button>
      <button @click="$refs.assets.click();" type="button" class="ml-2 inline-flex items-center justify-center rounded-md border border-transparent bg-blue-700 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 sm:w-auto">Add files</button>
    </div>
  </div>
  <div class="mt-6">
    <div x-cloak x-show="error" class="rounded-md bg-red-50 p-4 mt-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <!-- Heroicon name: mini/x-circle -->
          <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800" x-text="error"></h3>
        </div>
      </div>
    </div>
    <div class="max-h-[300px] overflow-y-auto">
      <div class="shadow-sm ring-1 ring-black ring-opacity-5">
        <table class="min-w-full border-separate" style="border-spacing: 0">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="relative w-12 px-6 sm:w-16 sm:px-8 sticky top-0 z-10 border-b border-gray-300 bg-gray-50 bg-opacity-75 py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 backdrop-blur backdrop-filter sm:pl-6 lg:pl-8">
                <input x-model="isSelectAllChecked" @change="isSelectAllChecked ? selectAll:unSelectAll" type="checkbox" class="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-blue-700 focus:ring-blue-600 sm:left-6">
              </th>
              <th scope="col" class="sticky top-0 z-10 border-b border-gray-300 bg-gray-50 bg-opacity-75 py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 backdrop-blur backdrop-filter sm:pl-6 lg:pl-8">Name</th>
              <th scope="col" class="sticky top-0 z-10 hidden border-b border-gray-300 bg-gray-50 bg-opacity-75 px-3 py-3.5 text-left text-sm font-semibold text-gray-900 backdrop-blur backdrop-filter sm:table-cell">Type</th>
              <th scope="col" class="sticky top-0 z-10 hidden border-b border-gray-300 bg-gray-50 bg-opacity-75 px-3 py-3.5 text-left text-sm font-semibold text-gray-900 backdrop-blur backdrop-filter lg:table-cell">Size</th>
              <th scope="col" class="sticky top-0 z-10 border-b border-gray-300 bg-gray-50 bg-opacity-75 py-3.5 pr-4 pl-3 backdrop-blur backdrop-filter sm:pr-6 lg:pr-8">
                <span class="sr-only">Edit</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white">
            <tr>
              <td colspan="5" class="px-4 sm:px-6 md:px-0 mt-6" x-show="assets.length == 0">
                <div class="flex items-center justify-center w-full">
                  <label for="assets" class="flex flex-col items-center justify-center w-full h-24 border border-t-0 border-gray-300 rounded cursor-pointer bg-gray-50 hover:bg-gray-100 dark:hover:border-gray-500 dark:hover:bg-gray-600 py-2"
                  @dragover.prevent
                  @drop="selectFile">
                    <div class="flex flex-col items-center justify-center">
                      <p class="text-sm text-gray-500 dark:text-gray-400">
                        Drag and drop files you want to upload here, or click <span class="font-semibold">Add files</span>
                      </p>
                    </div>
                    <input id="assets" x-ref="assets" @change="selectFile" type="file" multiple="true" class="hidden" accept="video/mp4,video/x-m4v,video/*" />
                  </label>
                </div>
              </td>
            </tr>
            <template x-for="asset in assets">
              <tr>
                <td class="relative w-12 px-6 sm:w-16 sm:px-8 whitespace-normal border-b border-gray-200 py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8">
                  <input :checked="asset.isSelected" type="checkbox" class="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-blue-700 focus:ring-blue-600 sm:left-6">
                </td>
                <td class="whitespace-normal border-b border-gray-200 py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8 max-w-sm" x-text="asset.name "></td>
                <td class="whitespace-normal border-b border-gray-200 px-3 py-4 text-sm text-gray-500 hidden sm:table-cell" x-text="asset.file.type"></td>
                <td class="whitespace-normal border-b border-gray-200 px-3 py-4 text-sm text-gray-500 hidden lg:table-cell" x-text="humanizeBytes(asset.file.size)"></td>
                <td class="relative whitespace-normal border-b border-gray-200 py-4 pr-4 pl-3 text-right text-sm font-medium sm:pr-6 lg:pr-8">
                  <a @click="removeFile(asset.id)" class="cursor-pointer text-blue-700 hover:text-blue-900">Remove<span class="sr-only" x-text="asset.name "></span></a>
                </td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
