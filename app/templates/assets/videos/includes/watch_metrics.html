{% load humanize %}
{% if asset.views_count%}
<dl class="grid grid-cols-1 mt-6 md:grid-cols-2 xl:grid-cols-1 gap-y-4">

    <div class="flex flex-none w-full gap-x-4">
      <dt class="flex-none">
        <span class="sr-only">Views</span>
        <svg fill="none" stroke="currentColor" stroke-width="1.5" class="w-6 h-6 text-gray-400" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" d="M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0z" />
          <path stroke-linecap="round" stroke-linejoin="round"
            d="M15.91 11.672a.375.375 0 0 1 0 .656l-5.603 3.113a.375.375 0 0 1-.557-.328V8.887c0-.286.307-.466.557-.327l5.603 3.112z" />
        </svg>

      </dt>
      <dd class="text-sm leading-6 text-gray-500">This video has been watched <span class="font-semibold text-gray-700">
        {{ asset.views_count|intword }}
      </span> times</dd>
    </div>


    <div class="flex flex-none w-full gap-x-4">
      <dt class="flex-none">
        <span class="sr-only">Unique Views</span>
        <svg fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-gray-400"><path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0z"/></svg>

      </dt>
      <dd class="text-sm leading-6 text-gray-500"><span class="font-semibold text-gray-700">
        {{ asset.unique_viewers_count|intword }}
      </span> unique viewers have watched this video</dd>
    </div>


    <div class="flex flex-none w-full gap-x-4">
      <dt class="flex-none">
        <span class="sr-only">Total Watch Time</span>
        <svg fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-gray-400"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0z"/></svg>
      </dt>
      <dd class="text-sm leading-6 text-gray-500">Viewers have spent a total of <span class="font-semibold text-gray-700">{{ asset.total_watch_time | humanize_time  }}</span> watching this video</dd>
    </div>


    <div class="flex flex-none w-full gap-x-4">
      <dt class="flex-none">
        <span class="sr-only">Average Watch Time</span>
        <svg fill="none" stroke="currentColor" stroke-width="1.5" class="w-6 h-6 text-gray-400" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round"
            d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
          <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0z" />
        </svg>
      </dt>
      <dd class="text-sm leading-6 text-gray-500">On average, viewers watch this video for <span class="font-semibold text-gray-700">{{ asset.average_watched_time | humanize_time }}</span></dd>
    </div>
  </dl>
{% else %}
  <div class="flex items-center justify-center w-full h-32 text-gray-500">
    <p class="text-xl font-semibold">This video hasn't been viewed yet.</p>
  </div>
{% endif %}
