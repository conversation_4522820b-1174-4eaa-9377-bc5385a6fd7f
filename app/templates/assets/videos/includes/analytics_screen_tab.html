<!-- Header Section -->
<div class="pb-2 px-7 flex gap-x-2">
  <div class="w-full">
    <h3 class="inline-block font-medium text-xs uppercase text-gray-800 dark:text-white">
      Screen Size
    </h3>
  </div>
</div>

<div class="p-5 pt-0">
  <div class="mt-1">
    <div class="grid grid-cols-2 gap-1">
      <div class="flex flex-col items-center text-center py-1">
        <div class="relative mb-1">
          <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
            <circle cx="50" cy="50" r="44" fill="none" stroke="#e5e7eb" stroke-width="8" />
            <circle id="system-progress" cx="50" cy="50" r="44" fill="none" stroke="#3b82f6"
                    stroke-width="8" stroke-dasharray="276"
                    stroke-dashoffset="276"
                    class="transition-all duration-1000 ease-out" stroke-linecap="round" />
          </svg>
          <div class="absolute inset-0 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                 stroke-width="1.5" stroke="currentColor"
                 class="w-6 h-6 text-gray-600 dark:text-gray-400">
              <path stroke-linecap="round" stroke-linejoin="round"
                    d="M9 17.25v1.007a3 3 0 0 1-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0 1 15 18.257V17.25m6-12V15a2.25
                    2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 15V5.25m18 0A2.25 2.25 0 0 0 18.75
                    3H5.25A2.25 2.25 0 0 0 3 5.25m18 0V12a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0
                    0 1 3 12V5.25" />
            </svg>
          </div>
        </div>
        <h4 class="text-xs font-medium text-gray-800 dark:text-white mb-0.5">Desktop</h4>
        <p class="text-xs text-gray-600 dark:text-gray-400">Views: <span id="system-views" class="font-medium">0</span></p>
      </div>

      <div class="flex flex-col items-center text-center py-1">
        <div class="relative mb-1">
          <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
            <circle cx="50" cy="50" r="44" fill="none" stroke="#e5e7eb" stroke-width="8" />
            <circle id="mobile-progress" cx="50" cy="50" r="44" fill="none" stroke="#3b82f6"
                    stroke-width="8" stroke-dasharray="276"
                    stroke-dashoffset="276"
                    class="transition-all duration-1000 ease-out" stroke-linecap="round" />
          </svg>
          <div class="absolute inset-0 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                 stroke-width="1.5" stroke="currentColor"
                 class="w-6 h-6 text-gray-600 dark:text-gray-400">
              <path stroke-linecap="round" stroke-linejoin="round"
                    d="M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25
                    2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3
                    18.75h3" />
            </svg>
          </div>
        </div>
        <h4 class="text-xs font-medium text-gray-800 dark:text-white mb-0.5">Mobile</h4>
        <p class="text-xs text-gray-600 dark:text-gray-400">Views: <span id="mobile-views" class="font-medium">0</span></p>
      </div>
    </div>

    <div class="grid grid-cols-2 gap-1 mt-1">
      <div class="flex flex-col items-center text-center py-1">
        <div class="relative mb-1">
          <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
            <circle cx="50" cy="50" r="44" fill="none" stroke="#e5e7eb" stroke-width="8" />
            <circle id="tablet-progress" cx="50" cy="50" r="44" fill="none" stroke="#3b82f6"
                    stroke-width="8" stroke-dasharray="276"
                    stroke-dashoffset="276"
                    class="transition-all duration-1000 ease-out" stroke-linecap="round" />
          </svg>
          <div class="absolute inset-0 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                 stroke-width="1.5" stroke="currentColor"
                 class="w-6 h-6 text-gray-600 dark:text-gray-400">
              <path stroke-linecap="round" stroke-linejoin="round"
                    d="M10.5 19.5h3m-6.75 2.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-15a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25
                    2.25 0 0 0 4.5 4.5v15a2.25 2.25 0 0 0 2.25 2.25Z" />
            </svg>
          </div>
        </div>
        <h4 class="text-xs font-medium text-gray-800 dark:text-white mb-0.5">Tablet</h4>
        <p class="text-xs text-gray-600 dark:text-gray-400">Views: <span id="tablet-views" class="font-medium">0</span></p>
      </div>

      <div class="flex flex-col items-center text-center py-1">
        <div class="relative mb-1">
          <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
            <circle cx="50" cy="50" r="44" fill="none" stroke="#e5e7eb" stroke-width="8" />
            <circle id="other-progress" cx="50" cy="50" r="44" fill="none" stroke="#3b82f6"
                    stroke-width="8" stroke-dasharray="276"
                    stroke-dashoffset="276"
                    class="transition-all duration-1000 ease-out" stroke-linecap="round" />
          </svg>
          <div class="absolute inset-0 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                 stroke-width="1.5" stroke="currentColor"
                 class="w-6 h-6 text-gray-600 dark:text-gray-400">
              <path stroke-linecap="round" stroke-linejoin="round"
                    d="M6.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM12.75 12a.75.75 0 1 1-1.5 0 .75.75
                    0 0 1 1.5 0ZM18.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z" />
            </svg>
          </div>
        </div>
        <h4 class="text-xs font-medium text-gray-800 dark:text-white mb-0.5">Others</h4>
        <p class="text-xs text-gray-600 dark:text-gray-400">Views: <span id="other-views" class="font-medium">0</span></p>
      </div>
    </div>
  </div>
</div>

<script>
function initScreenAnalytics() {
  const deviceData = {{ analytics_data|safe }};

  const deviceViews = {
    mobile: 0,
    system: 0,
    tablet: 0,
    other: 0
  };

  let totalViews = 0;

  deviceData.forEach(item => {
    switch (item.device) {
      case 0: category = 'mobile'; break;
      case 1: category = 'system'; break;
      case 2: category = 'tablet'; break;
      case 3: category = 'other'; break;
      default: category = 'other';
    }

    deviceViews[category] += item.views;
    totalViews += item.views;
  });

  for (const [key, views] of Object.entries(deviceViews)) {
    const span = document.getElementById(`${key}-views`);
    const circle = document.getElementById(`${key}-progress`);

    const formatted = views >= 1_000_000
      ? (views / 1_000_000).toFixed(1) + 'M'
      : views >= 1_000
        ? (views / 1_000).toFixed(1) + 'k'
        : views.toString();

    if (span) span.textContent = formatted;

    if (circle && totalViews > 0) {
      const percent = views / totalViews;
      const dashoffset = 276 - (276 * percent);
      circle.setAttribute('stroke-dashoffset', dashoffset);
    }
  }
}
</script>
