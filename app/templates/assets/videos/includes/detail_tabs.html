<div class="px-4 xl:col-span-2 sm:px-0 2xl:col-span-3 2xl:row-span-2">
    <div class="sm:hidden">
      <label for="tabs" class="sr-only">Select a tab</label>
      <select id="tabs" name="tabs"
        class="block w-full border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
        @change="active_tab = $event.target.value">
        <option value="thumbnails">Thumbnails</option>
        <option value="embed_link">Embed & Links</option>
        <option value="chapters">Chapters</option>
        <option value="watch_metrics">Watch Metrics</option>
        <option value="analytics">Analytics</option>
      </select>
    </div>
    <div class="hidden sm:block">
      <div class="border-b border-gray-200">
        <nav class="flex -mb-px space-x-8 overflow-x-auto" aria-label="Tabs">
          <button @click.prevent="active_tab = 'thumbnails'"
          class="inline-flex items-center px-1 py-4 text-sm font-medium border-b-2 group"
          :class="active_tab === 'thumbnails' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'">
          <svg fill="none" stroke="currentColor" stroke-width="1.5" class="-ml-0.5 mr-2 h-5 w-5"
            :class="active_tab === 'thumbnails' ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'"
            viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0z" />
          </svg>
          <span>Thumbnails</span>
          </button>
          <button  @click.prevent="active_tab = 'captions'"
          class="inline-flex items-center px-1 py-4 text-sm font-medium border-b-2 group"
          :class="active_tab === 'captions' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'">
          <svg class="-ml-0.5 mr-2 h-5 w-5" :class="active_tab === 'captions' ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 6.878V6a2.25 2.25 0 0 1 2.25-2.25h7.5A2.25 2.25 0 0 1 18 6v.878m-12 0c.235-.083.487-.128.75-.128h10.5c.263 0 .515.045.75.128m-12 0A2.25 2.25 0 0 0 4.5 9v.878m13.5-3A2.25 2.25 0 0 1 19.5 9v.878m0 0a2.246 2.246 0 0 0-.75-.128H5.25c-.263 0-.515.045-.75.128m15 0A2.25 2.25 0 0 1 21 12v6a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 18v-6c0-.98.626-1.813 1.5-2.122" />
          </svg>
          <span>Captions</span>
          </button>
          <button @click.prevent="active_tab = 'chapters'"
            class="inline-flex items-center px-1 py-4 text-sm font-medium border-b-2 group"
            :class="active_tab === 'chapters' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'">

            <svg fill="none" stroke="currentColor" stroke-width="1.5" class="-ml-0.5 mr-2 h-5 w-5"
              :class="active_tab === 'chapters' ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12H12m-8.25 5.25h16.5" />
            </svg>
            <span>Chapters</span>
          </button>
          <button @click.prevent="active_tab = 'embed_link'"
            class="inline-flex items-center px-1 py-4 text-sm font-medium border-b-2 group"
            :class="active_tab === 'embed_link' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'">
            <svg fill="none" stroke="currentColor" stroke-width="1.5" class="-ml-0.5 mr-2 h-5 w-5"
              :class="active_tab === 'embed_link' ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186zm0-12.814a2.25 2.25 0 1 0 3.933-2.185 2.25 2.25 0 0 0-3.933 2.185z" />
            </svg>
            <span>Embed & Links</span>
          </button>
          {% if is_video %}
            <button @click.prevent="active_tab = 'player'"
            class="inline-flex items-center px-1 py-4 text-sm font-medium border-b-2 group"
            :class="active_tab === 'player' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'">
            <svg fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="-ml-0.5 mr-2 h-5 w-5" :class="active_tab === 'player' ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'">
              <path stroke-linecap="round" stroke-linejoin="round" d="M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
              <path stroke-linecap="round" stroke-linejoin="round" d="M15.91 11.672a.375.375 0 0 1 0 .656l-5.603 3.113a.375.375 0 0 1-.557-.328V8.887c0-.286.307-.466.557-.327l5.603 3.112Z"/>
            </svg>
            <span>Player</span>
            </button>
          {% endif %}
          <button @click.prevent="active_tab = 'watch_metrics'"
            class="inline-flex items-center px-1 py-4 text-sm font-medium border-b-2 group"
            :class="active_tab === 'watch_metrics' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'">
            <svg fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="-ml-0.5 mr-2 h-5 w-5"
              :class="active_tab === 'watch_metrics' ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
              <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0z" />
            </svg>
            <span>Watch Metrics</span>
          </button>
          <button @click.prevent="active_tab = 'analytics'"
          class="inline-flex items-center px-1 py-4 text-sm font-medium border-b-2 group"
          :class="active_tab === 'analytics' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'">
          <svg fill="none" stroke="currentColor" stroke-width="1.5" class="-ml-0.5 mr-2 h-5 w-5"
            :class="active_tab === 'analytics' ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'"
            viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M7.5 14.25v2.25m3-4.5v4.5m3-6.75v6.75m3-9v9M6 20.25h12A2.25 2.25 0 0 0 20.25 18V6A2.25 2.25 0 0 0 18 3.75H6A2.25 2.25 0 0 0 3.75 6v12A2.25 2.25 0 0 0 6 20.25z" />
          </svg>
          <span>Analytics</span>
        </button>
        <button @click.prevent="active_tab = 'advance'"
          class="inline-flex items-center px-1 py-4 text-sm font-medium border-b-2 group"
          :class="active_tab === 'advance' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'">
          <svg fill="none" stroke="currentColor" stroke-width="1.5" class="-ml-0.5 mr-2 h-5 w-5"
            :class="active_tab === 'advance' ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'"
            viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.343 3.94c.09-.542.56-.94 1.11-.94h1.093c.55 0 1.02.398 1.11.94l.149.894c.07.424.384.764.78.93.398.164.855.142 1.205-.108l.737-.527a1.125 1.125 0 0 1 1.45.12l.773.774c.39.389.44 1.002.12 1.45l-.527.737c-.25.35-.272.806-.107 1.204.165.397.505.71.93.78l.893.15c.543.09.94.559.94 1.109v1.094c0 .55-.397 1.02-.94 1.11l-.894.149c-.424.07-.764.383-.929.78-.165.398-.143.854.107 1.204l.527.738c.32.447.269 1.06-.12 1.45l-.774.773a1.125 1.125 0 0 1-1.449.12l-.738-.527c-.35-.25-.806-.272-1.203-.107-.398.165-.71.505-.781.929l-.149.894c-.09.542-.56.94-1.11.94h-1.094c-.55 0-1.019-.398-1.11-.94l-.148-.894c-.071-.424-.384-.764-.781-.93-.398-.164-.854-.142-1.204.108l-.738.527c-.447.32-1.06.269-1.45-.12l-.773-.774a1.125 1.125 0 0 1-.12-1.45l.527-.737c.25-.35.272-.806.108-1.204-.165-.397-.506-.71-.93-.78l-.894-.15c-.542-.09-.94-.56-.94-1.109v-1.094c0-.55.398-1.02.94-1.11l.894-.149c.424-.07.765-.383.93-.78.165-.398.143-.854-.108-1.204l-.526-.738a1.125 1.125 0 0 1 .12-1.45l.773-.773a1.125 1.125 0 0 1 1.45-.12l.737.527c.35.25.807.272 1.204.107.397-.165.71-.505.78-.929l.15-.894Z"/><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
          </svg>
          <span>Advance</span>
        </button>
        </nav>
      </div>
    </div>
    <template x-if="active_tab === 'embed_link'">
        {% include "./embed_link.html" %}
    </template>

    <template x-if="active_tab === 'advance'">
      {% include "./advance_settings.html" %}
  </template>
    <template x-if="active_tab === 'watch_metrics'">
      <div>
      {% if asset.organization.show_analytics %}
       {% include "./watch_metrics.html" %}
      {% else %}
       {% include "./watch_metrics_support.html" %}
      {% endif %}
      </div>
    </template>
    <template x-if="active_tab === 'analytics'">
      <div>
      {% if asset.organization.show_analytics %}
       {% include "./analytics.html" %}
      {% else %}
       {% include "./analytics_graph_support.html" %}
      {% endif %}
      </div>
    </template>
    <template x-if="active_tab === 'thumbnails'">
      {% include "./thumbnails.html" %}
    </template>
    <template x-if="active_tab === 'chapters'">
      <div>
        {% include "./chapters.html" %}
      </div>
    </template>

    <template x-if="active_tab === 'player'">
      <div>
        {% include "./player.html" %}
      </div>
    </template>
    <template x-if="active_tab === 'captions'">
    <div>
      {% if active_captions.exists or disabled_captions.exists %}
        {% include "./captions.html" %}
      {% else %}
        {% include "./empty_caption.html" %}
      {% endif %}
      {% include "./captions_form.html" %}
    </div>
    </template>
  </div>
{% block extra_body %}
  {% include "./chart_component.html"  %}
{% endblock %}
