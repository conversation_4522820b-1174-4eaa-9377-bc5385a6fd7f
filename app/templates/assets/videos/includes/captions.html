<div class="max-w-3xl">
  {% if active_captions %}
    <div class="relative" class="mt-2">
      <div class="flex justify-between items-center mt-2">
        <h2 class="text-sm font-semibold leading-7 text-gray-900">Captions</h3>
          <button type="button" @click="showCreateCaptionModel = !showCreateCaptionModel"
            class="text-sm font-semibold leading-6 text-blue-600 hover:text-blue-500"><span aria-hidden="true">+</span>
            Upload Caption</button>
      </div>
      <div class="mt-2 text-sm text-gray-900 sm:col-span-2">
        <ul role="list" class="divide-y divide-gray-100 rounded-md border border-gray-200">
          {% for caption in active_captions %}
            {% include "./captions_list.html" %}
          {% endfor %}
        </ul>
      </div>
    </div>
  {% endif %}
  {% if disabled_captions %}
    <div class="relative" class="mt-2">
      <h2 class="mt-4 text-sm font-semibold leading-6 text-gray-900">Disabled</h3>
        <div class="mt-2 text-sm text-gray-900 sm:col-span-2">
          <ul role="list" class="divide-y divide-gray-100 rounded-md border border-gray-200">
            {% for caption in disabled_captions %}
              {% include "./captions_list.html" %}
            {% endfor %}
          </ul>
        </div>
    </div>
  {% endif %}
</div>