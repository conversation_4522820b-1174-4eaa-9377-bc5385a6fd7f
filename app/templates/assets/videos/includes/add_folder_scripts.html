<script>
  document.addEventListener('alpine:init', () => {
    window.Alpine.data('addFolder', () => ({
      loading: false,
      folderName: '',
      errorMessage: '',
      parent: '{{ folder.uuid }}',

      async submit() {
        this.createFolder()
          .then((folder) => {
            this.redirectToFolderDetail(folder.uuid);
          })
          .catch((error) => {
            this.errorMessage = error.message;
          })
      },

      async createFolder() {
        if (this.loading) return;
        this.loading = true;

        const url = `/api/v1/{{ request.user.current_organization_uuid }}/assets/folders/`;
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}'
          },
          body: JSON.stringify({
            title: this.folderName,
            parent: this.parent
          })
        });
        const json = await response.json();

        if (!response.ok) {
          this.loading = false;
          throw new Error(json.non_field_errors?.[0]);
        }

        return json;
      },

      redirectToFolderDetail(uuid) {
        const url = new URL(window.location.href);
        url.searchParams.delete('page');
        url.searchParams.set('folder', uuid);
        window.history.replaceState(null, '', url.toString());
        location.reload();
      },

      reset() {
        this.folderName = ""
        this.errorMessage = ""
      }
    }));
  });
</script>
