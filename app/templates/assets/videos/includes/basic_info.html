{% load humanize %}
<div class="px-4 sm:px-0">
    <h2 class="text-base font-semibold leading-6 text-gray-900">Basic Info</h2>
    <dl class="grid grid-cols-1 mt-6 md:grid-cols-2 xl:grid-cols-1 gap-y-4">
      <div class="flex flex-none w-full gap-x-4 ">
        <dt class="flex-none">
          <span class="sr-only">Asset ID</span>
          <svg fill="none" stroke="currentColor" stroke-width="1.5" class="w-5 h-6 text-gray-400" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M3.75 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 0 1 3.75 9.375v-4.5zm0 9.75c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5a1.125 1.125 0 0 1-1.125-1.125v-4.5zm9.75-9.75c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 0 1 13.5 9.375v-4.5z" />
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M6.75 6.75h.75v.75h-.75v-.75zm0 9.75h.75v.75h-.75v-.75zm9.75-9.75h.75v.75h-.75v-.75zm-3 6.75h.75v.75h-.75v-.75zm0 6h.75v.75h-.75v-.75zm6-6h.75v.75h-.75v-.75zm0 6h.75v.75h-.75v-.75zm-3-3h.75v.75h-.75v-.75z" />
          </svg>
        </dt>
        <dd class="text-sm font-medium leading-6 text-gray-900">{{ asset.uuid }}</dd>
      </div>
      <div class="flex flex-none w-full gap-x-4">
        <dt class="flex-none">
          <span class="sr-only">Duration</span>
          <svg fill="none" stroke="currentColor" stroke-width="1.5" class="w-5 h-6 text-gray-400" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0z" />
          </svg>
        </dt>
        {% if asset.video.duration %}
        <dd class="text-sm leading-6 text-gray-500">
          {{ asset.video.duration|humanize_time }} ({{ asset.video.duration}})
        </dd>
        {% else %}
        <dd class="text-sm leading-6 text-gray-500">
          Duration not available yet
        </dd>
        {% endif %}
      </div>
      <div class="flex flex-none w-full gap-x-4">
        <dt class="flex-none">
          <span class="sr-only">Upload date</span>
          <svg class="w-5 h-6 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path
              d="M5.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H6a.75.75 0 01-.75-.75V12zM6 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H6zM7.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H8a.75.75 0 01-.75-.75V12zM8 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H8zM9.25 10a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H10a.75.75 0 01-.75-.75V10zM10 11.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V12a.75.75 0 00-.75-.75H10zM9.25 14a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H10a.75.75 0 01-.75-.75V14zM12 9.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V10a.75.75 0 00-.75-.75H12zM11.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H12a.75.75 0 01-.75-.75V12zM12 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H12zM13.25 10a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H14a.75.75 0 01-.75-.75V10zM14 11.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V12a.75.75 0 00-.75-.75H14z">
            </path>
            <path fill-rule="evenodd"
              d="M5.75 2a.75.75 0 01.75.75V4h7V2.75a.75.75 0 011.5 0V4h.25A2.75 2.75 0 0118 6.75v8.5A2.75 2.75 0 0115.25 18H4.75A2.75 2.75 0 012 15.25v-8.5A2.75 2.75 0 014.75 4H5V2.75A.75.75 0 015.75 2zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75z"
              clip-rule="evenodd"></path>
          </svg>
        </dt>
        <dd class="text-sm leading-6 text-gray-500">
          <time id="upload-time" > Uploaded on {{ asset.created|date:'jS F Y' }}</time>
        </dd>
      </div>
      {% if asset.bytes%}
      <div class="flex flex-none w-full gap-x-4">
        <dt class="flex-none">
          <span class="sr-only">Size</span>
          <svg fill="none" stroke="currentColor" stroke-width="1.5" class="w-5 h-6 text-gray-400" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125" />
          </svg>
        </dt>
        <dd class="text-sm leading-6 text-gray-500">This video takes up about <strong>{{ asset.bytes|humanize_bytes }}</strong> of space</dd>
      </div>
      {% endif %}

      {% if is_video %}


      <div class="flex flex-none w-full gap-x-4">
        <dt class="flex-none">
          <span class="sr-only">Size</span>
          <svg fill="none" stroke="currentColor" stroke-width="1.5" class="w-5 h-6 text-gray-400" viewBox="0 0 24 24">
           {% if asset.video.get_content_protection_type_display == "Disabled" %}

           <path stroke-linecap="round" stroke-linejoin="round"
           d="M18 1.5c2.9 0 5.25 2.35 5.25 5.25v3.75a.75.75 0 0 1-1.5 0V6.75a3.75 3.75 0 1 0-7.5 0v3a3 3 0 0 1 3 3v6.75a3 3 0 0 1-3 3H3.75a3 3 0 0 1-3-3v-6.75a3 3 0 0 1 3-3h9v-3c0-2.9 2.35-5.25 5.25-5.25Z" />
          {% else %}
          <path stroke-linecap="round" stroke-linejoin="round"
          d="M12 1.5a5.25 5.25 0 0 0-5.25 5.25v3a3 3 0 0 0-3 3v6.75a3 3 0 0 0 3 3h10.5a3 3 0 0 0 3-3v-6.75a3 3 0 0 0-3-3v-3c0-2.9-2.35-5.25-5.25-5.25Zm3.75 8.25v-3a3.75 3.75 0 1 0-7.5 0v3h7.5Z" />
          {% endif %}



          </svg>
        </dt>
        <dd class="text-sm leading-6 text-gray-500">{{asset.video.get_content_protection_type_display}}</dd>
      </div>

      {% endif %}


      {% if asset.organization.show_analytics and asset.views_count%}
      <div class="flex flex-none w-full gap-x-4">
        <dt class="flex-none">
          <span class="sr-only">Views</span>
          <svg fill="none" stroke="currentColor" stroke-width="1.5" class="w-6 h-6 text-gray-400" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0z" />
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M15.91 11.672a.375.375 0 0 1 0 .656l-5.603 3.113a.375.375 0 0 1-.557-.328V8.887c0-.286.307-.466.557-.327l5.603 3.112z" />
          </svg>

        </dt>
        <dd class="text-sm leading-6 text-gray-500">
          {{ asset.views_count|intword }} view{{ asset.views_count|pluralize }}
        </dd>
      </div>
      <div class="flex flex-none w-full gap-x-4">
        <dt class="flex-none">
          <span class="sr-only">Average Watch Time</span>
          <svg fill="none" stroke="currentColor" stroke-width="1.5" class="w-6 h-6 text-gray-400" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0z" />
          </svg>
        </dt>
        <dd class="text-sm leading-6 text-gray-500">Viewers typically watch for an average of <strong>{{ asset.average_watched_time|humanize_time }}</strong>
        </dd>
      </div>
      {% elif asset.organization.show_analytics  %}
      <div class="flex flex-none w-full gap-x-4">
        <dt class="flex-none">
          <span class="sr-only">Views</span>
          <svg fill="none" stroke="currentColor" stroke-width="1.5" class="w-6 h-6 text-gray-400" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0z" />
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M15.91 11.672a.375.375 0 0 1 0 .656l-5.603 3.113a.375.375 0 0 1-.557-.328V8.887c0-.286.307-.466.557-.327l5.603 3.112z" />
          </svg>

        </dt>
        <dd class="text-sm leading-6 text-gray-500">
          No views yet
        </dd>
      </div>
      {% endif %}
    </dl>
  </div>
