{% if asset.views_count%}
<div x-data="chartComponent()">
    <div class="flex items-center justify-end mt-4">
      <button type="button"
        class="relative inline-flex items-center justify-center flex-shrink-0 w-10 h-5 rounded-full cursor-pointer group focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2"
        role="switch" :aria-checked="showSeparate.toString()" @click="toggleCharts">
        <span class="sr-only">Use setting</span>
        <span aria-hidden="true" class="absolute w-full h-full bg-white rounded-md pointer-events-none"></span>
        <!-- Enabled: "bg-blue-600", Not Enabled: "bg-gray-200" -->
        <span aria-hidden="true"
          class="absolute h-4 mx-auto transition-colors duration-200 ease-in-out bg-gray-200 rounded-full pointer-events-none w-9"
          :class="{ 'bg-blue-600': showSeparate }"></span>
        <!-- Enabled: "translate-x-5", Not Enabled: "translate-x-0" -->
        <span aria-hidden="true"
          class="absolute left-0 inline-block w-5 h-5 transition-transform duration-200 ease-in-out transform translate-x-0 bg-white border border-gray-200 rounded-full shadow pointer-events-none ring-0"
          :class="{ 'translate-x-5': showSeparate }"></span>
      </button>
      <span class="ml-3 text-sm" id="annual-billing-label">
        <span class="font-medium text-gray-900">Split Charts</span>
      </span>
    </div>

    <div class="flex justify-center">
      <div id="views_chart" :hidden="!showSeparate" class="w-80 h-52 sm:w-[600px] sm:h-[400px] md:w-[700px] sm:w-[600px] sm:h-[400px] md:h-[400px] 2xl:w-[1000px] 2xl:h-[500px] mt-4"></div>
    </div>
    <div class="flex justify-center">
      <div id="minutes_watched_chart" :hidden="!showSeparate" class="w-80 h-52 sm:w-[600px] sm:h-[400px] md:w-[700px] sm:w-[600px] sm:h-[400px] md:h-[400px] 2xl:w-[1000px] 2xl:h-[500px] mt-4"></div>
    </div>
    <div class="flex justify-center">
      <div id="chart" :hidden="showSeparate" class="w-80 h-52 sm:w-[600px] sm:h-[400px] md:w-[700px] sm:w-[600px] sm:h-[400px] md:h-[400px] 2xl:w-[1000px] 2xl:h-[500px] mt-4"></div>
    </div>

    <div class="flex flex-wrap gap-6 px-4 mb-6 mt-6">
      {% include "./analytics_location.html" %}
      {% include "./analytics_devices.html" %}
    </div>
  </div>
  {% else %}
  <div class="flex items-center justify-center w-full h-32 text-gray-500">
    <p class="text-xl font-semibold">No view data available at the moment</p>
  </div>
  {% endif %}
