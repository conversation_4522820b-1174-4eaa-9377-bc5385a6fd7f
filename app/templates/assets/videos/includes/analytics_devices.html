<div class="flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700 flex-1 min-h-96" x-data="{
  activeTab: 'browser',
  animated: false,
  switchTab(tab) {
    this.animated = false;
    this.activeTab = tab;
    setTimeout(() => {
      this.animated = true;
    }, 50);
  }
}">
  <div class="p-5 pb-4 flex justify-between items-center">
    <div>
      <h2 class="inline-block font-semibold text-lg text-gray-800 dark:text-neutral-200">
        Devices
      </h2>
    </div>

    <div class="flex items-center">
      <nav class="flex space-x-1" aria-label="Tabs" role="tablist">
        <button type="button" @click="switchTab('browser')" :class="activeTab === 'browser' ? 'border-blue-600 text-blue-600 dark:border-blue-500 dark:text-blue-500' : 'border-transparent text-gray-500 dark:text-neutral-400'" class="py-1 px-2 inline-flex items-center gap-x-2 border-b-2 text-xs font-medium" role="tab">
          Browser
        </button>
        <button type="button" @click="switchTab('os')" :class="activeTab === 'os' ? 'border-blue-600 text-blue-600 dark:border-blue-500 dark:text-blue-500' : 'border-transparent text-gray-500 dark:text-neutral-400'" class="py-1 px-2 inline-flex items-center gap-x-2 border-b-2 text-xs font-medium" role="tab">
          OS
        </button>
        <button type="button" @click="switchTab('screen')" :class="activeTab === 'screen' ? 'border-blue-600 text-blue-600 dark:border-blue-500 dark:text-blue-500' : 'border-transparent text-gray-500 dark:text-neutral-400'" class="py-1 px-2 inline-flex items-center gap-x-2 border-b-2 text-xs font-medium" role="tab">
          Screen
        </button>
      </nav>
    </div>
  </div>

  <div class="mt-0 min-h-96">
    <div x-show="activeTab === 'screen'" role="tabpanel" class="min-h-96" x-init="initScreenAnalytics()">
      {% include "./analytics_screen_tab.html" %}
    </div>

    <div x-show="activeTab === 'os'" role="tabpanel" class="min-h-96" x-init="initOSAnalytics()">
      {% include "./analytics_os_tab.html" %}
    </div>



    <div x-show="activeTab === 'browser'" role="tabpanel" class="min-h-96" x-init="initBrowserAnalytics()">
      {% include "./analytics_browser_tab.html" %}
    </div>
  </div>
</div>
