
<div class="pb-2 px-7 flex gap-x-2">
  <div class="w-full">
    <h3 class="inline-block font-medium text-xs uppercase text-gray-800 dark:text-white">
      Country
    </h3>
  </div>
  <div class="w-20 text-end">
    <h3 class="inline-block font-medium text-xs uppercase text-gray-800 dark:text-white">
      Views
    </h3>
  </div>
</div>

<div class="p-5 pt-0">
  <ul id="countries-list" class="space-y-2">
  </ul>
</div>

<script>
const countryFlags = {
  'United States': '🇺🇸',
  'India': '🇮🇳',
  'Canada': '🇨🇦',
  'China': '🇨🇳',
  'United Kingdom': '🇬🇧',
  'Brazil': '🇧🇷',
  'Indonesia': '🇮🇩',
  'Germany': '🇩🇪',
  'France': '🇫🇷',
  'Japan': '🇯🇵',
  'Australia': '🇦🇺',
  'South Korea': '🇰🇷',
  'Netherlands': '🇳🇱',
  'Spain': '🇪🇸',
  'Italy': '🇮🇹',
  'Singapore': '🇸🇬',
  'Mexico': '🇲🇽',
  'Argentina': '🇦🇷',
  'Colombia': '🇨🇴',
  'Chile': '🇨🇱',
  'Peru': '🇵🇪',
  'South Africa': '🇿🇦',
  'Nigeria': '🇳🇬',
  'Egypt': '🇪🇬',
  'Kenya': '🇰🇪',
  'Morocco': '🇲🇦',
};

function formatViewCount(count) {
  if (count >= 1000000) {
    return (count / 1000000).toFixed(1) + 'M';
  } else if (count >= 1000) {
    return (count / 1000).toFixed(1) + 'k';
  } else {
    return count.toString();
  }
}

function getCountryFlag(countryName) {
  return countryFlags[countryName] || '🌍';
}

function renderCountriesList() {
  const rawData = {{ analytics_data|safe }};

  const countriesList = document.getElementById('countries-list');

  if (!rawData || rawData.length === 0) {
    countriesList.innerHTML = `
      <li class="flex justify-center items-center py-8">
        <div class="text-center">
          <p class="text-sm text-gray-500 dark:text-neutral-400">No location data available</p>
          <p class="text-xs text-gray-400 dark:text-neutral-500 mt-1">Views will appear here once data is collected</p>
        </div>
      </li>
    `;
    return;
  }

  // 🧠 Group valid countries and sum up null/empty locations
  const grouped = {};
  let nullLocationViews = 0;

  for (const item of rawData) {
    const loc = item.location;
    if (!loc || loc.trim() === '' || loc === 'null') {
      nullLocationViews += item.views;
    } else {
      grouped[loc] = (grouped[loc] || 0) + item.views;
    }
  }

  const processedData = Object.entries(grouped)
    .map(([location, views]) => ({ location, views }))
    .sort((a, b) => b.views - a.views);

  const totalViews = processedData.reduce((sum, item) => sum + item.views, 0) + nullLocationViews;

  const maxCountriesToShow = 7;
  const displayCountries = processedData.slice(0, maxCountriesToShow);
  const remainingCountries = processedData.slice(maxCountriesToShow);

  const countriesHTML = displayCountries.map(country => {
    const percentage = totalViews > 0 ? (country.views / totalViews) * 100 : 0;
    const flagEmoji = getCountryFlag(country.location);
    const formattedViews = formatViewCount(country.views);

    return `
      <li class="flex justify-between items-center gap-x-2">
        <div class="relative size-full truncate">
          <span class="relative z-10 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
            ${flagEmoji} ${country.location}
          </span>
          <div class="absolute inset-y-0 start-0 bg-yellow-100 h-full rounded-sm dark:bg-yellow-500/20 z-0 transition-all duration-1000 ease-out" :style="'width: ' + (animated && activeTab === 'countries' ? '${percentage.toFixed(1)}%' : '0%')"></div>
        </div>
        <div class="w-20 text-end">
          <span class="text-sm text-gray-500 dark:text-neutral-500">
            ${formattedViews}
          </span>
        </div>
      </li>
    `;
  }).join('');

  const othersViews = remainingCountries.reduce((sum, item) => sum + item.views, 0) + nullLocationViews;

  let othersHTML = '';
  if (othersViews > 0) {
    const othersPercentage = totalViews > 0 ? (othersViews / totalViews) * 100 : 0;
    const formattedOthersViews = formatViewCount(othersViews);

    othersHTML = `
      <li class="flex justify-between items-center gap-x-2">
        <div class="relative size-full truncate">
          <span class="relative z-10 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
            🌍 Others
          </span>
          <div class="absolute inset-y-0 start-0 bg-yellow-100 h-full rounded-sm dark:bg-yellow-500/20 z-0 transition-all duration-1000 ease-out" :style="'width: ' + (animated && activeTab === 'countries' ? '${othersPercentage.toFixed(1)}%' : '0%')"></div>
        </div>
        <div class="w-20 text-end">
          <span class="text-sm text-gray-500 dark:text-neutral-500">
            ${formattedOthersViews}
          </span>
        </div>
      </li>
    `;
  }

  countriesList.innerHTML = countriesHTML + othersHTML;
}
</script>
