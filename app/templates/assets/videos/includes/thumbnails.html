{% load i18n widget_tweaks %}
<form id="form_element" x-data="{ isFileTooLarge: false }" x-ref="thumbnail_form" method="POST" action="{% url 'update_thumbnail' asset_uuid=asset.uuid %}" enctype="multipart/form-data">
  {% csrf_token %}
  <div class="max-w-4xl">
    <div class="relative mt-4">
      <h3 class="mt-4 text-sm font-semibold leading-6 text-gray-900">Thumbnail</h3>
      <p class="text-gray-500 text-sm leading-6">Choose or upload a compelling thumbnail to represent your video, visible before playback begins.</p>

      <!-- Custom Alert Message -->
      <div x-show="isFileTooLarge" class="rounded-md bg-yellow-50 p-4 mt-4 relative z-50" role="alert" x-transition>
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">File Size Limit Exceeded</h3>
            <div class="mt-2 text-sm text-yellow-700">
              <p>Your file exceeds the maximum allowed size of 2MB. Please choose a smaller file to continue.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Upload New Thumbnail Section -->
      <div class="mt-4">
        <ul role="list" class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-y-0.5 sm:gap-y-1.5 xl:gap-y-2.5 gap-x-0.5 sm:gap-x-1.5 xl:gap-x-2.5">
          <li class="relative">
            <div class="group aspect-w-16 aspect-h-9 block w-full overflow-hidden bg-gray-100 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 focus-within:ring-offset-gray-100">
              <div class="flex justify-center items-center pointer-events-none object-cover group-hover:opacity-75">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <!-- Input to store the uploaded thumbnail -->
              <input type="file" name="file" id="fileInput"
                x-on:change="
                  if ($event.target.files[0]) {
                    if ($event.target.files[0].size > 2097152) {
                      isFileTooLarge = true;
                      $event.target.value = '';
                      return;
                    } else {
                      isFileTooLarge = false;
                      $nextTick(() => $refs.thumbnail_form.submit());
                    }
                  }
                "
                class="opacity-0 absolute inset-0 focus:outline-none" accept=".png, .jpeg, .jpg" />
            </div>
          </li>

          <!-- Display Existing Thumbnails Section -->
          {% for thumbnail in asset.video.thumbnails %}
            <li class="relative" x-data="{ thumbnailValue: '{{ thumbnail|escapejs }}' }">
              <div x-data="{ isPortrait: false }" class="group block w-full bg-gray-100 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 focus-within:ring-offset-gray-100 relative">
                <div class="aspect-w-16 aspect-h-9">
                  <img src="{{ asset.organization.cdn_url }}{{ thumbnail }}" class="object-contain w-full h-full" x-on:load="isPortrait = $event.target.naturalHeight > $event.target.naturalWidth">
                </div>
                <button type="button" class="absolute inset-0 focus:outline-none"
                  x-on:click="
                    document.getElementById('path').value = thumbnailValue;
                    $refs.thumbnail_form.submit();
                  ">
                  <span class="sr-only">Select thumbnail</span>
                </button>
                <!-- Cross button to delete thumbnail -->
                <div class="absolute top-2 right-2 z-10">
                  <button type="submit" name="delete_thumbnail" value="1" class="size-7 inline-flex justify-center items-center gap-x-1.5 font-medium text-sm rounded-full border border-stone-200 bg-white text-stone-600 shadow-sm hover:bg-stone-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-stone-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-remove-element="#dismiss-img1"
                    x-on:click="
                      document.getElementById('thumbnail_url_to_delete').value = thumbnailValue;">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M18 6 6 18"></path>
                      <path d="m6 6 12 12"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </li>
          {% endfor %}
        </ul>

        <!-- Hidden input to store the selected thumbnail path -->
        <input type="hidden" name="path" id="path" value="">
        <!-- Hidden input to store the thumbnail URL to delete -->
        <input type="hidden" name="thumbnail_url" id="thumbnail_url_to_delete" value="">
      </div>
    </div>
  </div>
</form>
