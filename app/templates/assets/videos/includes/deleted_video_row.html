<tr x-cloak x-data="{showDropDown: false}">
    <td class="whitespace-nowrap pl-4 sm:pl-6 pr-3 py-4 text-sm font-medium text-gray-900">
      <div class="flex space-x-3 items-center">

        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-8 h-8 text-red-400">
          <path fill-rule="evenodd"
                d="M1.5 5.625c0-1.036.84-1.875 1.875-1.875h17.25c1.035 0 1.875.84 1.875 1.875v12.75c0 1.035-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 011.5 18.375V5.625zm1.5 0v1.5c0 .207.168.375.375.375h1.5a.375.375 0 00.375-.375v-1.5a.375.375 0 00-.375-.375h-1.5A.375.375 0 003 5.625zm16.125-.375a.375.375 0 00-.375.375v1.5c0 .207.168.375.375.375h1.5A.375.375 0 0021 7.125v-1.5a.375.375 0 00-.375-.375h-1.5zM21 9.375A.375.375 0 0020.625 9h-1.5a.375.375 0 00-.375.375v1.5c0 .207.168.375.375.375h1.5a.375.375 0 00.375-.375v-1.5zm0 3.75a.375.375 0 00-.375-.375h-1.5a.375.375 0 00-.375.375v1.5c0 .207.168.375.375.375h1.5a.375.375 0 00.375-.375v-1.5zm0 3.75a.375.375 0 00-.375-.375h-1.5a.375.375 0 00-.375.375v1.5c0 .207.168.375.375.375h1.5a.375.375 0 00.375-.375v-1.5zM4.875 18.75a.375.375 0 00.375-.375v-1.5a.375.375 0 00-.375-.375h-1.5a.375.375 0 00-.375.375v1.5c0 .207.168.375.375.375h1.5zM3.375 15h1.5a.375.375 0 00.375-.375v-1.5a.375.375 0 00-.375-.375h-1.5a.375.375 0 00-.375.375v1.5c0 .207.168.375.375.375zm0-3.75h1.5a.375.375 0 00.375-.375v-1.5A.375.375 0 004.875 9h-1.5A.375.375 0 003 9.375v1.5c0 .207.168.375.375.375zm4.125 0a.75.75 0 000 1.5h9a.75.75 0 000-1.5h-9z"
                clip-rule="evenodd"/>
        </svg>
          <span title="{{ asset.title }}">{{ asset.title|truncatechars:60 }}</span>
          {% include "assets/includes/asset_status_badge.html" %}
      </div>
    </td>
    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ asset.uuid }}</td>
    {% if  asset.video.get_status_display  == 'Deleting' %}
      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">-</td>
    {% else %}
      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{% days_count asset.deleted %} days</td>
    {% endif %}
    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
        {% include "./delete_action_buttons.html" %}
    </td>
  </tr>
  </tr>
