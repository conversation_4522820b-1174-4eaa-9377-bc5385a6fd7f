<nav aria-label="Progress" class="mt-6">
  <ol role="list" class="space-y-4 md:flex md:space-y-0 md:space-x-8">
    <li class="md:flex-1">
      <!-- Completed Step -->
      <a href="#"
         :class="{'border-blue-700': step==1}"
         class="group flex flex-col border-l-4  py-2 pl-4 hover:border-blue-800 md:border-l-0 md:border-t-4 md:pl-0 md:pt-4 md:pb-0">
        <span class="text-sm font-medium text-blue-700 group-hover:text-blue-800">Step 1</span>
        <span class="text-sm font-medium">Select files</span>
      </a>
    </li>
    <li class="md:flex-1">
        <a href="#"
           :class="{'border-blue-700': step==2}"
           class="group flex flex-col border-l-4  py-2 pl-4 hover:border-gray-300 md:border-l-0 md:border-t-4 md:pl-0 md:pt-4 md:pb-0">
          <span class="text-sm font-medium text-gray-500 group-hover:text-gray-700">Step 2</span>
          <span class="text-sm font-medium">Video Settings</span>
        </a>
    </li>
    <li class="md:flex-1">
      <!-- Current Step -->
      <a href="#"
         :class="{'border-blue-700': step==3}"
         class="flex flex-col border-l-4 py-2 pl-4 md:border-l-0 md:border-t-4 md:pl-0 md:pt-4 md:pb-0" aria-current="step">
        <span class="text-sm font-medium text-blue-700">Step 3</span>
        <span class="text-sm font-medium">Transcode Settings</span>
      </a>
    </li>
    <li class="md:flex-1">
      <!-- Upcoming Step -->
      <a href="#"
         :class="{'border-blue-700': step==4}"
         class="group flex flex-col border-l-4 border-gray-200 py-2 pl-4 hover:border-gray-300 md:border-l-0 md:border-t-4 md:pl-0 md:pt-4 md:pb-0">
        <span class="text-sm font-medium text-gray-500 group-hover:text-gray-700">Step 4</span>
        <span class="text-sm font-medium">Preview</span>
      </a>
    </li>
  </ol>
</nav>
