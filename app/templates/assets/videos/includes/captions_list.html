{% load humanize %}
<li class="flex items-center justify-between py-4 pl-4 pr-5 text-sm leading-6">
  <div class="flex w-0 flex-1 items-center">
    <svg class="h-5 w-5 flex-shrink-0 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
      <path fill-rule="evenodd"
        d="M15.621 4.379a3 3 0 00-4.242 0l-7 7a3 3 0 004.241 4.243h.001l.497-.5a.75.75 0 011.064 1.057l-.498.501-.002.002a4.5 4.5 0 01-6.364-6.364l7-7a4.5 4.5 0 016.368 6.36l-3.455 3.553A2.625 2.625 0 119.52 9.52l3.45-3.451a.75.75 0 111.061 1.06l-3.45 3.451a1.125 1.125 0 001.587 1.595l3.454-3.553a3 3 0 000-4.242z"
        clip-rule="evenodd" />
    </svg>
    <div class="ml-4 flex min-w-0 flex-1 gap-2">
      <span class="truncate font-medium">{{caption.name}}.vtt</span>
      <span class="flex-shrink-0 text-gray-400">{{ caption.bytes|humanize_bytes }}</span>
    </div>
  </div>
  <div class="ml-4 flex-shrink-0 flex">
    <div class="relative inline-block text-left" x-data="{showMenu:false}">
      <div>
        <button @click="showMenu = !showMenu" @click.away="showMenu=false" type="button"
          class="flex items-center rounded-full text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 focus:ring-offset-gray-100"
          id="menu-button" aria-expanded="true" aria-haspopup="true">
          <span class="sr-only">Open options</span>
          <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path
              d="M10 3a1.5 1.5 0 110 3 1.5 1.5 0 010-3zM10 8.5a1.5 1.5 0 110 3 1.5 1.5 0 010-3zM11.5 15.5a1.5 1.5 0 10-3 0 1.5 1.5 0 003 0z" />
          </svg>
        </button>
      </div>
      <div x-cloak x-show="showMenu"
        class="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
        role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabindex="-1"
        x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95"
        x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75"
        x-transition:leave-start="transform opacity-100 scale-100"
        x-transition:leave-end="transform opacity-0 scale-95">
        <div class="py-1" role="none">
          <form id="form_element" method="POST" action="{% url 'edit_subtitle' asset_uuid=asset.uuid subtitle_id=caption.id %}">
            {% csrf_token %}
            {% if caption.is_active %}
              <input type="hidden" name="is_active" value="False">
              <button type="submit" class="text-gray-700 block px-4 py-2 text-sm" role="menuitem" tabindex="-1">Disable</button>
            {% else %}
              <input type="hidden" name="is_active" value="True">
              <button type="submit" class="text-gray-700 block px-4 py-2 text-sm" role="menuitem" tabindex="-1">Enable</button>
            {% endif %}
          </form>
          <a href="{{ asset.organization.cdn_url }}{{caption.url}}" class="text-gray-700 block px-4 py-2 text-sm" role="menuitem" tabindex="-1"
            id="menu-item-2">Download</a>
          {% if caption.get_subtitle_type_display != "Auto Generated" %}
            <form id="delete-form" method="POST" action="{% url 'delete_subtitle' asset_uuid=asset.uuid subtitle_id=caption.id %}">
              {% csrf_token %}
              <a href="#" class="text-gray-700 block px-4 py-2 text-sm" role="menuitem" tabindex="-1"
                id="menu-item-1" onclick="event.preventDefault(); document.getElementById('delete-form').submit();">Delete</a>
            </form>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</li>
