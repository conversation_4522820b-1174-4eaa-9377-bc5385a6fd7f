{% load humanize %}
<div class="px-4 sm:px-0 2xl:row-start-1 2xl:col-start-4 2xl:row-span-2">
    <h2 class="text-base font-semibold leading-6 text-gray-900">Transcoding Activity Log</h2>
    <ul role="list" class="mt-4 space-y-6">
      {% if asset.created %}
      <li class="relative flex gap-x-4">
        {% if asset.video.status == asset.video.Status.UPLOADING %}
        <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
            <svg class="w-6 h-6 text-blue-600" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd"
                d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
                clip-rule="evenodd" />
            </svg>
          </div>
        {% else %}
        <div class="absolute top-0 left-0 flex justify-center w-6 -bottom-6">
            <div class="w-px bg-gray-200"></div>
          </div>
        <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
            <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300"></div>
          </div>
        {% endif %}
        <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500"><span class="font-medium text-gray-900">{{ request.user.name }}</span> initiated video upload.</p>
        <time title="{{ asset.created | date:'jS F Y g:i A' }}" class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ asset.created | naturaltime }}</time>
      </li>
      {% endif %}
      {% if asset.video.transcoding_submission_time %}
      <li class="relative flex gap-x-4">
        {% if asset.video.status == asset.video.Status.QUEUED %}
        <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
            <svg class="w-6 h-6 text-blue-600" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd"
                d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
                clip-rule="evenodd" />
            </svg>
          </div>
        {% else %}
        <div class="absolute top-0 left-0 flex justify-center w-6 -bottom-6">
            <div class="w-px bg-gray-200"></div>
          </div>
        <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
            <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300"></div>
          </div>
        {% endif %}
        <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">Video uploaded successfully.</p>
        <time title="{{ asset.video.transcoding_submission_time | date:'jS F Y g:i A' }}" class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ asset.video.transcoding_submission_time | naturaltime }}</time>
      </li>
      {% endif %}
      {% if asset.video.transcoding_start_time %}
      <li class="relative flex gap-x-4">
        {% if asset.video.status == asset.video.Status.TRANSCODING %}
        <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
            <svg class="w-6 h-6 text-blue-600" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd"
                d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
                clip-rule="evenodd" />
            </svg>
          </div>
        {% else %}
        <div class="absolute top-0 left-0 flex justify-center w-6 -bottom-6">
            <div class="w-px bg-gray-200"></div>
          </div>
        <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
            <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300"></div>
          </div>
        {% endif %}
        <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">Transcoding process initiated.</p>
        <time title="{{ asset.video.transcoding_start_time | date:'jS F Y g:i A' }}" class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ asset.video.transcoding_start_time | naturaltime }}</time>
      </li>
      {% endif %}
      {% if asset.video.transcoding_end_time %}
      <li class="relative flex gap-x-4">
        {% if asset.video.status == asset.video.Status.COMPLETED %}
        <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
            <svg class="w-6 h-6 text-blue-600" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd"
                d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
                clip-rule="evenodd" />
            </svg>
          </div>
        <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">Transcoding completed successfully</p>
        {% elif asset.video.status == asset.video.Status.ERROR  %}
        <div class="relative flex items-center justify-center flex-none w-6 h-6 bg-white">
            <svg class="w-6 h-6 text-red-600" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd"
                d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
                clip-rule="evenodd" />
            </svg>
          </div>
        <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">Transcoding failed</p>
        {% endif %}
        <time title="{{ asset.video.transcoding_end_time | date:'jS F Y g:i A' }}" class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ asset.video.transcoding_end_time | naturaltime }}</time>
      </li>
      {% endif %}
    </ul>
  </div>
