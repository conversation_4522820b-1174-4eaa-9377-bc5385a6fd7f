<div class="pb-2 px-7 flex gap-x-2">
  <div class="w-full">
    <h3 class="inline-block font-medium text-xs uppercase text-gray-800 dark:text-white">
      Browsers
    </h3>
  </div>
</div>
<div class="p-5 pt-0">

<div class="mt-1">
  <div class="grid grid-cols-3 gap-3">
  <div class="p-6 bg-gray-100 dark:bg-neutral-700 rounded-lg relative overflow-hidden">
    <img class="shrink-0 w-8 h-8 mb-4" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/chrome/chrome-original.svg" alt="Chrome Logo">
    <p class="text-sm text-gray-800 dark:text-neutral-200">
      Chrome
    </p>
    <p class="font-semibold text-lg text-gray-800 dark:text-neutral-200 mb-3" id="chrome-percentage">
      0%
    </p>
    <!-- Progress Bar -->
    <div class="absolute bottom-0 left-0 w-full py-1 px-1">
      <div class="h-2 bg-gray-200 dark:bg-neutral-600 rounded-full">
        <div class="h-full transition-all duration-1000 ease-out rounded-full" id="chrome-progress" style="width: 0%; background-color: #4285F4;"></div>
      </div>
    </div>
  </div>

  <div class="p-6 bg-gray-100 dark:bg-neutral-700 rounded-lg relative overflow-hidden">
    <img class="shrink-0 w-8 h-8 mb-4" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/firefox/firefox-original.svg" alt="Firefox Logo">
    <p class="text-sm text-gray-800 dark:text-neutral-200">
      Firefox
    </p>
    <p class="font-semibold text-lg text-gray-800 dark:text-neutral-200 mb-3" id="firefox-percentage">
      0%
    </p>
    <!-- Progress Bar -->
    <div class="absolute bottom-0 left-0 w-full py-1 px-1">
      <div class="h-2 bg-gray-200 dark:bg-neutral-600 rounded-full">
        <div class="h-full transition-all duration-1000 ease-out rounded-full" id="firefox-progress" style="width: 0%; background-color: #FF7139;"></div>
      </div>
    </div>
  </div>

  <div class="p-6 bg-gray-100 dark:bg-neutral-700 rounded-lg relative overflow-hidden">
    <img class="shrink-0 w-8 h-8 mb-4" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/safari/safari-original.svg" alt="Safari Logo">
    <p class="text-sm text-gray-800 dark:text-neutral-200">
      Safari
    </p>
    <p class="font-semibold text-lg text-gray-800 dark:text-neutral-200 mb-3" id="safari-percentage">
      0%
    </p>
    <!-- Progress Bar -->
    <div class="absolute bottom-0 left-0 w-full py-1 px-1">
      <div class="h-2 bg-gray-200 dark:bg-neutral-600 rounded-full">
        <div class="h-full transition-all duration-1000 ease-out rounded-full" id="safari-progress" style="width: 0%; background-color: #0FB5EE;"></div>
      </div>
    </div>
  </div>
</div>

<div class="grid grid-cols-3 gap-3 mt-3">
  <div class="p-6 bg-gray-100 dark:bg-neutral-700 rounded-lg relative overflow-hidden">
    <img class="shrink-0 w-8 h-8 mb-4" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/opera/opera-original.svg" alt="Opera Logo">
    <p class="text-sm text-gray-800 dark:text-neutral-200">
      Opera
    </p>
    <p class="font-semibold text-lg text-gray-800 dark:text-neutral-200 mb-3" id="opera-percentage">
      0%
    </p>
    <!-- Progress Bar -->
    <div class="absolute bottom-0 left-0 w-full py-1 px-1">
      <div class="h-2 bg-gray-200 dark:bg-neutral-600 rounded-full">
        <div class="h-full transition-all duration-1000 ease-out rounded-full" id="opera-progress" style="width: 0%; background-color: #CC0F16;"></div>
      </div>
    </div>
  </div>

  <div class="p-6 bg-gray-100 dark:bg-neutral-700 rounded-lg relative overflow-hidden">
    <svg class="shrink-0 w-8 h-8 mb-4" viewBox="0 0 27600 27600" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <linearGradient id="A" gradientUnits="userSpaceOnUse"/>
      <linearGradient id="B" x1="6870" x2="24704" y1="18705" y2="18705" xlink:href="#A">
        <stop offset="0" stop-color="#0c59a4"/>
        <stop offset="1" stop-color="#114a8b"/>
      </linearGradient>
      <linearGradient id="C" x1="16272" x2="5133" y1="10968" y2="23102" xlink:href="#A">
        <stop offset="0" stop-color="#1b9de2"/>
        <stop offset=".16" stop-color="#1595df"/>
        <stop offset=".67" stop-color="#0680d7"/>
        <stop offset="1" stop-color="#0078d4"/>
      </linearGradient>
      <radialGradient id="D" cx="16720" cy="18747" r="9538" xlink:href="#A">
        <stop offset=".72" stop-opacity="0"/>
        <stop offset=".95" stop-opacity=".53"/>
        <stop offset="1"/>
      </radialGradient>
      <radialGradient id="E" cx="7130" cy="19866" r="14324" gradientTransform="matrix(.14843 -.98892 .79688 .1196 -8759 25542)" xlink:href="#A">
        <stop offset=".76" stop-opacity="0"/>
        <stop offset=".95" stop-opacity=".5"/>
        <stop offset="1"/>
      </radialGradient>
      <radialGradient id="F" cx="2523" cy="4680" r="20243" gradientTransform="matrix(-.03715 .99931 -2.12836 -.07913 13579 3530)" xlink:href="#A">
        <stop offset="0" stop-color="#35c1f1"/>
        <stop offset=".11" stop-color="#34c1ed"/>
        <stop offset=".23" stop-color="#2fc2df"/>
        <stop offset=".31" stop-color="#2bc3d2"/>
        <stop offset=".67" stop-color="#36c752"/>
      </radialGradient>
      <radialGradient id="G" cx="24247" cy="7758" r="9734" gradientTransform="matrix(.28109 .95968 -.78353 .22949 24510 -16292)" xlink:href="#A">
        <stop offset="0" stop-color="#66eb6e"/>
        <stop offset="1" stop-color="#66eb6e" stop-opacity="0"/>
      </radialGradient>
      <path id="H" d="M24105 20053a9345 9345 0 01-1053 472 10202 10202 0 01-3590 646c-4732 0-8855-3255-8855-7432 0-1175 680-2193 1643-2729-4280 180-5380 4640-5380 7253 0 7387 6810 8137 8276 8137 791 0 1984-230 2704-456l130-44a12834 12834 0 006660-5282c220-350-168-757-535-565z"/>
      <path id="I" d="M11571 25141a7913 7913 0 01-2273-2137 8145 8145 0 01-1514-4740 8093 8093 0 013093-6395 8082 8082 0 011373-859c312-148 846-414 1554-404a3236 3236 0 012569 1297 3184 3184 0 01636 1866c0-21 2446-7960-8005-7960-4390 0-8004 4166-8004 7820 0 2319 538 4170 1212 5604a12833 12833 0 007684 6757 12795 12795 0 003908 610c1414 0 2774-233 4045-656a7575 7575 0 01-6278-803z"/>
      <path id="J" d="M16231 15886c-80 105-330 250-330 566 0 260 170 512 472 723 1438 1003 4149 868 4156 868a5954 5954 0 003027-839 6147 6147 0 001133-850 6180 6180 0 001910-4437c26-2242-796-3732-1133-4392-2120-4141-6694-6525-11668-6525-7011 0-12703 5635-12798 12620 47-3654 3679-6605 7996-6605 350 0 2346 34 4200 1007 1634 858 2490 1894 3086 2921 618 1067 728 2415 728 2952s-271 1333-780 1990z"/>
      <use fill="url(#B)" xlink:href="#H"/>
      <use fill="url(#D)" opacity=".35" xlink:href="#H"/>
      <use fill="url(#C)" xlink:href="#I"/>
      <use fill="url(#E)" opacity=".4" xlink:href="#I"/>
      <use fill="url(#F)" xlink:href="#J"/>
      <use fill="url(#G)" xlink:href="#J"/>
    </svg>
    <p class="text-sm text-gray-800 dark:text-neutral-200">
      Edge
    </p>
    <p class="font-semibold text-lg text-gray-800 dark:text-neutral-200 mb-3" id="edge-percentage">
      0%
    </p>
    <div class="absolute bottom-0 left-0 w-full py-1 px-1">
      <div class="h-2 bg-gray-200 dark:bg-neutral-600 rounded-full">
        <div class="h-full transition-all duration-1000 ease-out rounded-full" id="edge-progress" style="width: 0%; background-color: #1B9DE2;"></div>
      </div>
    </div>
  </div>

  <div class="p-6 bg-gray-100 dark:bg-neutral-700 rounded-lg relative overflow-hidden">
    <svg class="shrink-0 w-8 h-8 mb-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="10" fill="#4A90E2"/>
      <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z" fill="#87CEEB"/>
      <path d="M12 18C13.1 18 14 18.9 14 20C14 21.1 13.1 22 12 22C10.9 22 10 21.1 10 20C10 18.9 10.9 18 12 18Z" fill="#87CEEB"/>
      <ellipse cx="12" cy="12" rx="10" ry="4" fill="none" stroke="#87CEEB" stroke-width="1.5"/>
      <ellipse cx="12" cy="12" rx="4" ry="10" fill="none" stroke="#87CEEB" stroke-width="1.5"/>
      <path d="M7.5 7.5C8.3 7.5 9 8.2 9 9C9 9.8 8.3 10.5 7.5 10.5C6.7 10.5 6 9.8 6 9C6 8.2 6.7 7.5 7.5 7.5Z" fill="#90EE90"/>
      <path d="M16.5 13.5C17.3 13.5 18 14.2 18 15C18 15.8 17.3 16.5 16.5 16.5C15.7 16.5 15 15.8 15 15C15 14.2 15.7 13.5 16.5 13.5Z" fill="#90EE90"/>
      <path d="M7.5 16.5C8.3 16.5 9 15.8 9 15C9 14.2 8.3 13.5 7.5 13.5C6.7 13.5 6 14.2 6 15C6 15.8 6.7 16.5 7.5 16.5Z" fill="#FFB347"/>
      <path d="M16.5 7.5C17.3 7.5 18 8.2 18 9C18 9.8 17.3 10.5 16.5 10.5C15.7 10.5 15 9.8 15 9C15 8.2 15.7 7.5 16.5 7.5Z" fill="#FFB347"/>
    </svg>
    <p class="text-sm text-gray-800 dark:text-neutral-200">
      Others
    </p>
    <p class="font-semibold text-lg text-gray-800 dark:text-neutral-200 mb-3" id="others-percentage">
      0%
    </p>
    <div class="absolute bottom-0 left-0 w-full py-1 px-1">
      <div class="h-2 bg-gray-200 dark:bg-neutral-600 rounded-full">
        <div class="h-full transition-all duration-1000 ease-out rounded-full" id="others-progress" style="width: 0%; background-color: #6B7280;"></div>
      </div>
    </div>
  </div>
</div>
</div>

<script>
  function initBrowserAnalytics() {
    try {
      const deviceData = {{ analytics_data|safe }};

      const browserCounts = {
        'Chrome': 0,
        'Firefox': 0,
        'Safari': 0,
        'Opera': 0,
        'Edge': 0,
        'Others': 0
      };

      let totalViews = 0;

      deviceData.forEach(item => {
        if (!item.user_agent) return;

        let browser = detectBrowser(item.user_agent);
        browserCounts[browser] += item.views;
        totalViews += item.views;
      });


      if (totalViews > 0) {
        Object.keys(browserCounts).forEach(browser => {

          const percentage = Math.round((browserCounts[browser] / totalViews) * 100);
          const elementId = browser.toLowerCase();

          const percentEl = document.getElementById(elementId + '-percentage');
          if (percentEl) percentEl.textContent = percentage + '%';

          const progressEl = document.getElementById(elementId + '-progress');
          if (progressEl) progressEl.style.width = percentage + '%';
        });
      }
    } catch (error) {
      console.error('Error initializing browser analytics:', error);
    }
  };

  function detectBrowser(userAgent) {
    userAgent = userAgent.toLowerCase();

    if (userAgent.includes('edge') || userAgent.includes('edg/')) {
      return 'Edge';
    } else if (userAgent.includes('chrome')) {
      return 'Chrome';
    } else if (userAgent.includes('firefox')) {
      return 'Firefox';
    } else if (userAgent.includes('safari') && !userAgent.includes('chrome')) {
      return 'Safari';
    } else if (userAgent.includes('opera') || userAgent.includes('opr/')) {
      return 'Opera';
    } else {
      return 'Others';
    }
  }
</script>
