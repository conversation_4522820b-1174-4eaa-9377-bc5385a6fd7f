<div class="flex flex-col bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700 flex-1" x-data="{
  activeTab: 'map',
  animated: false,
  selectedCountry: 'United States',
  switchTab(tab) {
    this.animated = false;
    this.activeTab = tab;
    setTimeout(() => this.animated = true, 50);
  },
  selectCountry(country) {
    this.selectedCountry = country;
    this.switchTab('states');
  }
}" >
  <div class="p-5 pb-4 flex justify-between items-center">
    <div>
      <h2 class="inline-block font-semibold text-lg text-gray-800 dark:text-neutral-200">
        Locations
      </h2>
    </div>

    <div class="flex items-center">
      <nav class="flex space-x-1" aria-label="Tabs" role="tablist">
        <button type="button" @click="switchTab('map')" :class="activeTab === 'map' ? 'border-blue-600 text-blue-600 dark:border-blue-500 dark:text-blue-500' : 'border-transparent text-gray-500 dark:text-neutral-400'" class="py-1 px-2 inline-flex items-center gap-x-2 border-b-2 text-xs font-medium" role="tab">
          Map
        </button>
        <button type="button" @click="switchTab('countries')" onclick="renderCountriesList()" :class="activeTab === 'countries' ? 'border-blue-600 text-blue-600 dark:border-blue-500 dark:text-blue-500' : 'border-transparent text-gray-500 dark:text-neutral-400'" class="py-1 px-2 inline-flex items-center gap-x-2 border-b-2 text-xs font-medium" role="tab">
          Countries
        </button>
        {% comment %} <button type="button" @click="switchTab('states')" :class="activeTab === 'states' ? 'border-blue-600 text-blue-600 dark:border-blue-500 dark:text-blue-500' : 'border-transparent text-gray-500 dark:text-neutral-400'" class="py-1 px-2 inline-flex items-center gap-x-2 border-b-2 text-xs font-medium" role="tab">
          States
        </button> {% endcomment %}
      </nav>
    </div>
  </div>

  <div class="mt-0 min-h-96">

    <div x-show="activeTab === 'map'" role="tabpanel" class="min-h-96 p-5 pt-0">
      {% include "./analytics_map_tab.html" %}
    </div>

    <div x-show="activeTab === 'countries'" role="tabpanel" class="min-h-96">
      {% include "./analytics_countries_tab.html" %}
    </div>

    {% comment %} <div x-show="activeTab === 'states'" role="tabpanel" class="min-h-96">
      {% include "./analytics_states_tab.html" %}
    </div> {% endcomment %}
  </div>
</div>
