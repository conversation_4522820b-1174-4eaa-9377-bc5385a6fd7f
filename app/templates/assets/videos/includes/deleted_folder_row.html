<tr>
    <td class="whitespace-nowrap pl-4 sm:pl-6 pr-3 py-4 text-sm font-medium text-gray-900">
        <div class="flex space-x-3 items-center">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-8 h-8 text-yellow-300">
            <path
              d="M19.5 21a3 3 0 003-3v-4.5a3 3 0 00-3-3h-15a3 3 0 00-3 3V18a3 3 0 003 3h15zM1.5 10.146V6a3 3 0 013-3h5.379a2.25 2.25 0 011.59.659l2.122 2.121c.14.141.331.22.53.22H19.5a3 3 0 013 3v1.146A4.483 4.483 0 0019.5 9h-15a4.483 4.483 0 00-3 1.146z" />
          </svg>

          <div class="flex space-x-2">
            <span class="truncate" title="{{folder.title}}">{{ folder.title |truncatechars:60 }}</span>
            <span
              class="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">{{ folder.children_count }} item{{ folder.children_count|pluralize }}</span>
          </div>
        </div>
      </a>
    </td>
    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ folder.uuid }}</td>
    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{% days_count folder.deleted %} days</td>
    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
      {% include "./delete_action_buttons.html" %}
    </td>
  </tr>
