<script>
    var monthlyMetrics = JSON.parse('{{ monthly_metrics_json|escapejs }}');

    function formatDateString(dateString) {
      var date = new Date(dateString);
      var monthAbbreviation = date.toLocaleString('default', { month: 'short' });
      var day = date.getDate();
      return monthAbbreviation + ' ' + day;
    }


    function chartComponent() {

      var chart = echarts.init(document.getElementById('chart'));
      var views_chart = echarts.init(document.getElementById('views_chart'));
      var minutes_watched_chart = echarts.init(document.getElementById('minutes_watched_chart'));

      var dateKeys = Object.keys(monthlyMetrics);

      var formattedDates = dateKeys.map(function(dateKey) {
        return formatDateString(dateKey);
      });
      var viewsData = dateKeys.map(function(dateKey) {
        return monthlyMetrics[dateKey].views_count;
      });
      var watchTimeData = dateKeys.map(function(dateKey) {
        return monthlyMetrics[dateKey].total_watch_time;
      });

      var common_chart_options = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        toolbox: {
          feature: {
            saveAsImage: { show: true }
          }
        },
        legend: {
          data: []
        },
        xAxis: [
          {
            type: 'category',
            data: formattedDates,
            axisPointer: {
              type: 'shadow'
            },
            axisLabel: {
              interval: 2,  // Show label every 3 days (0-based index, so 2 means every 3rd item)
              rotate: 45   // Optional: Rotate labels for better visibility if they overlap
            }
          }
        ],
        yAxis: [],
        series: []
      };

      var option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        toolbox: {
          feature: {
            saveAsImage: { show: true }
          }
        },
        legend: {
          data: ['Views', 'Minutes Watched']
        },
        xAxis: [
          {
            type: 'category',
            data: formattedDates,
            axisPointer: {
              type: 'shadow'
            },
            axisLabel: {
              interval: 2,  // Show label every 3 days (0-based index, so 2 means every 3rd item)
              rotate: 45   // Optional: Rotate labels for better visibility if they overlap
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: 'Views',
            axisLabel: {
              formatter: '{value}'
            }
          },
          {
            type: 'value',
            name: 'Minutes Watched',
            axisLabel: {
              formatter: '{value}'
            }
          }
        ],
        series: [
          {
            name: 'Views',
            type: 'bar',
            data: viewsData,
            itemStyle: {
              color: '#3B82F6'
            }
          },
          {
            name: 'Minutes Watched',
            type: 'line',
            yAxisIndex: 1,
            data: watchTimeData,
            smooth: true,
            itemStyle: {
              color: '#9CA3AF'
            }
          }
        ]
      };

      var view_chart_option = JSON.parse(JSON.stringify(common_chart_options));
      view_chart_option['legend']['data'] = ['Views']
      view_chart_option['yAxis'] = [
        {
          type: 'value',
          name: 'Views',
          axisLabel: {
            formatter: '{value}'
          }
        }
      ];
      view_chart_option['series'] = [
        {
          name: 'Views',
          type: 'bar',
          data: viewsData,
          itemStyle: {
            color: '#3B82F6'
          }
        }
      ];

      var minutes_watched_chart_option = JSON.parse(JSON.stringify(common_chart_options));
      minutes_watched_chart_option['legend']['data'] = ['Minutes Watched']
      minutes_watched_chart_option['yAxis'] = [

        {
          type: 'value',
          name: 'Minutes Watched',
          axisLabel: {
            formatter: '{value}'
          }
        }
      ];
      minutes_watched_chart_option['series'] = [
        {
          name: 'Minutes Watched',
          type: 'line',
          yAxisIndex: 0,
          data: watchTimeData,
          itemStyle: {
            color: '#6b7280'
          }
        }
      ];
      chart.setOption(option);
      return {
        showSeparate: false,
        toggleCharts() {
          this.showSeparate = !this.showSeparate;
          if (this.showSeparate) {
            views_chart.setOption(view_chart_option);
            minutes_watched_chart.setOption(minutes_watched_chart_option);
          } else {
            chart.setOption(option);
          }
        }
      };
    }
  </script>
