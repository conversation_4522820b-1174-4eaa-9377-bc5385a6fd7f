<!-- Heading -->
<div class="pb-2 px-7 flex gap-x-2">
  <div class="w-full">
    <h3 class="inline-block font-medium text-xs uppercase text-gray-800 dark:text-white">
      <span x-text="selectedCountry === 'United States' ? 'State' : 'Region'"></span>
    </h3>
  </div>
  <div class="w-20 text-end">
    <h3 class="inline-block font-medium text-xs uppercase text-gray-800 dark:text-white">
      Views
    </h3>
  </div>
</div>
<!-- End Heading -->

<!-- Body -->
<div class="p-5 pt-0 -mt-2">
  <!-- List Group -->
  <ul class="space-y-2">
    <!-- United States - California -->
    <template x-if="selectedCountry === 'United States'">
      <li class="flex justify-between items-center gap-x-2">
        <div class="relative size-full truncate">
          <span class="relative z-10 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
             🇺🇸 California
          </span>
          <div class="absolute inset-y-0 start-0 bg-yellow-100 h-full rounded-sm dark:bg-yellow-500/20 z-0 transition-all duration-1000 ease-out" :style="'width: ' + (animated && activeTab === 'states' ? '100%' : '0%')"></div>
        </div>
        <div class="w-20 text-end">
          <span class="text-sm text-gray-500 dark:text-neutral-500">42.3k</span>
        </div>
      </li>
    </template>

    <!-- United States - Texas -->
    <template x-if="selectedCountry === 'United States'">
      <li class="flex justify-between items-center gap-x-2">
        <div class="relative size-full truncate">
          <span class="relative z-10 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
             🇺🇸 Texas
          </span>
          <div class="absolute inset-y-0 start-0 bg-yellow-100 h-full rounded-sm dark:bg-yellow-500/20 z-0 transition-all duration-1000 ease-out" :style="'width: ' + (animated && activeTab === 'states' ? '85%' : '0%')"></div>
        </div>
        <div class="w-20 text-end">
          <span class="text-sm text-gray-500 dark:text-neutral-500">35.9k</span>
        </div>
      </li>
    </template>

    <!-- United States - New York -->
    <template x-if="selectedCountry === 'United States'">
      <li class="flex justify-between items-center gap-x-2">
        <div class="relative size-full truncate">
          <span class="relative z-10 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
             🇺🇸 New York
          </span>
          <div class="absolute inset-y-0 start-0 bg-yellow-100 h-full rounded-sm dark:bg-yellow-500/20 z-0 transition-all duration-1000 ease-out" :style="'width: ' + (animated && activeTab === 'states' ? '70%' : '0%')"></div>
        </div>
        <div class="w-20 text-end">
          <span class="text-sm text-gray-500 dark:text-neutral-500">29.6k</span>
        </div>
      </li>
    </template>

    <!-- United States - Florida -->
    <template x-if="selectedCountry === 'United States'">
      <li class="flex justify-between items-center gap-x-2">
        <div class="relative size-full truncate">
          <span class="relative z-10 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
             🇺🇸 Florida
          </span>
          <div class="absolute inset-y-0 start-0 bg-yellow-100 h-full rounded-sm dark:bg-yellow-500/20 z-0 transition-all duration-1000 ease-out" :style="'width: ' + (animated && activeTab === 'states' ? '55%' : '0%')"></div>
        </div>
        <div class="w-20 text-end">
          <span class="text-sm text-gray-500 dark:text-neutral-500">23.2k</span>
        </div>
      </li>
    </template>

    <!-- United States - Illinois -->
    <template x-if="selectedCountry === 'United States'">
      <li class="flex justify-between items-center gap-x-2">
        <div class="relative size-full truncate">
          <span class="relative z-10 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
             🇺🇸 Illinois
          </span>
          <div class="absolute inset-y-0 start-0 bg-yellow-100 h-full rounded-sm dark:bg-yellow-500/20 z-0 transition-all duration-1000 ease-out" :style="'width: ' + (animated && activeTab === 'states' ? '40%' : '0%')"></div>
        </div>
        <div class="w-20 text-end">
          <span class="text-sm text-gray-500 dark:text-neutral-500">16.8k</span>
        </div>
      </li>
    </template>

    <!-- India - Maharashtra -->
    <template x-if="selectedCountry === 'India'">
      <li class="flex justify-between items-center gap-x-2">
        <div class="relative size-full truncate">
          <span class="relative z-10 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
             🇮🇳 Maharashtra
          </span>
          <div class="absolute inset-y-0 start-0 bg-yellow-100 h-full rounded-sm dark:bg-yellow-500/20 z-0 transition-all duration-1000 ease-out" :style="'width: ' + (animated && activeTab === 'states' ? '100%' : '0%')"></div>
        </div>
        <div class="w-20 text-end">
          <span class="text-sm text-gray-500 dark:text-neutral-500">8.2k</span>
        </div>
      </li>
    </template>

    <!-- India - Karnataka -->
    <template x-if="selectedCountry === 'India'">
      <li class="flex justify-between items-center gap-x-2">
        <div class="relative size-full truncate">
          <span class="relative z-10 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
             🇮🇳 Karnataka
          </span>
          <div class="absolute inset-y-0 start-0 bg-yellow-100 h-full rounded-sm dark:bg-yellow-500/20 z-0 transition-all duration-1000 ease-out" :style="'width: ' + (animated && activeTab === 'states' ? '75%' : '0%')"></div>
        </div>
        <div class="w-20 text-end">
          <span class="text-sm text-gray-500 dark:text-neutral-500">6.1k</span>
        </div>
      </li>
    </template>

    <!-- India - Delhi -->
    <template x-if="selectedCountry === 'India'">
      <li class="flex justify-between items-center gap-x-2">
        <div class="relative size-full truncate">
          <span class="relative z-10 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
             🇮🇳 Delhi
          </span>
          <div class="absolute inset-y-0 start-0 bg-yellow-100 h-full rounded-sm dark:bg-yellow-500/20 z-0 transition-all duration-1000 ease-out" :style="'width: ' + (animated && activeTab === 'states' ? '60%' : '0%')"></div>
        </div>
        <div class="w-20 text-end">
          <span class="text-sm text-gray-500 dark:text-neutral-500">4.9k</span>
        </div>
      </li>
    </template>

    <!-- India - Tamil Nadu -->
    <template x-if="selectedCountry === 'India'">
      <li class="flex justify-between items-center gap-x-2">
        <div class="relative size-full truncate">
          <span class="relative z-10 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
             🇮🇳 Tamil Nadu
          </span>
          <div class="absolute inset-y-0 start-0 bg-yellow-100 h-full rounded-sm dark:bg-yellow-500/20 z-0 transition-all duration-1000 ease-out" :style="'width: ' + (animated && activeTab === 'states' ? '45%' : '0%')"></div>
        </div>
        <div class="w-20 text-end">
          <span class="text-sm text-gray-500 dark:text-neutral-500">3.7k</span>
        </div>
      </li>
    </template>

    <!-- India - Others -->
    <template x-if="selectedCountry === 'India'">
      <li class="flex justify-between items-center gap-x-2">
        <div class="relative size-full truncate">
          <span class="relative z-10 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
             🇮🇳 Others
          </span>
          <div class="absolute inset-y-0 start-0 bg-yellow-100 h-full rounded-sm dark:bg-yellow-500/20 z-0 transition-all duration-1000 ease-out" :style="'width: ' + (animated && activeTab === 'states' ? '30%' : '0%')"></div>
        </div>
        <div class="w-20 text-end">
          <span class="text-sm text-gray-500 dark:text-neutral-500">4.2k</span>
        </div>
      </li>
    </template>

    <!-- Default for other countries -->
    <template x-if="selectedCountry !== 'United States' && selectedCountry !== 'India'">
      <li class="flex justify-between items-center gap-x-2">
        <div class="relative size-full truncate">
          <span class="relative z-10 block py-1 px-2 w-full text-sm truncate text-gray-800 dark:text-neutral-200">
             No detailed data available
          </span>
        </div>
        <div class="w-20 text-end">
          <span class="text-sm text-gray-500 dark:text-neutral-500">N/A </span>
        </div>
      </li>
    </template>
  </ul>
  <!-- End List Group -->
</div>
<!-- End Body --> 