{% extends "assets/includes/list_table.html" %}
{% load i18n %}


{% block row %}
    {% for asset in assets %}
        {% if asset.is_folder %}
            {% include "assets/includes/folder_row.html" with folder=asset %}
        {% else %}
            {% include "./row.html" with asset=asset %}
        {% endif %}
    {% endfor %}
{% endblock %}


{% block empty_state %}
    {% if is_empty %}
        {% if request.resolver_match.view_name == 'asset_move' or request.resolver_match.view_name == 'live_stream_move' %}
            {% include "./move_empty_state.html" %}
        {% else %}
            {% include "./asset_empty_state.html" %}
        {% endif %}
    {% endif %}
{% endblock %}
