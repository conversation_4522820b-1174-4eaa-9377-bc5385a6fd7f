<tr x-cloak x-data="{showDropDown: false}">
  <td class="whitespace-nowrap pl-4 sm:pl-6 pr-3 py-4 text-sm font-medium text-gray-900">
    <div class="flex space-x-3 items-center">

      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-8 h-8 text-red-400">
        <path fill-rule="evenodd"
              d="M1.5 5.625c0-1.036.84-1.875 1.875-1.875h17.25c1.035 0 1.875.84 1.875 1.875v12.75c0 1.035-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 011.5 18.375V5.625zm1.5 0v1.5c0 .207.168.375.375.375h1.5a.375.375 0 00.375-.375v-1.5a.375.375 0 00-.375-.375h-1.5A.375.375 0 003 5.625zm16.125-.375a.375.375 0 00-.375.375v1.5c0 .207.168.375.375.375h1.5A.375.375 0 0021 7.125v-1.5a.375.375 0 00-.375-.375h-1.5zM21 9.375A.375.375 0 0020.625 9h-1.5a.375.375 0 00-.375.375v1.5c0 .207.168.375.375.375h1.5a.375.375 0 00.375-.375v-1.5zm0 3.75a.375.375 0 00-.375-.375h-1.5a.375.375 0 00-.375.375v1.5c0 .207.168.375.375.375h1.5a.375.375 0 00.375-.375v-1.5zm0 3.75a.375.375 0 00-.375-.375h-1.5a.375.375 0 00-.375.375v1.5c0 .207.168.375.375.375h1.5a.375.375 0 00.375-.375v-1.5zM4.875 18.75a.375.375 0 00.375-.375v-1.5a.375.375 0 00-.375-.375h-1.5a.375.375 0 00-.375.375v1.5c0 .207.168.375.375.375h1.5zM3.375 15h1.5a.375.375 0 00.375-.375v-1.5a.375.375 0 00-.375-.375h-1.5a.375.375 0 00-.375.375v1.5c0 .207.168.375.375.375zm0-3.75h1.5a.375.375 0 00.375-.375v-1.5A.375.375 0 004.875 9h-1.5A.375.375 0 003 9.375v1.5c0 .207.168.375.375.375zm4.125 0a.75.75 0 000 1.5h9a.75.75 0 000-1.5h-9z"
              clip-rule="evenodd"/>
      </svg>
      <a href="{% url 'asset_detail' asset_uuid=asset.uuid %}">
        <span title="{{ asset.title }}">{{ asset.title|truncatechars:60 }}</span>
        {% include "assets/includes/asset_status_badge.html" %}
      </a>
    </div>
  </td>
  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ asset.uuid }}</td>
  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ asset.created }}</td>

  <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
    <div class="inline-flex space-x-2 items-center">
          <a class="cursor-pointer" x-data="Clipboard" @click="copy('{{ asset.video.get_playback_url }}')">
        <div class="flex" x-on:mouseover="tooltip = true" x-on:mouseleave="tooltip = false">
          <div class="relative z-30 inline-flex">
            {% if not asset.video.is_drm_encrypted %}
              <div class="cursor-pointer">
                <svg class="w-5 h-5 text-blue-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                     stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244"/>
                </svg>
              </div>
            {% endif %}
            <div class="relative" x-cloak x-show.transition.origin.top="tooltip">
              <div
                class="absolute flex justify-center top-0 z-10 w-32 p-2 -mt-1 text-xs leading-tight text-white transform -translate-x-1/2 -translate-y-full bg-gray-900 rounded-lg shadow-lg">
                <span x-text="copyPlaybackURLButtonText">Copy Playback URL</span>
              </div>

              <svg
                class="absolute z-10 w-6 h-6 text-gray-900 transform -translate-x-6 -translate-y-3 fill-current stroke-current"
                width="8" height="8">
                <rect x="12" y="-10" width="8" height="8" transform="rotate(45)"/>
              </svg>
            </div>
          </div>
        </div>
      </a>
      <a class="cursor-pointer" x-data="Clipboard" @click="copyEmbedCodeToClipboard('{{ asset.uuid }}')">
        <div class="flex" x-on:mouseover="tooltip = true" x-on:mouseleave="tooltip = false">
          <div class="relative z-30 inline-flex">
            <div class="cursor-pointer">
              <svg class="w-5 h-5 text-blue-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                   stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round"
                      d="M17.25 6.75L22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3l-4.5 16.5"/>
              </svg>
            </div>
            <div class="relative" x-cloak x-show.transition.origin.top="tooltip">
              <div
                class="absolute flex justify-center top-0 z-10 w-32 p-2 -mt-1 text-xs leading-tight text-white transform -translate-x-1/2 -translate-y-full bg-gray-900 rounded-lg shadow-lg">
                <span x-show="!loading" x-text="copyEmbedCodebuttonText">Copy Embed Code</span>
                <svg x-show="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                     xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>

              <svg
                class="absolute z-10 w-6 h-6 text-gray-900 transform -translate-x-6 -translate-y-3 fill-current stroke-current"
                width="8" height="8">
                <rect x="12" y="-10" width="8" height="8" transform="rotate(45)"/>
              </svg>
            </div>
          </div>
        </div>
      </a>
        {% include "assets/includes/dropdown.html" %}
    </div>
  </td>
</tr>
