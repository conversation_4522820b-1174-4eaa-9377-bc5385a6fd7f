<div class="pb-2 px-7 flex gap-x-2">
  <div class="w-full">
    <h3 class="inline-block font-medium text-xs uppercase text-gray-800 dark:text-white">
      Operating System
    </h3>
  </div>
</div>

<div class="p-5 pt-0">

<div class="mt-1">
  <div class="grid grid-cols-3 gap-3">
    <div class="p-6 bg-gray-100 dark:bg-neutral-700 rounded-lg relative overflow-hidden">
      <img class="shrink-0 w-8 h-8 mb-4" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/windows8/windows8-original.svg" alt="Windows Logo">
      <p class="text-sm text-gray-800 dark:text-neutral-200">
        Windows
      </p>
      <p class="font-semibold text-lg text-gray-800 dark:text-neutral-200 mb-3" id="windows-percentage">
         0%
      </p>
      <!-- Progress Bar -->
      <div class="absolute bottom-0 left-0 w-full py-1 px-1">
        <div class="h-2 bg-gray-200 dark:bg-neutral-600 rounded-full">
          <div class="h-full transition-all duration-1000 ease-out rounded-full"
               id="windows-progress"
               style="width: 0%; background-color: #0078D4;"></div>
        </div>
      </div>
    </div>

    <div class="p-6 bg-gray-100 dark:bg-neutral-700 rounded-lg relative overflow-hidden">
      <img class="shrink-0 w-8 h-8 mb-4" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/apple/apple-original.svg" alt="macOS Logo">
      <p class="text-sm text-gray-800 dark:text-neutral-200">
        macOS
      </p>
      <p class="font-semibold text-lg text-gray-800 dark:text-neutral-200 mb-3" id="macos-percentage">
         0%
      </p>
      <!-- Progress Bar -->
      <div class="absolute bottom-0 left-0 w-full py-1 px-1">
        <div class="h-2 bg-gray-200 dark:bg-neutral-600 rounded-full">
          <div class="h-full transition-all duration-1000 ease-out rounded-full"
               id="macos-progress"
               style="width: 0%; background-color: #000000;"></div>
        </div>
      </div>
    </div>

    <div class="p-6 bg-gray-100 dark:bg-neutral-700 rounded-lg relative overflow-hidden">
      <img class="shrink-0 w-8 h-8 mb-4" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/linux/linux-original.svg" alt="Linux Logo">
      <p class="text-sm text-gray-800 dark:text-neutral-200">
        Linux
      </p>
      <p class="font-semibold text-lg text-gray-800 dark:text-neutral-200 mb-3" id="linux-percentage">
         0%
      </p>
      <!-- Progress Bar -->
      <div class="absolute bottom-0 left-0 w-full py-1 px-1">
        <div class="h-2 bg-gray-200 dark:bg-neutral-600 rounded-full">
          <div class="h-full transition-all duration-1000 ease-out rounded-full"
               id="linux-progress"
               style="width: 0%; background-color: #FCC624;"></div>
        </div>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-3 gap-3 mt-3">
    <div class="p-6 bg-gray-100 dark:bg-neutral-700 rounded-lg relative overflow-hidden">
      <img class="shrink-0 w-8 h-8 mb-4" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/android/android-original.svg" alt="Android Logo">
      <p class="text-sm text-gray-800 dark:text-neutral-200">
        Android
      </p>
      <p class="font-semibold text-lg text-gray-800 dark:text-neutral-200 mb-3" id="android-percentage">
         0%
      </p>
      <!-- Progress Bar -->
      <div class="absolute bottom-0 left-0 w-full py-1 px-1">
        <div class="h-2 bg-gray-200 dark:bg-neutral-600 rounded-full">
          <div class="h-full transition-all duration-1000 ease-out rounded-full"
               id="android-progress"
               style="width: 0%; background-color: #3DDC84;"></div>
        </div>
      </div>
    </div>

    <div class="p-6 bg-gray-100 dark:bg-neutral-700 rounded-lg relative overflow-hidden">
      <svg class="shrink-0 w-10 h-10 mb-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48">
        <path fill="#eceff1" d="M16,42h16c5.523,0,10-4.477,10-10V16c0-5.523-4.477-10-10-10H16C10.477,6,6,10.477,6,16v16C6,37.523,10.477,42,16,42z"/>
        <path fill="#ffc107" d="M12.783 17.974A0.762 0.762 0 1 0 12.783 19.498A0.762 0.762 0 1 0 12.783 17.974Z"/>
        <path fill="#ff5722" d="M15.982 21.81l1.444-.61c.68-1.22 1.835-1.927 3.332-1.927.34 0 .659.043.962.113l1.372-.579c-.676-.333-1.451-.526-2.334-.526C18.368 18.281 16.663 19.594 15.982 21.81zM13.297 22.944L13.297 21.375 12.273 21.375 12.273 23.377z"/>
        <path fill="#f44336" d="M13.297 25.733L13.297 22.944 12.273 23.377 12.273 26.165zM16.742 24.148c0-1.169.246-2.163.684-2.948l-1.444.61c-.214.696-.333 1.476-.333 2.338 0 .201.028.382.04.574l1.062-.449C16.750 24.23 16.742 24.192 16.742 24.148zM30.421 18.5c-.279.086-.537.195-.774.327L30.421 18.5zM23.092 18.807l-1.372.579c1.027.237 1.828.863 2.35 1.796l1.022-.432C24.624 19.878 23.941 19.226 23.092 18.807z"/>
        <path fill="#e91e63" d="M13.297 28.521L13.297 25.733 12.273 26.165 12.273 28.953zM30.421 18.5l-.774.327c-.983.547-1.577 1.464-1.577 2.58 0 .302.046.571.117.825l1.032-.436c-.034-.132-.056-.27-.056-.42 0-1.227 1.117-2.117 2.734-2.117.796 0 1.467.213 1.958.579l1.048-.443c-.694-.684-1.735-1.113-2.974-1.113C31.381 18.281 30.876 18.36 30.421 18.5zM16.75 24.274l-1.062.449c.059.959.26 1.811.597 2.536l1.004-.424C16.954 26.121 16.766 25.26 16.75 24.274zM25.092 20.751l-1.022.432c.381.682.603 1.532.658 2.51l1.061-.448C25.695 22.297 25.467 21.452 25.092 20.751z"/>
        <path fill="#9c27b0" d="M25.609 26.108c.146-.602.242-1.247.242-1.96 0-.316-.033-.609-.063-.904l-1.061.448c.009.153.03.296.03.456 0 .968-.177 1.809-.481 2.523L25.609 26.108zM17.29 26.834l-1.004.424c.408.879 1.008 1.568 1.777 2.038l1.258-.531C18.420 28.427 17.727 27.764 17.29 26.834zM13.297 28.521L12.273 28.953 12.273 29.789 13.297 29.789zM29.22 21.795l-1.032.436c.245.866.915 1.471 2.129 1.889l1.6-.676-.338-.085C30.122 22.995 29.406 22.527 29.22 21.795zM34.719 21.273h1.078c-.05-.731-.379-1.373-.893-1.879l-1.048.443C34.328 20.189 34.635 20.684 34.719 21.273z"/>
        <path fill="#3f51b5" d="M25.609 26.108l-1.333.563c-.629 1.476-1.85 2.36-3.519 2.36-.528 0-1.001-.103-1.437-.267l-1.258.531c.752.459 1.648.728 2.695.728C23.3 30.023 25.019 28.541 25.609 26.108zM28.828 26.859H27.75c.026.368.127.705.264 1.021l.989-.418C28.919 27.273 28.853 27.074 28.828 26.859zM32.695 23.641l-.779-.196-1.6.676c.234.081.487.156.762.224l1.289.328c.714.176 1.257.399 1.659.669l1.205-.509C34.703 24.318 33.878 23.934 32.695 23.641z"/>
        <path fill="#03a9f4" d="M29.003 27.463l-.989.418c.377.87 1.139 1.531 2.166 1.873l1.692-.714C30.493 29.007 29.415 28.396 29.003 27.463zM35.914 27.333c.035-.193.063-.39.063-.598 0-.784-.234-1.404-.745-1.902l-1.205.509c.579.39.856.883.856 1.51 0 .393-.131.75-.348 1.063L35.914 27.333z"/>
        <path fill="#009688" d="M35.914,27.333l-1.379,0.583c-0.472,0.682-1.394,1.132-2.55,1.132c-0.039,0-0.074-0.006-0.112-0.007l-1.692,0.714c0.514,0.171,1.086,0.269,1.71,0.269C34.098,30.023,35.615,28.964,35.914,27.333z"/>
      </svg>
      <p class="text-sm text-gray-800 dark:text-neutral-200">
        iOS
      </p>
      <p class="font-semibold text-lg text-gray-800 dark:text-neutral-200 mb-3" id="ios-percentage">
         0%
      </p>
      <div class="absolute bottom-0 left-0 w-full py-1 px-1">
        <div class="h-2 bg-gray-200 dark:bg-neutral-600 rounded-full">
          <div class="h-full transition-all duration-1000 ease-out rounded-full"
               id="ios-progress"
               style="width: 0%; background-color: #007AFF;"></div>
        </div>
      </div>
    </div>

    <div class="p-6 bg-gray-100 dark:bg-neutral-700 rounded-lg relative overflow-hidden">
      <svg class="shrink-0 w-8 h-8 mb-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="2" y="4" width="20" height="12" rx="2" fill="#6B7280" stroke="#6B7280" stroke-width="1"/>
        <rect x="3" y="5" width="18" height="8" rx="1" fill="#374151"/>
        <circle cx="5" cy="7" r="0.5" fill="#10B981"/>
        <circle cx="7" cy="7" r="0.5" fill="#F59E0B"/>
        <circle cx="9" cy="7" r="0.5" fill="#EF4444"/>
        <rect x="4" y="9" width="16" height="0.5" fill="#6B7280"/>
        <rect x="4" y="10.5" width="12" height="0.5" fill="#6B7280"/>
        <rect x="4" y="12" width="8" height="0.5" fill="#6B7280"/>
        <rect x="9" y="16" width="6" height="2" rx="0.5" fill="#6B7280"/>
        <rect x="7" y="18" width="10" height="1" rx="0.5" fill="#6B7280"/>
      </svg>
      <p class="text-sm text-gray-800 dark:text-neutral-200">
        Others
      </p>
      <p class="font-semibold text-lg text-gray-800 dark:text-neutral-200 mb-3" id="os-others-percentage">
         0%
      </p>
      <div class="absolute bottom-0 left-0 w-full py-1 px-1">
        <div class="h-2 bg-gray-200 dark:bg-neutral-600 rounded-full">
          <div class="h-full transition-all duration-1000 ease-out rounded-full"
               id="os-others-progress"
               style="width: 0%; background-color: #6B7280;"></div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>

<script>
  function initOSAnalytics() {
  try {
    const deviceData = {{ analytics_data|safe }};

    const osCounts = {
      'Windows': 0,
      'macOS': 0,
      'Linux': 0,
      'Android': 0,
      'iOS': 0,
      'Others': 0
    };

    let totalViews = 0;

    deviceData.forEach(item => {
      if (!item.user_agent || !item.views) return;

      const osName = detectOS(item.user_agent);

      const views = item.views || 0;

      osCounts[osName] += views;
      totalViews += views;
    });

    if (totalViews > 0) {
      Object.keys(osCounts).forEach(os => {
        const percentage = Math.round((osCounts[os] / totalViews) * 100);
        const elementId = os === 'Others' ? 'os-others' : os.toLowerCase();

        const percentEl = document.getElementById(`${elementId}-percentage`);
        if (percentEl) percentEl.textContent = `${percentage}%`;

        const progressEl = document.getElementById(`${elementId}-progress`);
        if (progressEl) progressEl.style.width = `${percentage}%`;
      });
    }
  } catch (error) {
    console.error('Error initializing OS analytics:', error);
  }
}

function detectOS(userAgent) {
  userAgent = userAgent.toLowerCase();

  if (userAgent.includes('windows')) return 'Windows';
  if (userAgent.includes('mac os') || userAgent.includes('macintosh')) return 'macOS';
  if (userAgent.includes('linux') && !userAgent.includes('android')) return 'Linux';
  if (userAgent.includes('android')) return 'Android';
  if (userAgent.includes('iphone') || userAgent.includes('ipad') || userAgent.includes('ios')) return 'iOS';
  return 'Others';
}

initOSAnalytics();
</script>
