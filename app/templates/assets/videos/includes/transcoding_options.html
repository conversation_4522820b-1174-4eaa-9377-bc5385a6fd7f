<div x-cloak class="mt-8" x-show="step==3">

        <div>
          <h1 class="text-xl font-semibold text-gray-900">Transcoding Settings</h1>
          <p class="mt-1 text-sm text-gray-500">Choose the options by which you need to transcode the video.</p>
        </div>
        <div x-cloak x-show="error" class="rounded-md bg-red-50 p-4 mt-6">
          <div class="flex">
            <div class="flex-shrink-0">
              <!-- Heroicon name: mini/x-circle -->
              <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800" x-text="error"></h3>
            </div>
          </div>
        </div>

    <div class="mt-6 flex justify-around">
        <fieldset class="flex-1">
          <legend class="sr-only">Resolutions</legend>
          <div class="relative flex items-start">
            <div class="flex h-5 items-center">
              <input value="resolutions" name="resolutions" x-model="isSelectAllChecked" @change="isSelectAllChecked ? selectAllResolutions:unSelectAllResolutions"  type="checkbox" class="h-4 w-4 rounded border-gray-300 text-blue-700 focus:ring-blue-600">
            </div>
            <div class="ml-3 text-sm">
              <label class="text-base font-medium text-gray-900" aria-hidden="true">Resolutions</label>
            </div>
          </div>
          <div class="mt-4 space-y-4">

            {% if '240p' in enabled_resolutions %}
            <div class="relative flex items-start">
              <div class="flex h-5 items-center">
                <input value="240p" name="resolutions" x-model="resolutions" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-blue-700 focus:ring-blue-600">
              </div>
              <div class="ml-3 text-sm">
                <label for="comments" class="font-medium text-gray-700">240p</label>
                <p class="text-gray-500">Lower picture quality. Uses less data.</p>
              </div>
            </div>
            {% endif %}


            {% if '360p' in enabled_resolutions %}
            <div class="relative flex items-start">
              <div class="flex h-5 items-center">
                <input value="360p" name="resolutions" x-model="resolutions" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-blue-700 focus:ring-blue-600">
              </div>
              <div class="ml-3 text-sm">
                <label for="candidates" class="font-medium text-gray-700">360p</label>
                <p class="text-gray-500">Slightly improved quality with less data.</p>
              </div>
            </div>
            {% endif %}


            {% if '480p' in enabled_resolutions %}
            <div class="relative flex items-start">
              <div class="flex h-5 items-center">
                <input value="480p" name="resolutions" x-model="resolutions" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-blue-700 focus:ring-blue-600">
              </div>
              <div class="ml-3 text-sm">
                <label for="candidates" class="font-medium text-gray-700">480p</label>
                <p class="text-gray-500">Improved quality with normal data.</p>
              </div>
            </div>
            {% endif %}


            {% if '720p' in enabled_resolutions %}
            <div class="relative flex items-start">
              <div class="flex h-5 items-center">
                <input value="720p" name="resolutions" x-model="resolutions" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-blue-700 focus:ring-blue-600">
              </div>
              <div class="ml-3 text-sm">
                <label for="candidates" class="font-medium text-gray-700">720p</label>
                <p class="text-gray-500">HD Ready.</p>
              </div>
            </div>
            {% endif %}

            {% if '1080p' in enabled_resolutions %}
            <div class="relative flex items-start">
              <div class="flex h-5 items-center">
                <input value="1080p" name="resolutions" x-model="resolutions" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-blue-700 focus:ring-blue-600">
              </div>
              <div class="ml-3 text-sm">
                <label for="candidates" class="font-medium text-gray-700">1080p</label>
                <p class="text-gray-500">Full HD.</p>
              </div>
            </div>
            {% endif %}

            {% if '4k' in enabled_resolutions %}
            <div class="relative flex items-start">
              <div class="flex h-5 items-center">
                <input value="4k" name="resolutions" x-model="resolutions" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-blue-700 focus:ring-blue-600">
              </div>
              <div class="ml-3 text-sm">
                <label for="candidates" class="font-medium text-gray-700">4k</label>
                <p class="text-gray-500">Ultra HD</p>
              </div>
            </div>
            {% endif %}

          </div>
        </fieldset>
       <div class="flex-1">
        <fieldset class="mb-8">
          <legend class="contents text-base font-medium text-gray-900">Content Protection</legend>
          <p class="text-sm text-gray-500">Safeguard your videos from unauthorized access and distribution</p>
          <div class="mt-4 space-y-4">
            <div class="flex items-center">
              <input id="drm" value="drm" x-model="contentProtectionType" type="radio"
                     class="h-4 w-4 border-gray-300 text-blue-700 focus:ring-blue-600">
              <label for="drm" class="ml-3 block text-sm font-medium text-gray-700">DRM</label>
            </div>
            <div class="flex items-center">
              <input id="aes" value="aes" x-model="contentProtectionType" type="radio"
                     class="h-4 w-4 border-gray-300 text-blue-700 focus:ring-blue-600">
              <label for="aes" class="ml-3 block text-sm font-medium text-gray-700">AES (Advanced Encryption Standard)</label>
            </div>
            <div class="hidden flex items-center">
              <input id="aes_signed_url" value="aes_signed_url" x-model="contentProtectionType" type="radio"
                     class="h-4 w-4 border-gray-300 text-blue-700 focus:ring-blue-600">
              <label for="aes_signed_url" class="ml-3 block text-sm font-medium text-gray-700">AES with Signed URL</label>
            </div>
            <div class="flex items-center">
              <input id="disabled" value="disabled" x-model="contentProtectionType" type="radio"
                     class="h-4 w-4 border-gray-300 text-blue-700 focus:ring-blue-600">
              <label for="disabled" class="ml-3 block text-sm font-medium text-gray-700">Disabled</label>
            </div>
          </div>
        </fieldset>
        {% if request.GET.multiple_codecs %}
        <hr/>
        <fieldset class="mt-8">
          <legend class="sr-only">Codec</legend>
          <div class="relative flex items-start">
            <div class="text-sm">
              <label class="text-base font-medium text-gray-900" aria-hidden="true">Codec</label>
            </div>
          </div>
          <div class="mt-4 space-y-4">
            <div class="relative flex items-start">
              <div class="flex h-5 items-center">
                <input value="h264" name="codecs" x-model="codecs" type="checkbox" onclick="return false" class="h-4 w-4 rounded border-gray-300 text-blue-700 focus:ring-blue-600">
              </div>
              <div class="ml-3 text-sm">
                <label for="comments" class="font-medium text-gray-700">H264</label>
                <p class="text-gray-500">A widely supported video compression standard offering good quality at lower bitrates, compatible with most devices and platforms</p>
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex h-5 items-center">
                <input value="h265" name="codecs" x-model="codecs" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-blue-700 focus:ring-blue-600">
              </div>
              <div class="ml-3 text-sm">
                <label for="candidates" class="font-medium text-gray-700">H265</label>
                <p class="text-gray-500">An advanced compression standard with better quality at lower bitrates, supported only on mobile SDKs  </p>
              </div>
            </div>
          </div>
        </fieldset>
        {% endif %}
      </div>
    </div>
</div>
