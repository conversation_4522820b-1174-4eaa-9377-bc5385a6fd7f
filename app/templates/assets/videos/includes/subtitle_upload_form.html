{% load i18n widget_tweaks %}

<form id="form_element" method="POST" enctype="multipart/form-data">
    <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:py-6">
        <label for="file" class="flex items-center text-sm font-medium text-gray-500">Upload vtt file</label>
        <div class="mt-2 sm:col-span-2 sm:mt-0">
          <div class="flex">
            <div x-data="{ isFileSelected: false, fileName: '', fileInput: null }">
              <div
                class="relative bg-white py-2 px-3 border border-blue-gray-300 rounded-md shadow-sm flex items-center cursor-pointer hover:bg-blue-gray-50 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-offset-blue-gray-50 focus-within:ring-blue-500 login_screen_image-UppyModalOpenerBtn"
                x-show="!isFileSelected">
                <label for="fileInput" class="relative text-sm font-medium text-blue-gray-900 cursor-pointer">
                  <span>Choose file</span>
                  <input type="file" name="file" id="fileInput" class="hidden" x-on:change="
                  isFileSelected = true;
                  fileName = $event.target.files[0].name;
                  fileInput = $event.target;
                " accept=".vtt">
                </label>
              </div>
              <div class="flex items-center" x-show="isFileSelected">
                <svg class="h-5 w-5 flex-shrink-0 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd"
                    d="M15.621 4.379a3 3 0 00-4.242 0l-7 7a3 3 0 004.241 4.243h.001l.497-.5a.75.75 0 011.064 1.057l-.498.501-.002.002a4.5 4.5 0 01-6.364-6.364l7-7a4.5 4.5 0 016.368 6.36l-3.455 3.553A2.625 2.625 0 119.52 9.52l3.45-3.451a.75.75 0 111.061 1.06l-3.45 3.451a1.125 1.125 0 001.587 1.595l3.454-3.553a3 3 0 000-4.242z"
                    clip-rule="evenodd"></path>
                </svg>
                <span class="ml-2 text-sm font-medium text-blue-gray-900" x-text="fileName"></span>
                <button class="ml-2 text-sm font-medium text-blue-600 hover:underline" x-show="fileInput" x-on:click="
                  isFileSelected = false;
                  fileName = '';
                  fileInput.value = '';
                ">
                  Remove
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>


      <div class=" py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:pt-5">
          <dt class="flex items-center text-sm font-medium text-gray-500">Language</dt>
          <dd class="mt-1 flex text-sm text-gray-900 sm:col-span-2 sm:mt-0">
            <span class="flex-grow">
                <div class="flex items-baseline space-x-2">
                  {{ form.language | add_class:"flex items-center block sm:w-1/2 w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-blue-600 sm:text-sm sm:leading-6" }}
                      {% csrf_token %}
                      <button type="submit" class="rounded-md bg-blue-700 px-2.5 py-1.5 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-bg-blue-700 ml-2 mr-2">Upload</button>
                </div>
            </span>
          </dd>
      </div>
</form>
