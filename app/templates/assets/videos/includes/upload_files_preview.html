<div x-cloak="" class="mt-8" x-show="step==4">
  <div class="px-4 sm:px-0 sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-xl font-semibold text-gray-900">Files</h1>
    </div>
  </div>
  <div class="mt-6">
    <div x-cloak x-show="error" class="rounded-md bg-red-50 p-4 mt-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <!-- Heroicon name: mini/x-circle -->
          <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800" x-text="error"></h3>
        </div>
      </div>
    </div>

    <div x-cloak x-show="isAllVideosUploaded" class="rounded-md bg-green-50 p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-green-800">All videos are successfully uploaded</p>
        </div>
      </div>
    </div>


    <div class="max-h-[300px] overflow-y-auto">
      <div class="shadow-sm ring-1 ring-black ring-opacity-5">
        <table class="min-w-full border-separate" style="border-spacing: 0">
          <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="sticky top-0 z-10 border-b border-gray-300 bg-gray-50 bg-opacity-75 py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 backdrop-blur backdrop-filter sm:pl-6 lg:pl-8">Name</th>
            <th scope="col" class="sticky top-0 z-10 hidden border-b border-gray-300 bg-gray-50 bg-opacity-75 px-3 py-3.5 text-left text-sm font-semibold text-gray-900 backdrop-blur backdrop-filter sm:table-cell">Type</th>
            <th scope="col" class="sticky top-0 z-10 hidden border-b border-gray-300 bg-gray-50 bg-opacity-75 px-3 py-3.5 text-left text-sm font-semibold text-gray-900 backdrop-blur backdrop-filter lg:table-cell">Size</th>
            <th x-show="uploadStarted" scope="col" class="sticky top-0 z-10 hidden border-b border-gray-300 bg-gray-50 bg-opacity-75 px-3 py-3.5 text-left text-sm font-semibold text-gray-900 backdrop-blur backdrop-filter sm:table-cell">
              Status
            </th>
          </tr>
          </thead>
          <tbody class="bg-white">
          <template x-for="asset in assets">
            <tr>
              <td class="whitespace-normal border-b border-gray-200 py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8 max-w-sm truncate" x-text="asset.name "></td>
              <td class="whitespace-normal border-b border-gray-200 px-3 py-4 text-sm text-gray-500 hidden sm:table-cell" x-text="asset.file.type"></td>
              <td class="whitespace-normal border-b border-gray-200 px-3 py-4 text-sm text-gray-500 hidden lg:table-cell" x-text="humanizeBytes(asset.file.size)"></td>
              <td x-show="uploadStarted" class="whitespace-normal border-b border-gray-200 px-3 py-4 text-sm text-gray-500 hidden sm:table-cell">
                <div x-show="asset.progress!=100 && asset.status != 'Error'" class="w-full bg-gray-200 rounded-full dark:bg-gray-700">
                  <div class="bg-blue-700 text-xs font-medium text-blue-100 text-center p-0.5 leading-none rounded-full" :style="`width: ${asset.progress}%`" x-text="asset.progress + '%'"></div>
                </div>
                <p x-show="asset.progress==100">
                  <span class="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800" x-text="asset.status"></span>
                </p>
                <p x-show="asset.status=='Error'">
                  <span class="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 capitalize" x-text="asset.status"></span>
                </p>
              </td>
            </tr>
          </template>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
