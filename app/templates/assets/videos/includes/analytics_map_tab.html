{% load static %}
<script src="{% static 'js/d3.min.js' %}"></script>
<script src="{% static 'js/topojson.min.js' %}"></script>
<script src="{% static 'js/datamaps.world.min.js' %}"></script>
<div id="hs-users-datamap" class="w-full" style="height: 350px;"></div>
<style>
  .datamaps-subunit {
    transition: fill 1.5s ease-in-out !important;
  }
  .datamaps-subunit:hover {
    transition: none !important;
  }
</style>
<script>
  (function () {
    const countryCodeMap = {
      'United States': 'USA',
      'India': 'IND',
      'Canada': 'CAN',
      'China': 'CHN',
      'United Kingdom': 'GBR',
      'Brazil': 'BRA',
      'Indonesia': 'IDN',
      'Germany': 'DEU',
      'France': 'FRA',
      'Japan': 'JPN',
      'Australia': 'AUS',
      'South Korea': 'KOR',
      'Netherlands': 'NLD',
      'Spain': 'ESP',
      'Italy': 'ITA',
      'Singapore': 'SGP',
      'Mexico': 'MEX',
      'Argentina': 'ARG',
      'Colombia': 'COL',
      'Chile': 'CHL',
      'Peru': 'PER',
      'South Africa': 'ZAF',
      'Nigeria': 'NGA',
      'Egypt': 'EGY',
      'Kenya': 'KEN',
      'Morocco': 'MAR',
    };

    const alpha3ToAlpha2 = {
      'USA': 'us',
      'IND': 'in',
      'CAN': 'ca',
      'CHN': 'cn',
      'GBR': 'gb',
      'BRA': 'br',
      'IDN': 'id',
      'DEU': 'de',
      'FRA': 'fr',
      'JPN': 'jp',
      'AUS': 'au',
      'KOR': 'kr',
      'NLD': 'nl',
      'ESP': 'es',
      'ITA': 'it',
      'SGP': 'sg',
      'MEX': 'mx',
      'ARG': 'ar',
      'COL': 'co',
      'CHL': 'cl',
      'PER': 'pe',
      'ZAF': 'za',
      'NGA': 'ng',
      'EGY': 'eg',
      'KEN': 'ke',
      'MAR': 'ma',
    };

    function formatViewCount(count) {
      if (count >= 1000000) {
        return (count / 1000000).toFixed(1) + 'M';
      } else if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'k';
      } else {
        return count.toString();
      }
    }

    function getFillKey(views, maxViews) {
      if (maxViews === 0) return 'LOW';

      const percentage = (views / maxViews) * 100;

      if (percentage >= 80) return 'HIGH';
      if (percentage >= 60) return 'MEDIUM_HIGH';
      if (percentage >= 30) return 'MEDIUM';
      return 'LOW';
    }

    const rawData = {{ analytics_data|safe }};
    const locationData = {};
    rawData.forEach(item => {
      const location = item.location;
      if (location && location.trim() !== '' && location !== 'null') {
        if (!locationData[location]) {
          locationData[location] = 0;
        }
        locationData[location] += item.views;
      }
    });

    // Convert to array format for processing
    const aggregatedData = Object.entries(locationData).map(([location, views]) => ({
      location: location,
      views: views
    }));

    const maxViews = aggregatedData.length > 0 ? Math.max(...aggregatedData.map(item => item.views)) : 0;

    const dataSet = {};
    aggregatedData.forEach(country => {
      const countryCode = countryCodeMap[country.location];

      if (countryCode) {
        const alpha2Code = alpha3ToAlpha2[countryCode] || countryCode.toLowerCase();

        dataSet[countryCode] = {
          views: {
            value: formatViewCount(country.views)
          },
          fillKey: getFillKey(country.views, maxViews),
          short: alpha2Code
        };
      }
    });
      const dataMap = new Datamap({
        element: document.querySelector('#hs-users-datamap'),
        projection: 'mercator',
        responsive: true,
        fills: {
          defaultFill: '#f3f4f6',
          LOW: '#93c5fd',
          MEDIUM: '#3b82f6',
          MEDIUM_HIGH: '#2563eb',
          HIGH: '#1e40af'
        },
      data: {},
      geographyConfig: {
        borderColor: '#6b7280',
        highlightOnHover: true,
        highlightFillColor: false,
        highlightBorderColor: '#2563eb',
        highlightBorderWidth: 2,
        popupTemplate: function (geo, data) {
          return `<div class="bg-white rounded-xl shadow-xl w-37.5 p-3">
            <div class="flex mb-1">
              <div class="me-2">
                <div class="h-4 w-4 rounded-full bg-no-repeat bg-center bg-cover" style="background-image: url('https://flagcdn.com/h20/${data.short}.png')"></div>
              </div>
              <span class="text-sm font-medium">${geo.properties.name}</span>
            </div>
            <div class="flex items-center">
              <span class="text-xs text-gray-500 dark:text-neutral-500">Views:&nbsp;</span>
              <span class="text-sm font-medium text-gray-900">${data.views.value}</span>
            </div>
          </div>`;
        }
      },
      done: function(dataMap) {
        setTimeout(() => {
          dataMap.updateChoropleth(dataSet);
        }, 100);
      }
    });
    dataMap.addPlugin('update', function (_, mode) {
      this.options.fills = {
        defaultFill: '#f3f4f6',
        LOW: '#268bff',
        MEDIUM: '#1c69e8',
        MEDIUM_HIGH: '#1f56cf',
        HIGH: '#1e40af'
      };

      this.updateChoropleth(dataSet, {reset: true});
    });

    dataMap.update(localStorage.getItem('hs_theme'));

    window.addEventListener('on-hs-appearance-change', (evt) => {
      dataMap.update(evt.detail);
    });

    window.addEventListener('resize', function () {
      dataMap.resize();
    });
  })();
</script>
