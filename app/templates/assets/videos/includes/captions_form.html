{% load i18n widget_tweaks %}
<form id="form_element" method="POST" enctype="multipart/form-data" action="{% url 'upload_subtitle' asset_uuid=asset.uuid %}">
{% csrf_token %}
<div x-show="showCreateCaptionModel" class="relative z-50" aria-labelledby="modal-title" role="dialog"
  aria-modal="true">
  <div x-show="showCreateCaptionModel" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200"
    x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
    class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
   <div class="fixed inset-0 z-10 w-screen overflow-y-auto">
    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
      <div x-show="showCreateCaptionModel" x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
        x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        @click.away ="showCreateCaptionModel = false"
        class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl sm:p-6">
        <div class="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
          <button type="button" @click="showCreateCaptionModel = false"
            class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            <span class="sr-only">Close</span>
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
              aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="mt-3 sm:mt-0 sm:text-left">
          <h2 class="text-base font-semibold leading-7 text-gray-900">Upload Caption</h2>
          <p class="mt-1 text-sm leading-6 text-gray-600">Add captions, or subtitles to the videos. Supported formats
            is
            .VTT.</p>
          <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
            <div class="sm:col-span-3">
              <label for="first-name" class="block text-sm font-medium leading-6 text-gray-900">Name</label>
              <div class="mt-2">
                <input type="text" name="name" id="name" autocomplete="given-name" placeholder="Type caption name"
                  class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6">
              </div>
            </div>
            <div class="sm:col-span-3">
              <label for="language" class="block text-sm font-medium leading-6 text-gray-900">Language</label>
            <div class="mt-2">
              {{ subtitle_form.language | add_class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:max-w-xs sm:text-sm sm:leading-6" }}
            </div>
            </div>
            <div class="col-span-full border-b border-gray-900/10 pb-10"
              x-data="{ isFileSelected: false, fileName: '', fileInput: null }">
              <label for="file" class="block text-sm font-medium leading-6 text-slate-900">
                Caption
              </label>
              <div class="mt-2 flex items-center" x-show="!isFileSelected">
                <div class="relative select-none">
                  <input type="file" name="file" id="fileInput" required
                    class="peer absolute inset-0 h-full w-full rounded-md opacity-0 cursor-pointer" x-on:change="
                            isFileSelected = true;
                            fileName = $event.target.files[0].name;
                            fileInput = $event.target;
                          "
                    accept=".vtt">
                  <label for="fileInput"
                    class="rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
                    <span>Choose file</span>
                    <span class="sr-only"> caption file</span>
                  </label>
                </div>
              </div>
              <div class="mt-2 flex items-center" x-show="isFileSelected">
                <svg class="h-5 w-5 flex-shrink-0 text-gray-400" viewBox="0 0 20 20" fill="currentColor"
                  aria-hidden="true">
                  <path fill-rule="evenodd"
                    d="M15.621 4.379a3 3 0 00-4.242 0l-7 7a3 3 0 004.241 4.243h.001l.497-.5a.75.75 0 011.064 1.057l-.498.501-.002.002a4.5 4.5 0 01-6.364-6.364l7-7a4.5 4.5 0 016.368 6.36l-3.455 3.553A2.625 2.625 0 119.52 9.52l3.45-3.451a.75.75 0 111.061 1.06l-3.45 3.451a1.125 1.125 0 001.587 1.595l3.454-3.553a3 3 0 000-4.242z"
                    clip-rule="evenodd" />
                </svg>
                <div class="ml-4 flex min-w-0 flex-1 gap-2">
                  <span class="text-sm leading-6 font-medium text-gray-900" x-text="fileName"></span>
                  <button class="ml-6 text-sm font-medium leading-6 text-blue-600" x-show="fileInput" x-on:click="
                      isFileSelected = false;
                      fileName = '';
                      fileInput.value = '';
                    ">
                    Remove
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
            <button type="submit"
              class="inline-flex w-full justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 sm:ml-3 sm:w-auto">Upload</button>
            <button type="button" @click="showCreateCaptionModel = false"
              class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto">Cancel</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>