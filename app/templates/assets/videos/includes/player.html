<form id="playerForm"  method="post" action="{% url 'edit_video' asset_uuid=asset.uuid %}">
        {% csrf_token %}
        <div class="space-y-12">
            <div class="border-b border-gray-900/10 pb-12">
                <h2 class="mt-6 text-base font-semibold leading-7 text-gray-900">Controls</h2>
                <div class="mt-6 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">

                    {% include "partials/checkbox_input.html" with field=video_form.show_player_controls label="Enable player controls" description="Show the player controls for video playback." %}

                    {% include "partials/checkbox_input.html" with field=video_form.show_video_title label="Show video title" description="Display the title of the video on the player." %}

                    {% include "partials/checkbox_input.html" with field=video_form.show_play_button label="Show play button" description="Show the play button on the video player." %}

                    {% include "partials/checkbox_input.html" with field=video_form.show_progress_bar label="Show progress bar" description="Show the progress bar on the video player." %}

                    {% include "partials/checkbox_input.html" with field=video_form.show_volume_bar label="Show Volume bar" description="Show the Volume bar on the video player." %}

                    {% include "partials/checkbox_input.html" with field=video_form.show_speed_control label="Show speed control" description="Allow viewers to change the playback speed." %}

                    {% include "partials/checkbox_input.html" with field=video_form.show_full_screen_control label="Show Full Screen" description="Allow viewers to view the video in full-screen mode." %}

                    {% include "partials/checkbox_input.html" with field=video_form.show_video_quality_control label="Show quality controls" description="Allow viewers to change the video quality." %}

                </div>

                <h2 class="mt-6 text-base font-semibold leading-7 text-gray-900">Behavior</h2>
                <div class="mt-6 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">

                    {% include "partials/checkbox_input.html" with field=video_form.show_subtitles label="Enable subtitles" description="Show subtitles for the video." %}

                    {% include "partials/checkbox_input.html" with field=video_form.show_picture_in_picture_control label="Enable picture-in-picture mode" description="Allow the video to be viewed in a small overlay window." %}

                    {% include "partials/checkbox_input.html" with field=video_form.autoplay_enabled label="Enable autoplay" description="Automatically start playback when the video is loaded." %}

                    {% include "partials/checkbox_input.html" with field=video_form.background_mode_enabled label="Enable background mode" description="Disables all player controls, mutes the video, loops it, and starts playback automatically." %}

                    {% include "partials/checkbox_input.html" with field=video_form.loop_enabled label="Enable loop playback" description="Automatically replay the video when it ends." %}

                    {% include "partials/checkbox_input.html" with field=video_form.muted_on_start label="Mute video on start" description="Start the video with the sound muted." %}

                    <!-- Default Video Quality -->
                    <div class="sm:col-span-3">
                        <label for="{{ video_form.default_quality.id_for_label }}" class="block text-sm font-medium leading-6 text-gray-900">
                            Default video quality
                        </label>
                        <div class="mt-2">
                            <select id="{{ video_form.default_quality.id_for_label }}" name="{{ video_form.default_quality.html_name }}" class="mt-2 block w-1/2 rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6">
                                {% for value, text in video_form.default_quality.field.choices %}
                                    <option value="{{ value }}" {% if video_form.default_quality.value == value %}selected{% endif %}>
                                        {{ text }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <h2 class="mt-6 text-base font-semibold leading-7 text-gray-900">Appearance</h2>
                <div class="mt-6 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">

                    {% include "partials/color_picker.html" with field=video_form.primary_color label="Primary color" picker_id="primary_color_picker" input_id="primary_color_input" %}

                    {% include "partials/color_picker.html" with field=video_form.icons_color label="Icons color" picker_id="icons_color_picker" input_id="icons_color_input" %}

                    {% include "partials/color_picker.html" with field=video_form.accent_color label="Accent color" picker_id="accent_color_picker" input_id="accent_color_input" %}

                    {% include "partials/color_picker.html" with field=video_form.background_color label="Background color" picker_id="background_color_picker" input_id="background_color_input" %}

                <!-- Buttons location -->
                <div class="sm:col-span-3">
                    <label for="{{ video_form.play_button_position.id_for_label }}" class="block text-sm font-medium leading-6 text-gray-900">
                        Play button position
                    </label>
                    <div class="mt-2">
                        <select id="{{ video_form.play_button_position.id_for_label }}" name="{{ video_form.play_button_position.html_name }}" class="mt-2 block w-1/2 rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6">
                            {% for value, text in video_form.play_button_position.field.choices %}
                                <option value="{{ value }}" {% if video_form.play_button_position.value == value %}selected{% endif %}>
                                    {{ text }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>


                  <!-- Default subtitle language -->
                  <div class="sm:col-span-3">
                    <label for="{{ video_form.default_subtitle_language.id_for_label }}" class="block text-sm font-medium leading-6 text-gray-900">
                        Default subtitle language
                    </label>
                    <div class="mt-2">
                        <select id="{{ video_form.default_subtitle_language.id_for_label }}" name="{{ video_form.default_subtitle_language.html_name }}" class="mt-2 block w-1/2 rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6">
                            {% for value, text in video_form.default_subtitle_language.field.choices %}
                                <option value="{{ value }}" {% if video_form.default_subtitle_language.value == value %}selected{% endif %}>
                                    {{ text }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                </div>
            </div>

            <div class="mt-6 flex items-center justify-end gap-x-6">
                <button type="button" class="text-sm font-semibold leading-6 text-gray-900" onclick="window.location.reload();">Cancel</button>
                <button type="submit" class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Save</button>
            </div>
        </div>
    </form>
