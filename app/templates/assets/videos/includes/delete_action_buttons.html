{% load static i18n %}

<div class="inline-flex space-x-4">
  {% if  asset.video.get_status_display  != 'Deleting' %}
  <form method="post" action="{% url 'deleted_assets' %}?{% get_updated_query_string name='asset' value=asset.uuid %}">
        {% csrf_token %}
    <input type="hidden" name="action" value="undelete">
    <button type="submit" class="text-sm font-medium text-blue-600 hover:text-blue-500">
        {% translate "Restore" %}
    </button>
  </form>

    <div class="flex border-l border-gray-300 pl-4">
        {% csrf_token %}
        <input type="hidden" name="action" value="delete">
        <button type="submit" class="text-sm font-medium text-red-600 hover:text-red-500" @click="selectedAssetID='{{ asset.uuid }}'; showDeleteModal = true;">
          {% translate "Delete" %}
        </button>
    </div>
    {% else %}
    <div class="whitespace-nowrap text-sm text-gray-500">Getting deleted...</div>
    {% endif %}
</div>
