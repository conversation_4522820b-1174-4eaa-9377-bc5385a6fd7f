<div class="text-center py-12">
    <h3 class="mt-2 text-sm font-medium text-gray-900">Move this asset {{ source |truncatechars:60 }}</h3>
    <p class="mt-1 text-sm text-gray-500">To this new location</p>
    <div class="mt-6">
      {% if request.resolver_match.view_name == "asset_move" %}
          <form  class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-700 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 sm:w-auto" method="post" action="{% url 'asset_move' %}?{% get_updated_query_string name='folder' value=folder.uuid %}">
            {% csrf_token %}
            <button type="submit" >Move Here</button>
          </form>
        {% endif %}
      {% if request.resolver_match.view_name == "live_stream_move" %}
          <form  class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-700 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 sm:w-auto" method="post" action="{% url 'live_stream_move' %}?{% get_updated_query_string name='folder' value=folder.uuid %}">
            {% csrf_token %}
            <button type="submit" >Move Here</button>
          </form>
        {% endif %}
    </div>
  </div>
