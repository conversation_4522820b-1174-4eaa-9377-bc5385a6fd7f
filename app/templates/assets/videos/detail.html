{% extends "layouts/top_searchbar_layout.html" %}
{% block extra_head %}
{% load static %}
<link rel="stylesheet" href="{% static 'css/scroll.css' %}">
<script src="{% static 'js/echarts.min.js' %}"></script>
<script src="{% static 'js/d3.min.js' %}"></script>
<script src="{% static 'js/topojson.min.js' %}"></script>
<script src="{% static 'js/datamaps.world.min.js' %}"></script>
{% endblock extra_head %}

{% block main_content %}
<div class="" x-data="{ showAddFolderModal: false ,active_tab: '{% if asset.video.thumbnails %}thumbnails{% else %}embed_link{% endif %}' ,showCreateCaptionModel: false}">
  {% include "./includes/detail_heading.html" %}
  <div class="grid items-start grid-cols-1 mt-6 xl:grid-cols-3 2xl:grid-cols-4 gap-x-8 gap-y-6 2xl:gap-y-2">
    {% include "./includes/video_preview.html" %}
    {% include "./includes/basic_info.html" %}
    {% include "./includes/detail_tabs.html" %}
    {% include "./includes/activity_log.html" %}
  </div>
  <div x-show="active_tab === 'player'" x-effect="if (active_tab === 'player') {
        initializeControls();
        initializePickers(); }">
  </div>
{% endblock %}
{% block extra_body %}
  {{block.super}}
  {% include "partials/copy_to_clipboard.html"  %}
  {% include "partials/color_picker_script.html" %}
  {% endblock %}
