{% extends "layouts/base.html" %}

{% load static i18n %}

{% block extra_head %}
{% load static %}
{% endblock extra_head %}

{% block content %}

{% if is_video %}
{% include "assets/includes/heading.html" with title="Video Asset" %}
  <div class="grid items-start grid-cols-1 mt-6 xl:grid-cols-3 2xl:grid-cols-4 gap-x-8 gap-y-6 2xl:gap-y-2">
    <nav aria-label="Progress">
      <ol role="list" class="overflow-hidden pt-6">
        {% include "./includes/status_check.html" with status=check_wasabi_status label="Storage provider" %}
        {% include "./includes/status_check.html" with status=check_cdn_status label="CDN" %}
        {% include "./includes/status_check.html" with status=check_license_server label="License Server" last=True %}
        <li class="relative pb-10">
          <form id="validate-access-token-form" method="post" action="">
            {% csrf_token %}
            <div class="flex items-center">
                <label for="access_key" class="mr-4 text-sm font-bold text-black">Access Key:</label>
                <input
                    type="text"
                    id="access_key"
                    name="access_key"
                    placeholder="Enter access key"
                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    required
                    value="{{ access_key }}"
                />
                <button
                    type="submit"
                    class="ml-4 inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                >
                    Validate
                </button>
            </div>
          </form>
          {% include "./includes/token_validation_check.html" with status=access_token_in_cache label="Available in Redis" %}
          {% include "./includes/token_validation_check.html" with status=access_token_in_db label="Available in DB" last=True %}
        </li>
      </ol>
    </nav>
  </div>
{% else %}
{% include "assets/includes/heading.html" with title="Live stream Asset" %}
  <div class="grid items-start grid-cols-1 mt-6 xl:grid-cols-3 2xl:grid-cols-4 gap-x-8 gap-y-6 2xl:gap-y-2">
    <nav aria-label="Progress">
      <ol role="list" class="overflow-hidden pt-6">
        {% include "./includes/status_check.html" with status=check_cdn_status label="CDN" %}
        {% include "./includes/status_check.html" with status=check_haproxy_status label="HA proxy" %}
        {% include "./includes/status_check.html" with status=check_live_server_status label="Live Server" last=True %}
      </ol>
    </nav>
  </div>
{% endif %}
{% endblock content %}
