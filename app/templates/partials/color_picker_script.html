<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@simonwep/pickr/dist/themes/classic.min.css" />
<script src="https://cdn.jsdelivr.net/npm/@simonwep/pickr"></script>
    <style>
        .pcr-button {
            border: 1px solid #efefef;
        }
        .pickr .pcr-button::after {
            border-radius: 0;
        }
        .disabled-checkbox {
            background-color: #f9fafb;
            border-color: #d1d5db;
            cursor: not-allowed;
        }
        .disabled-label {
            color: #9ca3af;
            cursor: not-allowed;
        }
    </style>
    <script>
        // Define the functions globally
        window.initializeControls = function() {
            const dependentControls = [
                "{{ video_form.show_play_button.id_for_label }}",
                "{{ video_form.show_progress_bar.id_for_label }}",
                "{{ video_form.show_volume_bar.id_for_label }}",
                "{{ video_form.show_speed_control.id_for_label }}",
                "{{ video_form.show_full_screen_control.id_for_label }}",
                "{{ video_form.show_picture_in_picture_control.id_for_label }}",
                "{{ video_form.show_subtitles.id_for_label }}",
                "{{ video_form.show_video_quality_control.id_for_label }}",
            ];

            const updateDependentControls = () => {
                const isChecked = document.querySelector('#{{ video_form.show_player_controls.id_for_label }}').checked;
                dependentControls.forEach(id => {
                    const checkbox = document.querySelector(`#${id}`);
                    const label = document.querySelector(`label[for=${id}]`);
                    if (isChecked) {
                        checkbox.removeAttribute('disabled');
                        checkbox.classList.remove('disabled-checkbox');
                        label.classList.remove('disabled-label');
                    } else {
                        checkbox.setAttribute('disabled', 'true');
                        checkbox.classList.add('disabled-checkbox');
                        label.classList.add('disabled-label');
                        checkbox.checked = false;
                    }
                });
            };

            document.querySelector('#{{ video_form.show_player_controls.id_for_label }}').addEventListener('change', updateDependentControls);
            updateDependentControls();
        };

        window.initializePickers = function() {
            const pickers = [
                {
                    element: '#primary_color_picker',
                    input: '#primary_color_input',
                    defaultColor: '{{ video_form.primary_color.value }}' || '#03a4eb'
                },
                {
                    element: '#icons_color_picker',
                    input: '#icons_color_input',
                    defaultColor: '{{ video_form.icons_color.value }}' || '#fff'
                },
                {
                    element: '#accent_color_picker',
                    input: '#accent_color_input',
                    defaultColor: '{{ video_form.accent_color.value }}' || '#03a4eb'
                },
                {
                    element: '#background_color_picker',
                    input: '#background_color_input',
                    defaultColor: '{{ video_form.background_color.value }}' || '#000000'
                }
            ];

            pickers.forEach(pickerConfig => {
                const colorPickerElement = document.querySelector(pickerConfig.element);
                const colorInputElement = document.querySelector(pickerConfig.input);
                const pickr = Pickr.create({
                    el: colorPickerElement,
                    theme: 'classic',
                    default: pickerConfig.defaultColor,
                    lockOpacity: true,
                    comparison: false,
                    swatches: [
                        'rgba(244, 67, 54, 1)',
                        'rgba(233, 30, 99, 1)',
                        'rgba(156, 39, 176, 1)',
                        'rgba(103, 58, 183, 1)',
                        'rgba(63, 81, 181, 1)',
                        'rgba(33, 150, 243, 1)',
                        'rgba(3, 169, 244, 1)',
                        'rgba(0, 188, 212, 1)',
                        'rgba(0, 150, 136, 1)',
                        'rgba(76, 175, 80, 1)',
                        'rgba(139, 195, 74, 1)',
                        'rgba(205, 220, 57, 1)',
                        'rgba(255, 235, 59, 1)',
                        'rgba(255, 193, 7, 1)'
                    ],
                    components: {
                        preview: true,
                        opacity: false,
                        hue: true,
                        interaction: {
                            hex: false,
                            rgba: false,
                            hsla: false,
                            hsva: false,
                            cmyk: false,
                            input: true,
                            clear: false,
                            save: false
                        }
                    }
                });

                pickr.on('change', (color, source, instance) => {
                    const hexaColor = color.toHEXA().toString();
                    colorInputElement.value = hexaColor;
                });
            });
        };
    </script>
