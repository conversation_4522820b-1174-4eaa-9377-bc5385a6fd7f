<script>
    function clipboardComponent() {

      return {
        showCopiedTooltip: false,


        copyToClipboard() {
          const textarea = document.createElement('textarea');
          textarea.value = this.$refs.contentToCopy.innerText;
          document.body.appendChild(textarea);
          textarea.select();
          document.execCommand('copy');
          document.body.removeChild(textarea);
          this.showCopiedTooltip = true;

          setTimeout(() => {
            this.showCopiedTooltip = false;
          }, 2000);
        }
      };
    }
  </script>
