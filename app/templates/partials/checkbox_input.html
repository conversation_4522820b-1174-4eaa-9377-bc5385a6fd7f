{% load i18n %}
{% load static %}
{% load i18n widget_tweaks %}
<div class="sm:col-span-3">
    <div class="relative flex gap-x-3">
        <div class="flex h-6 items-center">
            <input id="{{ field.id_for_label }}"
                   name="{{ field.html_name }}"
                   type="checkbox"
                   class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                   {% if field.value %} checked {% endif %}/>
        </div>
        <div class="text-sm leading-6">
            <label for="{{ field.id_for_label }}" class="font-medium text-gray-900">{{ label }}</label>
            <p class="text-gray-500">{{ description }}</p>
        </div>
    </div>
</div>
