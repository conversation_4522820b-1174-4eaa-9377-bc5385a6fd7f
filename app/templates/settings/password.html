{% extends "./base.html" %}
{% load i18n %}

{% block settings %}
<div class="mt-8 sm:w-full lg:max-w-md">
  <form class="space-y-6" action="{% url 'password_settings' %}" method="POST">
    {% csrf_token %}
    <div>
      <label for="current_password" class="block text-sm font-medium leading-6 text-gray-900">{% translate "Current Password" %}</label>
      <div class="mt-2">
        <input id="current_password" name="old_password" type="password" autocomplete="current-password" required
          class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6">
      </div>
    </div>
    <div>
      <label for="new_password1" class="block text-sm font-medium leading-6 text-gray-900">{% translate "New Password" %}</label>
      <div class="mt-2">
        <input id="new_password1" name="new_password1" type="password" autocomplete="new-password" required
          class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6">
      </div>
    </div>
    <div>
      <button type="submit"
        class="flex w-full justify-center rounded-md bg-blue-700 py-2 px-3 text-sm font-semibold text-white shadow-sm hover:bg-blue-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">{% translate "Change Password" %}</button>
    </div>
  </form>
</div>
{% endblock %}
