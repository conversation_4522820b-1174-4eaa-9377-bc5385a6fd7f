<div x-show="zoomSettingsModel" class="relative z-50" aria-labelledby="modal-title" role="dialog"
  aria-modal="true">
  <div x-show="zoomSettingsModel" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200"
    x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
    class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
  <div class="fixed inset-0 z-10 w-screen max-h-screen overflow-y-auto">
    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-6">
      <div x-show="zoomSettingsModel" x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
        x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        @click.away ="zoomSettingsModel = false"
        class="relative transform overflow-visible rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 w-full max-w-[90%] sm:max-w-2xl sm:p-6">
        <div class="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
          <button type="button" @click="zoomSettingsModel = false"
            class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            <span class="sr-only">Close</span>
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
              aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <form method="POST" action="{% url 'zoom_account_update' %}">
          {% csrf_token %}
        <div class="mt-3 sm:mt-0 sm:text-left">
          <h2 class="text-base font-semibold leading-7 text-gray-900">Zoom Settings</h2>
          <div class="mt-3 space-y-4" x-cloak>
            <!-- List -->
            <div class="divide-y divide-dashed divide-gray-200">
                <!-- List Item -->
                <div class="py-3 flex flex-col space-y-1 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:space-y-0">
                  <span class="block text-sm font-medium text-gray-500">
                    Email
                  </span>

                  <div class="flex gap-2">
                    <span id="hs-pro-ccdcfn" class="text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ zoom_account.email }}</span>
                    <button type="button" class="js-clipboard [--is-toggle-tooltip:false] hs-tooltip w-5 h-5 shrink-0 inline-flex justify-center items-center gap-x-2 rounded-md text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden sm:focus:bg-gray-100"
                      data-clipboard-target="#hs-pro-ccdcfn"
                      data-clipboard-action="copy"
                      data-clipboard-success-text="Email copied">
                      <svg class="js-clipboard-default shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"/><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/></svg>
                      <svg class="js-clipboard-success hidden w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                      <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-10 py-1 px-2 bg-gray-900 text-xs font-medium text-white rounded-md shadow-2xs" role="tooltip">
                        <span class="js-clipboard-success-text">Copy Email</span>
                      </span>
                    </button>
                  </div>
                </div>
                <!-- End List Item -->

                <!-- List Item -->
                <div class="py-3 flex flex-col space-y-1 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:space-y-0">
                  <span class="block text-sm font-medium text-gray-500">
                    Zoom User ID
                  </span>

                  <div class="flex gap-2">
                    <span id="hs-pro-ccdcem" class="text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ zoom_account.zoom_user_id }}</span>
                    <button type="button" class="js-clipboard [--is-toggle-tooltip:false] hs-tooltip w-5 h-5 shrink-0 inline-flex justify-center items-center gap-x-2 rounded-md text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden sm:focus:bg-gray-100"
                      data-clipboard-target="#hs-pro-ccdcem"
                      data-clipboard-action="copy"
                      data-clipboard-success-text="Zoom User ID copied">
                      <svg class="js-clipboard-default shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"/><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/></svg>
                      <svg class="js-clipboard-success hidden w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                      <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-10 py-1 px-2 bg-gray-900 text-xs font-medium text-white rounded-md shadow-2xs" role="tooltip">
                        <span class="js-clipboard-success-text">Copy Zoom User ID</span>
                      </span>
                    </button>
                  </div>
                </div>
                <!-- End List Item -->

                <!-- List Item -->
                <div class="py-3 flex flex-col space-y-1 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:space-y-0">
                  <span class="block text-sm font-medium text-gray-500">
                    Token expires on
                  </span>

                  <div class="flex gap-2">
                    <span class="text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ zoom_account.expires_at|date:"M d, Y H:i" }}</span>
                  </div>
                </div>
                <!-- End List Item -->

                <!-- List Item -->
                <div class="py-3 flex flex-col space-y-1 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:space-y-0">
                  <span class="block text-sm font-medium text-gray-500">
                    Connected on
                  </span>

                  <div class="flex gap-2">
                    <span class="text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ zoom_account.created|date:"M d, Y H:i" }}</span>
                  </div>
                </div>
                <!-- End List Item -->

                <!-- List Item -->
                <div class="py-3 flex flex-col space-y-1 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:space-y-0">
                  <span class="block text-sm font-medium text-gray-500">
                    DRM Protection
                  </span>

                  <div class="flex items-center gap-2 sm:col-span-2">
                    <input type="checkbox" name="enable_drm" {% if zoom_account.enable_drm %}checked{% endif %}
                    class="cursor-pointer shrink-0 h-4 w-4 mt-0.5 border-gray-300 rounded border-gray-300 text-blue-600 focus:ring-blue-600 checked:border-blue-500 disabled:opacity-50 disabled:pointer-events-none" id="zoom-drm-checkbox">
                    <label for="zoom-drm-checkbox" class="cursor-pointer text-sm text-gray-900 sm:col-span-2 sm:mt-0">Enable DRM for imported recordings</label>
                  </div>
                </div>
                <!-- End List Item -->

                <!-- List Item -->
                <div class="py-3 flex flex-col space-y-1 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:space-y-0 sm:items-center">
                  <span class="block text-sm font-medium text-gray-500">
                    Default Folder
                  </span>

                  <div class="grid grid-cols-3 col-span-2 gap-2">
                    <!-- Select -->
                    <div class="col-span-2">
                      {% include "widgets/searchable_select.html" with name="import_destination" placeholder="Select folder" url="/api/v1/"|add:zoom_account.organization.uuid|add:"/assets/folders/" labelField="title" valueField="uuid" queryParam="q" pageSize=60 value=zoom_account.import_destination.uuid|default:"" label=zoom_account.import_destination.title|default:"" %}
                    </div>
                    <!-- End Select -->
                    <a href="{% url 'assets' %}?folder={{ zoom_account.import_destination.uuid }}" target="_blank" class="whitespace-nowrap col-span-1 inline-flex items-center gap-x-2 text-sm font-semibold rounded-lg text-blue-600 hover:text-blue-800 focus:outline-hidden focus:text-blue-800 disabled:opacity-50 disabled:pointer-events-none">
                      View
                    </a>
                  </div>
                </div>
                <!-- End List Item -->
              </div>
              <!-- End List -->
              </div>
              <!-- End List -->


            </div>

          <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
            <button type="submit"
              class="inline-flex w-full justify-center rounded-md bg-blue-700 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 sm:ml-3 sm:w-auto">Save Changes</button>
            <button type="button" @click="zoomSettingsModel = false"
              class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto">Cancel</button>
          </div>
        </div>
        </form>
      </div>
    </div>
  </div>
