<script>
  var edit = document.getElementById("edit_button");
  var cancel=document.getElementById("cancel_button")
  var displayElement = document.getElementById("display_element");
  var formElement = document.getElementById("form_element");

  function showForm() {
    if (edit.textContent === "Edit") {
      edit.style.display = "none";
      displayElement.style.display = "none";
      formElement.style.display = "block";
    }
  }

  function revertToOriginal(event) {
    var target = event.target;
    if (target === cancel) {
      edit.style.display = "block";
      displayElement.style.display = "block";
      formElement.style.display = "none";
    }
  }

  edit.addEventListener("click", showForm);
  cancel.addEventListener("click", revertToOriginal);
</script>
