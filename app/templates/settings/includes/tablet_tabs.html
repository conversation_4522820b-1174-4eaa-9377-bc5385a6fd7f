{% load i18n %}

<!-- Tabs -->
<div class="lg:hidden">
  <label for="selected-tab" class="sr-only">Select a tab</label>
  <select id="selected-tab" name="selected-tab"
    class="mt-1 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
    onchange="window.location.href=this.value;">
    <option value="{% url 'general_settings' %}"  {% if request.resolver_match.view_name == 'general_settings' %} selected {% endif %}>
      {% translate "General" %}</option>
      <option value="{% url 'usages' %}"   {% if request.resolver_match.view_name == 'usages' %} selected {% endif %}>
        {% translate "Usages" %}</option>
        <option value="{% url 'password_settings' %}"  {% if request.resolver_match.view_name == 'password_settings' %} selected {% endif %}>
          {% translate "Password" %} </option>
          <option value="{% url 'video_settings' %}"  {% if request.resolver_match.view_name == 'videos' %} selected {% endif %}>
            {% translate "Videos" %} </option>
            <option value="{% url 'player_settings' %}"  {% if request.resolver_match.view_name == 'player_settings' %} selected {% endif %}>
              {% translate "Player" %} </option>
  </select>
</div>
