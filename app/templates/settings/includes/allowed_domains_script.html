<script>
    const addAllowedDomains = () => ({
      domains: {{ object.allowed_domains_for_embedding|safe }},
      newDomain: '',

      addDomain() {
        if (this.newDomain.trim() !== '') {
          const newDomainTrimmed = this.newDomain.trim();
          const domainExists = this.domains.some(domain => domain === newDomainTrimmed);
          if (!domainExists) {
            this.domains.push(newDomainTrimmed);
            this.newDomain = '';
            this.submitForm();
          }
        }
      },

      removeDomain(domain) {
        this.domains = this.domains.filter(item => item !== domain);
        this.submitForm();
      },

      submitForm() {
        const hiddenInput = this.$refs.domainform.elements.allowed_domains_for_embedding;
        hiddenInput.value = this.domains.join(",");
        this.$refs.domainform.submit();
      }
    });

    window.addEventListener('DOMContentLoaded', () => {
      Alpine.data('addAllowedDomains', addAllowedDomains);
    });
  </script>
