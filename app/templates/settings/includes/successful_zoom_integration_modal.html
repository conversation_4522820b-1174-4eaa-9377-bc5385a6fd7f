<div x-show="zoomSuccessModel" class="relative z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true" x-cloak>
  <div x-show="zoomSuccessModel" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200"
    x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
    class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
  <div class="fixed inset-0 z-10 w-screen overflow-y-auto">
    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
      <div x-show="zoomSuccessModel" x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
        x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        @click.away ="zoomSuccessModel = false"
        class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl sm:p-6">

        <div class="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
          <button type="button" @click="zoomSuccessModel = false"
            class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            <span class="sr-only">Close</span>
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
              aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="mt-3 sm:mt-0 sm:text-left">
          <span class="shrink-0 w-14 h-14 sm:w-16 sm:h-16 mx-auto flex justify-center items-center border-2 border-green-500 text-green-500 rounded-full">
              <svg class="shrink-0 w-8 h-8" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6 9 17l-5-5"></path></svg>
          </span>
          <h2 class="text-center mt-2 text-base font-semibold leading-7 text-gray-900">Zoom Connected Successfully</h2>
          <p class="mt-2 text-sm leading-6 text-gray-600">
            Your Zoom account has been connected successfully.Recordings will be imported automatically into the <strong>Zoom Recordings</strong> default folder.You can change this folder anytime by going to <strong>View integration</strong>.
          </p>

          <div class="mt-5 sm:mt-6 sm:flex sm:flex-row-reverse">
            <button type="button" @click="zoomSuccessModel = false"
              class="inline-flex w-full justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 sm:ml-3 sm:w-auto">
              Done
            </button>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
