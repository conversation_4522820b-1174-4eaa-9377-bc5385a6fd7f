<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('integrations', () => ({
            zoomSuccessModel: false,
            zoomSettingsModel: false,

            init() {
                this.checkZoomConnectionSuccess();
            },

            checkZoomConnectionSuccess() {
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.get('zoom_connected') === 'true') {
                    this.zoomSuccessModel = true;
                    this.removeQueryParams();
                }
            },

            removeQueryParams() {
                window.history.replaceState({}, document.title, window.location.pathname);
            },

        }));
    });
</script>
