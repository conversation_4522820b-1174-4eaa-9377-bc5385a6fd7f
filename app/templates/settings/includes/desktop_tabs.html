{% load i18n %}

<div class="hidden lg:block">
  <div class="border-b border-gray-200">
    <nav class="flex -mb-px space-x-8">

      <a href="{% url 'general_settings' %}" class="px-1 py-4 text-sm font-medium border-b-2
      {% if request.resolver_match.view_name == 'general_settings' %}text-blue-700 border-blue-600 {% else %} border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 {% endif %} whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium">{% translate "General" %}</a>

      <a href="{% url 'usages' %}"  class="px-1 py-4 text-sm font-medium border-b-2
      {% if request.resolver_match.view_name == 'usages' %}text-blue-700 border-blue-600 {% else %} border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 {% endif %} whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium">{% translate "Usages" %}</a>

      <a href="{% url 'password_settings' %}"  class="px-1 py-4 text-sm font-medium border-b-2
      {% if request.resolver_match.view_name == 'password_settings' %}text-blue-700 border-blue-600 {% else %} border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 {% endif %} whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium">{% translate "Password" %}</a>

      <a href="{% url 'video_settings' %}"  class="px-1 py-4 text-sm font-medium border-b-2
      {% if request.resolver_match.view_name == 'video_settings' %}text-blue-700 border-blue-600 {% else %} border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 {% endif %} whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium">{% translate "Videos" %}</a>

      <a href="{% url 'player_settings' %}"  class="px-1 py-4 text-sm font-medium border-b-2
      {% if request.resolver_match.view_name == 'player_settings' %}text-blue-700 border-blue-600 {% else %} border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 {% endif %} whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium">{% translate "Player" %}</a>

      <a href="{% url 'integrations_settings' %}"  class="px-1 py-4 text-sm font-medium border-b-2
      {% if request.resolver_match.view_name == 'integrations_settings' %}text-blue-700 border-blue-600 {% else %} border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 {% endif %} whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium">{% translate "Integrations" %}</a>
    </nav>
  </div>
</div>
