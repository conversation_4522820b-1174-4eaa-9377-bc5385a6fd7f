{% extends "./base.html" %}
{% load i18n widget_tweaks %}

{% block settings %}
<div class="mt-8 sm:w-full lg:max-w-md" x-data="addAllowedDomains">
  <div>
      <h2 class="mt-2 text-base font-semibold leading-6 text-gray-900">Where can the videos be embedded?</h2>
      <p class="mt-1 text-sm text-gray-500">To play only on specific domains, enter domain names here.</p>
      <form x-ref="domainform" action="{% url 'video_settings' %}"method="POST" class="mt-6 flex">
        {% csrf_token %}
        {{form.allowed_domains_for_embedding|add_class:"hidden"}}
        <input x-model="newDomain" type="text" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" placeholder="Enter a domain" />
        <button type="submit" class="ml-4 flex-shrink-0 rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" x-on:click="addDomain()">Add domain</button>
      </form>
      <div class="mt-1 text-sm text-red-500">{{form.allowed_domains_for_embedding.errors}}</div>
  </div>
  {% if object.allowed_domains_for_embedding%}
  <div class="mt-6">
    <h3 class=" text-sm font-medium text-gray-500">Domains Allowed for Video Embedding</h3>
    <ul role="list" class="mt-2 divide-y divide-gray-200 border-b border-t border-gray-200">
      {% for domain in object.allowed_domains_for_embedding %}
      <li class="flex items-center justify-between py-3 ">
        <p class="text-sm font-medium text-gray-900">{{ domain }}</p>
        <button type="submit" class="ml-6 rounded-md bg-white text-sm font-medium text-indigo-600 hover:text-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                x-on:click="removeDomain('{{ domain }}')">Remove<span class="sr-only">{{ domain }}</span></button>
      </li>
      {% endfor %}
    </ul>
  </div>
  {% endif %}
</div>
{% endblock %}
{% block extra_body %}
{% include "./includes/allowed_domains_script.html"  %}
{% endblock %}
