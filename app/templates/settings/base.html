{% extends "layouts/base.html" %}
{% load i18n %}

{% block html_class %}h-full bg-white{% endblock html_class %}
{% block body_class %}h-full{% endblock body_class %}
{% block head_title %}{% translate "Settings" %}{% endblock %}

{% block content %}
{% translate "Settings" as title %}
<div x-cloak x-data="{open: false}"
  @keydown.window.escape="open = false;">
  {% include "includes/sidebar.html" %}
  <div class="lg:pl-64">
    <div class="lg:px-8">
      <div class="mx-auto flex flex-col max-w-5xl 2xl:max-w-screen-2xl">
        {% include "includes/topbar.html" %}
        <main class="flex-1">
          <div class="relative mx-auto max-w-5xl 2xl:max-w-screen-2xl">
            <div class="pt-10 pb-16">
              {% include "./includes/heading.html" %}
              <div class="px-4 sm:px-6 lg:px-0">
                <div class="py-6">
                    {% include "./includes/desktop_tabs.html" %}
                    {% include "./includes/tablet_tabs.html" %}
                  {% block settings %} {% endblock %}
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
