{% extends "./base.html" %}
{% load i18n %}

{% block extra_head %}
    <script type="module">
      import preline from 'https://cdn.jsdelivr.net/npm/preline@3.1.0/+esm'
    </script>
    <script src="https://preline.co/assets/vendor/clipboard/dist/clipboard.min.js"></script>
    <script src="https://preline.co/assets/js/hs-copy-clipboard-helper.js"></script>
{% endblock extra_head%}

{% block settings %}
<div class="mt-10" x-data="integrations">
    <div class="grid sm:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-5">
      <!-- Card -->
      <div class="p-5 space-y-4 flex flex-col bg-white border border-gray-200 rounded-xl">
        <!-- Header -->
        <div class="flex justify-between">
          <div class="flex flex-col justify-center items-center size-9.5 border border-gray-200 rounded-lg">
            <svg class="w-9 h-9" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg" aria-label="Zoom" role="img">
              <rect width="512" height="512" rx="15%" fill="#2D8CFF" />
              <path fill="#ffffff" d="M428 357c8 2 15-2 19-8 2-3 2-8 2-19V179c0-11 0-15-2-19-3-8-11-11-19-8-21 14-67 55-68 72-.8 3-.8 8-.8 15v38c0 8 0 11 .8 15 1 8 4 15 8 19 12 9 52 45 61 45zM64 187c0-15 0-23 3-27 2-4 8-8 11-11 4-3 11-3 27-3h129c38 0 57 0 72 8 11 8 23 15 30 30 8 15 8 34 8 72v68c0 15 0 23-3 27-2 4-8 8-11 11-4 3-11 3-27 3H174c-38 0-57 0-72-8-11-8-23-15-30-30-8-15-8-34-8-72z"/>
            </svg>
          </div>

          <div>
            {% if not zoom_account.is_connected %}
              <a href="{% url 'zoom_authorize' %}" class="relative py-2 px-3 flex items-center justify-center sm:justify-start border border-gray-200 cursor-pointer font-medium text-xs text-gray-800 rounded-lg hover:border-gray-300 focus:outline-none focus:border-gray-300">
                Connect
              </a>
            {% else %}
              <form action="{% url 'zoom_disconnect' %}" method="post">
                {% csrf_token %}
                <button type="submit" class="relative py-2 px-3 flex items-center justify-center gap-x-1.5 text-gray-800 sm:justify-start border border-gray-200 cursor-pointer font-medium text-xs rounded-lg hover:border-gray-300 focus:outline-none focus:border-gray-300">
                  <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6 9 17l-5-5"/></svg>
                  Connected
                </button>
              </form>
            {% endif %}
          </div>
        </div>
        <!-- End Header -->

        <!-- Body -->
        <div>
          <h3 class="font-medium text-gray-800">
            Zoom
          </h3>

          <p class="mt-1 text-sm text-gray-500">
            Connect your Zoom account to automatically import your meeting recordings.
          </p>
        </div>
        <!-- End Body -->

        {% if zoom_account.is_connected %}
          <button type="button" @click="zoomSettingsModel = true" class="py-2 px-3 w-full inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50">
            View integration
          </button>
        {% endif %}
      </div>
      <!-- End Card -->
    </div>
    {% include "./includes/integrations_script.html" %}
    {% include "./includes/successful_zoom_integration_modal.html" %}
    {% if zoom_account and zoom_account.is_connected %}
      {% include "./includes/zoom_settings_modal.html" %}
    {% endif %}
    {% include "widgets/searchable_select_component_script.html" %}
  </div>
{% endblock %}
