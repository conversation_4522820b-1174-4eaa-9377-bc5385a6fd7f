{% extends "./base.html" %}
{% load i18n %}
{% load i18n widget_tweaks %}

{% block settings %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Video Settings</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@simonwep/pickr/dist/themes/classic.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/@simonwep/pickr"></script>
    <style>
        .pcr-button {
            border: 1px solid #efefef;
        }
        .pickr .pcr-button::after {
            border-radius: 0;
        }
        .disabled-checkbox {
            background-color: #f9fafb;
            border-color: #d1d5db;
            cursor: not-allowed;
        }
        .disabled-label {
            color: #9ca3af;
            cursor: not-allowed;
        }
    </style>
    <script>
        document.addEventListener('DOMContentLoaded', (event) => {
            const dependentControls = [
                "{{ form.show_play_button.id_for_label }}",
                "{{ form.show_progress_bar.id_for_label }}",
                "{{ form.show_volume_bar.id_for_label }}",
                "{{ form.show_speed_control.id_for_label }}",
                "{{ form.show_full_screen_control.id_for_label }}",
                "{{ form.show_picture_in_picture_control.id_for_label }}",
                "{{ form.show_subtitles.id_for_label }}",
                "{{ form.show_video_quality_control.id_for_label }}",
            ];

            const updateDependentControls = () => {
                const isChecked = document.querySelector('#{{ form.show_player_controls.id_for_label }}').checked;
                dependentControls.forEach(id => {
                    const checkbox = document.querySelector(`#${id}`);
                    const label = document.querySelector(`label[for=${id}]`);
                    if (isChecked) {
                        checkbox.removeAttribute('disabled');
                        checkbox.classList.remove('disabled-checkbox');
                        label.classList.remove('disabled-label');
                    } else {
                        checkbox.setAttribute('disabled', 'true');
                        checkbox.classList.add('disabled-checkbox');
                        label.classList.add('disabled-label');
                        checkbox.checked = false; // Uncheck the checkbox
                    }
                });
            };

            document.querySelector('#{{ form.show_player_controls.id_for_label }}').addEventListener('change', updateDependentControls);
            updateDependentControls(); // initial check

            const pickers = [
                {
                    element: '#primary_color_picker',
                    input: '#primary_color_input',
                    defaultColor: '{{ form.primary_color.value }}' || '#03a4eb'
                },
                {
                    element: '#icons_color_picker',
                    input: '#icons_color_input',
                    defaultColor: '{{ form.icons_color.value }}' || '#fff'
                },
                {
                    element: '#accent_color_picker',
                    input: '#accent_color_input',
                    defaultColor: '{{ form.accent_color.value }}' || '#03a4eb'
                },
                {
                    element: '#background_color_picker',
                    input: '#background_color_input',
                    defaultColor: '{{ form.background_color.value }}' || '#000000'
                }
            ];

            pickers.forEach(pickerConfig => {
                const colorPickerElement = document.querySelector(pickerConfig.element);
                const colorInputElement = document.querySelector(pickerConfig.input);
                const pickr = Pickr.create({
                    el: colorPickerElement,
                    theme: 'classic',
                    default: pickerConfig.defaultColor,
                    lockOpacity: false,
                    comparison: false,
                    swatches: [
                        'rgba(244, 67, 54, 1)',
                        'rgba(233, 30, 99, 1)',
                        'rgba(156, 39, 176, 1)',
                        'rgba(103, 58, 183, 1)',
                        'rgba(63, 81, 181, 1)',
                        'rgba(33, 150, 243, 1)',
                        'rgba(3, 169, 244, 1)',
                        'rgba(0, 188, 212, 1)',
                        'rgba(0, 150, 136, 1)',
                        'rgba(76, 175, 80, 1)',
                        'rgba(139, 195, 74, 1)',
                        'rgba(205, 220, 57, 1)',
                        'rgba(255, 235, 59, 1)',
                        'rgba(255, 193, 7, 1)'
                    ],
                    components: {
                        preview: true,
                        opacity: true,
                        hue: true,
                        interaction: {
                            hex: false,
                            rgba: false,
                            hsla: false,
                            hsva: false,
                            cmyk: false,
                            input: true,
                            clear: false,
                            save: false
                        }
                    }
                });

                pickr.on('change', (color, source, instance) => {
                    const hexaColor = color.toHEXA().toString();
                    colorInputElement.value = hexaColor;
                });
            });
        });
    </script>
</head>
<body class="bg-gray-100 p-8">
    <form method="post" enctype="multipart/form-data">
        {% csrf_token %}
        <div class="space-y-12">
            <div class="border-b border-gray-900/10 pb-12">
                <h2 class="mt-6 text-base font-semibold leading-7 text-gray-900">Controls</h2>
                <div class="mt-6 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">

                    {% include "partials/checkbox_input.html" with field=form.show_player_controls label="Enable player controls" description="Show the player controls for video playback." %}

                    {% include "partials/checkbox_input.html" with field=form.show_video_title label="Show video title" description="Display the title of the video on the player." %}

                    {% include "partials/checkbox_input.html" with field=form.show_play_button label="Show play button" description="Show the play button on the video player." %}

                    {% include "partials/checkbox_input.html" with field=form.show_progress_bar label="Show progress bar" description="Show the progress bar on the video player." %}

                    {% include "partials/checkbox_input.html" with field=form.show_volume_bar label="Show Volume bar" description="Show the Volume bar on the video player." %}

                    {% include "partials/checkbox_input.html" with field=form.show_speed_control label="Show speed control" description="Allow viewers to change the playback speed." %}

                    {% include "partials/checkbox_input.html" with field=form.show_full_screen_control label="Show Full Screen" description="Allow viewers to view the video in full-screen mode." %}

                    {% include "partials/checkbox_input.html" with field=form.show_video_quality_control label="Show quality controls" description="Allow viewers to change the video quality." %}
                </div>

                <h2 class="mt-6 text-base font-semibold leading-7 text-gray-900">Behavior</h2>
                <div class="mt-6 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">

                    {% include "partials/checkbox_input.html" with field=form.show_subtitles label="Enable subtitles" description="Show subtitles for the video." %}

                    {% include "partials/checkbox_input.html" with field=form.show_picture_in_picture_control label="Enable picture-in-picture mode" description="Allow the video to be viewed in a small overlay window." %}

                    {% include "partials/checkbox_input.html" with field=form.autoplay_enabled label="Enable autoplay" description="Automatically start playback when the video is loaded." %}

                    {% include "partials/checkbox_input.html" with field=form.background_mode_enabled label="Enable background mode" description="Disables all player controls, mutes the video, loops it, and starts playback automatically." %}

                    {% include "partials/checkbox_input.html" with field=form.loop_enabled label="Enable loop playback" description="Automatically replay the video when it ends." %}

                    {% include "partials/checkbox_input.html" with field=form.muted_on_start label="Mute video on start" description="Start the video with the sound muted." %}

                    <!-- Default Video Quality -->
                    <div class="sm:col-span-3">
                        <label for="{{ form.default_quality.id_for_label }}" class="block text-sm font-medium leading-6 text-gray-900">
                            Default video quality
                        </label>
                        <div class="mt-2">
                            <select id="{{ form.default_quality.id_for_label }}" name="{{ form.default_quality.html_name }}" class="mt-2 block w-1/2 rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6">
                                {% for value, text in form.default_quality.field.choices %}
                                    <option value="{{ value }}" {% if form.default_quality.value == value %}selected{% endif %}>
                                        {{ text }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>


                </div>

                <h2 class="mt-6 text-base font-semibold leading-7 text-gray-900">Appearance</h2>
                <div class="mt-6 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">

                    {% include "partials/color_picker.html" with field=form.primary_color label="Primary color" picker_id="primary_color_picker" input_id="primary_color_input" %}

                    {% include "partials/color_picker.html" with field=form.icons_color label="Icons color" picker_id="icons_color_picker" input_id="icons_color_input" %}

                    {% include "partials/color_picker.html" with field=form.accent_color label="Accent color" picker_id="accent_color_picker" input_id="accent_color_input" %}

                    {% include "partials/color_picker.html" with field=form.background_color label="Background color" picker_id="background_color_picker" input_id="background_color_input" %}

                    <!-- Buttons location -->
                    <div class="sm:col-span-3">
                        <label for="{{ form.play_button_position.id_for_label }}" class="block text-sm font-medium leading-6 text-gray-900">
                            Play button position
                        </label>
                        <div class="mt-2">
                            <select id="{{ form.play_button_position.id_for_label }}" name="{{ form.play_button_position.html_name }}" class="mt-2 block w-1/2 rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6">
                                {% for value, text in form.play_button_position.field.choices %}
                                    <option value="{{ value }}" {% if form.play_button_position.value == value %}selected{% endif %}>
                                        {{ text }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>


                    <!-- Default subtitle language -->
                    <div class="sm:col-span-3">
                        <label for="{{ form.default_subtitle_language.id_for_label }}" class="block text-sm font-medium leading-6 text-gray-900">
                            Default subtitle language
                        </label>
                        <div class="mt-2">
                            <select id="{{ form.default_subtitle_language.id_for_label }}" name="{{ form.default_subtitle_language.html_name }}" class="mt-2 block w-1/2 rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6">
                                {% for value, text in form.default_subtitle_language.field.choices %}
                                    <option value="{{ value }}" {% if form.default_subtitle_language.value == value %}selected{% endif %}>
                                        {{ text }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                </div>
            </div>

            <div class="mt-6 flex items-center justify-end gap-x-6">
                <button type="button" class="text-sm font-semibold leading-6 text-gray-900" onclick="window.location.reload();">Cancel</button>
                <button type="submit" class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Save</button>
            </div>
        </div>
    </form>
</body>
</html>
{% endblock %}
