{% extends "./base.html" %}
{% load i18n %}
{% load widget_tweaks %}
{% block settings %}


<div class="mt-8 flex justify-end space-x-2" >
  {% include "./includes/month_filter.html" %}
  {% include "./includes/usage_export_dropdown.html" %}
</div>

<div class="bg-white mt-12 2xl:mt-14 ">
  <div class="mx-auto max-w-7xl">
    <dl class="grid justify-item-start grid-cols-1 gap-y-8  text-center sm:grid-cols-3">
      <div class="mx-auto flex max-w-xs flex-col">
        <dt class="text-base leading-7 text-gray-600">Total Bandwidth Used</dt>
        <dd class="order-first text-3xl font-semibold tracking-tight text-gray-900 xl:text-4xl">{{ monthly_usage.bandwidth_used | filesizeformat  }}</dd>
      </div>

      <div class="mx-auto flex max-w-xs flex-col">
        <dt class="text-base leading-7 text-gray-600">Total Storage Used</dt>
        <dd class="order-first text-3xl font-semibold tracking-tight text-gray-900 xl:text-4xl">{{ monthly_usage.total_storage_bytes | filesizeformat  }}</dd>
      </div>

      <div class="mx-auto flex max-w-xs flex-col">
        <dt class="text-base leading-7 text-gray-600">Total Live Stream Usage</dt>
        <dd class="order-first text-3xl font-semibold tracking-tight text-gray-900 xl:text-4xl">{{ monthly_usage.live_stream_usage | seconds_to_hours_minutes  }}</dd>
      </div>

    </dl>
  </div>
</div>

<div class="mt-8 2xl:mt-14 flow-root max-w-7xl mx-auto">
  <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
    <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
      <table class="min-w-full divide-y divide-gray-300">
        {% if daily_usages.exists %}
        <thead>
          <tr>
            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">
              <span class="group inline-flex" x-data="{ ordering: '{{ request.GET.ordering }}' }"
              @click="ordering = ordering === 'date' ? '-date' : 'date';
                       window.location.href = '{{ request.path }}?' + new URLSearchParams({ month: '{{ request.GET.month }}', ordering: ordering })"
              x-bind:class="{ 'cursor-pointer': !ordering }">
          Date
          <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"
               x-bind:class="{ 'rotate-180': ordering === '-date' }">
            <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
          </svg>
        </span>
            </th>
            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
              <a  class="group inline-flex">
                Bandwidth
                <span class="ml-2 flex-none rounded bg-gray-100 text-gray-900 group-hover:bg-gray-200">
                    <path fill-rule="evenodd"
                      d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                      clip-rule="evenodd" />
                  </svg>
                </span>
              </a>
            </th>
            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
              <a  class="group inline-flex">
                Live Stream Duration
                <span class="invisible ml-2 flex-none rounded text-gray-400 group-hover:visible group-focus:visible">
                    <path fill-rule="evenodd"
                      d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                      clip-rule="evenodd" />
                  </svg>
                </span>
              </a>
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 bg-white">
          {% for daily_usage in daily_usages %}
          <tr>
            <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">{{ daily_usage.date }}</td>
            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ daily_usage.bandwidth_used | filesizeformat }}</td>
            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
              {{ daily_usage.live_stream_usage | seconds_to_hours_minutes }}
              <a href="{% url 'live_search' %}?streamed_at={{ daily_usage.date|date:'Y-m-d' }}" id="detail_button"
              class="rounded-md bg-white text-xs underline font-medium text-blue-600 hover:text-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">View Details</a>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
</div>
{% else %}
<div class="mt-20 text-center">
    <p class="text-base font-semibold  text-gray-900">No Usage Found.</p>
    <p class="text-gray-600">This will get populated based on the bandwidth usage. Try changing the month in the filter.</p>
</div>
{% endif %}
{% endblock %}
