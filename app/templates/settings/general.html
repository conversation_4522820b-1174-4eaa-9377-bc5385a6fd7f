{% extends "./base.html" %}
{% load i18n widget_tweaks %}

{% block settings %}
<div class="mt-10 divide-y divide-gray-200">
  <div class="space-y-1">
    <h3 class="text-lg font-medium leading-6 text-gray-900">{% translate "Profile" %}</h3>
    <p class="max-w-2xl text-sm text-gray-500">{% translate "View your account information with our profile settings page." %}</p>
  </div>
  <div class="mt-6">
    <dl class="divide-y divide-gray-200">
      <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
        <dt class="text-sm font-medium text-gray-500">{% translate "Name" %}</dt>
        <dd class="mt-1 flex text-sm text-gray-900 sm:col-span-2 sm:mt-0">
          <span class="flex-grow">{{ request.user.name }}</span>
        </dd>
      </div>
      <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:pt-5">
        <dt class="text-sm font-medium text-gray-500">{% translate "Email" %}</dt>
        <dd class="mt-1 flex text-sm text-gray-900 sm:col-span-2 sm:mt-0">
          <span class="flex-grow">{{ request.user.email }}</span>
        </dd>
      </div>
      <div class=" py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:pt-5">
        <dt class="flex items-center text-sm font-medium text-gray-500">Timezone</dt>
        <dd class="mt-1 flex text-sm text-gray-900 sm:col-span-2 sm:mt-0">
          <span class="flex-grow" id="display_element">{{ request.user.timezone }}</span>
          <span class="flex-grow">
            <form id="form_element" method="POST" style="display: none;">
              <div class="flex items-baseline">
                {% csrf_token %}
                {{ form.timezone|add_class:"flex items-center block sm:w-1/2 w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-blue-600 sm:text-sm sm:leading-6" }}
                <button type="submit" class="rounded-md bg-blue-700 px-2.5 py-1.5 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-bg-blue-700 ml-2 mr-2">Save</button>
                <a href="#" id="cancel_button"
                  class="rounded-md bg-white text-xs underline font-medium text-blue-600 hover:text-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">Cancel</a>
              </div>
            </form>
          </span>
          <span class="ml-4 flex-shrink-0 flex items-center">
            <button type="button" id="edit_button"
              class="rounded-md bg-white font-medium text-blue-600 hover:text-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">Edit</button>
          </span>
        </dd>
      </div>
    </dl>
  </div>
</div>


<div class="mt-10 divide-y divide-gray-200">
  <div class="space-y-1">
    <h3 class="text-lg font-medium leading-6 text-gray-900">{% translate "Organization" %}</h3>
    <p class="max-w-2xl text-sm text-gray-500">{% translate "Get an overview of your organization. Review the organization's name, id, and basic information." %}
    </p>
  </div>
  <div class="mt-6">
    <dl class="divide-y divide-gray-200">
      <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
        <dt class="text-sm font-medium text-gray-500">{% translate "Name" %}</dt>
        <dd class="mt-1 flex text-sm text-gray-900 sm:col-span-2 sm:mt-0">
          <span class="flex-grow">{{ request.user.current_organization.name }}</span>
        </dd>
      </div>
      <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:pt-5">
        <dt class="text-sm font-medium text-gray-500">{% translate "Id" %}</dt>
        <dd class="mt-1 flex text-sm text-gray-900 sm:col-span-2 sm:mt-0">
          <span class="flex-grow">{{ request.user.current_organization_uuid }}</span>
        </dd>
      </div>
    </dl>
  </div>
</div>
{% endblock %}
{% block extra_body %}
{% include "./includes/settings_script.html"  %}
{% endblock %}
