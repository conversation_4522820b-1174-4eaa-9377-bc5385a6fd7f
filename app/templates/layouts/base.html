{% load static i18n tailwind_tags %}
{% get_current_language as LANGUAGE_CODE %}
<!DOCTYPE html>
<html class="{% block html_class %}{% endblock html_class %}" lang="{{ LANGUAGE_CODE }}">
  <head>
    <title>{% block head_title %}Streams{% endblock head_title %}</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="{% static 'fonts/inter/inter.css' %}">
    <link rel="icon" href="{% static 'images/favicons/favicon.ico' %}">
    {% tailwind_css %}
    <style>
        [x-cloak] { display: none !important; }
    </style>
    {% block extra_head %}
    {% endblock extra_head%}
    {% block javascript %}
      <script defer src="{% static 'js/alpine-3.10.4.min.js' %}"></script>
    {% endblock %}
  </head>

  <body class="{% block body_class %}{% endblock body_class %}" {% block body_attr %}{% endblock %}>
   {% block body %}
     {% if messages %}
       {% include "./messages.html" %}
     {% endif %}
     <header>
       {% block header %}
       {% endblock header %}
     </header>

     <main>
       {% block content %}
       {% endblock content %}
     </main>

     <footer>
       {% block footer %}
       {% endblock footer %}
     </footer>
   {% endblock body %}

   {% block extra_body %}
   {% endblock extra_body %}

   {% block script %}
   {% endblock script %}
  </body>
</html>
