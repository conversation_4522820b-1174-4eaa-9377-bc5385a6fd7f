{% load static i18n %}

<div class="w-full absolute flex flex-col items-center gap-2 z-50 top-2">
  {% for message in messages %}
    {% if message.tags == 'success' %}
      {% set container_class='bg-green-50 border-green-300' icon_class='text-green-400' text_class='text-green-800' button_class='bg-green-50 text-green-500 hover:bg-green-100 focus:ring-offset-green-50 focus:ring-green-600' %}
    {% elif message.tags == 'error' %}
      {% set container_class='bg-red-50 border-red-300' icon_class='text-red-400' text_class='text-red-800' button_class='bg-red-50 text-red-500 hover:bg-red-100 focus:ring-offset-red-50 focus:ring-red-600' %}
    {% elif message.tags == 'warning' %}
      {% set container_class='bg-yellow-50 border-yellow-300' icon_class='text-yellow-400' text_class='text-yellow-800' button_class='bg-yellow-50 text-yellow-500 hover:bg-yellow-100 focus:ring-offset-yellow-50 focus:ring-yellow-600' %}
    {% else %}
      {% set container_class='bg-blue-50 border-blue-300' icon_class='text-blue-400' text_class='text-blue-800' button_class='bg-blue-50 text-blue-600 hover:bg-blue-100 focus:ring-offset-blue-50 focus:ring-blue-700' %}
    {% endif %}
    <div x-data='{ open: true }' x-show='open' class="rounded border {{ container_class }} py-1.5 px-3">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 {{ icon_class }}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium {{ text_class }}">
            {% translate message.message %}
          </p>
        </div>
        <div class="ml-auto pl-3">
          <div class="h-5 w-5">
            <button type="button" @click="open=false" class="inline-flex rounded-sm focus:outline-none focus:ring-2 {{ button_class }}">
              <span class="sr-only">Dismiss</span>
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  {% endfor %}
</div>
