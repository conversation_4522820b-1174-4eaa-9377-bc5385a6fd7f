{% extends "layouts/base.html" %}
{% load i18n %}

{% block html_class %}h-full bg-white{% endblock html_class %}
{% block body_class %}h-full overflow-y-scroll{% endblock body_class %}

{% block content %}
  <div x-data="{open: false}" @keydown.window.escape="open = false">
    {% include "includes/sidebar.html" %}
    <div class="lg:pl-64">

      <!-- header -->
      <div class="sticky top-0 z-40 lg:mx-auto lg:max-w-7xl 2xl:max-w-screen-2xl lg:px-8">
        <div class="flex h-16 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-0 lg:shadow-none">
          {% include "includes/mobile_sidebar_open_button.html" %}
          {% include "includes/header_separator.html" %}
          <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            {% if request.resolver_match.view_name == 'deleted_assets' or request.resolver_match.view_name == 'deleted_search' %}
              {% include "includes/deleted_search.html" %}
            {% elif request.resolver_match.view_name == 'live_streams' or request.resolver_match.view_name == 'live_search' %}
              {% include "includes/live_search.html" %}
            {% else %}
              {% include "includes/global_search.html" %}
            {% endif %}
            {% include "includes/profile_dropdown.html" %}
          </div>
        </div>
      </div>

      <main class="py-10 relative">
        <div class="mx-auto max-w-7xl 2xl:max-w-screen-2xl sm:px-6 lg:px-8">
          <!-- Your content -->
          {% block main_content %}
          {% endblock main_content %}
        </div>
      </main>
    </div>
  </div>
{% endblock content %}
