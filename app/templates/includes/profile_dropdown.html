{% load i18n %}

<!-- profile dropdown -->
<div class="flex shrink-0 items-center gap-x-4 lg:gap-x-6">

  <!-- Separator -->
  <div class="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200" aria-hidden="true"></div>

  <!-- Profile dropdown -->
  <div x-data="{ showDropdown: false }" class="relative">
    <button type="button" class="-m-1.5 flex items-center p-1.5" id="user-menu-button" aria-expanded="false"
      aria-haspopup="true"
      @click="showDropdown = !showDropdown" @click.away="showDropdown = false">
      <span class="sr-only">Open user menu</span>

      <div class="relative inline-flex items-center justify-center w-8 h-8 overflow-hidden bg-blue-100 rounded-full dark:bg-blue-600">
        <span class="font-medium text-blue-600 dark:text-blue-300">{{ request.user.name|make_list|first }}</span>
      </div>

      <span class="hidden lg:flex lg:items-center">
        <span class="ml-4 text-sm font-semibold leading-6 text-gray-900" aria-hidden="true">{{ request.user.name }}</span>
        <svg class="ml-2 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd"
            d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
            clip-rule="evenodd" />
        </svg>
      </span>
    </button>

    <div
      class="absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none"
      role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1"
      x-show="showDropdown"
      x-transition:enter="transition ease-out duration-100"
      x-transition:enter-start="transform opacity-0 scale-95"
      x-transition:enter-end="transform opacity-100 scale-100"
      x-transition:leave="transition ease-in duration-75"
      x-transition:leave-start="transform opacity-100 scale-100"
      x-transition:leave-end="transform opacity-0 scale-95">
      <!-- Active: "bg-gray-50", Not Active: "" -->
      <a href="{% url 'general_settings' %}" class="block px-3 py-1 text-sm leading-6 text-gray-900" role="menuitem" tabindex="-1"
        id="user-menu-item-0">{% translate "Settings" %}</a>
      <a href="{% url 'account_logout' %}" class="block px-3 py-1 text-sm leading-6 text-gray-900" role="menuitem" tabindex="-1"
        id="user-menu-item-1">
        {% translate "Logout" %}</a>
    </div>
  </div>
</div>
