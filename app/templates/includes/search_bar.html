<div class="sticky top-0 z-10 flex flex-shrink-0 h-16 bg-white border-b border-gray-200">
  {% include "includes/open_sidebar_button.html" %}
  <div class="flex justify-between flex-1 px-4 md:px-0">
    <div class="flex flex-1">
      <form class="flex w-full md:ml-0" action="#" method="GET">
        <label for="mobile-search-field" class="sr-only">Search</label>
        <label for="desktop-search-field" class="sr-only">Search</label>
        <div class="relative w-full text-gray-400 focus-within:text-gray-600">
          <div class="absolute inset-y-0 left-0 flex items-center pointer-events-none">
            <!-- Heroicon name: mini/magnifying-glass -->
            <svg class="flex-shrink-0 w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd" />
            </svg>
          </div>
          <input name="mobile-search-field" id="mobile-search-field" class="w-full h-full py-2 pl-8 pr-3 text-base text-gray-900 placeholder-gray-500 border-transparent focus:border-transparent focus:placeholder-gray-400 focus:outline-none focus:ring-0 sm:hidden" placeholder="Search" type="search">
          <input name="desktop-search-field" id="desktop-search-field" class="hidden w-full h-full py-2 pl-8 pr-3 text-sm text-gray-900 placeholder-gray-500 border-transparent focus:border-transparent focus:placeholder-gray-400 focus:outline-none focus:ring-0 sm:block" placeholder="Search videos" type="search">
        </div>
      </form>
    </div>
    {% include "includes/notification_bell.html" %}
  </div>
</div>
