{% load static i18n %}

<!-- Static sidebar for desktop -->
<div class="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
  <!-- Sidebar component, swap this element with another sidebar if you like -->
  <nav class="flex flex-grow flex-col overflow-y-auto border-r border-gray-200 bg-gray-50 pt-5 pb-4">
    <a href="/" class="flex flex-shrink-0 items-center px-4">
      <img class="w-auto h-8" src="{% static 'images/logo.svg' %}" alt="TPStreams">
    </a>
    <div class="mt-5 flex-grow">
      <div class="space-y-1">
        <!-- Current: "border-blue-700 bg-blue-50 text-blue-700", Default: "border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900" -->
        <a href="{{ assets_url }}"
          class="group flex items-center border-l-4 py-2 px-3 text-sm font-medium {% if request.path == assets_url %} bg-blue-50 border-blue-700 text-blue-700 {% else %} border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900 {% endif %}">
          <!-- Current: "text-purple-500", Default: "text-gray-400 group-hover:text-gray-500" -->
          <svg
            class="mr-3 h-6 w-6 flex-shrink-0 {% if request.path == assets_url %} text-blue-600 {% else %} text-gray-400 group-hover:text-gray-500 {% endif %}"
            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
            aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z">
            </path>
          </svg>
          {% translate "Assets" %}
        </a>
        <a href="{% url 'live_streams' %}"
          class="group flex items-center border-l-4 py-2 px-3 text-sm font-medium {% if '/live/' in request.path %}bg-blue-50 border-blue-700 text-blue-700 {% else %} border-transparent text-gray-600 hover:bg-gray-50hover:text-gray-900 {% endif %}">
          <svg class="mr-3 h-6 w-6 flex-shrink-0 {% if '/live/' in request.path %} text-blue-600 {% else %}
            text-gray-400 group-hover:text-gray-500 {% endif %}" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
            stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M9.348 14.651a3.75 3.75 0 0 1 0-5.303m5.304 0a3.75 3.75 0 0 1 0 5.303m-7.425 2.122a6.75 6.75 0 0 1 0-9.546m9.546 0a6.75 6.75 0 0 1 0 9.546M5.106 18.894c-3.808-3.808-3.808-9.98 0-13.789m13.788 0c3.808 3.808 3.808 9.981 0 13.79M12 12h.008v.007H12V12zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0z" />
          </svg>
          {% translate "Live streams" %}
        </a>
        <a href="{% url 'general_settings' %}"
          class="group flex items-center border-l-4 py-2 px-3 text-sm font-medium {% if "/settings/" in request.path %}
          bg-blue-50 border-blue-700 text-blue-700 {% else %} border-transparent text-gray-600 hover:bg-gray-50
          hover:text-gray-900 {% endif %}">
          <svg class="mr-3 h-6 w-6 flex-shrink-0 {% if "/settings/" in request.path %} text-blue-600 {% else %}
            text-gray-400 group-hover:text-gray-500 {% endif %}" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
            stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M4.5 12a7.5 7.5 0 0015 0m-15 0a7.5 7.5 0 1115 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077l1.41-.513m14.095-5.13l1.41-.513M5.106 17.785l1.15-.964m11.49-9.642l1.149-.964M7.501 19.795l.75-1.3m7.5-12.99l.75-1.3m-6.063 16.658l.26-1.477m2.605-14.772l.26-1.477m0 17.726l-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205L12 12m6.894 5.785l-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864l-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495" />
          </svg>
          {% translate "Settings" %}
        </a>
      </div>
    </div>
    <div class="block w-full flex-shrink-0">
      <a href={% url 'deleted_assets' %}
        class="group flex items-center border-l-4 border-transparent py-2 px-3 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" stroke-width="1.5" class="mr-3 h-6 w-6 text-gray-400 group-hover:text-gray-500" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"/>
        </svg>
        {% translate "Trash" %}
      </a>

      <a href="https://developer.tpstreams.com/" target="_blank"
        class="group flex items-center border-l-4 border-transparent py-2 px-3 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900">
        <svg class="mr-3 h-6 w-6 text-gray-400 group-hover:text-gray-500" fill="none" viewBox="0 0 24 24"
          stroke-width="1.5" stroke="currentColor" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round"
            d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
        </svg>
        {% translate "Help" %}
      </a>

      <a href="{% url 'account_logout' %}"
        class="group flex items-center border-l-4 border-transparent py-2 px-3 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900">
        <svg class="mr-3 h-6 w-6 text-gray-400 group-hover:text-gray-500" fill="none" viewBox="0 0 24 24"
          stroke-width="1.5" stroke="currentColor" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round"
            d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
        </svg>
        {% translate "Logout" %}
      </a>
    </div>
  </nav>
</div>
