{% load i18n widget_tweaks %}
  <div>
    <label for="{{ field.auto_id }}" class="block text-sm font-medium text-gray-700">{{ field.label }}</label>
    <div class="mt-1 relative rounded-md shadow-sm">
      {% if field.errors %}
      {{ field|add_class:"block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-blue-600 focus:outline-none focus:ring-blue-600 sm:text-sm border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:outline-none focus:ring-red-500" }}
      <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
        <!-- Heroicon name: mini/exclamation-circle -->
        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
        </svg>
      </div>
      {% else %}
        {{ field|add_class:"block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-blue-600 focus:outline-none focus:ring-blue-600 sm:text-sm" }}
      {% endif %}
    </div>
    {% if field.errors %}
      {% for error in field.errors  %}
      <p class="mt-2 text-sm text-red-600" id="email-error">{{ error }}</p>
      {% endfor %}
    {% endif %}
  </div>
