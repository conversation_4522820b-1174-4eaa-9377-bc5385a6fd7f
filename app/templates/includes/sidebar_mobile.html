{% load static i18n %}

<!-- Off-canvas menu for mobile, show/hide based on off-canvas menu state. -->
<div x-show="open" class="relative z-50 lg:hidden" role="dialog" aria-modal="true">

  <div x-show="open" x-transition:enter="transition-opacity ease-linear duration-300"
    x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
    x-transition:leave="transition-opacity ease-linear duration-300" x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"
    x-description="Off-canvas menu backdrop, show/hide based on off-canvas menu state."
    class="fixed inset-0 bg-gray-600 bg-opacity-75"></div>

  <div class="fixed inset-0 z-40 flex">
    <div x-show="open" x-transition:enter="transition ease-in-out duration-300 transform"
      x-transition:enter-start="-translate-x-full" x-transition:enter-end="translate-x-0"
      x-transition:leave="transition ease-in-out duration-300 transform" x-transition:leave-start="translate-x-0"
      x-transition:leave-end="-translate-x-full"
      x-description="Off-canvas menu, show/hide based on off-canvas menu state."
      class="relative flex w-full max-w-xs flex-1 flex-col bg-white pt-5 pb-4" @click.away="open = false">
      <div x-show="open" x-transition:enter="ease-in-out duration-300" x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100" x-transition:leave="ease-in-out duration-300"
        x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
        x-description="Close button, show/hide based on off-canvas menu state."
        class="absolute top-0 right-0 -mr-14 p-1">
        <button type="button"
          class="flex h-12 w-12 items-center justify-center rounded-full focus:bg-gray-600 focus:outline-none">
          <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
            aria-hidden="true" @click="open=false">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
          <span class="sr-only">Close sidebar</span>
        </button>
      </div>

      <a href="/" class="flex flex-shrink-0 items-center px-4">
        <img class="w-auto h-8" src="{% static 'images/logo.svg' %}" alt="TPStreams">
      </a>
      <div class="mt-5 h-0 flex-1 overflow-y-auto">
        <nav class="flex h-full flex-col">
          <div class="space-y-1">
            <a href="{{ assets_url }}"
            class="group flex items-center border-l-4 py-2 px-3 text-base font-medium {% if request.path == assets_url %} bg-blue-50 border-blue-700 text-blue-700 {% else %} border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900 {% endif %}">
            <svg class="mr-3 h-6 w-6 flex-shrink-0 {% if request.path == assets_url %} text-blue-600 {% else %} text-gray-400 group-hover:text-gray-500 {% endif %}" fill="none"
                viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
              </svg>
              {% translate "Assets" %}
            </a>
            <a href="{% url 'live_streams' %}"
            class="group flex items-center border-l-4 py-2 px-3 text-base font-medium  {% if '/live/' in request.path %}bg-blue-50 border-blue-700 text-blue-700 {% else %} border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900 {% endif %}">
            <svg
                class="mr-3 h-6 w-6 flex-shrink-0 {% if '/live/' in request.path %} text-blue-600 {% else %}
                text-gray-400 group-hover:text-gray-500 {% endif %}" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="M9.348 14.651a3.75 3.75 0 0 1 0-5.303m5.304 0a3.75 3.75 0 0 1 0 5.303m-7.425 2.122a6.75 6.75 0 0 1 0-9.546m9.546 0a6.75 6.75 0 0 1 0 9.546M5.106 18.894c-3.808-3.808-3.808-9.98 0-13.789m13.788 0c3.808 3.808 3.808 9.981 0 13.79M12 12h.008v.007H12V12zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0z"/>
            </svg>
            {% translate "Live streams" %}
            </a>
            <a href="{% url 'general_settings' %}"
            class="group flex items-center border-l-4 py-2 px-3 text-base font-medium {% if "/settings/" in request.path %} bg-blue-50 border-blue-700 text-blue-700 {% else %} border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900 {% endif %}"
            aria-current="page">
            <svg class="mr-3 h-6 w-6 flex-shrink-0 {% if "/settings/" in request.path %} text-blue-600 {% else %} text-gray-400 group-hover:text-gray-500 {% endif %}" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
              stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M4.5 12a7.5 7.5 0 0015 0m-15 0a7.5 7.5 0 1115 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077l1.41-.513m14.095-5.13l1.41-.513M5.106 17.785l1.15-.964m11.49-9.642l1.149-.964M7.501 19.795l.75-1.3m7.5-12.99l.75-1.3m-6.063 16.658l.26-1.477m2.605-14.772l.26-1.477m0 17.726l-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205L12 12m6.894 5.785l-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864l-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495" />
            </svg>
            {% translate "Settings" %}
          </a>
          </div>
          <div class="mt-auto space-y-1 pt-10">
            <a href="{% url 'deleted_assets' %}"
              class="group flex items-center border-l-4 border-transparent py-2 px-3 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" stroke-width="1.5" class="mr-3 h-6 w-6 text-gray-400 group-hover:text-gray-500" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"/>
              </svg>
              {% translate "Trash" %}
            </a>

            <a href="https://developer.tpstreams.com/" target="_blank"
              class="group flex items-center border-l-4 border-transparent py-2 px-3 text-base font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900">
              <svg class="mr-4 h-6 w-6 text-gray-400 group-hover:text-gray-500" fill="none" viewBox="0 0 24 24"
                stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
              </svg>
              {% translate "Help" %}
            </a>

            <a href="{% url 'account_logout' %}"
              class="group flex items-center border-l-4 border-transparent py-2 px-3 text-base font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900">
              <svg class="mr-4 h-6 w-6 text-gray-400 group-hover:text-gray-500" fill="none" viewBox="0 0 24 24"
                stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
              </svg>
              {% translate "Logout" %}
            </a>
          </div>
        </nav>
      </div>
    </div>
  </div>
</div>
