<script>
    function selectDropdown(config) {
      return {
        name: config.name,
        valueField: config.valueField,
        labelField: config.labelField,
        url: config.url,
        queryParam: config.queryParam || 'search',
        placeholder: config.placeholder || 'Select...',
        pageSize: config.pageSize || 10,
        limitParam: config.limitParam || 'limit',
        offsetParam: config.offsetParam || 'offset',
        responsePath: config.responsePath || null,
        filter_param: config.filter_param || null,
        filter_value: config.filter_value || null,
        options: [],
        selected: null,
        search: '',
        open: false,
        loading: false,
        error: null,
        page: 0,
        hasMore: true,
        highlightedIndex: -1,
        hasLoaded: false,

        async init() {
          if (config.value && config.label) {
            this.selected = {
              [this.valueField]: config.value,
              [this.labelField]: config.label
            };
          }
          this.hasLoaded = false;
        },

        getOptionsFromResponse(responseJson) {
          if (responseJson.results) {
            return responseJson.results;
          }

          if (responseJson.data) {
            return responseJson.data;
          }

          if (this.responsePath) {
            try {
              const result = this.responsePath.split('.').reduce((obj, key) => obj?.[key], responseJson) || [];
              return result;
            } catch (err) {
              console.error("Error parsing responsePath:", err);
              return [];
            }
          }

          return Array.isArray(responseJson) ? responseJson : [];
        },

        async load(reset = false) {
          if (this.loading || (!this.hasMore && !reset)) return;

          this.loading = true;
          this.error = null;

          if (reset) {
            this.page = 0;
            this.options = [];
            this.hasMore = true;
            this.highlightedIndex = -1;
          }

          const params = new URLSearchParams({
            [this.queryParam]: this.search,
            [this.limitParam]: this.pageSize,
            offset: this.page * this.pageSize
          });

          // Add filter parameters
          if (this.filter_param && this.filter_value) {
            params.append(this.filter_param, this.filter_value);
          }

          const url = `${this.url}?${params.toString()}`;

          try {
            const resp = await fetch(url);

            if (!resp.ok) {
              throw new Error(`Error ${resp.status}: ${resp.statusText}`);
            }

            const data = await resp.json();
            const items = this.getOptionsFromResponse(data);

            // Create a new array to avoid duplicate keys
            const newOptions = [...this.options];
            items.forEach(item => {
              if (!newOptions.some(opt => opt[this.valueField] === item[this.valueField])) {
                newOptions.push(item);
              }
            });
            this.options = newOptions;

            if (data.next) {
              this.hasMore = true;
            } else if (data.count !== undefined) {
              this.hasMore = this.options.length < data.count;
            } else {
              this.hasMore = items.length === this.pageSize;
            }
            this.page += 1;
          } catch (err) {
            console.error('Dropdown fetch error:', err);
            this.error = {
              message: 'Something went wrong while loading options.',
              detail: err.message || 'Failed to load data'
            };
          } finally {
            this.loading = false;
          }
        },

        async onSearch() {
          this.page = 0;
          this.options = [];
          this.hasMore = true;
          this.highlightedIndex = -1;
          await this.load();
        },

        select(item) {
          this.selected = item;
          this.closeDropdown();
        },

        highlightNext() {
          if (!this.open) return this.open = true;
          if (this.highlightedIndex < this.options.length - 1) {
            this.highlightedIndex++;
            this.scrollToHighlight();
          }
        },

        highlightPrev() {
          if (!this.open) return this.open = true;
          if (this.highlightedIndex > 0) {
            this.highlightedIndex--;
            this.scrollToHighlight();
          }
        },

        selectHighlighted() {
          if (this.highlightedIndex >= 0 && this.highlightedIndex < this.options.length) {
            this.select(this.options[this.highlightedIndex]);
          }
        },

        async toggleDropdownOrFocusSearch() {
          if (this.open) {
            this.closeDropdownAndFocusTrigger();
            return;
          }

          this.open = true;

          if (!this.hasLoaded) {
            await this.load();
            this.hasLoaded = true;
          }

          this.$nextTick(() => this.$refs.searchInput?.focus());
        },

        loadMoreOptionsIfScrolledToEnd(event) {
          const el = event.target;
          if (el.scrollTop + el.clientHeight >= el.scrollHeight - 30) {
            if (this.hasMore && !this.loading) {
              this.load();
            }
          }
        },

        closeDropdown() {
          this.open = false;
        },

        closeDropdownAndFocusTrigger() {
          this.open = false;
          this.$nextTick(() => this.$refs.trigger?.focus());
        },

        scrollToHighlight() {
          this.$nextTick(() => {
            const container = this.$refs.dropdownList;
            const highlighted = container.querySelector('.highlighted');
            if (highlighted) {
              const offsetTop = highlighted.offsetTop;
              const scrollTop = container.scrollTop;
              const containerHeight = container.clientHeight;
              if (offsetTop < scrollTop || offsetTop >= scrollTop + containerHeight) {
                container.scrollTop = offsetTop - containerHeight / 2;
              }
            }
          });
        }
      }
    }
  </script>
