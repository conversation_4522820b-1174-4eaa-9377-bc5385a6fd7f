<div x-data="selectDropdown({ name: '{{ name }}', value: '{{ value|default:"" }}', label: '{{ label|default:"" }}', placeholder: '{{ placeholder|default:"Select..." }}', url: '{{ url }}', responsePath: '{{ responsePath }}', labelField: '{{ labelField|default:"title" }}', valueField: '{{ valueField|default:"id" }}', queryParam: '{{ queryParam|default:"search" }}', pageSize: {{ pageSize|default:10 }}, filter_param: '{{ filter_param|default:"" }}', filter_value: '{{ filter_value|default:"" }}' })"
     x-init="init()"
     class="relative w-full">
  <!-- Hidden input for Django -->
  <input type="hidden" :name="name" :value="selected?.[valueField]" />
  <!-- Trigger (styled like a select box) -->
  <div tabindex="0"
       x-ref="trigger"
       @keyup.enter="toggleDropdownOrFocusSearch"
       @click="toggleDropdownOrFocusSearch"
       class="relative py-2 ps-4 pe-9 flex items-center gap-x-2 w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
    <span x-text="selected ? selected[labelField] : placeholder"
          class="text-sm text-gray-900"></span>
    <div class="absolute top-1/2 end-3 -translate-y-1/2"><svg class="shrink-0 w-4 h-4 text-gray-500 dark:text-neutral-500 " xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m7 15 5 5 5-5"/><path d="m7 9 5-5 5 5"/></svg></div>
  </div>
  <!-- Dropdown -->
  <div x-show="open"
       @mousedown.outside="closeDropdown"
       :class="open ? 'bottom-full mb-2.5' : ''"
       x-ref="dropdownList"
       @scroll.passive="loadMoreOptionsIfScrolledToEnd"
       class="absolute max-h-64 p-1 pt-0 space-y-0.5 z-50 w-full overflow-hidden overflow-y-auto bg-white rounded-xl border shadow-xl [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 mt-2.5">
    <div class="sticky top-0 p-2 -mx-1 bg-white border-b">
      <input type="text"
             x-model="search"
             @input.debounce="onSearch"
             x-ref="searchInput"
             @keydown.arrow-down.prevent="highlightNext"
             @keydown.arrow-up.prevent="highlightPrev"
             @keydown.enter.prevent="selectHighlighted"
             @keydown.escape.prevent="closeDropdownAndFocusTrigger"
             @keydown.shift.tab.prevent="closeDropdownAndFocusTrigger"
             @keydown.tab="closeDropdown"
             placeholder="Search..."
             class="block w-full sm:text-sm border-gray-200 rounded-lg focus:border-blue-500 focus:ring-blue-500 py-1.5 sm:py-2 px-3" />
    </div>
    <template x-for="(item, index) in options" :key="item[valueField]">
      <div @click="select(item)"
           :class="[ 'w-full px-4 py-2 text-sm text-gray-800 rounded-lg cursor-pointer hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100', highlightedIndex === index ? 'bg-indigo-100 highlighted' : '', selected?.[valueField] === item[valueField] ? 'bg-gray-100  font-semibold' : '' ]"
           x-text="item[labelField]"></div>
    </template>
    <div x-show="loading" class="p-2 text-sm text-center text-gray-500">Loading...</div>
    <div x-show="!loading && !error && options.length === 0"
         class="p-2 text-sm text-center text-gray-500">No results found</div>
    <div x-show="error" class="p-4 mt-2 rounded-md bg-red-50">
      <div class="flex">
        <div class="shrink-0">
          <svg class="text-red-400 size-5"
               viewBox="0 0 20 20"
               fill="currentColor"
               aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16ZM8.28 7.22a.75.75 0 0 0-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 1 0 1.06 1.06L10 11.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L11.06 10l1.72-1.72a.75.75 0 0 0-1.06-1.06L10 8.94 8.28 7.22Z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800" x-text="error?.message"></h3>
          <div class="mt-2 text-sm text-red-700" x-show="error?.detail">
            <ul class="pl-5 space-y-1 list-disc">
              <li>
                <span class="font-semibold">Details:</span> <span x-text="error?.detail"></span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
