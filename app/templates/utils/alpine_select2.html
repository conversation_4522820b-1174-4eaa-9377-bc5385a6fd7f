<script>
  function select2(searchUrl = '', multiple = false) {
    return {
      items: [],
      selectedItems: [],
      allFetchedItems: [],
      showLoading: false,
      nextPage: false,
      searchUrl: searchUrl,
      multiple: multiple,
      search_param: 'search',
      extraHeaders: {},

      fetchItems(params = {}) {
        this.showLoading = true;
        let headers = {
          'accept': 'application/json',
          'content-type': 'application/json',
          ...this.extraHeaders
        }

        fetch(this.getSearchURL(params), {
          credentials: 'same-origin',
          headers: headers
        }).then(response => response.json())
          .then(data => {
            this.showLoading = false;
            this.items = this.processResult(data.results);
            if (!params.hasOwnProperty(this.search_param)){
             this.allFetchedItems.push(...this.items);
          }

            this.nextPage = this.processResult(data.next);
          })
          .catch(() => {
            this.showLoading = false;
          });
      },

      getSearchURL(paramsToAppend) {
        const queryParamsToAppend = new URLSearchParams(paramsToAppend).toString();
        const existingQueryString = this.searchUrl.split('?')[1];
        const searchURL = this.searchUrl.split('?')[0];

        if (existingQueryString) {
          return `${searchURL}?${existingQueryString}&${queryParamsToAppend}`;
        }
        return `${searchURL}?${queryParamsToAppend}`;
      },

      processResult(results) {
        return results
      },

      onItemSelect(selectedItem) {
        if (this.selectedItems.some(item => item.id === selectedItem.id)) {
          this.selectedItems = this.selectedItems.filter(item => item.id !== selectedItem.id)
        } else {
          if (!this.multiple) {
            this.selectedItems.pop();
          }
          this.selectedItems.push(selectedItem)
        }
      },

      removeItem(itemToRemove) {
        this.selectedItems = this.selectedItems.filter(item => item.id !== itemToRemove.id)
      },

      isItemSelected(item) {
        return this.selectedItems.some(e => e.id === item.id)
      },
    }
  }

  function getPageNumberFromUrl(url) {
    const urlObj = new URL(url);
    return urlObj.searchParams.get("page");
  };
</script>
