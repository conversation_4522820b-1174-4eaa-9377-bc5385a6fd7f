	main .forward-widget {
		text-align: center;
		width: 40%;
		height: 100%;
		border-radius: 100% 0 0 100%;
		position: fixed;
		display: flex;
		flex-direction: row;
		right: 0;
		top: 0;
	}

	main .forward-widget .icon {
		justify-content: flex-start;
		align-items: center;
		margin: auto 0 auto 15%;
		color: rgba(255, 255, 255, 1);
	}

	main .rewind-widget {
		text-align: center;
		width: 40%;
		height: 100%;
		border-radius: 0 100% 100% 0;
		position: fixed;
		display: flex;
		flex-direction: row;
		left: 0;
		top: 0;
	}

	main .rewind-widget .icon {
		justify-content: flex-start;
		align-items: center;
		margin: auto 0 auto 60%;
		color: rgba(255, 255, 255, 1);
	}

	main .icon i {
		display: block;
	}

	main .seek {
		transition: background 0.8s;
		background: rgba(200, 200, 200, .4) radial-gradient(circle, transparent 1%, rgba(200, 200, 200, .4) 1%) center/15000%;
		pointer-events: none;
		display: none;
	}

	main i {
		font-style: normal;
	}

	main .animate-in {
		display: flex;
		animation: ripple 1s forwards;
	}

	main .animate-in i {
		display: block;
	}

	main .animate-in.forward i {
		padding-bottom: 2px;
	}

	main .animate-in.forward i {
		animation: fadeInLeft 0.7s;
	}

	main .animate-in.rewind i {
		animation: fadeInRight 0.7s;
	}

	@keyframes ripple {
		0% {
			background-color: rgba(200, 200, 200, .4);
			background-size: 100%;
			transition: background 0s;
			opacity: 1;
		}

		100% {
			transition: background 0.8s;
			background: rgba(200, 200, 200, .4) radial-gradient(circle, transparent 1%, rgba(200, 200, 200, .4) 1%) center/15000%;
			display: flex;
			opacity: 0;
		}
	}

	@keyframes fadeInLeft {
		0% {
			opacity: 0;
			transform: translateX(-20px);
		}

		100% {
			opacity: 1;
			transform: translateX(0);
		}
	}

	@keyframes fadeInRight {
		0% {
			opacity: 0;
			transform: translateX(0px);
		}

		100% {
			opacity: 1;
			transform: translateX(-20px);
		}
	}
