/**
 * videojs-hls-quality-selector
 * @version 1.1.4
 * @copyright 2020 <PERSON> (<EMAIL>)
 * @license MIT
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("video.js")):"function"==typeof define&&define.amd?define(["video.js"],e):t.videojsHlsQualitySelector=e(t.videojs)}(this,function(o){"use strict";o=o&&o.hasOwnProperty("default")?o.default:o;var u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l=(function(){function a(t){this.value=t}function t(o){var r,u;function l(t,e){try{var n=o[t](e),i=n.value;i instanceof a?Promise.resolve(i.value).then(function(t){l("next",t)},function(t){l("throw",t)}):s(n.done?"return":"normal",n.value)}catch(t){s("throw",t)}}function s(t,e){switch(t){case"return":r.resolve({value:e,done:!0});break;case"throw":r.reject(e);break;default:r.resolve({value:e,done:!1})}(r=r.next)?l(r.key,r.arg):u=null}this._invoke=function(i,o){return new Promise(function(t,e){var n={key:i,arg:o,resolve:t,reject:e,next:null};u?u=u.next=n:(r=u=n,l(i,o))})},"function"!=typeof o.return&&(this.return=void 0)}"function"==typeof Symbol&&Symbol.asyncIterator&&(t.prototype[Symbol.asyncIterator]=function(){return this}),t.prototype.next=function(t){return this._invoke("next",t)},t.prototype.throw=function(t){return this._invoke("throw",t)},t.prototype.return=function(t){return this._invoke("return",t)}}(),function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}),t=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)},s=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e},e=o.getComponent("MenuButton"),r=o.getComponent("Menu"),a=o.getComponent("Component"),c=o.dom;var h=function(e){function n(t){return l(this,n),s(this,e.call(this,t,{title:t.localize("Quality"),name:"QualityButton"}))}return t(n,e),n.prototype.createItems=function(){return[]},n.prototype.createMenu=function(){var t,e=new r(this.player_,{menuButton:this});if(this.hideThreshold_=0,this.options_.title){var n=c.createEl("li",{className:"vjs-menu-title",innerHTML:(t=this.options_.title,"string"!=typeof t?t:t.charAt(0).toUpperCase()+t.slice(1)),tabIndex:-1}),i=new a(this.player_,{el:n});this.hideThreshold_+=1,e.addItem(i)}if(this.items=this.createItems(),this.items)for(var o=0;o<this.items.length;o++)e.addItem(this.items[o]);return e},n}(e),i=function(r){function u(t,e,n,i){l(this,u);var o=s(this,r.call(this,t,{label:e.label,selectable:!0,selected:e.selected||!1}));return o.item=e,o.qualityButton=n,o.plugin=i,o}return t(u,r),u.prototype.handleClick=function(){for(var t=0;t<this.qualityButton.items.length;++t)this.qualityButton.items[t].selected(!1);this.plugin.setQuality(this.item.value),this.selected(!0)},u}(o.getComponent("MenuItem")),y={},n=o.registerPlugin||o.plugin,f=function(){function n(t,e){l(this,n),this.player=t,this.config=e,this.player.qualityLevels&&this.getHls()&&(this.createQualityButton(),this.bindPlayerEvents())}return n.prototype.getHls=function(){return this.player.tech({IWillNotUseThisInPlugins:!0}).hls},n.prototype.bindPlayerEvents=function(){this.player.qualityLevels().on("addqualitylevel",this.onAddQualityLevel.bind(this))},n.prototype.createQualityButton=function(){var t=this.player;this._qualityButton=new h(t);var e=t.controlBar.children().length-2,n=t.controlBar.addChild(this._qualityButton,{componentClass:"qualitySelector"},this.config.placementIndex||e);if(n.addClass("vjs-quality-selector"),this.config.displayCurrentQuality)this.setButtonInnerText("auto");else{var i=" "+(this.config.vjsIconClass||"vjs-icon-hd");n.menuButton_.$(".vjs-icon-placeholder").className+=i}n.removeClass("vjs-hidden")},n.prototype.setButtonInnerText=function(t){this._qualityButton.menuButton_.$(".vjs-icon-placeholder").innerHTML=t},n.prototype.getQualityMenuItem=function(t){var e=this.player;return new i(e,t,this._qualityButton,this)},n.prototype.onAddQualityLevel=function(){for(var n=this,t=this.player,i=t.qualityLevels().levels_||[],o=[],e=function(e){if(!o.filter(function(t){return t.item&&t.item.value===i[e].height}).length){var t=n.getQualityMenuItem.call(n,{label:i[e].height+"p",value:i[e].height});o.push(t)}},r=0;r<i.length;++r)e(r);o.sort(function(t,e){return"object"!==(void 0===t?"undefined":u(t))||"object"!==(void 0===e?"undefined":u(e))?-1:t.item.value<e.item.value?-1:t.item.value>e.item.value?1:0}),o.push(this.getQualityMenuItem.call(this,{label:t.localize("Auto"),value:"auto",selected:!0})),this._qualityButton&&(this._qualityButton.createItems=function(){return o},this._qualityButton.update())},n.prototype.setQuality=function(t){var e=this.player.qualityLevels();this._currentQuality=t,this.config.displayCurrentQuality&&this.setButtonInnerText("auto"===t?t:t+"p");for(var n=0;n<e.length;++n){var i=e[n];i.enabled=i.height===t||"auto"===t}this._qualityButton.unpressButton()},n.prototype.getCurrentQuality=function(){return this._currentQuality||"auto"},n}(),p=function(n){var i=this;this.ready(function(){var t,e;t=i,e=o.mergeOptions(y,n),t.addClass("vjs-hls-quality-selector"),t.hlsQualitySelector=new f(t,e)})};return n("hlsQualitySelector",p),p.VERSION="1.1.4",p});
