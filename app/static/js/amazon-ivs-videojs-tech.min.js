/*! For license information please see amazon-ivs-videojs-tech.min.js.LICENSE.txt */
var registerIVSTech;!function(){var e={481:function(e){e.exports=function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=90)}({17:function(e,t,n){"use strict";t.__esModule=!0,t.default=void 0;var r=n(18),i=function(){function e(){}return e.getFirstMatch=function(e,t){var n=t.match(e);return n&&n.length>0&&n[1]||""},e.getSecondMatch=function(e,t){var n=t.match(e);return n&&n.length>1&&n[2]||""},e.matchAndReturnConst=function(e,t,n){if(e.test(t))return n},e.getWindowsVersionName=function(e){switch(e){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}},e.getMacOSVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),10===t[0])switch(t[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}},e.getAndroidVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),!(1===t[0]&&t[1]<5))return 1===t[0]&&t[1]<6?"Cupcake":1===t[0]&&t[1]>=6?"Donut":2===t[0]&&t[1]<2?"Eclair":2===t[0]&&2===t[1]?"Froyo":2===t[0]&&t[1]>2?"Gingerbread":3===t[0]?"Honeycomb":4===t[0]&&t[1]<1?"Ice Cream Sandwich":4===t[0]&&t[1]<4?"Jelly Bean":4===t[0]&&t[1]>=4?"KitKat":5===t[0]?"Lollipop":6===t[0]?"Marshmallow":7===t[0]?"Nougat":8===t[0]?"Oreo":9===t[0]?"Pie":void 0},e.getVersionPrecision=function(e){return e.split(".").length},e.compareVersions=function(t,n,r){void 0===r&&(r=!1);var i=e.getVersionPrecision(t),o=e.getVersionPrecision(n),s=Math.max(i,o),a=0,u=e.map([t,n],(function(t){var n=s-e.getVersionPrecision(t),r=t+new Array(n+1).join(".0");return e.map(r.split("."),(function(e){return new Array(20-e.length).join("0")+e})).reverse()}));for(r&&(a=s-Math.min(i,o)),s-=1;s>=a;){if(u[0][s]>u[1][s])return 1;if(u[0][s]===u[1][s]){if(s===a)return 0;s-=1}else if(u[0][s]<u[1][s])return-1}},e.map=function(e,t){var n,r=[];if(Array.prototype.map)return Array.prototype.map.call(e,t);for(n=0;n<e.length;n+=1)r.push(t(e[n]));return r},e.find=function(e,t){var n,r;if(Array.prototype.find)return Array.prototype.find.call(e,t);for(n=0,r=e.length;n<r;n+=1){var i=e[n];if(t(i,n))return i}},e.assign=function(e){for(var t,n,r=e,i=arguments.length,o=new Array(i>1?i-1:0),s=1;s<i;s++)o[s-1]=arguments[s];if(Object.assign)return Object.assign.apply(Object,[e].concat(o));var a=function(){var e=o[t];"object"==typeof e&&null!==e&&Object.keys(e).forEach((function(t){r[t]=e[t]}))};for(t=0,n=o.length;t<n;t+=1)a();return e},e.getBrowserAlias=function(e){return r.BROWSER_ALIASES_MAP[e]},e.getBrowserTypeByAlias=function(e){return r.BROWSER_MAP[e]||""},e}();t.default=i,e.exports=t.default},18:function(e,t,n){"use strict";t.__esModule=!0,t.ENGINE_MAP=t.OS_MAP=t.PLATFORMS_MAP=t.BROWSER_MAP=t.BROWSER_ALIASES_MAP=void 0,t.BROWSER_ALIASES_MAP={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},t.BROWSER_MAP={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},t.PLATFORMS_MAP={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},t.OS_MAP={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},t.ENGINE_MAP={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"}},90:function(e,t,n){"use strict";t.__esModule=!0,t.default=void 0;var r,i=(r=n(91))&&r.__esModule?r:{default:r},o=n(18);function s(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var a=function(){function e(){}var t,n;return e.getParser=function(e,t){if(void 0===t&&(t=!1),"string"!=typeof e)throw new Error("UserAgent should be a string");return new i.default(e,t)},e.parse=function(e){return new i.default(e).getResult()},t=e,n=[{key:"BROWSER_MAP",get:function(){return o.BROWSER_MAP}},{key:"ENGINE_MAP",get:function(){return o.ENGINE_MAP}},{key:"OS_MAP",get:function(){return o.OS_MAP}},{key:"PLATFORMS_MAP",get:function(){return o.PLATFORMS_MAP}}],null&&s(t.prototype,null),n&&s(t,n),e}();t.default=a,e.exports=t.default},91:function(e,t,n){"use strict";t.__esModule=!0,t.default=void 0;var r=u(n(92)),i=u(n(93)),o=u(n(94)),s=u(n(95)),a=u(n(17));function u(e){return e&&e.__esModule?e:{default:e}}var c=function(){function e(e,t){if(void 0===t&&(t=!1),null==e||""===e)throw new Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},!0!==t&&this.parse()}var t=e.prototype;return t.getUA=function(){return this._ua},t.test=function(e){return e.test(this._ua)},t.parseBrowser=function(){var e=this;this.parsedResult.browser={};var t=a.default.find(r.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.browser=t.describe(this.getUA())),this.parsedResult.browser},t.getBrowser=function(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()},t.getBrowserName=function(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""},t.getBrowserVersion=function(){return this.getBrowser().version},t.getOS=function(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()},t.parseOS=function(){var e=this;this.parsedResult.os={};var t=a.default.find(i.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.os=t.describe(this.getUA())),this.parsedResult.os},t.getOSName=function(e){var t=this.getOS().name;return e?String(t).toLowerCase()||"":t||""},t.getOSVersion=function(){return this.getOS().version},t.getPlatform=function(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()},t.getPlatformType=function(e){void 0===e&&(e=!1);var t=this.getPlatform().type;return e?String(t).toLowerCase()||"":t||""},t.parsePlatform=function(){var e=this;this.parsedResult.platform={};var t=a.default.find(o.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.platform=t.describe(this.getUA())),this.parsedResult.platform},t.getEngine=function(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()},t.getEngineName=function(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""},t.parseEngine=function(){var e=this;this.parsedResult.engine={};var t=a.default.find(s.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.engine=t.describe(this.getUA())),this.parsedResult.engine},t.parse=function(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this},t.getResult=function(){return a.default.assign({},this.parsedResult)},t.satisfies=function(e){var t=this,n={},r=0,i={},o=0;if(Object.keys(e).forEach((function(t){var s=e[t];"string"==typeof s?(i[t]=s,o+=1):"object"==typeof s&&(n[t]=s,r+=1)})),r>0){var s=Object.keys(n),u=a.default.find(s,(function(e){return t.isOS(e)}));if(u){var c=this.satisfies(n[u]);if(void 0!==c)return c}var d=a.default.find(s,(function(e){return t.isPlatform(e)}));if(d){var l=this.satisfies(n[d]);if(void 0!==l)return l}}if(o>0){var f=Object.keys(i),h=a.default.find(f,(function(e){return t.isBrowser(e,!0)}));if(void 0!==h)return this.compareVersion(i[h])}},t.isBrowser=function(e,t){void 0===t&&(t=!1);var n=this.getBrowserName().toLowerCase(),r=e.toLowerCase(),i=a.default.getBrowserTypeByAlias(r);return t&&i&&(r=i.toLowerCase()),r===n},t.compareVersion=function(e){var t=[0],n=e,r=!1,i=this.getBrowserVersion();if("string"==typeof i)return">"===e[0]||"<"===e[0]?(n=e.substr(1),"="===e[1]?(r=!0,n=e.substr(2)):t=[],">"===e[0]?t.push(1):t.push(-1)):"="===e[0]?n=e.substr(1):"~"===e[0]&&(r=!0,n=e.substr(1)),t.indexOf(a.default.compareVersions(i,n,r))>-1},t.isOS=function(e){return this.getOSName(!0)===String(e).toLowerCase()},t.isPlatform=function(e){return this.getPlatformType(!0)===String(e).toLowerCase()},t.isEngine=function(e){return this.getEngineName(!0)===String(e).toLowerCase()},t.is=function(e,t){return void 0===t&&(t=!1),this.isBrowser(e,t)||this.isOS(e)||this.isPlatform(e)},t.some=function(e){var t=this;return void 0===e&&(e=[]),e.some((function(e){return t.is(e)}))},e}();t.default=c,e.exports=t.default},92:function(e,t,n){"use strict";t.__esModule=!0,t.default=void 0;var r,i=(r=n(17))&&r.__esModule?r:{default:r},o=/version\/(\d+(\.?_?\d+)+)/i,s=[{test:[/googlebot/i],describe:function(e){var t={name:"Googlebot"},n=i.default.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/opera/i],describe:function(e){var t={name:"Opera"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/opr\/|opios/i],describe:function(e){var t={name:"Opera"},n=i.default.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/SamsungBrowser/i],describe:function(e){var t={name:"Samsung Internet for Android"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/Whale/i],describe:function(e){var t={name:"NAVER Whale Browser"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/MZBrowser/i],describe:function(e){var t={name:"MZ Browser"},n=i.default.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/focus/i],describe:function(e){var t={name:"Focus"},n=i.default.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/swing/i],describe:function(e){var t={name:"Swing"},n=i.default.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/coast/i],describe:function(e){var t={name:"Opera Coast"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe:function(e){var t={name:"Opera Touch"},n=i.default.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/yabrowser/i],describe:function(e){var t={name:"Yandex Browser"},n=i.default.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/ucbrowser/i],describe:function(e){var t={name:"UC Browser"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/Maxthon|mxios/i],describe:function(e){var t={name:"Maxthon"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/epiphany/i],describe:function(e){var t={name:"Epiphany"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/puffin/i],describe:function(e){var t={name:"Puffin"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/sleipnir/i],describe:function(e){var t={name:"Sleipnir"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/k-meleon/i],describe:function(e){var t={name:"K-Meleon"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/micromessenger/i],describe:function(e){var t={name:"WeChat"},n=i.default.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/qqbrowser/i],describe:function(e){var t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},n=i.default.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/msie|trident/i],describe:function(e){var t={name:"Internet Explorer"},n=i.default.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/\sedg\//i],describe:function(e){var t={name:"Microsoft Edge"},n=i.default.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/edg([ea]|ios)/i],describe:function(e){var t={name:"Microsoft Edge"},n=i.default.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/vivaldi/i],describe:function(e){var t={name:"Vivaldi"},n=i.default.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/seamonkey/i],describe:function(e){var t={name:"SeaMonkey"},n=i.default.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/sailfish/i],describe:function(e){var t={name:"Sailfish"},n=i.default.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return n&&(t.version=n),t}},{test:[/silk/i],describe:function(e){var t={name:"Amazon Silk"},n=i.default.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/phantom/i],describe:function(e){var t={name:"PhantomJS"},n=i.default.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/slimerjs/i],describe:function(e){var t={name:"SlimerJS"},n=i.default.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t={name:"BlackBerry"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t={name:"WebOS Browser"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/bada/i],describe:function(e){var t={name:"Bada"},n=i.default.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/tizen/i],describe:function(e){var t={name:"Tizen"},n=i.default.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/qupzilla/i],describe:function(e){var t={name:"QupZilla"},n=i.default.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/firefox|iceweasel|fxios/i],describe:function(e){var t={name:"Firefox"},n=i.default.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/electron/i],describe:function(e){var t={name:"Electron"},n=i.default.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/MiuiBrowser/i],describe:function(e){var t={name:"Miui"},n=i.default.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/chromium/i],describe:function(e){var t={name:"Chromium"},n=i.default.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/chrome|crios|crmo/i],describe:function(e){var t={name:"Chrome"},n=i.default.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/GSA/i],describe:function(e){var t={name:"Google Search"},n=i.default.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:function(e){var t=!e.test(/like android/i),n=e.test(/android/i);return t&&n},describe:function(e){var t={name:"Android Browser"},n=i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/playstation 4/i],describe:function(e){var t={name:"PlayStation 4"},n=i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/safari|applewebkit/i],describe:function(e){var t={name:"Safari"},n=i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/.*/i],describe:function(e){var t=-1!==e.search("\\(")?/^(.*)\/(.*)[ \t]\((.*)/:/^(.*)\/(.*) /;return{name:i.default.getFirstMatch(t,e),version:i.default.getSecondMatch(t,e)}}}];t.default=s,e.exports=t.default},93:function(e,t,n){"use strict";t.__esModule=!0,t.default=void 0;var r,i=(r=n(17))&&r.__esModule?r:{default:r},o=n(18),s=[{test:[/Roku\/DVP/],describe:function(e){var t=i.default.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:o.OS_MAP.Roku,version:t}}},{test:[/windows phone/i],describe:function(e){var t=i.default.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.WindowsPhone,version:t}}},{test:[/windows /i],describe:function(e){var t=i.default.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),n=i.default.getWindowsVersionName(t);return{name:o.OS_MAP.Windows,version:t,versionName:n}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(e){var t={name:o.OS_MAP.iOS},n=i.default.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return n&&(t.version=n),t}},{test:[/macintosh/i],describe:function(e){var t=i.default.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),n=i.default.getMacOSVersionName(t),r={name:o.OS_MAP.MacOS,version:t};return n&&(r.versionName=n),r}},{test:[/(ipod|iphone|ipad)/i],describe:function(e){var t=i.default.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:o.OS_MAP.iOS,version:t}}},{test:function(e){var t=!e.test(/like android/i),n=e.test(/android/i);return t&&n},describe:function(e){var t=i.default.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),n=i.default.getAndroidVersionName(t),r={name:o.OS_MAP.Android,version:t};return n&&(r.versionName=n),r}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t=i.default.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),n={name:o.OS_MAP.WebOS};return t&&t.length&&(n.version=t),n}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t=i.default.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||i.default.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||i.default.getFirstMatch(/\bbb(\d+)/i,e);return{name:o.OS_MAP.BlackBerry,version:t}}},{test:[/bada/i],describe:function(e){var t=i.default.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.Bada,version:t}}},{test:[/tizen/i],describe:function(e){var t=i.default.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.Tizen,version:t}}},{test:[/linux/i],describe:function(){return{name:o.OS_MAP.Linux}}},{test:[/CrOS/],describe:function(){return{name:o.OS_MAP.ChromeOS}}},{test:[/PlayStation 4/],describe:function(e){var t=i.default.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.PlayStation4,version:t}}}];t.default=s,e.exports=t.default},94:function(e,t,n){"use strict";t.__esModule=!0,t.default=void 0;var r,i=(r=n(17))&&r.__esModule?r:{default:r},o=n(18),s=[{test:[/googlebot/i],describe:function(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe:function(e){var t=i.default.getFirstMatch(/(can-l01)/i,e)&&"Nova",n={type:o.PLATFORMS_MAP.mobile,vendor:"Huawei"};return t&&(n.model=t),n}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet}}},{test:function(e){var t=e.test(/ipod|iphone/i),n=e.test(/like (ipod|iphone)/i);return t&&!n},describe:function(e){var t=i.default.getFirstMatch(/(ipod|iphone)/i,e);return{type:o.PLATFORMS_MAP.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"blackberry"===e.getBrowserName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"BlackBerry"}}},{test:function(e){return"bada"===e.getBrowserName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"windows phone"===e.getBrowserName()},describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"Microsoft"}}},{test:function(e){var t=Number(String(e.getOSVersion()).split(".")[0]);return"android"===e.getOSName(!0)&&t>=3},describe:function(){return{type:o.PLATFORMS_MAP.tablet}}},{test:function(e){return"android"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"macos"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop,vendor:"Apple"}}},{test:function(e){return"windows"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop}}},{test:function(e){return"linux"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop}}},{test:function(e){return"playstation 4"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.tv}}},{test:function(e){return"roku"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.tv}}}];t.default=s,e.exports=t.default},95:function(e,t,n){"use strict";t.__esModule=!0,t.default=void 0;var r,i=(r=n(17))&&r.__esModule?r:{default:r},o=n(18),s=[{test:function(e){return"microsoft edge"===e.getBrowserName(!0)},describe:function(e){if(/\sedg\//i.test(e))return{name:o.ENGINE_MAP.Blink};var t=i.default.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:o.ENGINE_MAP.EdgeHTML,version:t}}},{test:[/trident/i],describe:function(e){var t={name:o.ENGINE_MAP.Trident},n=i.default.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:function(e){return e.test(/presto/i)},describe:function(e){var t={name:o.ENGINE_MAP.Presto},n=i.default.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:function(e){var t=e.test(/gecko/i),n=e.test(/like gecko/i);return t&&!n},describe:function(e){var t={name:o.ENGINE_MAP.Gecko},n=i.default.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/(apple)?webkit\/537\.36/i],describe:function(){return{name:o.ENGINE_MAP.Blink}}},{test:[/(apple)?webkit/i],describe:function(e){var t={name:o.ENGINE_MAP.WebKit},n=i.default.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}}];t.default=s,e.exports=t.default}})},620:function(e){"use strict";var t,n="object"==typeof Reflect?Reflect:null,r=n&&"function"==typeof n.apply?n.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)};t=n&&"function"==typeof n.ownKeys?n.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var i=Number.isNaN||function(e){return e!=e};function o(){o.init.call(this)}e.exports=o,e.exports.once=function(e,t){return new Promise((function(n,r){function i(n){e.removeListener(t,o),r(n)}function o(){"function"==typeof e.removeListener&&e.removeListener("error",i),n([].slice.call(arguments))}p(e,t,o,{once:!0}),"error"!==t&&function(e,t,n){"function"==typeof e.on&&p(e,"error",t,{once:!0})}(e,i)}))},o.EventEmitter=o,o.prototype._events=void 0,o.prototype._eventsCount=0,o.prototype._maxListeners=void 0;var s=10;function a(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function u(e){return void 0===e._maxListeners?o.defaultMaxListeners:e._maxListeners}function c(e,t,n,r){var i,o,s,c;if(a(n),void 0===(o=e._events)?(o=e._events=Object.create(null),e._eventsCount=0):(void 0!==o.newListener&&(e.emit("newListener",t,n.listener?n.listener:n),o=e._events),s=o[t]),void 0===s)s=o[t]=n,++e._eventsCount;else if("function"==typeof s?s=o[t]=r?[n,s]:[s,n]:r?s.unshift(n):s.push(n),(i=u(e))>0&&s.length>i&&!s.warned){s.warned=!0;var d=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");d.name="MaxListenersExceededWarning",d.emitter=e,d.type=t,d.count=s.length,c=d,console&&console.warn&&console.warn(c)}return e}function d(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function l(e,t,n){var r={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},i=d.bind(r);return i.listener=n,r.wrapFn=i,i}function f(e,t,n){var r=e._events;if(void 0===r)return[];var i=r[t];return void 0===i?[]:"function"==typeof i?n?[i.listener||i]:[i]:n?function(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}(i):v(i,i.length)}function h(e){var t=this._events;if(void 0!==t){var n=t[e];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function v(e,t){for(var n=new Array(t),r=0;r<t;++r)n[r]=e[r];return n}function p(e,t,n,r){if("function"==typeof e.on)r.once?e.once(t,n):e.on(t,n);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function i(o){r.once&&e.removeEventListener(t,i),n(o)}))}}Object.defineProperty(o,"defaultMaxListeners",{enumerable:!0,get:function(){return s},set:function(e){if("number"!=typeof e||e<0||i(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");s=e}}),o.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},o.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||i(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},o.prototype.getMaxListeners=function(){return u(this)},o.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var i="error"===e,o=this._events;if(void 0!==o)i=i&&void 0===o.error;else if(!i)return!1;if(i){var s;if(t.length>0&&(s=t[0]),s instanceof Error)throw s;var a=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw a.context=s,a}var u=o[e];if(void 0===u)return!1;if("function"==typeof u)r(u,this,t);else{var c=u.length,d=v(u,c);for(n=0;n<c;++n)r(d[n],this,t)}return!0},o.prototype.addListener=function(e,t){return c(this,e,t,!1)},o.prototype.on=o.prototype.addListener,o.prototype.prependListener=function(e,t){return c(this,e,t,!0)},o.prototype.once=function(e,t){return a(t),this.on(e,l(this,e,t)),this},o.prototype.prependOnceListener=function(e,t){return a(t),this.prependListener(e,l(this,e,t)),this},o.prototype.removeListener=function(e,t){var n,r,i,o,s;if(a(t),void 0===(r=this._events))return this;if(void 0===(n=r[e]))return this;if(n===t||n.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete r[e],r.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(i=-1,o=n.length-1;o>=0;o--)if(n[o]===t||n[o].listener===t){s=n[o].listener,i=o;break}if(i<0)return this;0===i?n.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(n,i),1===n.length&&(r[e]=n[0]),void 0!==r.removeListener&&this.emit("removeListener",e,s||t)}return this},o.prototype.off=o.prototype.removeListener,o.prototype.removeAllListeners=function(e){var t,n,r;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[e]),this;if(0===arguments.length){var i,o=Object.keys(n);for(r=0;r<o.length;++r)"removeListener"!==(i=o[r])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=n[e]))this.removeListener(e,t);else if(void 0!==t)for(r=t.length-1;r>=0;r--)this.removeListener(e,t[r]);return this},o.prototype.listeners=function(e){return f(this,e,!0)},o.prototype.rawListeners=function(e){return f(this,e,!1)},o.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):h.call(e,t)},o.prototype.listenerCount=h,o.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]}},648:function(e,t,n){var r=n(288).default;function i(){"use strict";e.exports=i=function(){return t},e.exports.__esModule=!0,e.exports.default=e.exports;var t={},n=Object.prototype,o=n.hasOwnProperty,s=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},u=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",d=a.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,n){return e[t]=n}}function f(e,t,n,r){var i=t&&t.prototype instanceof p?t:p,o=Object.create(i.prototype),a=new C(r||[]);return s(o,"_invoke",{value:M(e,n,a)}),o}function h(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var v={};function p(){}function m(){}function g(){}var y={};l(y,u,(function(){return this}));var b=Object.getPrototypeOf,S=b&&b(b(_([])));S&&S!==n&&o.call(S,u)&&(y=S);var k=g.prototype=p.prototype=Object.create(y);function E(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function n(i,s,a,u){var c=h(e[i],e,s);if("throw"!==c.type){var d=c.arg,l=d.value;return l&&"object"==r(l)&&o.call(l,"__await")?t.resolve(l.__await).then((function(e){n("next",e,a,u)}),(function(e){n("throw",e,a,u)})):t.resolve(l).then((function(e){d.value=e,a(d)}),(function(e){return n("throw",e,a,u)}))}u(c.arg)}var i;s(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,i){n(e,r,t,i)}))}return i=i?i.then(o,o):o()}})}function M(e,t,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return{value:void 0,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var a=P(s,n);if(a){if(a===v)continue;return a}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var u=h(e,t,n);if("normal"===u.type){if(r=n.done?"completed":"suspendedYield",u.arg===v)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(r="completed",n.method="throw",n.arg=u.arg)}}}function P(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,P(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=h(r,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,v;var o=i.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,v):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,v)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function A(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function _(e){if(e){var t=e[u];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,r=function t(){for(;++n<e.length;)if(o.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:L}}function L(){return{value:void 0,done:!0}}return m.prototype=g,s(k,"constructor",{value:g,configurable:!0}),s(g,"constructor",{value:m,configurable:!0}),m.displayName=l(g,d,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,l(e,d,"GeneratorFunction")),e.prototype=Object.create(k),e},t.awrap=function(e){return{__await:e}},E(w.prototype),l(w.prototype,c,(function(){return this})),t.AsyncIterator=w,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var s=new w(f(e,n,r,i),o);return t.isGeneratorFunction(n)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},E(k),l(k,d,"Generator"),l(k,u,(function(){return this})),l(k,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=_,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(A),!e)for(var t in this)"t"===t.charAt(0)&&o.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(n,r){return s.type="throw",s.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],s=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var a=o.call(i,"catchLoc"),u=o.call(i,"finallyLoc");if(a&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(a){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var s=i?i.completion:{};return s.type=e,s.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),A(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;A(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:_(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),v}},t}e.exports=i,e.exports.__esModule=!0,e.exports.default=e.exports},288:function(e){function t(n){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},357:function(e,t,n){var r=n(648)();e.exports=r;try{regeneratorRuntime=r}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=r:Function("r","regeneratorRuntime = r")(r)}}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,n),o.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};var r={};!function(){"use strict";n.d(r,{registerIVSTech:function(){return Yt}});var e=function(e){return e.GENERIC="Error",e.NOT_SUPPORTED="ErrorNotSupported",e.NO_SOURCE="ErrorNoSource",e.INVALID_DATA="ErrorInvalidData",e.INVALID_STATE="ErrorInvalidState",e.INVALID_PARAMETER="ErrorInvalidParameter",e.TIMEOUT="ErrorTimeout",e.NETWORK="ErrorNetwork",e.NETWORK_IO="ErrorNetworkIO",e.AUTHORIZATION="ErrorAuthorization",e.NOT_AVAILABLE="ErrorNotAvailable",e}({}),t=function(e){return e.ID3="MetaID3",e.CAPTION="MetaCaption",e}({}),i=function(e){return e.METADATA_ID="metadata.live-video.net",e.INBAND_METADATA_ID="inband.metadata.live-video.net",e}({}),o=function(e){return e.INITIALIZED="PlayerInitialized",e.QUALITY_CHANGED="PlayerQualityChanged",e.DURATION_CHANGED="PlayerDurationChanged",e.VOLUME_CHANGED="PlayerVolumeChanged",e.MUTED_CHANGED="PlayerMutedChanged",e.PLAYBACK_RATE_CHANGED="PlayerPlaybackRateChanged",e.REBUFFERING="PlayerRebuffering",e.AUDIO_BLOCKED="PlayerAudioBlocked",e.PLAYBACK_BLOCKED="PlayerPlaybackBlocked",e.ERROR="PlayerError",e.RECOVERABLE_ERROR="PlayerRecoverableError",e.ANALYTICS_EVENT="PlayerAnalyticsEvent",e.TIME_UPDATE="PlayerTimeUpdate",e.BUFFER_UPDATE="PlayerBufferUpdate",e.SEEK_COMPLETED="PlayerSeekCompleted",e.SESSION_DATA="PlayerSessionData",e.STATE_CHANGED="PlayerStateChanged",e.WORKER_ERROR="PlayerWorkerError",e.METADATA="PlayerMetadata",e.TEXT_CUE="PlayerTextCue",e.TEXT_METADATA_CUE="PlayerTextMetadataCue",e.AD_CUE="PlayerAdCue",e.STREAM_SOURCE_CUE="PlayerStreamSourceCue",e.NETWORK_UNAVAILABLE="PlayerNetworkUnavailable",e.SEGMENT_DISCONTINUITY="PlayerSegmentDiscontinuity",e.SEGMENT_METADATA="PlayerSegmentMetadata",e}({}),s=function(e){return e.IDLE="Idle",e.READY="Ready",e.BUFFERING="Buffering",e.PLAYING="Playing",e.ENDED="Ended",e}({}),a=n(481);function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function c(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==u(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i),"symbol"===u(o)?o:String(o)),r)}var i,o}function d(e,t,n){return t&&c(e.prototype,t),n&&c(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var l=101,f=function(){function e(){this.buffer=void 0,this.head=void 0,this.tail=void 0,this.buffer=[],this.head=0,this.tail=0}var t=e.prototype;return t.push=function(e){this.tail===this.buffer.length?this.buffer.push(e):this.buffer[this.tail]=e,this.tail++},t.pop=function(){var e,t=null!=(e=this.buffer[this.head])?e:null;return this.buffer[this.head]=null,this.head++,this.empty()&&(this.head=0,this.tail=0),t},t.size=function(){return this.tail-this.head},t.empty=function(){return this.head>=this.tail},e}();function h(e){try{return JSON.parse(e)}catch(t){return console.error("Failed JSON parse:",e),{}}}function v(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}function p(e){return""===e.codecs||"undefined"==typeof MediaSource||MediaSource.isTypeSupported('video/mp4;codecs="'+e.codecs+'"')}function m(e){var t,n;return void 0!==e.hidden?(t="hidden",n="visibilitychange"):void 0!==e.msHidden?(t="msHidden",n="msvisibilitychange"):void 0!==e.webkitHidden&&(t="webkitHidden",n="webkitvisibilitychange"),{hidden:t,visibilityChange:n}}function g(e,t,n){return Math.min(n,Math.max(t,e))}function y(e){e.removeAttribute("src")}var b=.1,S=1<<30,k=12e4,E=3e3,w={audio:1936684398,video:1986618469};function M(e){return"number"==typeof e.webkitDecodedFrameCount?e.webkitDecodedFrameCount:"function"==typeof e.getVideoPlaybackQuality?e.getVideoPlaybackQuality().totalVideoFrames:"number"==typeof e.mozDecodedFrames?e.mozDecodedFrames:-1}function P(e){return"number"==typeof e.webkitDroppedFrameCount?e.webkitDroppedFrameCount:"function"==typeof e.getVideoPlaybackQuality?e.getVideoPlaybackQuality().droppedVideoFrames:-1}function T(e,t){for(var n=0;n<t.length;n++)console.info(e,"start: ",t.start(n),", end: ",t.end(n))}function A(e,t,n){for(var r=0;r<e.length;r++){var i=e.start(r),o=e.end(r);if(!(o<=t)){if(i-n>t)break;for(var s=r+1;s<e.length&&!(e.start(s)-o>n);s++)o=e.end(s);for(var a=r-1;a>=0&&!(i-e.end(a)>n);a--)i=e.start(a);return{start:Math.min(i,t),end:o}}}return{start:t,end:t}}function C(e,t,n){void 0===n&&(n=b);var r=A(e,t,n).end-t>n;if(e.length>1||!r)for(var i=0;i<e.length;i++){var o=e.start(i),s=e.end(i);if(t<o&&s-o>n)return o+n}return r?t+n:t}function _(e,t,n){return e.addEventListener(t,n),function(){e.removeEventListener(t,n)}}function L(e){if(e.src){var t=e.src;y(e),e.load(),URL.revokeObjectURL(t)}}function D(e){var t=(e||"").match(/codecs="([^.]+)\./);return t&&t[1]?t[1]:""}var R=function(){function e(e,t,n,r,i){this.rawCodec=t,this.group=n,this.isProtected=r,this.onError=i,this.pending=void 0,this.unsubscribers=[],this.srcBuf=void 0,this.blocked=!1,this.srcBuf=e,this.pending=new f,this.unsubscribers.push(_(e,"updateend",this.process.bind(this)))}var t=e.prototype;return t.getBufferedRanges=function(){try{var e=[];if(this.srcBuf)for(var t=this.srcBuf.buffered,n=0;n<t.length;n++)e.push({start:t.start(n),end:t.end(n)});return e}catch(e){return[]}},t.abort=function(){this.schedule((function(e){e.abort()}))},t.changeType=function(e){this.rawCodec=function(e){var t=(e||"").match(/codecs=".+"/);return t&&t[0]?t[0]:'codecs=""'}(e),this.schedule((function(t){t.changeType(e)}))},t.appendBuffer=function(e){this.schedule((function(t){try{t.appendBuffer(e)}catch(e){if("QuotaExceededError"!==e.name)throw e;var n=t.buffered,r=n.start(0),i=n.end(n.length-1),o=(r+i)/2;t.remove(o,i)}}))},t.setTimestampOffset=function(e){this.schedule((function(t){t.timestampOffset=e}))},t.remove=function(e,t){this.schedule((function(n){var r=n.buffered;if(r.length){var i=Math.max(e,r.start(0)),o=Math.min(t,r.end(r.length-1));i<o&&n.remove(i,o)}}))},t.block=function(){var e=this;return new Promise((function(t){e.schedule((function(){e.blocked=!0,t()}))}))},t.unblock=function(){this.blocked=!1,this.process()},t.destroy=function(){this.pending=new f,this.unsubscribers.forEach((function(e){return e()})),this.srcBuf=void 0},t.schedule=function(e){this.pending.empty()&&this.canProcess()?this.safeExecute(e):(this.pending.push(e),this.process())},t.safeExecute=function(e){try{if(!this.srcBuf)throw new Error("srcBuf is undefined");e(this.srcBuf)}catch(e){this.onError(e,!1)}},t.process=function(){for(;!this.pending.empty()&&this.canProcess();)this.safeExecute(this.pending.pop())},t.canProcess=function(){return!(!this.srcBuf||this.srcBuf.updating||this.blocked)},d(e,[{key:"buffer",get:function(){return this.srcBuf}},{key:"codec",get:function(){return this.rawCodec}},{key:"timestampOffset",get:function(){return this.buffer?this.buffer.timestampOffset:0}}]),e}(),O=function(){function e(e,t,n){this.mediaSource=e,this.onEnded=t,this.onError=n,this.sourceBuffers=Object.create(null),this.unsubscribers=[],this.unsubscribers.push(_(e,"sourceended",this.onEnded))}e.isSupported=function(){return void 0!==self.MediaSource},e.isSupportedInWorker=function(){return e.isSupported()&&MediaSource.canConstructInDedicatedWorker&&"function"==typeof MediaSourceHandle},e.create=function(t,n){var r=new MediaSource,i=new Promise((function(i,o){var s=_(r,"sourceopen",(function(){"open"===r.readyState?(i(new e(r,t,n)),s()):o("The MediaSource was closed upon opening")}))}));return{ms:r,sink:i}};var t=e.prototype;return t.getBufferedRanges=function(e){var t,n;return null!=(t=null==(n=this.sourceBuffers[w[e]])?void 0:n.getBufferedRanges())?t:[]},t.addTrack=function(e,t,n,r){var i=this.mediaSource,o=this.sourceBuffers,s=function(e){return"video/mp4;"+e}(t);if(o[e]){var a,u=D(o[e].codec);return D(t)!==u&&o[e].changeType(s),null!=(a=o[e].buffer)?a:null}try{var c=i.addSourceBuffer(s);return o[e]=new R(c,t,n,r,this.handleError.bind(this)),c}catch(e){this.handleError(e,"open"===i.readyState)}return null},t.append=function(e,t){var n;null==(n=this.sourceBuffers[e])||n.appendBuffer(t)},t.remove=function(e,t){for(var n=this.sourceBuffers,r=0,i=Object.keys(n);r<i.length;r++)n[i[r]].remove(e,t)},t.setTimestampOffset=function(e,t){var n=this.sourceBuffers[e];n&&(n.abort(),n.setTimestampOffset(t))},t.setDuration=function(e){var t=this;this.scheduleUpdate((function(){return t.mediaSource.duration=e})).catch((function(e){return t.handleError(e,!1)}))},t.setLiveSeekableRange=function(e,t){var n=this;this.scheduleUpdate((function(){return n.mediaSource.setLiveSeekableRange(e,t)})).catch((function(e){return n.handleError(e,!1)}))},t.scheduleUpdate=function(e){var t=this;void 0===e&&(e=I);var n=Object.keys(this.sourceBuffers).map((function(e){return t.sourceBuffers[e]}));return Promise.all(n.map((function(e){return e.block()}))).then(e).then((function(){return n.forEach((function(e){return e.unblock()}))}))},t.isDrmProtected=function(){return this.bufferProperties.some((function(e){return e.isProtected}))},t.isAudioOnly=function(){return 1===this.bufferProperties.length&&"audio_only"===this.bufferProperties[0].group},t.destroy=function(){this.destroySourceBuffers(),this.unsubscribers.forEach((function(e){return e()})),this.unsubscribers=[]},t.handleError=function(e,t){var n=e.code||102,r=102;"NotSupportedError"===e.name&&(r=n=4),this.onError(r,n,e.message,t)},t.destroySourceBuffers=function(){for(var e=this.mediaSource;e.sourceBuffers.length>0;)try{e.removeSourceBuffer(e.sourceBuffers[0])}catch(e){this.handleError(e,!1);break}for(var t=0,n=Object.keys(this.sourceBuffers);t<n.length;t++){var r=n[t];this.sourceBuffers[r].destroy()}this.sourceBuffers=Object.create(null)},d(e,[{key:"duration",get:function(){return this.mediaSource.duration}},{key:"bufferProperties",get:function(){var e=this.sourceBuffers;return Object.keys(e).map((function(t){var n=e[t];return{trackID:Number(t),codec:n.codec,mode:"mse",path:"",isProtected:n.isProtected,group:n.group,srcObj:null}}))}}]),e}(),I=function(){},x=/^(\d+)\.(\d+)\.(\d+)[+|-]?(.*)?$/,N=/^(\d+)\.(\d+)[+|-]?(.*)?$/,F=/^(\d+)$/,V={chrome:!1,chromecast:!1,domain:"",electron:!1,family:"",firefox:!1,host:"",major:-1,minor:-1,msEdgeLegacy:!1,msEdgeChromium:!1,msIE:!1,name:"",opera:!1,osName:"",osVersion:"",patch:-1,safari:!1,url:"",userAgent:"",mobile:!1,supportsDataChannels:!1,supportsWebTransport:!1,supportsMSEInWorkers:!1},B=null;function U(){if(B)return B;if("undefined"==typeof window||"undefined"==typeof navigator)return B=V;var e,t,n=a.getParser(navigator.userAgent),r=(e=String(n.getBrowserVersion()),t=x.exec(e)||N.exec(e)||F.exec(e)||[],{major:parseInt(t[1],10)||0,minor:parseInt(t[2],10)||0,patch:parseInt(t[3],10)||0}),i=n.getEngine();return B={chrome:!!n.some(["chrome"]),chromecast:navigator.userAgent.toLowerCase().indexOf("crkey")>-1,domain:window.location.host.split(".").slice(-2).join("."),electron:!!n.some(["electron"]),family:n.getBrowserName().toLowerCase(),firefox:!!n.some(["firefox"]),host:window.location.host,major:r.major,minor:r.minor,msEdgeLegacy:!!n.some(["microsoft edge"])&&"Blink"!==i.name,msEdgeChromium:!!n.some(["microsoft edge"])&&"Blink"===i.name,msIE:!!n.some(["internet explorer"]),name:navigator.appVersion,opera:!!n.some(["opera"]),osName:n.getOSName(),osVersion:String(n.getOSVersion()),patch:r.patch,safari:!!n.some(["safari"]),url:window.location.href,userAgent:navigator.userAgent,mobile:!(!n.some(["mobile"])&&!n.some(["tablet"])),supportsDataChannels:"RTCPeerConnection"in window,supportsWebTransport:"WebTransport"in window,supportsMSEInWorkers:O.isSupportedInWorker()}}function j(){return j=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},j.apply(this,arguments)}var H=null,W=null;function G(e){var t=e.type===o.ANALYTICS_EVENT,n=e.arg&&"video_error"===e.arg.name;if(t&&n){if(!H&&!W){var r=document.createElement("canvas");try{var i=r.getContext("webgl")||r.getContext("experimental-webgl");if(i&&"getExtension"in i){var s=i.getExtension("WEBGL_debug_renderer_info");s&&"getParameter"in i&&(H=i.getParameter(s.UNMASKED_RENDERER_WEBGL),W=i.getParameter(s.UNMASKED_VENDOR_WEBGL))}}catch(e){}}e.arg.properties.gl_renderer=H,e.arg.properties.gl_vendor=W}return e}var q=function(){function e(){var t=this;this.batteryManager=void 0,this.processor=this.processor.bind(this),e.isSupported()&&window.navigator.getBattery().then((function(e){t.batteryManager=e}))}return e.isSupported=function(){var e,t;return void 0!==(null==(e=window)||null==(t=e.navigator)?void 0:t.getBattery)},e.prototype.processor=function(e){var t=e.type===o.ANALYTICS_EVENT,n=e.arg&&"minute-watched"===e.arg.name;return t&&n&&this.batteryManager&&(e.arg.properties.battery_percent=this.batteryManager.level),e},e}();function K(){return"undefined"!=typeof MediaSource}var Q={keySystem:"org.w3.clearkey",uuid:"1077efec-c0b2-4d02-ace3-3c1e52e2fb4b"},z={keySystem:"com.apple.fps.2_0",certUrl:"https://fp-keyos-twitch.licensekeyserver.com/cert/a17fd33d3843df9b17679ccf50a419b2.der",licenseUrl:"https://fp-keyos-twitch.licensekeyserver.com/getkey",uuid:"94CE86FB-07FF-4F43-ADB8-93D2FA968CA2"},Y={keySystem:"com.microsoft.playready",licenseUrl:"https://pr-keyos-twitch.licensekeyserver.com/core/rightsmanager.asmx",uuid:"9a04f079-9840-4286-ab92-e65be0885f95"},X={keySystem:"com.widevine.alpha",licenseUrl:"https://wv-keyos-twitch.licensekeyserver.com",uuid:"edef8ba9-79d6-4ace-a3c8-27dcd51d21ed"},J={CLEARKEY:Q,FAIRPLAY:z,PLAYREADY:Y,WIDEVINE:X},Z={"com.widevine.alpha":X,"com.microsoft.playready":Y,"com.apple.fps.2_0":z,"org.w3.clearkey":Q},$={value:4,message:"Your browser does not support any DRM Content Decryption Modules"},ee={value:4,message:"There was an issue while updating DRM License"},te={value:204,message:"Error while requesting DRM license"},ne={value:201,message:"DRM license not authorized for this browser version"},re={value:202,message:"DRM license not available"},ie={value:203,message:"DRM license server error"},oe={value:4,message:"Error creating key session"},se={value:4,message:"Encryption key not usable because of internal error in CDM"},ae={value:4,message:"Unable to find valid CDM support on media"},ue={value:2,message:"Request for AuthXML failed"},ce={value:2,message:"Request for DRM certificate failed"};function de(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function le(e){return window.WebKitMediaKeys&&"function"==typeof window.WebKitMediaKeys.isTypeSupported&&window.WebKitMediaKeys.isTypeSupported(J.FAIRPLAY.keySystem)?J.FAIRPLAY.uuid:"function"==typeof navigator.requestMediaKeySystemAccess?e.safari?"":e.msIE||e.msEdgeLegacy?J.PLAYREADY.uuid:J.WIDEVINE.uuid:""}function fe(e,t){if((e=he(e))===(t=he(t)))return!0;if(e.byteLength!==t.byteLength)return!1;for(var n=new DataView(e),r=new DataView(t),i=0;i<n.byteLength;i++)if(n.getUint8(i)!==r.getUint8(i))return!1;return!0}function he(e){return e instanceof Uint8Array||e instanceof Uint16Array?e.buffer:e}function ve(e){return t=function(e){if(null===e)return[];for(var t=new DataView(e.buffer||e),n=[],r=0;!(r>=t.buffer.byteLength);){var i=r+t.getUint32(r);if(r+=4,t.getUint32(r)===ye("pssh")){r+=4;var o=t.getUint8(r);if(0===o||1===o){r++,r+=3;for(var s="",a=0;a<4;a++)s+=be(t.getUint8(r+a));r+=4,s+="-";for(var u=0;u<2;u++)s+=be(t.getUint8(r+u));r+=2,s+="-";for(var c=0;c<2;c++)s+=be(t.getUint8(r+c));r+=2,s+="-";for(var d=0;d<2;d++)s+=be(t.getUint8(r+d));r+=2,s+="-";for(var l=0;l<6;l++)s+=be(t.getUint8(r+l));r+=6,s=s.toLowerCase(),r+=4,n.push(s),r=i}else r=i}else r=i}return n}(e),n=[],t.forEach((function(e){Object.keys(J).forEach((function(t){var r=J[t];r.uuid.toLowerCase()===e.toLowerCase()&&n.push(r)}))})),n;var t,n}function pe(e,t){return new Promise((function(n,r){var i=new XMLHttpRequest;for(var o in i.open(t.method,e,!0),t.headers)Object.prototype.hasOwnProperty.call(t.headers,o)&&i.setRequestHeader(o,t.headers[o]);i.responseType=t.responseType,i.onload=function(){200===i.status&&n(i.response)},i.onloadend=function(){r(i.status)},i.send(t.body)}))}function me(e){var t=ge(Se(JSON.parse(String.fromCharCode.apply(null,e)).sinf[0]),"schi");return function(e){for(var t,n="",r=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return de(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?de(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(e);!(t=r()).done;)n+=be(t.value);return n}(ge(t,"tenc").subarray(8,24))}function ge(e,t){for(var n=new DataView(e.buffer,e.byteOffset,e.byteLength),r=ye(t),i=0;i<e.byteLength;){var o=n.getUint32(i);if(n.getUint32(i+4)===r)return e.subarray(i+8,i+o);i+=o}return new Uint8Array(e)}function ye(e){return(e.charCodeAt(0)<<24)+(e.charCodeAt(1)<<16)+(e.charCodeAt(2)<<8)+e.charCodeAt(3)}function be(e){var t=e.toString(16);return 1===t.length?"0"+t:t}function Se(e){for(var t=atob(e),n=t.length,r=new Uint8Array(n),i=0;i<n;i++)r[i]=t.charCodeAt(i);return r}function ke(e){return decodeURIComponent(e.replace(/\+/g," "))}var Ee=function(e){return e.AVAILABLE="RemotePlayerAvailable",e.UNAVAILABLE="RemotePlayerUnavailable",e.SESSION_STARTED="RemotePlayerSessionStarted",e.SESSION_ENDED="RemotePlayerSessionEnded",e}({});function we(e,t,n,r,i,o,s){try{var a=e[o](s),u=a.value}catch(e){return void n(e)}a.done?t(u):Promise.resolve(u).then(r,i)}function Me(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function s(e){we(o,r,i,s,a,"next",e)}function a(e){we(o,r,i,s,a,"throw",e)}s(void 0)}))}}var Pe=n(357),Te=n.n(Pe);function Ae(e){var t=e.querySelector("Challenge"),n=null==t?void 0:t.textContent;return n?atob(n):""}function Ce(e){var t=e.querySelectorAll("HttpHeader");return Array.from(t).reduce((function(e,t){var n,r,i=null==(n=t.querySelector("name"))?void 0:n.textContent,o=null==(r=t.querySelector("value"))?void 0:r.textContent;return i&&o&&(e[i]=o),e}),{})}function _e(e){var t=String.fromCharCode.apply(null,new Uint16Array(e)),n=(new DOMParser).parseFromString(t,"application/xml");return{headers:Ce(n),body:Ae(n)}}function Le(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var De=[{initDataTypes:["cenc"],audioCapabilities:[{contentType:'audio/mp4;codecs="mp4a.40.2"'}],videoCapabilities:[{robustness:"SW_SECURE_CRYPTO",contentType:'video/mp4;codecs="avc1.42E01E"'}]}],Re=function(){function e(e){var t=this;this.video=void 0,this.listener=void 0,this.cdmSupport=void 0,this.selectedCDM=void 0,this.mediaKeys=void 0,this.pendingSessions=void 0,this.sessions=void 0,this.authXml=void 0,this.video=e.video,this.listener=e.listener,this.cdmSupport=null,this.selectedCDM=null,this.mediaKeys=void 0,this.pendingSessions=[],this.reset(),this.video.addEventListener("encrypted",(function(e){return t.handleEncrypted(e)})),this.video.addEventListener("webkitneedkey",(function(e){return t.handleWebKitEncrypted(e)}))}var t=e.prototype;return t.configure=function(e){var t=this;if(!this.authXml){var n=new URL(e),r=n.pathname.split("/"),i=r[r.length-1].split(".")[0],o=function(e){var t=new URL(e).searchParams,n={};return t.forEach((function(e,t){n[ke(t)]=e?ke(e):""})),n}(e),s=o.token,a=o.sig,u="https://"+n.host+"/api/authxml/"+i+"?token="+encodeURIComponent(s)+"&sig="+a;this.authXml=pe(u,{method:"GET",responseType:"text"}).catch((function(e){t.handleError(Object.assign({code:e},ue))}))}},t.reset=function(){this.authXml=null,this.sessions=[]},t.isProtected=function(){return null!==this.authXml},t.handleError=function(e){this.listener.onSinkError({value:e.value||4,code:e.code||0,message:e.message||""})},t.hasSession=function(e){if(!e)return!1;for(var t,n=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return Le(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Le(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(this.sessions);!(t=n()).done;){var r=t.value;if(r.initData&&fe(r.initData,e))return!0}return!1},t.createKeySystemSupportChain=function(){if(null===this.cdmSupport||0===this.cdmSupport.length)return Promise.reject(ae);var e=Promise.reject();return this.cdmSupport.forEach((function(t){e=e.catch((function(){return navigator.requestMediaKeySystemAccess(t.keySystem,De)}))})),e=e.catch((function(){return Promise.reject($)}))},t.handleEncrypted=function(){var e=Me(Te().mark((function e(t){var n,r;return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.hasSession(t.initData)){e.next=2;break}return e.abrupt("return");case 2:if(this.sessions.push(t),"sinf"!==t.initDataType){e.next=6;break}return this.handleWebKitEncrypted(t),e.abrupt("return");case 6:if(null===this.cdmSupport&&(this.cdmSupport=ve(t.initData)),void 0!==this.mediaKeys){e.next=24;break}return this.mediaKeys=null,e.prev=9,e.next=12,this.createKeySystemSupportChain();case 12:return n=e.sent,this.selectedCDM=Z[n.keySystem],e.next=16,n.createMediaKeys();case 16:return r=e.sent,e.next=19,this.setMediaKeys(r);case 19:e.next=24;break;case 21:e.prev=21,e.t0=e.catch(9),this.handleError(e.t0);case 24:this.addSession(t);case 25:case"end":return e.stop()}}),e,this,[[9,21]])})));return function(t){return e.apply(this,arguments)}}(),t.setMediaKeys=function(e){var t=this;return this.mediaKeys=e,this.pendingSessions.forEach((function(e){return t.createSessionRequest(e).catch((function(){t.handleError(oe)}))})),this.pendingSessions=[],this.video.setMediaKeys(this.mediaKeys)},t.addSession=function(e){var t=this;this.mediaKeys?this.createSessionRequest(e).catch((function(){t.handleError(oe)})):this.pendingSessions.push(e)},t.createSessionRequest=function(e){var t,n=this,r=e.initDataType,i=e.initData,o=null==(t=this.mediaKeys)?void 0:t.createSession();return o?(o.addEventListener("message",(function(e){return n.handleMessage(e)})),o.addEventListener("keystatuseschange",(function(e){return n.handleKeyStatusesChange(e,i)})),o.generateRequest(r,i)):Promise.reject()},t.handleKeyStatusesChange=function(e,t){var n=this,r=e.target,i=!1;r.keyStatuses.forEach((function(e){switch(e){case"expired":i=!0;break;case"internal-error":n.handleError(se)}})),i&&r.close().then((function(){return n.removeSession(t)}))},t.removeSession=function(e){for(var t=0;t<this.sessions.length;t++)if(this.sessions[t].initData===e)return void this.sessions.splice(t,1)},t.handleMessage=function(){var e=Me(Te().mark((function e(t){var n,r,i=this;return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.target,e.next=3,this.generateLicense(t.message);case 3:(r=e.sent)&&n.update(r).catch((function(){i.handleError(ee)}));case 5:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),t.generateLicense=function(){var e=Me(Te().mark((function e(t){var n,r,i,o;return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.selectedCDM!==J.CLEARKEY){e.next=5;break}return n=JSON.parse((new TextDecoder).decode(t)),r=n.kids.map((function(e){return{kty:"oct",alg:"A128KW",kid:e,k:e}})),i=(new TextEncoder).encode(JSON.stringify({keys:r})),e.abrupt("return",Promise.resolve(i));case 5:if(!this.authXml){e.next=10;break}return e.next=8,this.authXml;case 8:return o=e.sent,e.abrupt("return",this.requestLicense(t,o));case 10:this.handleError(ue);case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),t.requestLicense=function(){var e=Me(Te().mark((function e(t,n){var r,i,o,s=this;return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i={method:"POST",responseType:"arraybuffer",body:t,headers:{customdata:n,"Content-Type":"application/octet-stream"}},this.selectedCDM===J.PLAYREADY&&(o=_e(t),i.body=o.body,i.headers=Object.assign(i.headers,o.headers)),e.abrupt("return",pe((null==(r=this.selectedCDM)?void 0:r.licenseUrl)||"",i).catch((function(e){var t;switch(e){case 0:t=te;break;case 404:t=re;break;case 403:t=ne;break;default:t=ie}s.handleError(Object.assign({code:e},t))})));case 3:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}(),t.handleWebKitEncrypted=function(){var e=Me(Te().mark((function e(t){var n,r=this;return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.selectedCDM=J.FAIRPLAY,e.prev=1,e.next=4,pe(J.FAIRPLAY.certUrl||"",{method:"GET",responseType:"arraybuffer",headers:{Pragma:"Cache-Control: no-cache","Cache-Control":"max-age=0"}});case 4:n=e.sent,this.setupWebKitMediaKeys(t,n).catch((function(e){return r.handleError(e)})),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(1),this.handleError(Object.assign({code:e.t0},ce));case 11:case"end":return e.stop()}}),e,this,[[1,8]])})));return function(t){return e.apply(this,arguments)}}(),t.setupWebKitMediaKeys=function(e,t){var n=this;if(!e.initData)return Promise.reject("Missing initData on MediaEncryptedEvent");this.video.webkitKeys||this.video.webkitSetMediaKeys(new window.WebKitMediaKeys(J.FAIRPLAY.keySystem));var r=me(e.initData),i=this.video.webkitKeys.createSession("video/mp4",e.initData);return i.contentId=r,new Promise((function(e,o){if(n.video.webkitKeys||o("Issue setting fairplay media keys"),!i)return o("Could not create key session");i.addEventListener("webkitkeymessage",(function(e){var i=e.target;"certificate"===String.fromCharCode.apply(null,e.message)?i.update(new Uint8Array(t)):n.getWebkitLicense(e.message,r).then((function(e){var t=e.trim();"<ckc>"===t.substr(0,5)&&"</ckc>"===t.substr(-6)&&(t=t.slice(5,-6)),i.update(Se(t))})).catch(o)})),i.addEventListener("webkitkeyadded",e),i.addEventListener("webkitkeyerror",o)}))},t.getWebkitLicense=function(e,t){return this.authXml?this.authXml.then((function(n){var r;return pe(J.FAIRPLAY.licenseUrl||"",{method:"POST",body:"spc="+(r=e,btoa(String.fromCharCode.apply(null,new Uint16Array(r)))+"&assetId=")+t,responseType:"text",headers:{"Content-Type":"application/x-www-form-urlencoded",customdata:n}}).catch((function(e){return Promise.reject(Object.assign({code:e},te))}))})):Promise.reject(ue)},e}();function Oe(e,t){return Oe=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Oe(e,t)}function Ie(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,Oe(e,t)}var xe=function(){function e(){}var t=e.prototype;return t.addTrack=function(e){},t.bufferDuration=function(){return 0},t.buffered=function(){return{start:0,end:0}},t.getBufferedRanges=function(e){return[]},t.captureGesture=function(){},t.configure=function(e){},t.decodedFrames=function(){return 0},t.delete=function(){},t.droppedFrames=function(){return 0},t.endOfStream=function(){},t.enqueue=function(e){},t.framerate=function(){return 0},t.getCurrentTime=function(){return 0},t.getDisplayHeight=function(){return 0},t.getDisplayWidth=function(){return 0},t.getPlaybackRate=function(){return 0},t.getVolume=function(){return 0},t.invoke=function(e){this[e.name].call(this,e.arg)},t.isMuted=function(){return!1},t.onSourceDurationChanged=function(e){},t.pause=function(){},t.play=function(){},t.reinit=function(){},t.remove=function(e){},t.seekTo=function(e){},t.setMuted=function(e){},t.setPlaybackRate=function(e){},t.setTimestampOffset=function(e){},t.setVolume=function(e){},t.changeSrc=function(e){},t.changeSrcObj=function(e){},t.onSegmentDiscontinuity=function(){},e}(),Ne="pc-chromecast-sender",Fe=function(e){function t(n){var r;return(r=e.call(this)||this).remotePlayer=void 0,r.remotePlayerController=void 0,r.listener=void 0,r.seekTime=void 0,r.currentDuration=void 0,r.listener=n,r.currentDuration=0,t.prepareCastContext().then((function(){r.remotePlayer=new cast.framework.RemotePlayer,r.remotePlayerController=new cast.framework.RemotePlayerController(r.remotePlayer)})).catch((function(){r.listener.onSessionError()})),r}Ie(t,e),t.canCast=function(){return U().chrome},t.stopLookingForRemotePlaybackDevices=function(e){window.cast&&window.cast.framework&&cast.framework.CastContext.getInstance().removeEventListener(cast.framework.CastContextEventType.CAST_STATE_CHANGED,e)},t.lookForRemotePlaybackDevices=function(){var e=Me(Te().mark((function e(n){var r,i;return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.prepareCastContext();case 3:return r=e.sent,i=function(e){switch(e.castState){case cast.framework.CastState.NO_DEVICES_AVAILABLE:break;case cast.framework.CastState.NOT_CONNECTED:n.onRemoteDevice(!0);break;case cast.framework.CastState.CONNECTED:var t=r.getCurrentSession();t&&t.getSessionState()===cast.framework.SessionState.SESSION_RESUMED&&n.onRemoteReconnect()}},r.addEventListener(cast.framework.CastContextEventType.CAST_STATE_CHANGED,i),r.setOptions({receiverApplicationId:"B3DCF968",autoJoinPolicy:chrome.cast.AutoJoinPolicy.TAB_AND_ORIGIN_SCOPED}),e.abrupt("return",i);case 10:e.prev=10,e.t0=e.catch(0),n.onRemoteDevice(!1);case 13:case"end":return e.stop()}}),e,null,[[0,10]])})));return function(t){return e.apply(this,arguments)}}(),t.prepareCastContext=function(){var e=Me(Te().mark((function e(){return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!window.cast||!window.cast.framework){e.next=2;break}return e.abrupt("return",Promise.resolve(cast.framework.CastContext.getInstance()));case 2:return e.abrupt("return",new Promise((function(e,t){if(n.g.__onGCastApiAvailable=function(n){n?e(cast.framework.CastContext.getInstance()):t()},!document.getElementById(Ne)){var r=document.createElement("script");r.id=Ne,r.onerror=function(){document.body.removeChild(r),n.g.__onGCastApiAvailable=function(){},t()},r.async=!0,r.src="https://www.gstatic.com/cv/js/sender/v1/cast_sender.js?loadCastFramework=1",document.body.appendChild(r)}})));case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();var r=t.prototype;return r.configure=function(){var e=Me(Te().mark((function e(n){var r,i,o,s,a,u,c;return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=n.path,e.prev=1,e.next=4,t.prepareCastContext();case 4:if(o=e.sent,s=o.getCurrentSession()){e.next=13;break}return e.next=9,o.requestSession();case 9:s=o.getCurrentSession(),this.setupRemotePlayerListeners(s),e.next=14;break;case 13:s.getSessionState()===cast.framework.SessionState.SESSION_RESUMED&&this.setupRemotePlayerListeners(s);case 14:return(a=new chrome.cast.media.MediaInfo(r,"")).streamType=chrome.cast.media.StreamType.BUFFERED,u=new chrome.cast.media.GenericMediaMetadata,a.metadata=u,a.customData={analytics:{chromecast_sender:"player-core",platform:"web"}},this.remotePlayerController.stop(),c=new chrome.cast.media.LoadRequest(a),this.seekTime>0&&(c.currentTime=this.seekTime,this.seekTime=0),this.currentDuration=0,e.next=25,null==(i=s)?void 0:i.loadMedia(c);case 25:e.next=30;break;case 27:return e.prev=27,e.t0=e.catch(1),e.abrupt("return",this.handleError(e.t0));case 30:case"end":return e.stop()}}),e,this,[[1,27]])})));return function(t){return e.apply(this,arguments)}}(),r.stopMedia=function(){var e=Me(Te().mark((function e(n){var r,i;return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return void 0===n&&(n=!0),e.next=3,t.prepareCastContext();case 3:r=e.sent,(i=r.getCurrentSession())&&i.getSessionState()!==cast.framework.SessionState.SESSION_RESUMED&&i.endSession(n);case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),r.invoke=function(e){this[e.name].call(this,e.arg)},r.play=function(){this.remotePlayer&&this.remotePlayer.isPaused&&this.remotePlayerController.playOrPause()},r.pause=function(){this.remotePlayer&&!this.remotePlayer.isPaused&&this.remotePlayerController.playOrPause()},r.seekTo=function(e){this.remotePlayer&&(this.remotePlayer.playerState!==chrome.cast.media.PlayerState.IDLE?(this.remotePlayer.currentTime=e,this.remotePlayerController.seek()):this.seekTime=e)},r.getCurrentTime=function(){return this.remotePlayer?this.remotePlayer.currentTime:0},r.delete=function(){this.remotePlayer&&this.stopMedia()},r.setMuted=function(e){this.remotePlayer&&e!==this.remotePlayer.isMuted&&this.remotePlayerController.muteOrUnmute()},r.isMuted=function(){return!!this.remotePlayer&&this.remotePlayer.isMuted},r.setVolume=function(e){this.remotePlayer&&(this.remotePlayer.volumeLevel=e,this.remotePlayerController.setVolumeLevel())},r.getVolume=function(){return this.remotePlayer?this.remotePlayer.volumeLevel:0},r.getDevice=function(){var e=Me(Te().mark((function e(){var n,r;return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.prepareCastContext();case 2:return n=e.sent,r=n.getCurrentSession(),e.abrupt("return",(null==r?void 0:r.getCastDevice().friendlyName)||"");case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),r.setupRemotePlayerListeners=function(e){var t=this,n=function(){var n=Me(Te().mark((function n(){var r,i;return Te().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:(r=e.getMediaSession())&&(i=r.media,0===t.currentDuration&&null===i.duration&&(t.currentDuration=1/0,t.listener.onSinkDurationChanged(t.currentDuration)),t.listener.onSinkTimeUpdate());case 2:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}(),r=function(){switch(t.remotePlayer.playerState){case chrome.cast.media.PlayerState.BUFFERING:t.listener.onSinkIdle();break;case chrome.cast.media.PlayerState.PLAYING:t.listener.onSinkPlaying(!1);break;case chrome.cast.media.PlayerState.IDLE:var n=e.getMediaSession();n&&n.idleReason===chrome.cast.media.IdleReason.FINISHED&&t.listener.onSinkEnded()}},i=function(){t.listener.onSinkVolumeChanged(t.remotePlayer.volumeLevel,!0)},o=function(){t.listener.onSinkMutedChanged(t.remotePlayer.isMuted)},s=function(){0!==t.remotePlayer.duration&&(t.currentDuration=t.remotePlayer.duration,t.listener.onSinkDurationChanged(t.currentDuration))},a=function(){t.remotePlayerController.removeEventListener(cast.framework.RemotePlayerEventType.CURRENT_TIME_CHANGED,n),t.remotePlayerController.removeEventListener(cast.framework.RemotePlayerEventType.PLAYER_STATE_CHANGED,r),t.remotePlayerController.removeEventListener(cast.framework.RemotePlayerEventType.VOLUME_LEVEL_CHANGED,i),t.remotePlayerController.removeEventListener(cast.framework.RemotePlayerEventType.IS_MUTED_CHANGED,o),t.remotePlayerController.removeEventListener(cast.framework.RemotePlayerEventType.DURATION_CHANGED,s),t.listener.onSessionStop()};e.addEventListener(cast.framework.SessionEventType.MEDIA_SESSION,(function(){t.remotePlayerController.addEventListener(cast.framework.RemotePlayerEventType.CURRENT_TIME_CHANGED,n),t.remotePlayerController.addEventListener(cast.framework.RemotePlayerEventType.PLAYER_STATE_CHANGED,r),t.remotePlayerController.addEventListener(cast.framework.RemotePlayerEventType.VOLUME_LEVEL_CHANGED,i),t.remotePlayerController.addEventListener(cast.framework.RemotePlayerEventType.IS_MUTED_CHANGED,o),t.remotePlayerController.addEventListener(cast.framework.RemotePlayerEventType.DURATION_CHANGED,s),t.listener.onSessionStarted(e.getCastDevice().friendlyName)}));var u=e.getSessionObj();u.addUpdateListener((function(){u.status===chrome.cast.SessionStatus.STOPPED&&a()})),u.addMediaListener(a)},r.handleError=function(e){if(chrome.cast)switch(e){case chrome.cast.ErrorCode.SESSION_ERROR:this.listener.onSessionError();break;case chrome.cast.ErrorCode.RECEIVER_UNAVAILABLE:this.listener.onRemoteDevice(!1);break;case chrome.cast.ErrorCode.LOAD_MEDIA_FAILED:this.listener.onLoadMediaError();break;case chrome.cast.ErrorCode.CANCEL:this.listener.onUserCancel();break;default:this.listener.onSinkError({value:1,code:0,message:"Error requesting chromecast session"})}else this.listener.onSinkError({value:1,code:0,message:"Error loading chromecast SDK"})},t}(xe),Ve=function(e){function t(t){var n;return(n=e.call(this)||this).video=void 0,n.video=t,n}Ie(t,e);var n=t.prototype;return n.seekTo=function(e){this.video.currentTime=e},n.setPlaybackRate=function(e){this.video.playbackRate=e},n.setVolume=function(e){this.video.volume=e},n.getVolume=function(){return this.video.volume},n.isMuted=function(){return this.video.muted},n.setMuted=function(e){this.video.muted=e},n.getPlaybackRate=function(){return this.video.playbackRate},t}(xe),Be=function(){function e(e,t){this.muted=void 0,this.video=void 0,this.listener=void 0,this.unsubscribes=[],this.expectingMutedChanged=!1,this.expectingVolumeChanged=!1,this.expectedRateChange=void 0,this.video=e,this.listener=t,this.muted=e.muted,this.unsubscribes.push(_(e,"volumechange",this.volumeChange.bind(this))),this.unsubscribes.push(_(e,"ratechange",this.rateChange.bind(this)))}var t=e.prototype;return t.volumeChange=function(){var e=!this.expectingVolumeChanged;this.expectingMutedChanged=!1,this.expectingVolumeChanged=!1;var t=this.video.muted;this.muted!==t?(this.muted=t,this.listener.onSinkMutedChanged(t)):this.listener.onSinkVolumeChanged(this.video.volume,e)},t.rateChange=function(){this.video.playbackRate!==this.expectedRateChange&&this.listener.onSinkPlaybackRateChanged(this.video.playbackRate)},t.unsubscribe=function(){this.unsubscribes.forEach((function(e){return e()}))},t.onConfigure=function(){this.expectingVolumeChanged&&(this.listener.onSinkVolumeChanged(this.video.volume,!1),this.expectingVolumeChanged=!1),this.expectingMutedChanged&&(this.muted=this.video.muted,this.listener.onSinkMutedChanged(this.video.muted),this.expectingMutedChanged=!1),this.expectedRateChange=void 0},t.trackRPC=function(e){var t=e.name,n=e.arg;"setVolume"===t&&this.video.volume!==n?this.expectingVolumeChanged=!0:"setMuted"===t&&this.video.muted!==n?this.expectingMutedChanged=!0:"setPlaybackRate"===t&&this.video.playbackRate!==n&&(this.expectedRateChange=n)},e}();function Ue(e,t){this.name="AggregateError",this.errors=e,this.message=t||""}Ue.prototype=Error.prototype;var je=setTimeout;function He(e){return Boolean(e&&void 0!==e.length)}function We(){}function Ge(e){if(!(this instanceof Ge))throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],Xe(e,this)}function qe(e,t){for(;3===e._state;)e=e._value;0!==e._state?(e._handled=!0,Ge._immediateFn((function(){var n=1===e._state?t.onFulfilled:t.onRejected;if(null!==n){var r;try{r=n(e._value)}catch(e){return void Qe(t.promise,e)}Ke(t.promise,r)}else(1===e._state?Ke:Qe)(t.promise,e._value)}))):e._deferreds.push(t)}function Ke(e,t){try{if(t===e)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if(t instanceof Ge)return e._state=3,e._value=t,void ze(e);if("function"==typeof n)return void Xe((r=n,i=t,function(){r.apply(i,arguments)}),e)}e._state=1,e._value=t,ze(e)}catch(t){Qe(e,t)}var r,i}function Qe(e,t){e._state=2,e._value=t,ze(e)}function ze(e){2===e._state&&0===e._deferreds.length&&Ge._immediateFn((function(){e._handled||Ge._unhandledRejectionFn(e._value)}));for(var t=0,n=e._deferreds.length;t<n;t++)qe(e,e._deferreds[t]);e._deferreds=null}function Ye(e,t,n){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.promise=n}function Xe(e,t){var n=!1;try{e((function(e){n||(n=!0,Ke(t,e))}),(function(e){n||(n=!0,Qe(t,e))}))}catch(e){if(n)return;n=!0,Qe(t,e)}}Ge.prototype.catch=function(e){return this.then(null,e)},Ge.prototype.then=function(e,t){var n=new this.constructor(We);return qe(this,new Ye(e,t,n)),n},Ge.prototype.finally=function(e){var t=this.constructor;return this.then((function(n){return t.resolve(e()).then((function(){return n}))}),(function(n){return t.resolve(e()).then((function(){return t.reject(n)}))}))},Ge.all=function(e){return new Ge((function(t,n){if(!He(e))return n(new TypeError("Promise.all accepts an array"));var r=Array.prototype.slice.call(e);if(0===r.length)return t([]);var i=r.length;function o(e,s){try{if(s&&("object"==typeof s||"function"==typeof s)){var a=s.then;if("function"==typeof a)return void a.call(s,(function(t){o(e,t)}),n)}r[e]=s,0==--i&&t(r)}catch(e){n(e)}}for(var s=0;s<r.length;s++)o(s,r[s])}))},Ge.any=function(e){var t=this;return new t((function(n,r){if(!e||void 0===e.length)return r(new TypeError("Promise.any accepts an array"));var i=Array.prototype.slice.call(e);if(0===i.length)return r();for(var o=[],s=0;s<i.length;s++)try{t.resolve(i[s]).then(n).catch((function(e){o.push(e),o.length===i.length&&r(new Ue(o,"All promises were rejected"))}))}catch(e){r(e)}}))},Ge.allSettled=function(e){return new this((function(t,n){if(!e||void 0===e.length)return n(new TypeError(typeof e+" "+e+" is not iterable(cannot read property Symbol(Symbol.iterator))"));var r=Array.prototype.slice.call(e);if(0===r.length)return t([]);var i=r.length;function o(e,n){if(n&&("object"==typeof n||"function"==typeof n)){var s=n.then;if("function"==typeof s)return void s.call(n,(function(t){o(e,t)}),(function(n){r[e]={status:"rejected",reason:n},0==--i&&t(r)}))}r[e]={status:"fulfilled",value:n},0==--i&&t(r)}for(var s=0;s<r.length;s++)o(s,r[s])}))},Ge.resolve=function(e){return e&&"object"==typeof e&&e.constructor===Ge?e:new Ge((function(t){t(e)}))},Ge.reject=function(e){return new Ge((function(t,n){n(e)}))},Ge.race=function(e){return new Ge((function(t,n){if(!He(e))return n(new TypeError("Promise.race accepts an array"));for(var r=0,i=e.length;r<i;r++)Ge.resolve(e[r]).then(t,n)}))},Ge._immediateFn="function"==typeof setImmediate&&function(e){setImmediate(e)}||function(e){je(e,0)},Ge._unhandledRejectionFn=function(e){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)};var Je=Ge,Ze=("undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==n.g?n.g:void 0).Promise||Je,$e=function(e){function t(t,n){var r;return(r=e.call(this)||this).paused=!0,r.listener=void 0,r.video=void 0,r.unsubscribers=void 0,r.lastVolumeChangeEvent=void 0,r.video=t,r.listener=n,r.paused=!0,r.unsubscribers=[],r.addListener("volumechange",r.recordMuteChange.bind(function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(r))),r.recordMuteChange(),r}Ie(t,e);var n=t.prototype;return n.pause=function(){this.paused=!0,this.video.pause()},n.setPlaybackRate=function(e){this.video.playbackRate=e},n.delete=function(){this.unsubscribers.forEach((function(e){return e()}))},n.addListener=function(e,t,n){void 0===n&&(n=this.video),this.unsubscribers.push(_(n,e,t))},n.recordMuteChange=function(){this.lastVolumeChangeEvent={time:this.video.currentTime,muted:this.video.muted}},n.checkStopped=function(e){return!this.video.paused||this.video.ended||this.video.error||this.paused||this.listener.onSinkStop(e||this.unmuteAutopause()),!1},n.unmuteAutopause=function(){var e=this.lastVolumeChangeEvent;return!this.video.muted&&!e.muted&&this.video.currentTime===e.time},t}(xe),et=function(e){function t(t,n){var r;return(r=e.call(this,t,n)||this).onSinkIdle=void 0,r.intervalId=void 0,r.idle=void 0,r.lastPlayhead=void 0,r.lastBufferEnd=void 0,r.fps=void 0,r.lastDecodedFrames=void 0,r.lastTimeUpdate=void 0,r.idleTimeout=void 0,r.playAttempt=!1,r.seeking=!1,r.audioBufferList=void 0,r.awaitingAutoplayCompletion=!1,r.intervalId=0,r.idle=!0,r.lastPlayhead=0,r.lastBufferEnd=0,r.fps=0,r.lastDecodedFrames=0,r.lastTimeUpdate=performance.now(),r.idleTimeout=-1,r.audioBufferList=[],r.bindEvents(),r}Ie(t,e);var n=t.prototype;return n.bindEvents=function(){var e=this;this.addListener("play",(function(){return e.onVideoPlay()})),this.addListener("pause",(function(){return e.onVideoPause()})),this.addListener("timeupdate",(function(){return e.onVideoTimeUpdate()})),this.addListener("ended",(function(){return e.onVideoEnded()})),this.addListener("error",(function(){return e.onVideoError()})),this.addListener("playing",(function(){return e.onVideoPlaying()})),this.addListener("seeking",(function(){return e.onVideoSeeking()}))},n.delete=function(){e.prototype.delete.call(this),this.audioBufferList=[],clearInterval(this.intervalId)},n.play=function(){this.paused=!1;for(var e=this.video.buffered,t=0,n=0;n<e.length;n++){var r=e.start(n);if(this.video.currentTime<=r){t=r;break}}this.video.currentTime<t&&(console.warn("Moving to buffered region",t,this.video.currentTime),this.listener.onSinkGapJump(t-this.video.currentTime),this.seekTo(t)),this.playInternal()},n.endOfStream=function(){this.idle=!1,clearTimeout(this.idleTimeout),this.idleTimeout=-1},n.framerate=function(){return this.fps},n.seekTo=function(e){this.video.seekable.length&&e!==this.video.currentTime&&(this.seeking=!0,this.video.currentTime=e)},n.addSourceBuffer=function(e,t){(t.indexOf("mp4a")>-1||t.indexOf("opus")>-1)&&(this.audioBufferList=[e])},n.clearSourceBuffers=function(){this.audioBufferList=[]},n.onIdle=function(){var e;this.listener.onSinkIdle(),null==(e=this.onSinkIdle)||e.call(this)},n.onVideoPlay=function(){var e=this;this.playAttempt?(this.lastPlayhead=this.video.currentTime,clearInterval(this.intervalId),this.intervalId=self.setInterval((function(){return e.heartbeat()}),E)):(this.pause(),this.listener.play()),this.playAttempt=!1},n.onVideoPause=function(){this.awaitingAutoplayCompletion||(this.checkStopped(!1),clearInterval(this.intervalId))},n.onVideoTimeUpdate=function(){clearTimeout(this.idleTimeout),this.idleTimeout=-1,this.updateFrameRate(),this.listener.onSinkTimeUpdate();var e=A(this.video.buffered,this.video.currentTime,b);this.checkBufferUpdate(e),this.updateIdle(e)},n.updateFrameRate=function(){var e=M(this.video),t=performance.now();this.fps=1e3*Math.max(e-this.lastDecodedFrames,0)/(t-this.lastTimeUpdate),this.lastDecodedFrames=e,this.lastTimeUpdate=t},n.onVideoEnded=function(){this.listener.onSinkEnded()},n.onVideoPlaying=function(){this.video.paused||this.listener.onSinkPlaying(this.paused)},n.onVideoSeeking=function(){this.seeking?this.seeking=!1:this.listener.seekTo(this.video.currentTime)},n.onVideoError=function(){var e,t,n=null!=(e=null==this||null==(t=this.video)?void 0:t.error)?e:{},r=n.code,i=void 0===r?-1:r,o=n.message,s=void 0===o?"":o;this.listener.onSinkError({value:i,code:i,message:s})},n.heartbeat=function(){var e=A(this.video.buffered,this.video.currentTime,b);if(this.video.paused)clearInterval(this.intervalId);else if(this.video.currentTime===this.lastPlayhead){var t=C(this.video.buffered,this.video.currentTime,b);t!==this.video.currentTime&&(this.audioBufferList.map((function(e){T("Audio Buffer",e.buffered)})),T("<video> Buffer",this.video.buffered),console.warn("jumping "+(t-this.video.currentTime)+"s gap, current position "+this.video.currentTime+", new position "+t),this.listener.onSinkGapJump(t-this.video.currentTime),this.seekTo(t)),this.updateIdle(e,t===this.video.currentTime)}else this.checkBufferUpdate(e),this.lastPlayhead=this.video.currentTime;this.videoDisplaySizeUpdate()},n.videoDisplaySizeUpdate=function(){var e=this.video.clientWidth*window.devicePixelRatio,t=this.video.clientHeight*window.devicePixelRatio;this.listener.onSinkVideoDisplaySizeChanged(e,t)},n.checkBufferUpdate=function(e){var t=e.end;t!==this.lastBufferEnd&&(this.lastBufferEnd=t,this.listener.onSinkBufferUpdate())},n.updateIdle=function(e,t){var n=this,r=e.end,i=void 0===r?0:r;void 0===t&&(t=!1);var o=this.video,s=o.buffered,a=o.currentTime;if(o.paused)this.idle=!0;else{var u=[i].concat(this.audioBufferList.map((function(e){return A(e.buffered,a,b).end}))),c=Math.max.apply(null,u);Number.isFinite(c)||(c=0);var d=s.length,l=d>0?s.end(d-1):i,f=c-a<b||t&&i>l;f&&!this.idle&&(console.warn("playhead",a,"max buffer",c,"max played",l),clearTimeout(this.idleTimeout),this.idleTimeout=self.setTimeout((function(){return n.onBufferingTimeout()}),k),this.onIdle()),this.idle=f}},n.onBufferingTimeout=function(){clearTimeout(this.idleTimeout),this.idleTimeout=-1,this.listener.onSinkError({value:l,code:l,message:"Buffering timeout"})},n.playInternal=function(){var e=this;this.playAttempt=!0,this.awaitingAutoplayCompletion=!0,Ze.resolve(this.video.play()).then((function(){e.awaitingAutoplayCompletion=!1})).catch((function(){e.playAttempt=!1,e.checkStopped(!0)}))},t}($e),tt=function(e){function t(t,n){var r;return(r=e.call(this)||this).listener=t,r.video=n,r.playbackMonitor=void 0,r.controlsObserver=void 0,r.playbackMonitor=new et(n,t),r.observeControlsChange(),r}Ie(t,e);var n=t.prototype;return n.configure=function(e){var t=e.srcObj;this.video.srcObject||(this.video.srcObject=t)},n.invoke=function(e){this[e.name].call(this,e.arg)},n.play=function(){this.playbackMonitor.play()},n.pause=function(){this.playbackMonitor.pause()},n.seekTo=function(e){this.playbackMonitor.seekTo(e)},n.endOfStream=function(){this.playbackMonitor.endOfStream()},n.setVolume=function(e){this.video.volume!==e&&(this.video.volume=e)},n.getVolume=function(){return this.video.volume},n.isMuted=function(){return this.video.muted},n.setMuted=function(e){this.video.muted!==e&&(this.video.muted=e)},n.getDisplayWidth=function(){return this.video.clientWidth},n.getDisplayHeight=function(){return this.video.clientHeight},n.setPlaybackRate=function(e){this.playbackMonitor.setPlaybackRate(e)},n.getPlaybackRate=function(){return this.video.playbackRate},n.getCurrentTime=function(){return this.video.currentTime},n.buffered=function(){return A(this.video.buffered,this.video.currentTime,b)},n.bufferDuration=function(){var e=this.buffered(),t=e.start;return e.end-Math.max(t,this.video.currentTime)},n.decodedFrames=function(){return M(this.video)},n.droppedFrames=function(){return P(this.video)},n.framerate=function(){return this.playbackMonitor.framerate()},n.captureGesture=function(){this.playbackMonitor.play(),this.playbackMonitor.pause()},n.changeSrcObj=function(e){var t=this.video,n=t.playbackRate;t.srcObject=e,t.playbackRate=n},n.delete=function(){var e;this.playbackMonitor.delete(),this.video.srcObject=null,L(this.video),null==(e=this.controlsObserver)||e.disconnect()},n.observeControlsChange=function(){var e=this.listener,t=this.video;try{(this.controlsObserver=new MutationObserver((function(){e.onSinkControlsChanged(t.controls)}))).observe(t,{attributeFilter:["controls"]}),e.onSinkControlsChanged(t.controls)}catch(e){}},t}(xe),nt=function(){function e(e,t,n){void 0===n&&(n=new et(t,e)),this.listener=e,this.video=t,this.playbackMonitor=n,this.controlsObserver=void 0,this.mseSink=void 0,this.awaitSink=void 0,this.observeControlsChange(),this.awaitSink=void 0,_(t,"error",this.onVideoError.bind(this))}var t=e.prototype;return t.invoke=function(e){var t=this.awaitSink,n=this.mseSink;t&&n?["enqueue","addTrack","setTimestampOffset"].includes(e.name)?this.invokeAsync(e):this.invokeSync(e):t?this.invokeAsync(e):n&&this.invokeSync(e)},t.initSink=function(){var e=this,t=this.awaitSink;if(!this.mseSink&&!t){var n=O.create(this.onMediaSourceEnded.bind(this),this.onMediaSourceError.bind(this));this.awaitSink=new Ze((function(t,r){n.sink.then((function(n){e.handleCreateSuccess(n),t()})).catch((function(t){e.handleCreateError(t),r()}))})),this.changeSrc(URL.createObjectURL(n.ms))}},t.configure=function(e){var t=e.trackID,n=e.codec,r=e.group,i=e.isProtected;this.initSink(),this.awaitSink||!this.isContentProtectionChanging(e)&&!this.isChangingToFromAudioOnly(e)||this.queueNewSink(),this.invoke({name:"addTrack",arg:j({},rt,{trackID:t,codec:n,group:r,isProtected:i})})},t.queueNewSink=function(){var e=this;this.awaitSink=new Ze((function(t,n){e.deferUntilIdle().then((function(){var t=O.create(e.onMediaSourceEnded.bind(e),e.onMediaSourceError.bind(e));return e.changeSrc(URL.createObjectURL(t.ms)),t.sink})).then((function(n){e.destroyMSESink(),e.handleCreateSuccess(n),e.play(),t()})).catch((function(t){e.handleCreateError(t),n()}))}))},t.addTrack=function(e){var t=e.trackID,n=e.codec,r=e.group,i=e.isProtected,o=this.mseSink;try{var s=null==o?void 0:o.addTrack(t,n,r,i);s&&this.playbackMonitor.addSourceBuffer(s,n)}catch(e){this.handleCreateError(e)}},t.enqueue=function(e){var t,n=e.trackID,r=e.buffer;null==(t=this.mseSink)||t.append(n,r)},t.endOfStream=function(){var e,t=this;null==(e=this.mseSink)||e.scheduleUpdate().then((function(){return t.playbackMonitor.endOfStream()}))},t.setTimestampOffset=function(e){var t,n=e.trackID,r=e.offset;null==(t=this.mseSink)||t.setTimestampOffset(n,r)},t.onSourceDurationChanged=function(e){var t=this.mseSink,n=this.video;if(t){var r=t.duration,i=function(e,t,n){var r=e;return e===1/0||e===S?n?r=1/0:n||(r=S):e!==t&&(r=e),r}(e,r,n.controls);i!==r&&t.setDuration(i)}},t.play=function(){var e,t=this;null==(e=this.mseSink)||e.scheduleUpdate().then((function(){return t.playbackMonitor.play()}))},t.pause=function(){var e,t=this;null==(e=this.mseSink)||e.scheduleUpdate().then((function(){return t.playbackMonitor.pause()}))},t.remove=function(e){var t,n=e.start,r=e.end;null==(t=this.mseSink)||t.remove(n,r)},t.seekTo=function(e){var t=this.mseSink,n=this.playbackMonitor,r=this.video,i=A(r.buffered,r.currentTime,b),o=i.start,s=i.end;e>=o&&e<s?null==t||t.scheduleUpdate().then((function(){return n.seekTo(e)})):n.seekTo(e)},t.setVolume=function(e){this.video.volume!==e&&(this.video.volume=e)},t.getVolume=function(){return this.video.volume},t.isMuted=function(){return this.video.muted},t.setMuted=function(e){this.video.muted!==e&&(this.video.muted=e)},t.getDisplayWidth=function(){return this.video.clientWidth},t.getDisplayHeight=function(){return this.video.clientHeight},t.setPlaybackRate=function(e){this.playbackMonitor.setPlaybackRate(e)},t.getPlaybackRate=function(){return this.video.playbackRate},t.getCurrentTime=function(){return this.video.currentTime},t.buffered=function(){return A(this.video.buffered,this.video.currentTime,b)},t.getBufferedRanges=function(e){var t,n;return null!=(t=null==(n=this.mseSink)?void 0:n.getBufferedRanges(e))?t:[]},t.bufferDuration=function(){var e=this.buffered(),t=e.start;return e.end-Math.max(t,this.video.currentTime)},t.decodedFrames=function(){return M(this.video)},t.droppedFrames=function(){return P(this.video)},t.framerate=function(){return this.playbackMonitor.framerate()},t.captureGesture=function(){this.playbackMonitor.play(),this.playbackMonitor.pause()},t.changeSrc=function(e){!function(e,t){var n=e.playbackRate,r=e.src;r&&URL.revokeObjectURL(r),e.src=t,e.playbackRate=n}(this.video,e)},t.changeSrcObj=function(e){},t.delete=function(){var e;this.playbackMonitor.delete(),null==(e=this.controlsObserver)||e.disconnect(),this.destroyMSESink(),L(this.video)},t.invokeSync=function(e){this[e.name].call(this,e.arg)},t.invokeAsync=function(e){var t,n=this;null==(t=this.awaitSink)||t.then((function(){return n.invokeSync(e)})).catch((function(){}))},t.onMediaSourceEnded=function(){this.video.load(),this.listener.onSinkReset()},t.destroyMSESink=function(){var e=this,t=function(){e.mseSink&&e.mseSink.destroy(),e.awaitSink=void 0,e.mseSink=void 0};this.mseSink?t():this.awaitSink&&this.awaitSink.then((function(){return t()})),this.playbackMonitor&&this.playbackMonitor.clearSourceBuffers()},t.isContentProtectionChanging=function(e){var t,n;return(null!=(t=null==(n=this.mseSink)?void 0:n.isDrmProtected())&&t)!==e.isProtected},t.isChangingToFromAudioOnly=function(e){var t,n;return(null!=(t=null==(n=this.mseSink)?void 0:n.isAudioOnly())&&t)!==("audio_only"===e.group)},t.deferUntilIdle=function(){var e=this,t=this.mseSink,n=this.playbackMonitor;return new Ze((function(r){t&&!e.video.paused?n.onSinkIdle=function(){n.onSinkIdle=void 0,r()}:r()}))},t.handleCreateSuccess=function(e){this.mseSink=e,this.awaitSink=void 0,this.onSourceDurationChanged(S),this.mseSink.setLiveSeekableRange(0,S)},t.handleCreateError=function(e){this.listener.onSinkError({value:4,code:4,message:e.toString()})},t.onMediaSourceError=function(e,t,n,r){var i={value:e,code:t,message:n};r?this.listener.onSinkError(i):this.listener.onSinkRecoverableError(i)},t.onVideoError=function(){this.destroyMSESink()},t.observeControlsChange=function(){var e=this,t=this.video;try{(this.controlsObserver=new MutationObserver((function(){e.invoke({name:"onSourceDurationChanged",arg:t.duration})}))).observe(t,{attributeFilter:["controls"]})}catch(e){}},t.onSegmentDiscontinuity=function(){},e}(),rt={trackID:0,codec:'codecs="magic"',mode:"mse",isProtected:!1,path:"",group:"",srcObj:null},it=n(620),ot=function(){function e(){this.emitter=void 0,this.emitter=new it.EventEmitter}var t=e.prototype;return t.on=function(e,t){this.emitter.on(String(e),t)},t.removeListener=function(e,t){this.emitter.removeListener(String(e),t)},t.emit=function(e){for(var t,n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];(t=this.emitter).emit.apply(t,[String(e)].concat(r))},t.removeAllListeners=function(){this.emitter.removeAllListeners()},e}(),st=function(e){return e.Pending="pending",e.Ready="ready",e.Configuring="configuring",e.Active="active",e.Error="error",e}({}),at=function(e){return e.Heartbeat="heartbeat",e.Configure="configure",e.Set_Log_Config="setLogConfig",e.Debug="debug",e.Destroy="destroy",e}({}),ut=function(e){return e.State_Changed="stateChanged",e.Manifest_Load_Analytics="manifestLoadAnalytics",e.Available_Segments="availableSegments",e}({});function ct(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var dt={enabled:!1,levels:{debug:!0,log:!0,info:!0,warn:!0,error:!0}},lt=new ot,ft=function(e){var t=e.name,n=function(e){if(dt.enabled&&dt.levels[e]){for(var n=(new Date).toTimeString().split(" ")[0],r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];console[e].apply(console,[n+" - ("+t+")"].concat(i))}};return{debug:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return n.apply(void 0,["debug"].concat(t))},log:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return n.apply(void 0,["log"].concat(t))},info:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return n.apply(void 0,["info"].concat(t))},warn:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return n.apply(void 0,["warn"].concat(t))},error:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return n.apply(void 0,["error"].concat(t))}}},ht=ft({name:"sw-host-config"});function vt(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return pt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?pt(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function pt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var mt,gt=ft({name:"sw-host"}),yt=function(){return mt},bt=function(e){return e[e.Pending=0]="Pending",e[e.Installing=1]="Installing",e[e.Ready=2]="Ready",e[e.Error=3]="Error",e}(bt||{}),St=function(e){var t,n=function(){return{isMSESupported:K()}};if(((t=e)?t.url||(ht.error("Service worker config must specify a url"),0):(ht.error("Service worker config is required"),0))&&function(e,t){return!(!t.forceActivate&&e.isMSESupported)}(n(),e)){var r,i,o,s,a,u,c,d,l,f,h,v,p,m,g,y,b,S,k,E,w,M,P,T=new ot,A=!0,C=bt.Pending,_=[],L=[],D=[],R=st.Pending,O=!1,I=function(){var t=Me(Te().mark((function t(){var n,r;return Te().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,F(bt.Installing),navigator.serviceWorker.addEventListener("message",B),t.prev=3,t.next=6,x(e.url);case 6:t.next=11;break;case 8:t.prev=8,t.t0=t.catch(3),gt.error("[registerAndActivate] Failed to unregister service worker instances: "+t.t0);case 11:return n=void 0!==e.scope?{scope:e.scope}:void 0,t.next=14,navigator.serviceWorker.register(e.url,n);case 14:return(r=t.sent).installing?gt.log("Service worker installing"):r.waiting?gt.log("Service worker installed"):r.active&&gt.log("Service worker active"),t.next=18,N(r);case 18:F(bt.Ready),t.next=25;break;case 21:t.prev=21,t.t1=t.catch(0),gt.error("Registration failed with "+t.t1),F(bt.Error);case 25:case"end":return t.stop()}}),t,null,[[0,21],[3,8]])})));return function(){return t.apply(this,arguments)}}(),x=function(){var e=Me(Te().mark((function e(t){var n;return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=new RegExp(t),e.next=3,navigator.serviceWorker.getRegistrations().then(function(){var e=Me(Te().mark((function e(t){var r,i,o;return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:gt.debug("[unregisterOtherWorkerInstances] registrations",t),r=vt(t);case 2:if((i=r()).done){e.next=16;break}if(null===(o=i.value).active||!n.test(o.active.scriptURL)){e.next=14;break}return gt.debug("[unregisterOtherWorkerInstances] unregistering:",o),e.prev=6,e.next=9,o.unregister();case 9:e.next=14;break;case 11:e.prev=11,e.t0=e.catch(6),gt.error("[unregisterOtherWorkerInstances] Failed to unregister service worker: "+e.t0);case 14:e.next=2;break;case 16:case"end":return e.stop()}}),e,null,[[6,11]])})));return function(t){return e.apply(this,arguments)}}());case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),N=function(){var e=Me(Te().mark((function e(t){return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,n){null!==t.installing||null!==t.waiting?(t.installing||t.waiting).addEventListener("statechange",(function(t){var r=t.target;gt.debug("sw registration, statechange",r.state),"activated"===r.state?(gt.log("Service worker activated"),e()):"redundant"===r.state&&n()})):e()})));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),F=function(e){if(gt.log("[changeHostState] host state changed from "+C+" to "+e),(C=e)===bt.Ready){var t=_.slice();_=[];for(var n,r=vt(t);!(n=r()).done;)(0,n.value)();var i=L.slice();L=[];for(var o,s=vt(i);!(o=s()).done;){var a=o.value;U(a)}}else if(C===bt.Error){var u=D.slice();D=[];for(var c,d=vt(u);!(c=d()).done;)(0,c.value)()}},V=function(e){C===bt.Error?e():D.push(e)},B=function(e){var t=e.data;switch(t.type){case ut.State_Changed:var n=t.state;R===st.Configuring&&(O=!1),n===st.Ready&&R!==st.Pending&&gt.warn("[handleMessage] worker was restarted by the browser"),R=n;break;case ut.Manifest_Load_Analytics:gt.debug("Manifest_Load_Analytics",t)}T.emit(t.type,t)},U=function(){var e=Me(Te().mark((function e(t){var n;return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return gt.log("[sendMessage] sending message: ",t),e.abrupt("return",null==(n=navigator.serviceWorker.controller)?void 0:n.postMessage(t));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),H=function(){var e=Me(Te().mark((function e(t){return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(R!==st.Pending){e.next=4;break}return gt.log("[sendMessageWhenReady] worker is not ready, queueing message",t),L.push(t),e.abrupt("return");case 4:U(t);case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),W=function(){i&&clearTimeout(i),i=setTimeout((function(){G()}),1e4)},G=function(){A&&R!==st.Pending&&(U({type:at.Heartbeat}),W())},q=function(e,t){T.on(ut.State_Changed,(function n(r){r.type===ut.State_Changed&&r.state===e&&(T.removeListener(ut.State_Changed,n),t(r))}))},Q=function(){var e=Me(Te().mark((function e(){return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(C!==bt.Ready){e.next=2;break}return e.abrupt("return");case 2:return e.abrupt("return",new Promise((function(e,t){var n;n=function(){gt.log("[ensureHostReady] host state changed to ready"),e()},C===bt.Ready?n():_.push(n),V((function(){gt.log("[ensureHostReady] host state changed to error"),t()}))})));case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),z=function(){var e=Me(Te().mark((function e(){return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(R===st.Pending){e.next=2;break}return e.abrupt("return");case 2:return e.abrupt("return",new Promise((function(e,t){q(st.Ready,(function(t){gt.log("[ensureWorkerReady] worker state changed to ready",t),e()}))})));case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Y=function(){var e=Me(Te().mark((function e(){return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Q();case 2:return e.next=4,z();case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),X=function(){var e=Me(Te().mark((function e(t){return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return O=!0,r=J(null!=t?t:{}),e.next=4,Y();case 4:U({type:at.Configure,config:r});case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),J=function(e){return j({},r,e)},Z=function(){var e=Me(Te().mark((function e(){return Te().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(O||R!==st.Active){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,Y();case 4:return e.abrupt("return",new Promise((function(e,t){q(st.Active,(function(t){gt.log("[ensureConfigured] worker state changed to active",t),e()}))})));case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),$=function(e){H({type:at.Set_Log_Config,config:e})};return P=n().isMSESupported?{cacheMultivariant:!1,transformTargetDuration:!1,lowLatencyMode:{enabled:!1,useLocalSegment:!1,convertPrefetchSegment:!1,preloadPrefetchSegments:!1},driftDetection:{enabled:!1,maxPlaylistPosition:0,triggerDetectionThreshold:0,minDurationBetweenCorrections:0},debug:{addMultivariantDelay:void 0,addFirstVariantDelay:void 0}}:{cacheMultivariant:!0,transformTargetDuration:!1,lowLatencyMode:{enabled:!0,useLocalSegment:!0,convertPrefetchSegment:!0,preloadPrefetchSegments:!1},driftDetection:{enabled:!0,maxPlaylistPosition:3,triggerDetectionThreshold:10,minDurationBetweenCorrections:60},debug:{addMultivariantDelay:void 0,addFirstVariantDelay:void 0}},r={cacheMultivariant:null!=(o=e.cacheMultivariant)?o:P.cacheMultivariant,transformTargetDuration:null!=(s=e.transformTargetDuration)?s:P.transformTargetDuration,lowLatencyMode:{enabled:null!=(a=null==(u=e.lowLatencyMode)?void 0:u.enabled)?a:P.lowLatencyMode.enabled,useLocalSegment:null!=(c=null==(d=e.lowLatencyMode)?void 0:d.useLocalSegment)?c:P.lowLatencyMode.useLocalSegment,convertPrefetchSegment:null!=(l=null==(f=e.lowLatencyMode)?void 0:f.convertPrefetchSegment)?l:P.lowLatencyMode.convertPrefetchSegment,preloadPrefetchSegments:null!=(h=null==(v=e.lowLatencyMode)?void 0:v.preloadPrefetchSegments)?h:P.lowLatencyMode.preloadPrefetchSegments},driftDetection:{enabled:null!=(p=null==(m=e.driftDetection)?void 0:m.enabled)?p:P.driftDetection.enabled,maxPlaylistPosition:null!=(g=null==(y=e.driftDetection)?void 0:y.maxPlaylistPosition)?g:P.driftDetection.maxPlaylistPosition,triggerDetectionThreshold:null!=(b=null==(S=e.driftDetection)?void 0:S.triggerDetectionThreshold)?b:P.driftDetection.triggerDetectionThreshold,minDurationBetweenCorrections:null!=(k=null==(E=e.driftDetection)?void 0:E.minDurationBetweenCorrections)?k:P.driftDetection.minDurationBetweenCorrections},debug:{addMultivariantDelay:null==(w=e.debug)?void 0:w.addMultivariantDelay,addFirstVariantDelay:null==(M=e.debug)?void 0:M.addFirstVariantDelay}},gt.info("[init] instantiating with config",r),void 0!==e.debug&&(e.debug.api={delayNextMediaManifest:function(e){!function(e){if("delayNextMediaManifest"===e.api){var t=e.delayInMs;gt.log("[handleDebugApiMessage] delaying next manifest by "+t+"ms")}U(j({type:at.Debug},e))}({api:"delayNextMediaManifest",delayInMs:e})}}),lt.on("logConfigUpdated",$),W(),{registerAndActivate:I,addListener:function(e,t){T.on(e,t)},removeListener:function(e,t){T.removeListener(e,t)},sendMessage:U,ensureReady:Y,configure:X,ensureConfigured:Z,getConfig:function(){return r},getDriftDetectionConfig:function(){var e;return null==(e=r)?void 0:e.driftDetection},setLogConfig:$,destroy:function(){gt.log("[destroy] destroying"),A=!1,i&&clearTimeout(i),T.removeAllListeners(),lt.removeListener("logConfigUpdated",$),navigator.serviceWorker.removeEventListener("message",B),U({type:at.Destroy})},debugPassthroughAnalytics:function(t){var n;null==(n=e.debug)||null==n.onPassthroughAnalytics||n.onPassthroughAnalytics(t)}}}},kt=function(){function e(){this.serverOffsetVal=e.SERVER_OFFSET_DEFAULT,this.liveLatencyVal=0,this.lastTranscodeReceive=-1}var t=e.prototype;return t.tryGenerateServerOffset=function(t){return this.serverOffsetVal!==e.SERVER_OFFSET_DEFAULT?(console.warn("[generateServerOffset] the server offset has already been generated, skipping"),!1):(this.serverOffsetVal=function(e,t){return new Date(1e3*t).getTime()-e}(Date.now(),t),!0)},t.tryUpdateLatency=function(t){return this.serverOffsetVal!==e.SERVER_OFFSET_DEFAULT&&(t<this.lastTranscodeReceive?(console.warn("[updateLatency] received latency values too old, ignoring. previous: "+this.lastTranscodeReceive+" current: "+t),!1):(this.liveLatencyVal=function(e,t,n){return(e+t-n)/1e3}(Date.now(),this.serverOffsetVal,t),this.lastTranscodeReceive=t,!0))},d(e,[{key:"serverOffset",get:function(){return this.serverOffsetVal}},{key:"liveLatency",get:function(){return this.liveLatencyVal}}]),e}();kt.SERVER_OFFSET_DEFAULT=-1;var Et=function(e){var t=parseFloat(e);if(!isNaN(t))return t},wt=function(e){return e.Live_Latency_Load="liveLatencyLoad",e.Live_Latency_Stats="liveLatencyStats",e}({}),Mt=function(e){return e.Trigger_Drift_Correction="triggerDriftCorrection",e}({}),Pt=function(e){return e.ID3="org.id3",e.APPLE_HLS="com.apple.quicktime.HLS",e}(Pt||{}),Tt=function(e){return e.PRIV="PRIV",e.TXXX="TXXX",e.TDEN="TDEN",e.STREAM_LEVEL_SERVER_TIME="X-SERVER-TIME",e}(Tt||{}),At=ft({name:"sink"}),Ct=function(e){function t(t,n){var r;(r=e.call(this,n,t)||this).fps=void 0,r.intervalId=void 0,r.bufferingTimeoutId=void 0,r.attemptingToPlay=void 0,r.hasPlayedSrc=void 0,r.hasReloadedOnDecodeError=void 0,r.unsubscribersForTrackEvents=void 0,r.latencyStatistics=void 0,r.processedInitialCues=void 0,r.serviceWorker=void 0,r.serviceWorkerHandler=void 0,r.positionTracker=void 0,r.driftDetector=void 0,r.driftDetectorHandler=void 0,r.analytics=void 0,r.analyticsHandler=void 0,r.fps=0,r.intervalId=-1,r.bufferingTimeoutId=-1,r.attemptingToPlay=!1,r.hasPlayedSrc=!1,r.hasReloadedOnDecodeError=!1,r.unsubscribersForTrackEvents=[],r.latencyStatistics=new kt,r.processedInitialCues=!1,r.addListener("waiting",(function(){return r.onVideoWaiting()}),r.video),r.addListener("timeupdate",(function(){return r.onVideoTimeUpdate()}),r.video),r.addListener("durationchange",(function(){return r.onVideoDurationChange()}),r.video),r.addListener("error",(function(){return r.onVideoError()}),r.video),r.addListener("play",(function(){return r.onVideoPlay()}),r.video),r.addListener("pause",(function(){return r.onVideoPause()}),r.video),r.addListener("ended",(function(){return r.onVideoEnded()}),r.video),r.addListener("playing",(function(){return r.onVideoPlaying()}),r.video);var i,o,s,a,u,c,d,l,f,h,v,p,g,y,b=m(document).visibilityChange;if(r.addListener(b,(function(){return r.onVisibilityChange()}),document),r.serviceWorker=yt(),r.serviceWorkerHandler=function(e){return r.onServiceWorkerMessage(e)},r.addServiceWorkerListeners(),void 0!==r.serviceWorker){var S;r.positionTracker=(v=[],p=-1,g=function(){return 0===v.length||void 0===h?(p=-1,!1):-1!==(p=y(h))},y=function(e){var t=new RegExp("^"+e);if(v){var n=v.findIndex((function(e){return t.test(e.pdt)}));if(-1===n)for(var r=new Date(e+"Z"),i=0;i<v.length;i++){var o=v[i],s=new Date(o.pdt);if(s.getTime()<=r.getTime()&&s.getTime()+1e3*o.duration>r.getTime()){n=i;break}}if(-1!==n)return n}return console.warn("Couldn't find segment for PDT "+e),-1},{processAvailableSegments:function(e){return v=e,g()},processTDEN:function(e){return h=e,g()},getCurrentPosition:function(){return p}});var k=null==(S=r.serviceWorker)?void 0:S.getDriftDetectionConfig();void 0!==k&&k.enabled&&(r.driftDetector=(i={maxPlaylistPosition:k.maxPlaylistPosition,triggerDetectionThreshold:k.triggerDetectionThreshold,minDurationBetweenCorrections:k.minDurationBetweenCorrections},u=new ot,c=0,d=0,l=function(){if(void 0!==o&&void 0!==s)if(o<=i.maxPlaylistPosition)d=0;else if((d+=1)>i.triggerDetectionThreshold){console.debug("[handleDriftDetection] above detection threshold");var e=Date.now();(void 0===a||e-a>i.minDurationBetweenCorrections)&&(console.debug("[handleDriftDetection] within min duration, triggering rebuffer-to-live"),f(e,o,s))}},f=function(e,t,n){c+=1,a=e,d=0;var r={type:Mt.Trigger_Drift_Correction,playlistPosition:t,playhead:n,rebufferCount:c};u.emit(Mt.Trigger_Drift_Correction,r)},{addListener:function(e,t){u.on(e,t)},removeListener:function(e,t){u.removeListener(e,t)},processPlaylistPosition:function(e){o=e},processTimeUpdate:function(e){var t=Math.floor(e);void 0!==s&&t===s||(s=t,l())}}),r.driftDetectorHandler=function(e){return r.onDriftDetectorEvent(e)},r.addDriftDetectorListeners()),r.analytics=function(){var e,t,n,r,i=new ot,o={},s={duration:0},a={min:0,avg:0,max:0,last:0},u={first:void 0,last:void 0},c={count:0},d=function(){if((n=wt.Live_Latency_Load)===wt.Live_Latency_Load&&void 0===o[n]&&void 0!==e&&void 0!==t){var n,r=o[wt.Live_Latency_Load]||0;o[wt.Live_Latency_Load]=r+1,i.emit(wt.Live_Latency_Load,{type:wt.Live_Latency_Load,serverOffset:e,manifestLoadAnalytics:t})}},l=function(){var e=o[wt.Live_Latency_Stats]||0;o[wt.Live_Latency_Stats]=e+1,i.emit(wt.Live_Latency_Stats,{type:wt.Live_Latency_Stats,playlistPosition:j({},a),liveLatencyTiming:j({},u),driftCorrection:j({},c)})};return{addListener:function(e,t){i.on(e,t)},removeListener:function(e,t){i.removeListener(e,t)},processServerOffset:function(t){e={serverTime:1e3*t.serverTime,clientTime:t.clientTime,serverOffset:t.serverOffset},d()},processManifestLoadAnalytics:function(e){t=e,d()},processSinkState:function(e){"playing"===n&&"pause"===e&&l(),n=e},processTimeUpdate:function(e){var t=Math.floor(e);void 0!==r&&t===r||(s.duration+=1,r=t,s.duration%10==0&&l())},processPlaylistPosition:function(e){e<a.min&&(a.min=e),e>a.max&&(a.max=e),0===a.avg?a.avg=e:a.avg=(a.avg*s.duration+e)/(s.duration+1),a.last=e},processLiveLatencyUpdate:function(e){void 0===u.first&&(u.first=j({},e)),u.last=j({},e)},processDriftCorrectionStart:function(){c.count+=1}}}(),r.analyticsHandler=function(e){return r.onAnalyticsEvent(e)},r.addAnalyticsListeners()}return r}Ie(t,e);var n=t.prototype;return n.invoke=function(e){this[e.name].call(this,e.arg)},n.configure=function(e){var t=e.path;this.handleTrackEvents(),this.hasReloadedOnDecodeError=!1,this.hasPlayedSrc=!1,this.video.src=t},n.play=function(){var e=this,t=this.video.buffered;if(t.length>0){var n=t.start(t.length-1),r=t.end(t.length-1);this.video.duration===1/0&&(r<this.video.currentTime||this.video.currentTime<n)&&(this.listener.onSinkGapJump(n-this.video.currentTime),console.warn("Moving to buffered region"),this.video.currentTime=n)}this.paused=!1,this.attemptingToPlay=!0,(void 0===e.serviceWorker||e.hasPlayedSrc?Promise.resolve(e.video.play()):e.serviceWorker.ensureConfigured().catch((function(e){At.warn("sw, ensureConfigured failed",e)})).then((function(){return Promise.resolve(e.video.play())}))).then((function(){e.attemptingToPlay=!1,e.hasPlayedSrc=!0})).catch((function(){e.attemptingToPlay=!1,e.checkStopped(!0)}))},n.pause=function(){e.prototype.pause.call(this),clearTimeout(this.intervalId)},n.seekTo=function(e){this.video.currentTime=e},n.setVolume=function(e){this.video.volume=e},n.getVolume=function(){return this.video.volume},n.buffered=function(){return A(this.video.buffered,this.video.currentTime,b)},n.decodedFrames=function(){return M(this.video)},n.droppedFrames=function(){return P(this.video)},n.framerate=function(){return this.fps},n.delete=function(){e.prototype.delete.call(this),this.removeTrackListeners(),this.removeServiceWorkerListeners(),this.removeDriftDetectorListeners(),this.removeAnalyticsListeners(),this.video.src="",this.video.load()},n.isMuted=function(){return this.video.muted},n.setMuted=function(e){this.video.muted=e},n.getDisplayWidth=function(){return this.video.clientWidth},n.getDisplayHeight=function(){return this.video.clientHeight},n.getPlaybackRate=function(){return this.video.playbackRate},n.getCurrentTime=function(){return this.video.currentTime},n.bufferDuration=function(){var e=this.buffered(),t=e.start;return e.end-Math.max(t,this.video.currentTime)},n.captureGesture=function(){Promise.resolve(this.video.play()).catch((function(){})),this.video.pause()},n.addServiceWorkerListeners=function(){void 0!==this.serviceWorker&&(this.serviceWorker.addListener(ut.Manifest_Load_Analytics,this.serviceWorkerHandler),this.serviceWorker.addListener(ut.Available_Segments,this.serviceWorkerHandler))},n.onServiceWorkerMessage=function(e){switch(e.type){case ut.Manifest_Load_Analytics:var t;null==(t=this.analytics)||t.processManifestLoadAnalytics(e.data);break;case ut.Available_Segments:var n;null==(n=this.positionTracker)||n.processAvailableSegments(e.segments)}},n.removeServiceWorkerListeners=function(){void 0!==this.serviceWorker&&(this.serviceWorker.removeListener(ut.Manifest_Load_Analytics,this.serviceWorkerHandler),this.serviceWorker.removeListener(ut.Available_Segments,this.serviceWorkerHandler))},n.addDriftDetectorListeners=function(){var e;null==(e=this.driftDetector)||e.addListener(Mt.Trigger_Drift_Correction,this.driftDetectorHandler)},n.onDriftDetectorEvent=function(e){var t;e.type===Mt.Trigger_Drift_Correction&&(At.debug("triggering drift correction",e),null==(t=this.analytics)||t.processDriftCorrectionStart(),this.resumeAtLive())},n.resumeAtLive=function(){this.pause(),this.configure({path:this.video.src}),this.play()},n.removeDriftDetectorListeners=function(){var e;null==(e=this.driftDetector)||e.removeListener(Mt.Trigger_Drift_Correction,this.driftDetectorHandler)},n.addAnalyticsListeners=function(){var e,t;null==(e=this.analytics)||e.addListener(wt.Live_Latency_Load,this.analyticsHandler),null==(t=this.analytics)||t.addListener(wt.Live_Latency_Stats,this.analyticsHandler)},n.onAnalyticsEvent=function(e){var t;switch(null==(t=this.serviceWorker)||t.debugPassthroughAnalytics(e),e.type){case wt.Live_Latency_Load:At.debug("analytics event, Live Latency Load",e.serverOffset,e.manifestLoadAnalytics);break;case wt.Live_Latency_Stats:At.debug("analytics event, Live Latency Stats",e.playlistPosition,e.liveLatencyTiming,e.driftCorrection)}},n.removeAnalyticsListeners=function(){var e,t;null==(e=this.analytics)||e.removeListener(wt.Live_Latency_Load,this.analyticsHandler),null==(t=this.analytics)||t.removeListener(wt.Live_Latency_Stats,this.analyticsHandler)},n.addTrackListener=function(e,t,n){this.unsubscribersForTrackEvents.push(_(n,e,t))},n.removeTrackListeners=function(){this.unsubscribersForTrackEvents.forEach((function(e){return e()}))},n.checkTracksStatus=function(){for(var e=this.video.textTracks,t=0;t<e.length;t++){var n=e[t];"metadata"===n.kind&&"disabled"===n.mode&&(n.mode="hidden")}},n.handleTDENDataReceived=function(e){if(void 0!==this.positionTracker&&this.positionTracker.processTDEN(e)){var t,n,r=this.positionTracker.getCurrentPosition();null==(t=this.analytics)||t.processPlaylistPosition(r),null==(n=this.driftDetector)||n.processPlaylistPosition(r)}},n.handleTXXXSegmentDataReceived=function(e){var t,n=function(e){var t=h(e);if("transc_r"in t)return{transc_r:parseInt(t.transc_r)}}(e);void 0!==n&&this.latencyStatistics.tryUpdateLatency(n.transc_r)&&(this.listener.onPassthroughSinkPropertyChanged("liveLatency",this.latencyStatistics.liveLatency),null==(t=this.analytics)||t.processLiveLatencyUpdate({transc_r:n.transc_r,latency:this.latencyStatistics.liveLatency}))},n.handleInitialCues=function(e){for(var t,n,r=0;r<e.length;++r){var i=e[r];i.type===Pt.APPLE_HLS&&i.value.key===Tt.STREAM_LEVEL_SERVER_TIME&&(t=Et(i.value.data))}this.processedInitialCues=!0,void 0!==t&&this.latencyStatistics.tryGenerateServerOffset(t)&&(null==(n=this.analytics)||n.processServerOffset({serverTime:t,clientTime:Date.now(),serverOffset:this.latencyStatistics.serverOffset}))},n.shouldPropagateCue=function(e){var t=e.type,n=e.value;return!(t!==Pt.ID3||!n||!(n.key===Tt.TXXX&&"segmentmetadata"!==n.info||n.key===Tt.PRIV&&n.info===i.METADATA_ID||n.key===Tt.PRIV&&n.info===i.INBAND_METADATA_ID))},n.handleCueChange=function(e){var t=this,n=new Set;this.addTrackListener("cuechange",(function(){var r;!t.processedInitialCues&&e.cues&&t.handleInitialCues(e.cues);var i=null!=(r=e.activeCues)?r:[];if(i.length>0){for(var o=new Set,s=0;s<i.length;++s){var a=i[s];if(!n.has(a)){a.type;var u=a.value;if(u.key===Tt.TXXX&&"segmentmetadata"===u.info&&t.handleTXXXSegmentDataReceived(u.data),u.key===Tt.TDEN&&t.handleTDENDataReceived(u.data),t.shouldPropagateCue(a)){var c=u.key===Tt.PRIV?new TextDecoder("utf-8").decode(u.data):u.data||"",d=u.info||"";t.listener.onPassthroughSinkMetadata(a.startTime,a.endTime,c,d,d)}}o.add(a)}n=o}}),e)},n.handleTrackEvents=function(){var e=this;this.removeTrackListeners(),void 0===window.DataCue&&void 0===window.WebKitDataCue||(this.addTrackListener("change",(function(){e.checkTracksStatus()}),this.video.textTracks),this.addTrackListener("addtrack",(function(t){var n=t.track;"metadata"===n.kind&&"disabled"===n.mode&&(n.mode="hidden",e.handleCueChange(n))}),this.video.textTracks))},n.onVideoWaiting=function(){var e=this;if(A(this.video.buffered,this.video.currentTime,b).end-this.video.currentTime<b){this.listener.onSinkIdle(),clearTimeout(this.bufferingTimeoutId),this.bufferingTimeoutId=self.setTimeout((function(){e.listener.onSinkError({value:l,code:l,message:"Buffering timeout"})}),k);var t=_(this.video,"timeupdate",(function(){t(),clearTimeout(e.bufferingTimeoutId)}))}var n=_(this.video,"timeupdate",(function(){4===e.video.readyState&&(n(),e.onVideoPlaying())}))},n.onVideoTimeUpdate=function(){var e,t,n=this.listener,r=this.video;n.onSinkTimeUpdate(),n.onSinkVideoDisplaySizeChanged(r.clientWidth*self.devicePixelRatio,r.clientHeight*self.devicePixelRatio),null==(e=this.analytics)||e.processTimeUpdate(this.getCurrentTime()),null==(t=this.driftDetector)||t.processTimeUpdate(this.getCurrentTime())},n.onVideoDurationChange=function(){this.listener.onSinkDurationChanged(this.video.duration)},n.onVideoError=function(){var e=this.video.error,t=e.code,n=e.message,r=void 0===n?"":n,i=-1!==this.video.src.indexOf(".m3u8");if(4===t&&!this.hasPlayedSrc&&i)return clearTimeout(this.bufferingTimeoutId),void this.listener.onSinkError({value:404,code:404,message:r});3!==t||this.hasReloadedOnDecodeError?this.listener.onSinkError({value:t,code:t,message:r}):this.hasReloadedOnDecodeError||(this.hasReloadedOnDecodeError=!0,console.warn("Reload video element on MEDIA_ERR_DECODE 3"),this.video.load())},n.onVideoPlay=function(){var e=this,t=this.video.currentTime;clearTimeout(this.intervalId),this.intervalId=self.setTimeout((function(){return e.heartbeat(t)}),E)},n.onVideoPause=function(){var e;clearTimeout(this.intervalId),this.attemptingToPlay||(null==(e=this.analytics)||e.processSinkState("pause"),this.checkStopped(!1))},n.onVideoEnded=function(){this.listener.onSinkEnded()},n.onVideoPlaying=function(){var e;null==(e=this.analytics)||e.processSinkState("playing"),this.listener.onSinkPlaying(this.paused),this.trackFPS(M(this.video),performance.now()),this.trackBufferUpdate(A(this.video.buffered,this.video.currentTime,b).end)},n.onVisibilityChange=function(){var e=m(document).hidden;document[e]&&(this.hasReloadedOnDecodeError=!1)},n.heartbeat=function(e){var t,n,r,i=this,o=this.video.currentTime;if(o===e){if(t=this.video,b,r=A(t.buffered,t.currentTime,.1).end-(n=t.currentTime),!(t.ended||t.duration-n<.1)&&r<.1)return void this.listener.onSinkIdle();var s=C(this.video.buffered,o,b);s!==o&&(console.warn("jumping "+(s-o)+"s gap"),this.listener.onSinkGapJump(s-this.video.currentTime),this.video.currentTime=s,o=this.video.currentTime)}this.intervalId=self.setTimeout((function(){return i.heartbeat(o)}),E)},n.trackFPS=function(e,t){var n=this,r=M(this.video),i=performance.now();this.fps=(r-e)/(i-t)*1e3;var o=_(this.video,"timeupdate",(function(){o(),n.trackFPS(r,i)}))},n.trackBufferUpdate=function(e){var t=this,n=this.buffered().end;n!==e&&this.listener.onSinkBufferUpdate();var r=_(this.video,"timeupdate",(function(){r(),t.trackBufferUpdate(n)}))},t}($e),_t=function(e){function t(){return e.apply(this,arguments)||this}return Ie(t,e),t.prototype.onIdle=function(){this.onSinkIdle?this.onSinkIdle():this.listener.onSinkIdle()},t}(et),Lt=function(e){function t(t,n,r){var i;return(i=e.call(this,t,n,new _t(n,t))||this).listener=t,i.video=n,i.adjustments=r,i}Ie(t,e);var n=t.prototype;return n.configure=function(e){var t=e.trackID,n=e.codec,r=e.group,i=e.isProtected;if(this.initSink(),this.isContentProtectionChanging(e)&&this.queueNewSink(),this.isNewSinkNeeded(e)){this.queueNewSink();var o=this.getTrack(w.audio);o&&this.invoke({name:"addTrack",arg:o})}this.invoke({name:"addTrack",arg:j({},Dt,{trackID:t,codec:n,group:r,isProtected:i})})},n.queueNewSink=function(){var e=this;this.awaitSink=new Promise((function(t,n){e.deferUntilIdle().then((function(){var t=O.create(e.onMediaSourceEnded.bind(e),e.onMediaSourceError.bind(e));return e.changeSrc(URL.createObjectURL(t.ms)),t.sink})).then((function(n){e.invokeAsync({name:"play",arg:void 0}),e.destroyMSESink(),e.handleCreateSuccess(n),t()})).catch((function(t){e.handleCreateError(t),n()}))}))},n.deferUntilIdle=function(){var e=this,t=this.mseSink,n=this.playbackMonitor;return new Promise((function(r){t&&!e.video.paused?(n.onSinkIdle=function(){},e.video.addEventListener("waiting",(function(){n.onSinkIdle=void 0,r()}),{once:!0})):r()}))},n.onSegmentDiscontinuity=function(){var e=this.adjustments,t=this.awaitSink,n=this.mseSink;e.rebuildMediaSinkOnDiscontinuity&&n&&!t&&this.queueNewSink()},n.isSinkVideoSourceQualityChangeRequired=function(e,t){return!!this.adjustments.rebuildMediaSinkOnSourceQualityChange&&"chunked"===e!=("chunked"===t)},n.isNewSinkNeeded=function(e){var t=this.adjustments,n=this.mseSink,r=this.awaitSink;if(e.trackID!==w.video)return!1;if(!t.rebuildMediaSinkOnSourceQualityChange)return!1;if(!n||r)return!1;var i=this.getTrack(w.video),o=this.getTrack(w.audio);return!(!i||!o)&&this.isSinkVideoSourceQualityChangeRequired(i.group,e.group)},n.getTrack=function(e){var t,n=this.mseSink;return null!=(t=null==n?void 0:n.bufferProperties.find((function(t){return t.trackID===e})))?t:null},t}(nt),Dt={trackID:0,codec:'codecs="magic"',mode:"mse",isProtected:!1,path:"",group:"",srcObj:null},Rt=function(){function e(e,t,n,r){this.listener=e,this.adjustments=n,this.video=void 0,this.drmManager=void 0,this.codecs=void 0,this.sink=void 0,this.observer=void 0,this.remoteDevicesListener=void 0,this.video=r||document.createElement("video"),this.listener=e,this.drmManager=new Re({video:this.video,listener:e}),this.codecs=Object.create(null),this.sink=new Ve(this.video),t&&(this.remoteDevicesListener=Fe.lookForRemotePlaybackDevices(this.listener)),this.observer=new Be(this.video,e)}var t=e.prototype;return t.delete=function(){var e;this.reset(),null==(e=this.remoteDevicesListener)||e.then((function(e){e&&Fe.stopLookingForRemotePlaybackDevices(e)}))},t.configure=function(e){var t=e.mode,n=e.codec,r=e.trackID;this.sink instanceof Ve&&(this.sink=this.getSink(t)),n?this.codecs[r]=n:e.codec=this.codecs[r],this.sink.configure(e);var i=e.path,o=e.isProtected;i&&o&&this.drmManager.configure(i),this.observer.onConfigure()},t.applyRPC=function(e){this.observer.trackRPC(e),this.sink.invoke(e)},t.getCurrentSink=function(){return this.sink},t.reset=function(){this.sink.delete(),this.drmManager.reset(),this.sink=new Ve(this.video),this.listener.onSinkTimeUpdate(),this.listener.onSinkBufferUpdate()},t.videoElement=function(){return this.video},t.isProtected=function(){return this.drmManager.isProtected()},t.captureGesture=function(){this.video.played.length||this.sink.captureGesture()},t.destroy=function(){this.observer.unsubscribe(),this.delete()},t.isLowLatencyCapable=function(){return this.sink instanceof nt},t.onSegmentDiscontinuity=function(){this.sink.onSegmentDiscontinuity()},t.getSink=function(e){switch(e){case"chromecast":return new Fe(this.listener);case"mse-worker":return new tt(this.listener,this.video);case"passthrough":return new Ct(this.listener,this.video);case"webview":return new Lt(this.listener,this.video,this.adjustments);default:return new nt(this.listener,this.video)}},e}(),Ot=function(e){return e[e.STATE_CHANGED=0]="STATE_CHANGED",e[e.CONFIGURE=1]="CONFIGURE",e[e.RESET=2]="RESET",e[e.ADD_CUE=3]="ADD_CUE",e[e.GET_DECODE_INFO=4]="GET_DECODE_INFO",e[e.MEDIA_SINK_RPC=5]="MEDIA_SINK_RPC",e[e.GET_EXPERIMENTS=6]="GET_EXPERIMENTS",e[e.LOG_MESSAGE=7]="LOG_MESSAGE",e[e.DATA_CHANNEL_CREATE=8]="DATA_CHANNEL_CREATE",e[e.DATA_CHANNEL_CLOSE=9]="DATA_CHANNEL_CLOSE",e[e.DATA_CHANNEL_SEND=10]="DATA_CHANNEL_SEND",e[e.RTC_SET_REMOTE_DESCRIPTION=11]="RTC_SET_REMOTE_DESCRIPTION",e[e.PROPERTY_CHANGED=12]="PROPERTY_CHANGED",e[e.BUFFERED_RANGES=13]="BUFFERED_RANGES",e[e.DESTROY=14]="DESTROY",e}({}),It={rebuildMediaSinkOnSourceQualityChange:!1,rebuildMediaSinkOnDiscontinuity:!1,abrTranscodesOnly:!1},xt=function(){function e(t,n){var r,i,o=this;this.worker=void 0,this.id=void 0,this.emitter=void 0,this.seekTime=void 0,this.paused=void 0,this.isLoaded=void 0,this.autoPlayOptions=void 0,this.mediaSinkManager=void 0,this.experiments=void 0,this.adjustments=void 0,this.enableRemoteSearch=void 0,this.isQualitySupported=void 0,this.onvisibilitychange=void 0,this.onmessage=void 0,this.onOnline=void 0,this.onOffline=void 0,this.pauseHiddenSilentTab=void 0,this.state=void 0,this.workerMessageProcessors=void 0,this.startCapture=void 0,this.stopCapture=void 0,this.requestCaptureAnalytics=void 0,this.worker=n,this.id=e.instanceId++,this.emitter=new ot,this.seekTime=null,this.paused=!0,this.isLoaded=!1,this.autoPlayOptions=null,this.isQualitySupported=t.isQualitySupported||p,this.onvisibilitychange=function(){return o.onVisibilityChange()},this.onmessage=function(e){return o.onWorkerMessage(e)},this.onOnline=function(){return o.postMessage("onOnline")},this.onOffline=function(){return o.postMessage("onOffline")},this.enableRemoteSearch=t.enableRemoteSearch||!1;var s,a,u=U();if(this.pauseHiddenSilentTab=u.chrome&&63===u.major||u.opera,this.adjustments=(s=null!=(r=null==(i=t.webviewHost)?void 0:i.adjustments)?r:{},j({},It,s)),this.mediaSinkManager=new Rt(this,this.enableRemoteSearch,this.adjustments),this.experiments={},this.workerMessageProcessors=[G],q.isSupported()){var c=new q;this.workerMessageProcessors.push(c.processor)}void 0!==t.serviceWorker&&("serviceWorker"in navigator||(gt.warn("Service workers are not supported."),0))&&(function(e){mt=St(e)}(t.serviceWorker),null==(a=yt())||a.registerAndActivate()),this.state={averageBitrate:0,bandwidthEstimate:0,looping:!1,autoQualityMode:!0,volume:1,liveLatency:0,liveLowLatencyEnabled:!0,liveLowLatency:!1,statistics:{}},this.resetState(),this.attachHandlers();var d,l=[];d='video/mp4; codecs="hvc1.1.40000000.L60.80.0.0.0.0.0"',(K()?MediaSource.isTypeSupported(d):""!==document.createElement("video").canPlayType(d))&&l.push("hevc"),function(){var e='video/mp4; codecs="av01.0.05M.08"';return K()?MediaSource.isTypeSupported(e):""!==document.createElement("video").canPlayType(e)}()&&l.push("av01"),this.postMessage("create",[{mseSupported:K(),keySystem:void 0!==t.keySystem?t.keySystem:le(u),browserContext:j({},u,{webviewHost:t.webviewHost}),codecs:l,testOnly:t.testOnly,playerFramework:t.playerFramework,buildDistId:"cdn"}])}e.isVP9Supported=function(){return K()&&MediaSource.isTypeSupported('video/mp4;codecs="vp09.00.10.08"')};var n=e.prototype;return n.delete=function(){var e=this,t=yt();void 0!==t&&t.destroy();var n=m(document).visibilityChange;document.removeEventListener(n,this.onvisibilitychange),window.removeEventListener("online",this.onOnline),window.removeEventListener("offline",this.onOffline),this.emitter.removeAllListeners(),this.emitter.on(Ot.DESTROY,(function(){e.mediaSinkManager.destroy(),e.emitter.removeAllListeners(),e.worker.removeEventListener("message",e.onmessage)})),this.postMessage("delete")},n.attachHTMLVideoElement=function(e){this.mediaSinkManager&&this.mediaSinkManager.destroy(),this.mediaSinkManager=new Rt(this,this.enableRemoteSearch,this.adjustments,e),this.processVideoElementAttributes(e)},n.getHTMLVideoElement=function(){return this.mediaSinkManager.videoElement()},n.load=function(e,t){void 0===t&&(t="");var n=yt();void 0!==n&&n.configure(),this.postMessage("load",[e,t]),this.autoPlayOptions&&this.postMessage("playIntent")},n.play=function(){this.postMessage("playIntent"),this.mediaSinkManager.captureGesture(),this.paused=!1,this.attemptPlay()},n.setAutoplay=function(e){this.autoPlayOptions=e?{attemptMutedRetry:!0}:null},n.setAutoPlayOptions=function(e){this.autoPlayOptions=e},n.getExperiments=function(){return this.experiments},n.setExperiment=function(e,t){this.setExperimentData({id:e,assignment:t,version:0,type:""})},n.setExperimentData=function(e){this.postMessage("setExperiment",[e])},n.pause=function(){this.paused=!0,this.postMessage("pause")},n.isPaused=function(){return this.paused},n.seekTo=function(e){this.seekTime=e,this.postMessage("seekTo",[e])},n.isSeeking=function(){return null!==this.seekTime},n.isAutoplay=function(){return!!this.autoPlayOptions},n.getDuration=function(){return this.state.duration},n.getStartOffset=function(){return this.state.startOffset||0},n.getPosition=function(){return null===this.seekTime?this.mediaSinkManager.getCurrentSink().getCurrentTime():this.seekTime},n.getBuffered=function(){return this.mediaSinkManager.getCurrentSink().buffered()},n.getBufferedRanges=function(){return this.postMessage("updateBufferedRanges",[]),this.state.trackBufferedRanges},n.getSinkBufferedRanges=function(){var e=this.mediaSinkManager.getCurrentSink();return{audio:e.getBufferedRanges("audio"),video:e.getBufferedRanges("video")}},n.getBufferDuration=function(){return this.state.bufferedPosition-this.getPosition()},n.getState=function(){return this.state.state},n.getVideoWidth=function(){return this.mediaSinkManager.videoElement().videoWidth},n.getVideoHeight=function(){return this.mediaSinkManager.videoElement().videoHeight},n.getVideoFrameRate=function(){return this.state.statistics.framerate},n.getVideoBitRate=function(){return this.state.statistics.bitrate},n.getAverageBitrate=function(){return this.state.averageBitrate},n.getBandwidthEstimate=function(){return this.state.bandwidthEstimate},n.getPath=function(){return this.state.path},n.getProtocol=function(){return this.state.protocol},n.getVersion=function(){return"1.25.0"},n.isLiveLowLatency=function(){return this.state.liveLowLatencyEnabled&&this.state.liveLowLatency},n.isLooping=function(){return this.state.looping},n.setLogLevel=function(e){!function(e){var t={debug:!1,log:!1,info:!1,warn:!1,error:!1},n=function(e,n){for(var r,i=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return ct(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ct(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(e);!(r=i()).done;){var o=r.value;t[o]=n}};switch(e.level){case"debug":n(["debug","log","info","warn","error"],!0);break;case"log":n(["log","info","warn","error"],!0);break;case"info":n(["info","warn","error"],!0);break;case"warn":n(["warn","error"],!0);break;case"error":n(["error"],!0)}!function(e){dt.enabled=e.enabled,dt.levels=j({},e.levels),lt.emit("logConfigUpdated",e)}({enabled:e.enabled,levels:t})}({enabled:!0,level:e}),this.postMessage("setLogLevel",[e])},n.setLooping=function(e){this.state.looping=e,this.postMessage("setLooping",[e])},n.isMuted=function(){return this.mediaSinkManager.getCurrentSink().isMuted()},n.setMuted=function(e){this.mediaSinkManager.getCurrentSink().setMuted(e)},n.setVolume=function(e){this.state.volume=e,this.postMessage("setVolume",[this.state.volume])},n.getVolume=function(){return this.state.volume},n.getQuality=function(){return this.state.quality},n.setQuality=function(e,t){void 0===t&&(t=!1),this.mediaSinkManager.videoElement().controls||(this.postMessage("setQuality",[e,t]),this.state.autoQualityMode=!1)},n.getQualities=function(){return this.state.qualities},n.setAuthToken=function(e){this.postMessage("setAuthToken",[e])},n.isAutoQualityMode=function(){return this.state.autoQualityMode},n.setAutoQualityMode=function(e){this.state.autoQualityMode=e,this.postMessage("setAutoQualityMode",[e])},n.setAutoInitialBitrate=function(e){this.postMessage("setAutoInitialBitrate",[e])},n.setAutoMaxQuality=function(e){this.postMessage("setAutoMaxQuality",[e])},n.setAutoMaxBitrate=function(e){this.postMessage("setAutoMaxBitrate",[e])},n.setAutoMaxVideoSize=function(e,t){this.postMessage("setAutoMaxVideoSize",[e,t])},n.setAutoViewportSize=function(e,t){this.postMessage("setAutoViewportSize",[e,t])},n.getPlaybackRate=function(){return this.mediaSinkManager.getCurrentSink().getPlaybackRate()},n.setPlaybackRate=function(e){return this.mediaSinkManager.getCurrentSink().setPlaybackRate(e)},n.setClientId=function(e){this.postMessage("setClientId",[e])},n.setDeviceId=function(e){this.postMessage("setDeviceId",[e])},n.setLiveSpeedUpRate=function(e){this.postMessage("setLiveSpeedUpRate",[e])},n.setPlayerType=function(e){this.postMessage("setPlayerType",[e])},n.setLiveMaxLatency=function(e){this.postMessage("setLiveMaxLatency",[e])},n.setLiveLowLatencyEnabled=function(e){this.state.liveLowLatencyEnabled=e,this.postMessage("setLiveLowLatencyEnabled",[e])},n.setRebufferToLive=function(e){var t,n=yt();void 0!==n&&null!=(t=n.getDriftDetectionConfig())&&t.enabled||this.postMessage("setRebufferToLive",[e])},n.setVisible=function(e){this.postMessage("setVisible",[e])},n.setInitialBufferDuration=function(e){this.postMessage("setInitialBufferDuration",[e])},n.addEventListener=function(e,t){this.emitter.on(e,t)},n.removeEventListener=function(e,t){this.emitter.removeListener(e,t)},n.getDroppedFrames=function(){return this.state.statistics.droppedFrames},n.getDecodedFrames=function(){return this.state.statistics.decodedFrames},n.getDisplayWidth=function(){return this.mediaSinkManager.getCurrentSink().getDisplayWidth()},n.getDisplayHeight=function(){return this.mediaSinkManager.getCurrentSink().getDisplayHeight()},n.getSessionId=function(){return this.state.sessionId},n.getSessionData=function(){return this.state.sessionData},n.getLiveLatency=function(){return this.state.liveLatency},n.isProtected=function(){return this.mediaSinkManager.isProtected()},n.startRemotePlayback=function(){this.postMessage("startRemotePlayback")},n.endRemotePlayback=function(){this.postMessage("endRemotePlayback")},n.setPlatformName=function(e){this.postMessage("setPlatformName",[e])},n.setRequestCredentials=function(e){this.postMessage("setRequestCredentials",[e])},n.setSinkType=function(e){this.postMessage("setSinkType",[e])},n.onSinkTimeUpdate=function(){var e=this.mediaSinkManager.getCurrentSink();null===this.seekTime&&(this.postMessage("onClientSinkUpdate",[{currentTime:e.getCurrentTime(),decodedFrames:e.decodedFrames(),droppedFrames:e.droppedFrames(),framerate:e.framerate(),bufferDuration:e.bufferDuration(),displayHeight:e.getDisplayHeight(),displayWidth:e.getDisplayWidth()}]),this.emitter.emit(o.TIME_UPDATE,e.getCurrentTime()))},n.onSinkBufferUpdate=function(){this.emitter.emit(o.BUFFER_UPDATE)},n.onSinkDurationChanged=function(e){this.postMessage("onClientSinkDurationChanged",[e])},n.onSinkEnded=function(){this.postMessage("onClientSinkEnded")},n.onSinkIdle=function(){this.postMessage("onClientSinkIdle")},n.onSinkPlaying=function(e){this.postMessage("onClientSinkPlaying"),e&&this.play()},n.onSinkStop=function(e){var t,n,r=m(document).hidden;if(document[r])this.postMessage("pause");else if(e){if(!this.isMuted()&&(null==(t=null==(n=this.autoPlayOptions)?void 0:n.attemptMutedRetry)||t))return this.setMuted(!0),this.mediaSinkManager.getCurrentSink().play(),void this.emitter.emit(o.AUDIO_BLOCKED);this.pause(),this.emitter.emit(o.PLAYBACK_BLOCKED)}else this.pause()},n.onSinkReset=function(){this.postMessage("onClientSinkReset")},n.onSinkError=function(e){var t=e.value,n=e.code,r=e.message;this.postMessage("onClientSinkError",[t,n,r])},n.onSinkRecoverableError=function(e){var t=e.value,n=e.code,r=e.message;this.postMessage("onClientSinkRecoverableError",[t,n,r])},n.onSinkVideoDisplaySizeChanged=function(e,t){this.setAutoViewportSize(e,t)},n.onSinkVolumeChanged=function(e,t){this.mediaSinkManager.videoElement().controls&&t&&this.setVolume(e),this.emitter.emit(o.VOLUME_CHANGED,this.state.volume)},n.onSinkMutedChanged=function(e){this.postMessage("setMuted",[e]),this.emitter.emit(o.MUTED_CHANGED)},n.onSinkPlaybackRateChanged=function(e){this.postMessage("setPlaybackRate",[e])},n.onPassthroughSinkMetadata=function(e,t,n,r,i){this.emitter.emit(o.TEXT_METADATA_CUE,{description:r,endTime:t,startTime:e,text:n,owner:i,type:"TextMetadataCue"})},n.onPassthroughSinkPropertyChanged=function(e,t){this.state[e]=t,this.postMessage("onClientSinkPassthroughPropertyChanged",[e,t])},n.onSinkControlsChanged=function(e){this.postMessage("setControls",[e])},n.onSinkGapJump=function(e){this.postMessage("onClientSinkGapJump",[e])},n.onRemoteDevice=function(e){this.emitter.emit(e?Ee.AVAILABLE:Ee.UNAVAILABLE)},n.onRemoteReconnect=function(){this.startRemotePlayback()},n.onSessionError=function(){this.postMessage("onClientSinkError",[1,0,"Chromecast session error"])},n.onLoadMediaError=function(){this.postMessage("onClientSinkError",[1,0,"Chromecast load media failed"])},n.onUserCancel=function(){this.endRemotePlayback(),this.emitter.emit(Ee.SESSION_ENDED)},n.onSegmentDiscontinuity=function(){this.mediaSinkManager.onSegmentDiscontinuity()},n.onSessionStop=function(){this.endRemotePlayback(),this.emitter.emit(Ee.SESSION_ENDED)},n.onSessionStarted=function(e){this.emitter.emit(Ee.SESSION_STARTED,e)},n.attemptPlay=function(){var e=m(document).hidden;!document[e]&&this.isLoaded&&this.postMessage("play")},n.postMessage=function(e,t,n){void 0===n&&(n=[]),this.worker.postMessage({id:this.id,funcName:e,args:t},n)},n.resetState=function(){v(this.state,{state:s.IDLE,quality:{name:"",group:"",codecs:"",bitrate:0,width:0,height:0,framerate:0},qualities:[],duration:0,startOffset:0,sessionData:{},volume:1,statistics:{bitrate:0,framerate:0,droppedFrames:0,decodeFrames:0,renderedFrames:0},trackBufferedRanges:{audio:[],video:[]}}),this.emitter.emit(o.DURATION_CHANGED,0),this.seekTime=null,this.isLoaded=!1},n.attachHandlers=function(){var e=this;this.worker.addEventListener("message",this.onmessage);var n=m(document).visibilityChange;document.addEventListener(n,this.onvisibilitychange),window.addEventListener("online",this.onOnline),window.addEventListener("offline",this.onOffline);var r=this.emitter;r.on(o.VOLUME_CHANGED,(function(){return e.onVolumeChanged()})),r.on(o.MUTED_CHANGED,(function(){return e.onMutedChanged()})),r.on(o.SEEK_COMPLETED,(function(){return e.onSeekCompleted()})),r.on(o.ERROR,(function(){return e.onError()})),r.on(o.SESSION_DATA,(function(t){return e.onSessionData(t)})),r.on(o.SEGMENT_DISCONTINUITY,(function(){return e.onSegmentDiscontinuity()})),r.on(Ot.STATE_CHANGED,(function(t){return e.onStateChanged(t)})),r.on(Ot.MEDIA_SINK_RPC,(function(t){return e.mediaSinkManager.applyRPC(t)})),r.on(Ot.CONFIGURE,(function(t){e.mediaSinkManager.configure(t),e.setSinkType(t.mode)})),r.on(Ot.RESET,(function(){return e.mediaSinkManager.reset()})),r.on(t.ID3,(function(t){return e.onID3(t)})),r.on(Ot.GET_EXPERIMENTS,(function(t){e.experiments=t})),r.on(Ot.PROPERTY_CHANGED,(function(t){var n=t.key,r=t.value;e.state[n]=r})),r.on(Ot.BUFFERED_RANGES,(function(t){e.state.trackBufferedRanges=t})),r.on(Ot.LOG_MESSAGE,(function(e){var t=e.level,n=e.message;return console[t](n)}))},n.onVolumeChanged=function(){var e=m(document).hidden;this.pauseHiddenSilentTab&&document[e]&&0===this.getVolume()&&this.postMessage("pause")},n.onMutedChanged=function(){var e=m(document).hidden;this.pauseHiddenSilentTab&&document[e]&&this.isMuted()&&this.postMessage("pause")},n.onSeekCompleted=function(){this.seekTime=null},n.onError=function(){this.paused=!0},n.onStateChanged=function(e){var t=this;switch(e){case s.READY:var n=this.isQualitySupported;if(this.adjustments.abrTranscodesOnly&&this.state.qualities.length>1){var r=this.state.qualities.slice().sort((function(e,t){return t.bitrate-e.bitrate}));"chunked"!==r[0].group?n=function(e){return"chunked"!==e.group&&t.isQualitySupported(e)}:this.setAutoMaxQuality(r[1])}var i=function(e,t){var n=[],r=[];return e.forEach((function(e){t(e)?n.push(e):r.push(e)})),{supported:n,unsupported:r}}(this.state.qualities,n);this.state.qualities=i.supported,i.unsupported.forEach((function(e){return t.postMessage("removeQuality",[e])})),this.isLoaded=!0,this.autoPlayOptions&&this.play(),this.paused||this.attemptPlay();break;case s.ENDED:this.paused=!0}this.emitter.emit(o.STATE_CHANGED,e),this.emitter.emit(e)},n.onID3=function(e){var t=this;e.forEach((function(e){if("TXXX"===e.id&&"segmentmetadata"===e.desc&&e.info.length){var n=h(e.info[0]);if(Object.prototype.hasOwnProperty.call(n,"stream_offset")){var r=Number(n.stream_offset);isNaN(r)||(t.state.startOffset=r-t.getPosition())}}}))},n.onVisibilityChange=function(){var e=m(document).hidden;this.paused||document[e]||this.attemptPlay(),this.pauseHiddenSilentTab&&!this.paused&&document[e]&&(this.isMuted()||0===this.getVolume())&&this.postMessage("pause"),U().firefox||this.postMessage("setVisible",[!document[e]])},n.onSessionData=function(e){v(this.state,e)},n.onWorkerMessage=function(e){var t=e.data;if(t&&t.id===this.id){var n=this.workerMessageProcessors.reduce((function(e,t){return t(e)}),t),r=n.type,i=n.arg;void 0!==t.arg?this.emitter.emit(r,i):this.emitter.emit(r)}},n.processVideoElementAttributes=function(e){if(e.hasAttribute("autoplay")&&(e.removeAttribute("autoplay"),this.setAutoplay(!0)),e.hasAttribute("playbackRate")){var t,n=parseFloat(null!=(t=e.getAttribute("playbackRate"))?t:"1.0");if(!isNaN(n)){var r=g(n,.25,2);this.setPlaybackRate(r)}e.removeAttribute("playbackRate")}if(e.hasAttribute("src")){var i=e.src;y(e),this.load(i)}if(e.hasAttribute("loop")&&(e.removeAttribute("loop"),this.setLooping(!0)),e.hasAttribute("muted")&&(e.removeAttribute("muted"),this.setMuted(!0)),e.hasAttribute("volume")){var o,s=parseFloat(null!=(o=e.getAttribute("volume"))?o:"1.0");isNaN(s)||this.setVolume(g(s,0,1)),e.removeAttribute("volume")}},e}();xt.instanceId=0;var Nt=function(){function e(e){var t=this;this.workerPort=void 0,this.emitter=void 0,this.messageQueue=void 0,this.workerPort={postMessage:this.postMessageFromWorker.bind(this),onmessage:function(){}},this.emitter=new it.EventEmitter,this.messageQueue=new f,this.loadScript(e,(function(e){return t.applyWorkerEnv(e)}))}var t=e.prototype;return t.postMessage=function(e){this.messageQueue?this.messageQueue.push(e):this.postMessageToWorker(e)},t.addEventListener=function(e,t){this.emitter.on(e,t)},t.removeEventListener=function(e,t){this.emitter.off(e,t)},t.onmessage=function(){},t.onmessageerror=function(){},t.onerror=function(){},t.terminate=function(){},t.dispatchEvent=function(){return!0},t.loadScript=function(e,t){var n=this,r=new XMLHttpRequest;r.open("GET",e),r.addEventListener("load",(function(){r.status>=200&&r.status<400?t(r.response):n.emitter.emit("error",new Error(r.statusText))})),r.addEventListener("error",(function(e){n.emitter.emit("error",e)})),r.send()},t.applyWorkerEnv=function(e){if(this.messageQueue){try{Function("self","messageHandler",e)(window,this.workerPort)}catch(e){return void this.emitter.emit("error",e)}for(;!this.messageQueue.empty();)this.postMessageToWorker(this.messageQueue.pop());this.messageQueue=null}},t.postMessageFromWorker=function(e){var t=this;setTimeout((function(){t.emitter.emit("message",{data:e})}),0)},t.postMessageToWorker=function(e){var t=this;setTimeout((function(){t.workerPort.onmessage({data:e})}),0)},e}();var Ft="undefined"!=typeof window&&"object"==typeof window.WebAssembly&&"function"==typeof window.WebAssembly.instantiate,Vt=function(){function e(e,t){this.core=void 0,this.startCapture=void 0,this.stopCapture=void 0,this.requestCaptureAnalytics=void 0,this.core=new xt(e,t)}var t=e.prototype;return t.addEventListener=function(e,t){var n;null==(n=this.checkCore())||n.addEventListener(e,t)},t.attachHTMLVideoElement=function(e){var t;null==(t=this.checkCore())||t.attachHTMLVideoElement(e)},t.delete=function(){var e;null==(e=this.checkCore())||e.delete(),this.core=null},t.endRemotePlayback=function(){var e;null==(e=this.checkCore())||e.endRemotePlayback()},t.isAutoplay=function(){var e;return null==(e=this.checkCore())?void 0:e.isAutoplay()},t.isAutoQualityMode=function(){var e;return null==(e=this.checkCore())?void 0:e.isAutoQualityMode()},t.getAverageBitrate=function(){var e;return null==(e=this.checkCore())?void 0:e.getAverageBitrate()},t.getBandwidthEstimate=function(){var e;return null==(e=this.checkCore())?void 0:e.getBandwidthEstimate()},t.getBufferDuration=function(){var e;return null==(e=this.checkCore())?void 0:e.getBufferDuration()},t.getBuffered=function(){var e;return null==(e=this.checkCore())?void 0:e.getBuffered()},t.getBufferedRanges=function(){var e;return null==(e=this.checkCore())?void 0:e.getBufferedRanges()},t.getSinkBufferedRanges=function(){var e;return null==(e=this.checkCore())?void 0:e.getSinkBufferedRanges()},t.getDecodedFrames=function(){var e;return null==(e=this.checkCore())?void 0:e.getDecodedFrames()},t.getDisplayHeight=function(){var e;return null==(e=this.checkCore())?void 0:e.getDisplayHeight()},t.getDisplayWidth=function(){var e;return null==(e=this.checkCore())?void 0:e.getDisplayWidth()},t.getDroppedFrames=function(){var e;return null==(e=this.checkCore())?void 0:e.getDroppedFrames()},t.getDuration=function(){var e;return null==(e=this.checkCore())?void 0:e.getDuration()},t.getExperiments=function(){var e;return null==(e=this.checkCore())?void 0:e.getExperiments()},t.getHTMLVideoElement=function(){var e;return null==(e=this.checkCore())?void 0:e.getHTMLVideoElement()},t.getLiveLatency=function(){var e;return null==(e=this.checkCore())?void 0:e.getLiveLatency()},t.getPath=function(){var e;return null==(e=this.checkCore())?void 0:e.getPath()},t.getProtocol=function(){var e;return null==(e=this.checkCore())?void 0:e.getProtocol()},t.getPlaybackRate=function(){var e;return null==(e=this.checkCore())?void 0:e.getPlaybackRate()},t.getPosition=function(){var e;return null==(e=this.checkCore())?void 0:e.getPosition()},t.getQualities=function(){var e;return null==(e=this.checkCore())?void 0:e.getQualities()},t.getQuality=function(){var e;return null==(e=this.checkCore())?void 0:e.getQuality()},t.getSessionData=function(){var e;return null==(e=this.checkCore())?void 0:e.getSessionData()},t.getSessionId=function(){var e;return null==(e=this.checkCore())?void 0:e.getSessionId()},t.getStartOffset=function(){var e;return null==(e=this.checkCore())?void 0:e.getStartOffset()},t.getState=function(){var e;return null==(e=this.checkCore())?void 0:e.getState()},t.getVersion=function(){var e;return null==(e=this.checkCore())?void 0:e.getVersion()},t.getVideoBitRate=function(){var e;return null==(e=this.checkCore())?void 0:e.getVideoBitRate()},t.getVideoFrameRate=function(){var e;return null==(e=this.checkCore())?void 0:e.getVideoFrameRate()},t.getVideoHeight=function(){var e;return null==(e=this.checkCore())?void 0:e.getVideoHeight()},t.getVideoWidth=function(){var e;return null==(e=this.checkCore())?void 0:e.getVideoWidth()},t.getVolume=function(){var e;return null==(e=this.checkCore())?void 0:e.getVolume()},t.isLiveLowLatency=function(){var e;return null==(e=this.checkCore())?void 0:e.isLiveLowLatency()},t.isLooping=function(){var e;return null==(e=this.checkCore())?void 0:e.isLooping()},t.isMuted=function(){var e;return null==(e=this.checkCore())?void 0:e.isMuted()},t.isPaused=function(){var e;return null==(e=this.checkCore())?void 0:e.isPaused()},t.isProtected=function(){var e;return null==(e=this.checkCore())?void 0:e.isProtected()},t.isSeeking=function(){var e;return null==(e=this.checkCore())?void 0:e.isSeeking()},t.load=function(e,t){var n;return void 0===t&&(t=""),null==(n=this.checkCore())?void 0:n.load(e,t)},t.pause=function(){var e;null==(e=this.checkCore())||e.pause()},t.play=function(){var e;null==(e=this.checkCore())||e.play()},t.removeEventListener=function(e,t){var n;null==(n=this.checkCore())||n.removeEventListener(e,t)},t.seekTo=function(e){var t;null==(t=this.checkCore())||t.seekTo(e)},t.setAuthToken=function(e){var t;null==(t=this.checkCore())||t.setAuthToken(e)},t.setAutoInitialBitrate=function(e){var t;null==(t=this.checkCore())||t.setAutoInitialBitrate(e)},t.setAutoMaxQuality=function(e){var t;null==(t=this.checkCore())||t.setAutoMaxQuality(e)},t.setAutoMaxBitrate=function(e){var t;null==(t=this.checkCore())||t.setAutoMaxBitrate(e)},t.setAutoMaxVideoSize=function(e,t){var n;null==(n=this.checkCore())||n.setAutoMaxVideoSize(e,t)},t.setAutoplay=function(e){var t;null==(t=this.checkCore())||t.setAutoplay(e)},t.setAutoPlayOptions=function(e){var t;null==(t=this.checkCore())||t.setAutoPlayOptions(e)},t.setAutoQualityMode=function(e){var t;null==(t=this.checkCore())||t.setAutoQualityMode(e)},t.setAutoViewportSize=function(e,t){var n;null==(n=this.checkCore())||n.setAutoViewportSize(e,t)},t.setClientId=function(e){var t;null==(t=this.checkCore())||t.setClientId(e)},t.setDeviceId=function(e){var t;null==(t=this.checkCore())||t.setDeviceId(e)},t.setExperiment=function(e,t){var n;null==(n=this.checkCore())||n.setExperiment(e,t)},t.setExperimentData=function(e){var t;null==(t=this.checkCore())||t.setExperimentData(e)},t.setInitialBufferDuration=function(e){var t;null==(t=this.checkCore())||t.setInitialBufferDuration(e)},t.setLiveLowLatencyEnabled=function(e){var t;null==(t=this.checkCore())||t.setLiveLowLatencyEnabled(e)},t.setLiveMaxLatency=function(e){var t;null==(t=this.checkCore())||t.setLiveMaxLatency(e)},t.setLiveSpeedUpRate=function(e){var t;null==(t=this.checkCore())||t.setLiveSpeedUpRate(e)},t.setLogLevel=function(e){var t;null==(t=this.checkCore())||t.setLogLevel(e)},t.setLooping=function(e){var t;null==(t=this.checkCore())||t.setLooping(e)},t.setMuted=function(e){var t;null==(t=this.checkCore())||t.setMuted(e)},t.setPlaybackRate=function(e){var t;null==(t=this.checkCore())||t.setPlaybackRate(e)},t.setPlayerType=function(e){var t;null==(t=this.checkCore())||t.setPlayerType(e)},t.setQuality=function(e,t){var n;void 0===t&&(t=!1),null==(n=this.checkCore())||n.setQuality(e,t)},t.setRebufferToLive=function(e){var t;null==(t=this.checkCore())||t.setRebufferToLive(e)},t.setVisible=function(e){var t;null==(t=this.checkCore())||t.setVisible(e)},t.setVolume=function(e){var t;null==(t=this.checkCore())||t.setVolume(e)},t.startRemotePlayback=function(){var e;null==(e=this.checkCore())||e.startRemotePlayback()},t.setPlatformName=function(e){var t;null==(t=this.checkCore())||t.setPlatformName(e)},t.setRequestCredentials=function(e){var t;null==(t=this.checkCore())||t.setRequestCredentials(e)},t.checkCore=function(){return this.core||console.warn("Method called on deleted player instance."),this.core},e}();Vt.isVP9Supported=xt.isVP9Supported;var Bt,Ut=function(e){return e.DURATION_CHANGE="durationchange",e.ENDED="ended",e.ERROR="error",e.LOADED_METADATA="loadedmetadata",e.LOADSTART="loadstart",e.PAUSE="pause",e.PLAY="play",e.PLAYING="playing",e.RATE_CHANGE="ratechange",e.SEEKED="seeked",e.SEEKING="seeking",e.TIME_UPDATE="timeupdate",e.VOLUME_CHANGE="volumechange",e.WAITING="waiting",e}({}),jt=function(e){return e[e.HAVE_NOTHING=0]="HAVE_NOTHING",e[e.HAVE_METADATA=1]="HAVE_METADATA",e[e.HAVE_CURRENT_DATA=2]="HAVE_CURRENT_DATA",e[e.HAVE_FUTURE_DATA=3]="HAVE_FUTURE_DATA",e[e.HAVE_ENOUGH_DATA=4]="HAVE_ENOUGH_DATA",e}(jt||{}),Ht=((Bt={})[s.IDLE]=1,Bt[s.READY]=1,Bt[s.BUFFERING]=2,Bt[s.PLAYING]=2,Bt[s.ENDED]=1,Bt),Wt="AmazonIVS";function Gt(n,r){if(void 0===n||"function"!=typeof n.getTech)throw{message:"videojs not available, AmazonIVS tech not registered",code:1};if(!Ft)throw{message:"WebAssembly support is required for AmazonIVS tech",code:2};if(!n.getTech(Wt)){var i,a={featuresProgressEvents:!0,featuresTimeupdateEvents:!0,featuresPlaybackRate:!0,featuresFullscreenResize:!0,featuresVolumeControl:!0,featuresMuteControl:!0,featuresNativeTextTracks:!1,privateContructor:function(e,t,i){this._readyState=jt.HAVE_NOTHING,this._defaultPlaybackRate=1,this._seeking=!1,r.playerFramework={name:"videojs",version:n.VERSION},this._mediaPlayer=function(e){var t=e.asmWorker,n=e.wasmWorker,r=e.wasmBinary;if(!Ft&&!t)throw new Error("WebAssembly is not supported by the browser. This is required for playback.");var i,o,s,a,u,c,d,l=(i=Ft?n:t,o=r,void 0===(s=e.showWorkerLog)&&(s=!1),U().msIE?a=new Nt(i):(u=i,c=window.location,(d=document.createElement("a")).href=u,a=d.hostname===c.hostname&&d.port===c.port&&d.protocol===c.protocol?new Worker(i):new Worker(URL.createObjectURL(new Blob(["importScripts('"+i+"')"])))),a.postMessage({wasmBinaryUrl:o,showWorkerLogs:s}),a);return new Vt(e,l)}(r),this._mediaPlayer.setAutoplay(!0===e.autoplay),this._attachVideojsListeners(),this._mediaPlayer.addEventListener(o.METADATA,this._onCaptionEvent.bind(this));var s=this._createEl(e);e.el=s,i(this,e,t),window.vttjs&&window.vttjs.restore(),this.triggerReady(),setTimeout(function(){var e=this.options(),t=e.loop,n=e.muted;t&&this._mediaPlayer.setLooping(!0),n&&this._mediaPlayer.setMuted(!0)}.bind(this),0)},dispose:function(){this._mediaPlayer.delete()},setPreload:function(){},autoplay:function(e){if("boolean"!=typeof e)return this._mediaPlayer.autoplay;this.setAutoplay(e)},setAutoplay:function(e){this._mediaPlayer.setAutoplay(e)},preload:function(){},load:function(){},readyState:function(){return this._readyState},seeking:function(){return this._seeking},networkState:function(){if(!this._mediaPlayer)return 0;if(!this._mediaPlayer.getHTMLVideoElement().src)return 3;var e=this._mediaPlayer.getState();return Ht[e]},ended:function(){return this._mediaPlayer.getState()===s.ENDED},seekable:function(){return n.createTimeRange(0,this._mediaPlayer.getDuration())},play:function(){this._mediaPlayer.play(),this.trigger(Ut.PLAY)},pause:function(){this._mediaPlayer.pause()},setCurrentTime:function(e){this._mediaPlayer.getHTMLVideoElement().src&&(this._mediaPlayer.seekTo(e),this._seeking=!0,this.trigger(Ut.SEEKING))},controls:function(){return!1},setControls:function(){return!1},muted:function(){return this._mediaPlayer.isMuted()},setMuted:function(e){this._mediaPlayer.setMuted(e)},volume:function(){return this._mediaPlayer.getVolume()},setVolume:function(e){this._mediaPlayer.setVolume(e)},defaultPlaybackRate:function(e){if(!e)return this._defaultPlaybackRate;this._defaultPlaybackRate=e},playbackRate:function(){return this._mediaPlayer.getPlaybackRate()},setPlaybackRate:function(e){this._mediaPlayer.setPlaybackRate(e)},paused:function(){return this._mediaPlayer.isPaused()},duration:function(){return this._mediaPlayer.getDuration()},currentTime:function(){return this._mediaPlayer.getPosition()},_createEl:function(e){var t=this._mediaPlayer.getHTMLVideoElement();t.setAttribute("class","vjs-tech"),void 0!==e.disablePictureInPicture&&(t.disablePictureInPicture=e.disablePictureInPicture),["preload","poster"].forEach(function(n){e[n]&&t.setAttribute(n,e[n])}.bind(this)),e.playsinline&&(t.setAttribute("webkit-playsinline",""),t.setAttribute("playsinline",""));var n=document.createElement("div");return n.appendChild(t),n},src:function(e){this.trigger(Ut.LOADSTART),this._seeking=!1,this._captionTrack&&(this.textTracks().removeTrack(this._captionTrack),this._captionTrack=null),e&&this._mediaPlayer.load(e)},addEventListener:function(e,t){this._mediaPlayer.addEventListener(e,t)},removeEventListener:function(e,t){this._mediaPlayer.removeEventListener(e,t)},getMediaPlayerAPI:function(){return this._mediaPlayer},supportsFullScreen:function(){return!0},enterFullScreen:function(){var e=this._mediaPlayer.getHTMLVideoElement();(e.requestFullscreen||e.webkitRequestFullscreen||e.mozRequestFullScreen||e.msRequestFullscreen||e.webkitEnterFullscreen||function(){console.error("Fullscreen API is not available")}).call(e)},exitFullScreen:function(){(document.exitFullScreen||document.webkitExitFullscreen||document.mozCancelFullScreen||document.msExitFullscreen||function(){console.error("Exitscreen API is not available")}).call(document)},requestPictureInPicture:function(){return this._mediaPlayer.getHTMLVideoElement().requestPictureInPicture()},setDisablePictureInPicture:function(e){this._mediaPlayer.getHTMLVideoElement().disablePictureInPicture=e},disablePictureInPicture:function(){return this._mediaPlayer.getHTMLVideoElement().disablePictureInPicture},_onCaptionEvent:function(e){if("text/json"===e.type){var t=JSON.parse(e.data);if(t.caption){var n=t.caption;this._captionTrack||(this._captionTrack=this.addTextTrack("captions",n.format),this._currentCue=null),this._currentCue&&this._captionTrack.removeCue(this._currentCue);var r=this._mediaPlayer.getHTMLVideoElement(),i=window.VTTCue||window.vttjs.VTTCue;i?(this._currentCue=new i(r.currentTime,r.currentTime+2,n.text),this._captionTrack.addCue(this._currentCue)):console.warn("No VTTCue implementation available, caption may not be available")}}},_attachVideojsListeners:function(){this._mediaPlayer.addEventListener(s.READY,function(){this._readyState=jt.HAVE_METADATA,this.trigger(Ut.LOADED_METADATA)}.bind(this)),this._mediaPlayer.addEventListener(s.IDLE,function(){this._readyState=jt.HAVE_NOTHING,this.trigger(Ut.PAUSE)}.bind(this)),this._mediaPlayer.addEventListener(s.PLAYING,function(){this._readyState<=jt.HAVE_CURRENT_DATA&&(this._readyState=jt.HAVE_FUTURE_DATA),this.trigger(Ut.PLAY),this.trigger(Ut.PLAYING)}.bind(this)),this._mediaPlayer.addEventListener(s.ENDED,function(){this._readyState=jt.HAVE_NOTHING,this.trigger(Ut.ENDED)}.bind(this)),this._mediaPlayer.addEventListener(s.BUFFERING,function(){this._readyState=jt.HAVE_CURRENT_DATA}.bind(this)),this._mediaPlayer.addEventListener(o.REBUFFERING,function(){this._readyState=jt.HAVE_CURRENT_DATA,this.trigger(Ut.WAITING)}.bind(this)),this._mediaPlayer.addEventListener(o.TIME_UPDATE,function(){this.trigger(Ut.TIME_UPDATE)}.bind(this)),this._mediaPlayer.addEventListener(o.VOLUME_CHANGED,function(){this.trigger(Ut.VOLUME_CHANGE)}.bind(this)),this._mediaPlayer.addEventListener(o.MUTED_CHANGED,function(){this.trigger(Ut.VOLUME_CHANGE)}.bind(this)),this._mediaPlayer.addEventListener(o.ERROR,function(){this.trigger(Ut.ERROR)}.bind(this)),this._mediaPlayer.addEventListener(o.DURATION_CHANGED,function(){this.trigger(Ut.DURATION_CHANGE)}.bind(this)),this._mediaPlayer.addEventListener(o.SEEK_COMPLETED,function(){this._seeking=!1,this.trigger(Ut.SEEKED)}.bind(this)),this._mediaPlayer.addEventListener(o.PLAYBACK_RATE_CHANGED,function(){this.trigger(Ut.RATE_CHANGE)}.bind(this))}},u=n.getTech("Tech");"function"==typeof n.extend?(a.constructor=function(e,t){this.privateContructor(e,t,(function(e,t,n){u.call(e,t,n)}))},i=n.extend(u,a,Wt)):((i=function(e,t){this.privateContructor(e,t,(function(e,t,n){var r=u.prototype,i=r.featuresProgressEvents,o=r.featuresTimeupdateEvents,s=r.featuresPlaybackRate,a=r.featuresFullscreenResize,c=r.featuresVolumeControl,d=r.featuresMuteControl,l=r.featuresNativeTextTracks;r.featuresProgressEvents=!0,r.featuresTimeupdateEvents=!0,r.featuresPlaybackRate=!0,r.featuresFullscreenResize=!0,r.featuresVolumeControl=!0,r.featuresMuteControl=!0,r.featuresNativeTextTracks=!1,Object.assign(e,new u(t,n)),e.on(Ut.PLAYING,(function(){e.hasStarted_=!0})),e.on(Ut.LOADSTART,(function(){e.hasStarted_=!1})),r.featuresProgressEvents=i,r.featuresTimeupdateEvents=o,r.featuresPlaybackRate=s,r.featuresFullscreenResize=a,r.featuresVolumeControl=c,r.featuresMuteControl=d,r.featuresNativeTextTracks=l}))}).prototype=Object.create(u.prototype),Object.assign(i.prototype,a)),i.supportsFullScreen=function(){return!0},i.isSupported=function(){return-1===(navigator.appVersion||"").toLowerCase().indexOf("rv:11")},i.canPlayType=function(e){return"string"==typeof e&&e.length>0&&(e.indexOf("application/x-mpegURL")>-1?"undefined"!=typeof MediaSource&&MediaSource.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"'):""!==document.createElement("video").canPlayType(e))},i.canPlaySource=function(){return!0},n.registerTech("AmazonIVS",i);var c=n.registerPlugin||n.plugin;c("getIVSEvents",(function(){return{PlayerEventType:o,MetadataEventType:t,PlayerState:s,ErrorType:e}})),c("getIVSPlayer",(function(){return this.tech(!0).getMediaPlayerAPI()}))}}function qt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Kt,Qt,zt=document.currentScript||function(e){for(var t,n=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return qt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?qt(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}([].slice.call(e));!(t=n()).done;){var r=t.value;if(r.src.indexOf("amazon-ivs-videojs-tech.min.js")>-1)return r}}(document.scripts);try{Kt=zt.src.replace(/[\w-.]+\.js$/g,"amazon-ivs-wasmworker.min.js"),Qt=zt.src.replace(/[\w-.]+\.js$/g,"amazon-ivs-wasmworker.min.wasm")}catch(e){throw console.error(e),new Error("Error reading currentScript, expected to be included as script tag")}function Yt(e){var t=arguments[1]?arguments[1]:{},n={};n.wasmWorker=t.wasmWorker||Kt,n.wasmBinary=t.wasmBinary||Qt,t.logLevel&&(n.logLevel=t.logLevel),t.serviceWorker&&(n.serviceWorker=t.serviceWorker),Gt(e,n)}}(),registerIVSTech=r.registerIVSTech}();
