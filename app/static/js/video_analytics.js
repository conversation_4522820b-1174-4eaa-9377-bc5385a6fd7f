class VideoTrackingHandler {
  static TEN_MINUTES_IN_MILLISECONDS = 10 * 60 * 1000;
  static THREE_HOURS_IN_MILLISECONDS = 3 * 60 * 60 * 1000;
  static ONE_YEAR_IN_MILLISECONDS = 365 * 24 * 60 * 60 * 1000;

  constructor(organizationId, assetId, accessToken) {
    this.organizationId = organizationId;
    this.assetId = assetId;
    this.accessToken = accessToken;
    this.duration = 0;
    this.urlToGetAnalytics = null;
    this.urltoStoreAnalytics = null;
    this.simpleDuration = 0;
    this.startTime = 0;
    this.totalDuration = 0;
    this.isVideoPlaying = false;
    this.interval = null;
    this.initializeAnalyticsTracking();
  }
  async initializeAnalyticsTracking() {
    this.setUpSession();
    await this.retrieveUrlsToTrackAnalytics();
    this.duration = await this.getPreviousWatchDuration();
  }

  setUpSession(){
    this.visitorID = this.getCookie("visitorId") || this.generateRandomString(32);
    this.sessionID = this.getCookie("sessionId") || this.generateRandomString(40);
    this.setCookie("visitorId", this.visitorID, VideoTrackingHandler.ONE_YEAR_IN_MILLISECONDS);
    this.setCookie("sessionId", this.sessionID, VideoTrackingHandler.THREE_HOURS_IN_MILLISECONDS);
  }

  getCookie(name) {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.startsWith(name + '=')) {
        return cookie.substring(name.length + 1);
      }
    }
    return null;
  }

  generateRandomString(length) {
    const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let randomString = '';
    for (let i = 0; i < length; i++) {
      randomString += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return randomString;
  }

  setCookie(name, value, expiration) {
    const expiryDate = new Date(Date.now() + expiration);
    document.cookie = `${name}=${value}; path=/; secure; samesite=None; expires=${expiryDate.toUTCString()}`;
  }

  async retrieveUrlsToTrackAnalytics() {
    try {
      const queryParams = new URLSearchParams({
        visitorId: this.visitorID,
        sessionId: this.sessionID,
        access_token: this.accessToken
      });

      const path = `/api/v1/${this.organizationId}/assets/${this.assetId}/presigned_urls/?${queryParams}`;
      const response = await fetch(path, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const responseData = await response.json();
      this.urlToGetAnalytics = responseData.presigned_get_url
      this.urltoStoreAnalytics = responseData.presigned_put_url

    } catch (error) {
      console.error(error);
    }
  }

  async getPreviousWatchDuration() {
    try {
      const response = await fetch(this.urlToGetAnalytics, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const json =  await response.json();
        if (json && json.duration) {
          return json.duration;
        } else {
          return 0;
        }
      }
      return 0;
    } catch (error) {
      console.error("Error fetching JSON data:", error);
      return 0;
    }
  }

  addVideoTracking(player) {
    player.on("play", () => {
      this.startTime = performance.now();
      this.isVideoPlaying = true;
      this.startInterval();
    });

    player.on("pause", () => {
      this.trackAndSendVideoPlayback();
    });

    player.on("waiting", () => {
      this.trackAndSendVideoPlayback();
    });

    player.on("error", () => {
      this.trackAndSendVideoPlayback();
    });

    player.on("ended", () => {
      this.trackVideoPlayDuration();
      this.uploadTrackingData();
      this.resetTracking();
      this.stopInterval();
    });
  }

  startInterval() {
    this.interval=setInterval(() => {
      this.trackVideoPlayDuration();
      this.uploadTrackingData();
    }, VideoTrackingHandler.TEN_MINUTES_IN_MILLISECONDS);
  }

  trackVideoPlayDuration() {
    if (this.isVideoPlaying) {
      const currentTime = performance.now();
      this.totalDuration = (currentTime - this.startTime) / 1000;
      this.startTime = currentTime;
    }
  }

  async uploadTrackingData(maxRetries= 1) {
    this.duration += this.totalDuration;
    const dataToStore = this.prepareTrackingData();
    const jsonData = JSON.stringify(dataToStore);
    await this.uploadDataWithRetries(jsonData, maxRetries);
  }

  prepareTrackingData() {
    return {
      "assetId": this.assetId,
      "organization": this.organizationId,
      "visitor_id": this.visitorID,
      "session_id": this.sessionID,
      "duration": this.duration,
      "user_agent": navigator.userAgent,
      "time_stamp": new Date().toISOString(),
    };
  }

  async uploadDataWithRetries(jsonData, maxRetries) {
    const uploadData = async (retries) => {
      try {
        const response = await fetch(this.urltoStoreAnalytics, {
          method: 'PUT',
          body: jsonData,
          headers: {
            'Content-Type': 'application/json',
          },
        });
        if (response.status === 403 && retries > 0) {
            await this.refreshUploadUrlAndRetry(jsonData, retries - 1);
        }
      } catch (error) {
        console.error('Error:', error);
      }
    };

    await uploadData(maxRetries);
  }

  async refreshUploadUrlAndRetry(jsonData, retries) {
    await this.retrieveUrlsToTrackAnalytics();
    await this.uploadDataWithRetries(jsonData, retries);
  }

  trackAndSendVideoPlayback() {
    if (this.isVideoPlaying) {
      this.trackVideoPlayDuration();
      this.uploadTrackingData();
      this.isVideoPlaying = false;
      this.stopInterval();
    }
  }

  stopInterval() {
    clearInterval(this.interval);
  }

  resetTracking() {
    this.startTime = 0;
    this.totalDuration = 0;
    this.isVideoPlaying = false;
  }
}
