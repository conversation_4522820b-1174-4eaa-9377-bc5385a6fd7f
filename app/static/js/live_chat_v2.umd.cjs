(function(q,N){typeof exports=="object"&&typeof module<"u"?N(exports):typeof define=="function"&&define.amd?define(["exports"],N):(q=typeof globalThis<"u"?globalThis:q||self,N(q.TPStreamsChat={}))})(this,function(q){"use strict";var Di=Object.defineProperty;var Li=(q,N,O)=>N in q?Di(q,N,{enumerable:!0,configurable:!0,writable:!0,value:O}):q[N]=O;var Qt=(q,N,O)=>(Li(q,typeof N!="symbol"?N+"":N,O),O);function N(s,e){for(var t=0;t<e.length;t++){const r=e[t];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in s)){const n=Object.getOwnPropertyDescriptor(r,i);n&&Object.defineProperty(s,i,n.get?n:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(s,Symbol.toStringTag,{value:"Module"}))}function O(){}function ft(s){return s()}function pt(){return Object.create(null)}function te(s){s.forEach(ft)}function mt(s){return typeof s=="function"}function W(s,e){return s!=s?e==e:s!==e||s&&typeof s=="object"||typeof s=="function"}function Xt(s){return Object.keys(s).length===0}function vt(s,...e){if(s==null)return O;const t=s.subscribe(...e);return t.unsubscribe?()=>t.unsubscribe():t}function Yt(s){let e;return vt(s,t=>e=t)(),e}function fe(s,e,t){s.$$.on_destroy.push(vt(e,t))}function b(s,e){s.appendChild(e)}function D(s,e,t){s.insertBefore(e,t||null)}function U(s){s.parentNode&&s.parentNode.removeChild(s)}function Zt(s,e){for(let t=0;t<s.length;t+=1)s[t]&&s[t].d(e)}function w(s){return document.createElement(s)}function Je(s){return document.createElementNS("http://www.w3.org/2000/svg",s)}function pe(s){return document.createTextNode(s)}function F(){return pe(" ")}function er(){return pe("")}function J(s,e,t,r){return s.addEventListener(e,t,r),()=>s.removeEventListener(e,t,r)}function v(s,e,t){t==null?s.removeAttribute(e):s.getAttribute(e)!==t&&s.setAttribute(e,t)}function tr(s){return Array.from(s.childNodes)}function Ge(s,e){e=""+e,s.wholeText!==e&&(s.data=e)}function Ie(s,e){s.value=e??""}function rr(s,e,t,r){t===null?s.style.removeProperty(e):s.style.setProperty(e,t,r?"important":"")}let Te;function Oe(s){Te=s}function Ve(){if(!Te)throw new Error("Function called outside component initialization");return Te}function sr(s){Ve().$$.before_update.push(s)}function _t(s){Ve().$$.on_mount.push(s)}function ir(s){Ve().$$.after_update.push(s)}const me=[],Ke=[],De=[],gt=[],nr=Promise.resolve();let We=!1;function or(){We||(We=!0,nr.then(yt))}function Qe(s){De.push(s)}const Xe=new Set;let ve=0;function yt(){if(ve!==0)return;const s=Te;do{try{for(;ve<me.length;){const e=me[ve];ve++,Oe(e),ar(e.$$)}}catch(e){throw me.length=0,ve=0,e}for(Oe(null),me.length=0,ve=0;Ke.length;)Ke.pop()();for(let e=0;e<De.length;e+=1){const t=De[e];Xe.has(t)||(Xe.add(t),t())}De.length=0}while(me.length);for(;gt.length;)gt.pop()();We=!1,Xe.clear(),Oe(s)}function ar(s){if(s.fragment!==null){s.update(),te(s.before_update);const e=s.dirty;s.dirty=[-1],s.fragment&&s.fragment.p(s.ctx,e),s.after_update.forEach(Qe)}}const Le=new Set;let oe;function Ee(){oe={r:0,c:[],p:oe}}function xe(){oe.r||te(oe.c),oe=oe.p}function A(s,e){s&&s.i&&(Le.delete(s),s.i(e))}function P(s,e,t,r){if(s&&s.o){if(Le.has(s))return;Le.add(s),oe.c.push(()=>{Le.delete(s),r&&(t&&s.d(1),r())}),s.o(e)}else r&&r()}function Q(s){s&&s.c()}function G(s,e,t,r){const{fragment:i,after_update:n}=s.$$;i&&i.m(e,t),r||Qe(()=>{const o=s.$$.on_mount.map(ft).filter(mt);s.$$.on_destroy?s.$$.on_destroy.push(...o):te(o),s.$$.on_mount=[]}),n.forEach(Qe)}function V(s,e){const t=s.$$;t.fragment!==null&&(te(t.on_destroy),t.fragment&&t.fragment.d(e),t.on_destroy=t.fragment=null,t.ctx=[])}function lr(s,e){s.$$.dirty[0]===-1&&(me.push(s),or(),s.$$.dirty.fill(0)),s.$$.dirty[e/31|0]|=1<<e%31}function re(s,e,t,r,i,n,o,a=[-1]){const c=Te;Oe(s);const l=s.$$={fragment:null,ctx:[],props:n,update:O,not_equal:i,bound:pt(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(c?c.$$.context:[])),callbacks:pt(),dirty:a,skip_bound:!1,root:e.target||c.$$.root};o&&o(l.root);let h=!1;if(l.ctx=t?t(s,e.props||{},(u,m,...p)=>{const f=p.length?p[0]:m;return l.ctx&&i(l.ctx[u],l.ctx[u]=f)&&(!l.skip_bound&&l.bound[u]&&l.bound[u](f),h&&lr(s,u)),m}):[],l.update(),h=!0,te(l.before_update),l.fragment=r?r(l.ctx):!1,e.target){if(e.hydrate){const u=tr(e.target);l.fragment&&l.fragment.l(u),u.forEach(U)}else l.fragment&&l.fragment.c();e.intro&&A(s.$$.fragment),G(s,e.target,e.anchor,e.customElement),yt()}Oe(c)}class se{$destroy(){V(this,1),this.$destroy=O}$on(e,t){if(!mt(t))return O;const r=this.$$.callbacks[e]||(this.$$.callbacks[e]=[]);return r.push(t),()=>{const i=r.indexOf(t);i!==-1&&r.splice(i,1)}}$set(e){this.$$set&&!Xt(e)&&(this.$$.skip_bound=!0,this.$$set(e),this.$$.skip_bound=!1)}}const Fi="",_e=[];function bt(s,e=O){let t;const r=new Set;function i(a){if(W(s,a)&&(s=a,t)){const c=!_e.length;for(const l of r)l[1](),_e.push(l,s);if(c){for(let l=0;l<_e.length;l+=2)_e[l][0](_e[l+1]);_e.length=0}}}function n(a){i(a(s))}function o(a,c=O){const l=[a,c];return r.add(l),r.size===1&&(t=e(i)||O),a(s),()=>{r.delete(l),r.size===0&&(t(),t=null)}}return{set:i,update:n,subscribe:o}}var cr=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(h){try{l(r.next(h))}catch(u){o(u)}}function c(h){try{l(r.throw(h))}catch(u){o(u)}}function l(h){h.done?n(h.value):i(h.value).then(a,c)}l((r=r.apply(s,e||[])).next())})};const hr=s=>{let e;return s?e=s:typeof fetch>"u"?e=(...t)=>cr(void 0,void 0,void 0,function*(){return yield(yield Promise.resolve().then(()=>Fe)).fetch(...t)}):e=fetch,(...t)=>e(...t)};class Ye extends Error{constructor(e,t="FunctionsError",r){super(e),super.name=t,this.context=r}}class ur extends Ye{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class dr extends Ye{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class fr extends Ye{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var pr=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(h){try{l(r.next(h))}catch(u){o(u)}}function c(h){try{l(r.throw(h))}catch(u){o(u)}}function l(h){h.done?n(h.value):i(h.value).then(a,c)}l((r=r.apply(s,e||[])).next())})};class mr{constructor(e,{headers:t={},customFetch:r}={}){this.url=e,this.headers=t,this.fetch=hr(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r;return pr(this,void 0,void 0,function*(){try{const{headers:i,body:n}=t;let o={},a;n&&(i&&!Object.prototype.hasOwnProperty.call(i,"Content-Type")||!i)&&(typeof Blob<"u"&&n instanceof Blob||n instanceof ArrayBuffer?(o["Content-Type"]="application/octet-stream",a=n):typeof n=="string"?(o["Content-Type"]="text/plain",a=n):typeof FormData<"u"&&n instanceof FormData?a=n:(o["Content-Type"]="application/json",a=JSON.stringify(n)));const c=yield this.fetch(`${this.url}/${e}`,{method:"POST",headers:Object.assign(Object.assign(Object.assign({},o),this.headers),i),body:a}).catch(m=>{throw new ur(m)}),l=c.headers.get("x-relay-error");if(l&&l==="true")throw new dr(c);if(!c.ok)throw new fr(c);let h=((r=c.headers.get("Content-Type"))!==null&&r!==void 0?r:"text/plain").split(";")[0].trim(),u;return h==="application/json"?u=yield c.json():h==="application/octet-stream"?u=yield c.blob():h==="multipart/form-data"?u=yield c.formData():u=yield c.text(),{data:u,error:null}}catch(i){return{data:null,error:i}}})}}var vr=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function _r(s){return s&&s.__esModule&&Object.prototype.hasOwnProperty.call(s,"default")?s.default:s}var ge={},gr={get exports(){return ge},set exports(s){ge=s}};(function(s,e){var t=typeof self<"u"?self:vr,r=function(){function n(){this.fetch=!1,this.DOMException=t.DOMException}return n.prototype=t,new n}();(function(n){(function(o){var a={searchParams:"URLSearchParams"in n,iterable:"Symbol"in n&&"iterator"in Symbol,blob:"FileReader"in n&&"Blob"in n&&function(){try{return new Blob,!0}catch{return!1}}(),formData:"FormData"in n,arrayBuffer:"ArrayBuffer"in n};function c(d){return d&&DataView.prototype.isPrototypeOf(d)}if(a.arrayBuffer)var l=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],h=ArrayBuffer.isView||function(d){return d&&l.indexOf(Object.prototype.toString.call(d))>-1};function u(d){if(typeof d!="string"&&(d=String(d)),/[^a-z0-9\-#$%&'*+.^_`|~]/i.test(d))throw new TypeError("Invalid character in header field name");return d.toLowerCase()}function m(d){return typeof d!="string"&&(d=String(d)),d}function p(d){var _={next:function(){var $=d.shift();return{done:$===void 0,value:$}}};return a.iterable&&(_[Symbol.iterator]=function(){return _}),_}function f(d){this.map={},d instanceof f?d.forEach(function(_,$){this.append($,_)},this):Array.isArray(d)?d.forEach(function(_){this.append(_[0],_[1])},this):d&&Object.getOwnPropertyNames(d).forEach(function(_){this.append(_,d[_])},this)}f.prototype.append=function(d,_){d=u(d),_=m(_);var $=this.map[d];this.map[d]=$?$+", "+_:_},f.prototype.delete=function(d){delete this.map[u(d)]},f.prototype.get=function(d){return d=u(d),this.has(d)?this.map[d]:null},f.prototype.has=function(d){return this.map.hasOwnProperty(u(d))},f.prototype.set=function(d,_){this.map[u(d)]=m(_)},f.prototype.forEach=function(d,_){for(var $ in this.map)this.map.hasOwnProperty($)&&d.call(_,this.map[$],$,this)},f.prototype.keys=function(){var d=[];return this.forEach(function(_,$){d.push($)}),p(d)},f.prototype.values=function(){var d=[];return this.forEach(function(_){d.push(_)}),p(d)},f.prototype.entries=function(){var d=[];return this.forEach(function(_,$){d.push([$,_])}),p(d)},a.iterable&&(f.prototype[Symbol.iterator]=f.prototype.entries);function y(d){if(d.bodyUsed)return Promise.reject(new TypeError("Already read"));d.bodyUsed=!0}function g(d){return new Promise(function(_,$){d.onload=function(){_(d.result)},d.onerror=function(){$(d.error)}})}function j(d){var _=new FileReader,$=g(_);return _.readAsArrayBuffer(d),$}function H(d){var _=new FileReader,$=g(_);return _.readAsText(d),$}function R(d){for(var _=new Uint8Array(d),$=new Array(_.length),L=0;L<_.length;L++)$[L]=String.fromCharCode(_[L]);return $.join("")}function C(d){if(d.slice)return d.slice(0);var _=new Uint8Array(d.byteLength);return _.set(new Uint8Array(d)),_.buffer}function Z(){return this.bodyUsed=!1,this._initBody=function(d){this._bodyInit=d,d?typeof d=="string"?this._bodyText=d:a.blob&&Blob.prototype.isPrototypeOf(d)?this._bodyBlob=d:a.formData&&FormData.prototype.isPrototypeOf(d)?this._bodyFormData=d:a.searchParams&&URLSearchParams.prototype.isPrototypeOf(d)?this._bodyText=d.toString():a.arrayBuffer&&a.blob&&c(d)?(this._bodyArrayBuffer=C(d.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):a.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(d)||h(d))?this._bodyArrayBuffer=C(d):this._bodyText=d=Object.prototype.toString.call(d):this._bodyText="",this.headers.get("content-type")||(typeof d=="string"?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):a.searchParams&&URLSearchParams.prototype.isPrototypeOf(d)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},a.blob&&(this.blob=function(){var d=y(this);if(d)return d;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){return this._bodyArrayBuffer?y(this)||Promise.resolve(this._bodyArrayBuffer):this.blob().then(j)}),this.text=function(){var d=y(this);if(d)return d;if(this._bodyBlob)return H(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(R(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},a.formData&&(this.formData=function(){return this.text().then(Pi)}),this.json=function(){return this.text().then(JSON.parse)},this}var ze=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function Ri(d){var _=d.toUpperCase();return ze.indexOf(_)>-1?_:d}function de(d,_){_=_||{};var $=_.body;if(d instanceof de){if(d.bodyUsed)throw new TypeError("Already read");this.url=d.url,this.credentials=d.credentials,_.headers||(this.headers=new f(d.headers)),this.method=d.method,this.mode=d.mode,this.signal=d.signal,!$&&d._bodyInit!=null&&($=d._bodyInit,d.bodyUsed=!0)}else this.url=String(d);if(this.credentials=_.credentials||this.credentials||"same-origin",(_.headers||!this.headers)&&(this.headers=new f(_.headers)),this.method=Ri(_.method||this.method||"GET"),this.mode=_.mode||this.mode||null,this.signal=_.signal||this.signal,this.referrer=null,(this.method==="GET"||this.method==="HEAD")&&$)throw new TypeError("Body not allowed for GET or HEAD requests");this._initBody($)}de.prototype.clone=function(){return new de(this,{body:this._bodyInit})};function Pi(d){var _=new FormData;return d.trim().split("&").forEach(function($){if($){var L=$.split("="),I=L.shift().replace(/\+/g," "),E=L.join("=").replace(/\+/g," ");_.append(decodeURIComponent(I),decodeURIComponent(E))}}),_}function Ui(d){var _=new f,$=d.replace(/\r?\n[\t ]+/g," ");return $.split(/\r?\n/).forEach(function(L){var I=L.split(":"),E=I.shift().trim();if(E){var He=I.join(":").trim();_.append(E,He)}}),_}Z.call(de.prototype);function ee(d,_){_||(_={}),this.type="default",this.status=_.status===void 0?200:_.status,this.ok=this.status>=200&&this.status<300,this.statusText="statusText"in _?_.statusText:"OK",this.headers=new f(_.headers),this.url=_.url||"",this._initBody(d)}Z.call(ee.prototype),ee.prototype.clone=function(){return new ee(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new f(this.headers),url:this.url})},ee.error=function(){var d=new ee(null,{status:0,statusText:""});return d.type="error",d};var Ii=[301,302,303,307,308];ee.redirect=function(d,_){if(Ii.indexOf(_)===-1)throw new RangeError("Invalid status code");return new ee(null,{status:_,headers:{location:d}})},o.DOMException=n.DOMException;try{new o.DOMException}catch{o.DOMException=function(_,$){this.message=_,this.name=$;var L=Error(_);this.stack=L.stack},o.DOMException.prototype=Object.create(Error.prototype),o.DOMException.prototype.constructor=o.DOMException}function ut(d,_){return new Promise(function($,L){var I=new de(d,_);if(I.signal&&I.signal.aborted)return L(new o.DOMException("Aborted","AbortError"));var E=new XMLHttpRequest;function He(){E.abort()}E.onload=function(){var Ue={status:E.status,statusText:E.statusText,headers:Ui(E.getAllResponseHeaders()||"")};Ue.url="responseURL"in E?E.responseURL:Ue.headers.get("X-Request-URL");var dt="response"in E?E.response:E.responseText;$(new ee(dt,Ue))},E.onerror=function(){L(new TypeError("Network request failed"))},E.ontimeout=function(){L(new TypeError("Network request failed"))},E.onabort=function(){L(new o.DOMException("Aborted","AbortError"))},E.open(I.method,I.url,!0),I.credentials==="include"?E.withCredentials=!0:I.credentials==="omit"&&(E.withCredentials=!1),"responseType"in E&&a.blob&&(E.responseType="blob"),I.headers.forEach(function(Ue,dt){E.setRequestHeader(dt,Ue)}),I.signal&&(I.signal.addEventListener("abort",He),E.onreadystatechange=function(){E.readyState===4&&I.signal.removeEventListener("abort",He)}),E.send(typeof I._bodyInit>"u"?null:I._bodyInit)})}return ut.polyfill=!0,n.fetch||(n.fetch=ut,n.Headers=f,n.Request=de,n.Response=ee),o.Headers=f,o.Request=de,o.Response=ee,o.fetch=ut,Object.defineProperty(o,"__esModule",{value:!0}),o})({})})(r),r.fetch.ponyfill=!0,delete r.fetch.polyfill;var i=r;e=i.fetch,e.default=i.fetch,e.fetch=i.fetch,e.Headers=i.Headers,e.Request=i.Request,e.Response=i.Response,s.exports=e})(gr,ge);const Ze=_r(ge),Fe=N({__proto__:null,default:Ze},[ge]);class yr{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.allowEmpty=e.allowEmpty,e.fetch?this.fetch=e.fetch:typeof fetch>"u"?this.fetch=Ze:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}then(e,t){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const r=this.fetch;let i=r(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async n=>{var o,a,c;let l=null,h=null,u=null,m=n.status,p=n.statusText;if(n.ok){if(this.method!=="HEAD"){const j=await n.text();j===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?h=j:h=JSON.parse(j))}const y=(o=this.headers.Prefer)===null||o===void 0?void 0:o.match(/count=(exact|planned|estimated)/),g=(a=n.headers.get("content-range"))===null||a===void 0?void 0:a.split("/");y&&g&&g.length>1&&(u=parseInt(g[1]))}else{const y=await n.text();try{l=JSON.parse(y),Array.isArray(l)&&n.status===404&&(h=[],l=null,m=200,p="OK")}catch{n.status===404&&y===""?(m=204,p="No Content"):l={message:y}}if(l&&this.allowEmpty&&(!((c=l==null?void 0:l.details)===null||c===void 0)&&c.includes("Results contain 0 rows"))&&(l=null,m=200,p="OK"),l&&this.shouldThrowOnError)throw l}return{error:l,data:h,count:u,status:m,statusText:p}});return this.shouldThrowOnError||(i=i.catch(n=>({error:{message:`FetchError: ${n.message}`,details:"",hint:"",code:n.code||""},data:null,count:null,status:0,statusText:""}))),i.then(e,t)}}class br extends yr{select(e){let t=!1;const r=(e??"*").split("").map(i=>/\s/.test(i)&&!t?"":(i==='"'&&(t=!t),i)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:i}={}){const n=i?`${i}.order`:"order",o=this.url.searchParams.get(n);return this.url.searchParams.set(n,`${o?`${o},`:""}${e}.${t?"asc":"desc"}${r===void 0?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t}={}){const r=typeof t>"u"?"limit":`${t}.limit`;return this.url.searchParams.set(r,`${e}`),this}range(e,t,{foreignTable:r}={}){const i=typeof r>"u"?"offset":`${r}.offset`,n=typeof r>"u"?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${e}`),this.url.searchParams.set(n,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.headers.Accept="application/vnd.pgrst.object+json",this.allowEmpty=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:i=!1,wal:n=!1,format:o="text"}={}){const a=[e?"analyze":null,t?"verbose":null,r?"settings":null,i?"buffers":null,n?"wal":null].filter(Boolean).join("|"),c=this.headers.Accept;return this.headers.Accept=`application/vnd.pgrst.plan+${o}; for="${c}"; options=${a};`,o==="json"?this:this}rollback(){var e;return((e=this.headers.Prefer)!==null&&e!==void 0?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}class ye extends br{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){const r=t.map(i=>typeof i=="string"&&new RegExp("[,()]").test(i)?`"${i}"`:`${i}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return typeof t=="string"?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return typeof t=="string"?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return typeof t=="string"?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:i}={}){let n="";i==="plain"?n="pl":i==="phrase"?n="ph":i==="websearch"&&(n="w");const o=r===void 0?"":`(${r})`;return this.url.searchParams.append(e,`${n}fts${o}.${t}`),this}match(e){return Object.entries(e).forEach(([t,r])=>{this.url.searchParams.append(t,`eq.${r}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t}={}){const r=t?`${t}.or`:"or";return this.url.searchParams.append(r,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}}class wr{constructor(e,{headers:t={},schema:r,fetch:i}){this.url=e,this.headers=t,this.schema=r,this.fetch=i}select(e,{head:t=!1,count:r}={}){const i=t?"HEAD":"GET";let n=!1;const o=(e??"*").split("").map(a=>/\s/.test(a)&&!n?"":(a==='"'&&(n=!n),a)).join("");return this.url.searchParams.set("select",o),r&&(this.headers.Prefer=`count=${r}`),new ye({method:i,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t}={}){const r="POST",i=[],n=e;if(t&&i.push(`count=${t}`),this.headers.Prefer&&i.unshift(this.headers.Prefer),this.headers.Prefer=i.join(","),Array.isArray(e)){const o=e.reduce((a,c)=>a.concat(Object.keys(c)),[]);if(o.length>0){const a=[...new Set(o)].map(c=>`"${c}"`);this.url.searchParams.set("columns",a.join(","))}}return new ye({method:r,url:this.url,headers:this.headers,schema:this.schema,body:n,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:i}={}){const n="POST",o=[`resolution=${r?"ignore":"merge"}-duplicates`];t!==void 0&&this.url.searchParams.set("on_conflict",t);const a=e;return i&&o.push(`count=${i}`),this.headers.Prefer&&o.unshift(this.headers.Prefer),this.headers.Prefer=o.join(","),new ye({method:n,url:this.url,headers:this.headers,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){const r="PATCH",i=[],n=e;return t&&i.push(`count=${t}`),this.headers.Prefer&&i.unshift(this.headers.Prefer),this.headers.Prefer=i.join(","),new ye({method:r,url:this.url,headers:this.headers,schema:this.schema,body:n,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){const t="DELETE",r=[];return e&&r.push(`count=${e}`),this.headers.Prefer&&r.unshift(this.headers.Prefer),this.headers.Prefer=r.join(","),new ye({method:t,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}const $r={"X-Client-Info":"postgrest-js/1.4.0"};class kr{constructor(e,{headers:t={},schema:r,fetch:i}={}){this.url=e,this.headers=Object.assign(Object.assign({},$r),t),this.schema=r,this.fetch=i}from(e){const t=new URL(`${this.url}/${e}`);return new wr(t,{headers:Object.assign({},this.headers),schema:this.schema,fetch:this.fetch})}rpc(e,t={},{head:r=!1,count:i}={}){let n;const o=new URL(`${this.url}/rpc/${e}`);let a;r?(n="HEAD",Object.entries(t).forEach(([l,h])=>{o.searchParams.append(l,`${h}`)})):(n="POST",a=t);const c=Object.assign({},this.headers);return i&&(c.Prefer=`count=${i}`),new ye({method:n,url:o,headers:c,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}}var et,wt;function Tr(){if(wt)return et;wt=1;var s=function(){if(typeof self=="object"&&self)return self;if(typeof window=="object"&&window)return window;throw new Error("Unable to resolve global `this`")};return et=function(){if(this)return this;if(typeof globalThis=="object"&&globalThis)return globalThis;try{Object.defineProperty(Object.prototype,"__global__",{get:function(){return this},configurable:!0})}catch{return s()}try{return __global__||s()}finally{delete Object.prototype.__global__}}(),et}var Or={name:"websocket",description:"Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.",keywords:["websocket","websockets","socket","networking","comet","push","RFC-6455","realtime","server","client"],author:"Brian McKelvey <<EMAIL>> (https://github.com/theturtle32)",contributors:["Iñaki Baz Castillo <<EMAIL>> (http://dev.sipdoc.net)"],version:"1.0.34",repository:{type:"git",url:"https://github.com/theturtle32/WebSocket-Node.git"},homepage:"https://github.com/theturtle32/WebSocket-Node",engines:{node:">=4.0.0"},dependencies:{bufferutil:"^4.0.1",debug:"^2.2.0","es5-ext":"^0.10.50","typedarray-to-buffer":"^3.1.5","utf-8-validate":"^5.0.2",yaeti:"^0.0.6"},devDependencies:{"buffer-equal":"^1.0.0",gulp:"^4.0.2","gulp-jshint":"^2.0.4","jshint-stylish":"^2.2.1",jshint:"^2.0.0",tape:"^4.9.1"},config:{verbose:!1},scripts:{test:"tape test/unit/*.js",gulp:"gulp"},main:"index",directories:{lib:"./lib"},browser:"lib/browser.js",license:"Apache-2.0"}.version,ae;if(typeof globalThis=="object")ae=globalThis;else try{ae=Tr()}catch{}finally{if(!ae&&typeof window<"u"&&(ae=window),!ae)throw new Error("Could not determine global this")}var Se=ae.WebSocket||ae.MozWebSocket,Er=Or;function $t(s,e){var t;return e?t=new Se(s,e):t=new Se(s),t}Se&&["CONNECTING","OPEN","CLOSING","CLOSED"].forEach(function(s){Object.defineProperty($t,s,{get:function(){return Se[s]}})});var xr={w3cwebsocket:Se?$t:null,version:Er};const Sr={"X-Client-Info":"realtime-js/2.6.0"},jr="1.0.0",kt=1e4,Cr=1e3;var je;(function(s){s[s.connecting=0]="connecting",s[s.open=1]="open",s[s.closing=2]="closing",s[s.closed=3]="closed"})(je||(je={}));var M;(function(s){s.closed="closed",s.errored="errored",s.joined="joined",s.joining="joining",s.leaving="leaving"})(M||(M={}));var K;(function(s){s.close="phx_close",s.error="phx_error",s.join="phx_join",s.reply="phx_reply",s.leave="phx_leave",s.access_token="access_token"})(K||(K={}));var tt;(function(s){s.websocket="websocket"})(tt||(tt={}));var le;(function(s){s.Connecting="connecting",s.Open="open",s.Closing="closing",s.Closed="closed"})(le||(le={}));class Tt{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}class Ar{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t(typeof e=="string"?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){const i=t.getUint8(1),n=t.getUint8(2);let o=this.HEADER_LENGTH+2;const a=r.decode(e.slice(o,o+i));o=o+i;const c=r.decode(e.slice(o,o+n));o=o+n;const l=JSON.parse(r.decode(e.slice(o,e.byteLength)));return{ref:null,topic:a,event:c,payload:l}}}class rt{constructor(e,t,r={},i=kt){this.channel=e,this.event=t,this.payload=r,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null,this.rateLimited=!1}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){if(this._hasReceived("timeout"))return;this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()})==="rate limited"&&(this.rateLimited=!0)}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t((r=this.receivedResp)===null||r===void 0?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const e=t=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=t,this._matchReceive(t)};this.channel._on(this.refEvent,{},e),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(r=>r.status===e).forEach(r=>r.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var Ot;(function(s){s.SYNC="sync",s.JOIN="join",s.LEAVE="leave"})(Ot||(Ot={}));class Ce{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(t==null?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},i=>{const{onJoin:n,onLeave:o,onSync:a}=this.caller;this.joinRef=this.channel._joinRef(),this.state=Ce.syncState(this.state,i,n,o),this.pendingDiffs.forEach(c=>{this.state=Ce.syncDiff(this.state,c,n,o)}),this.pendingDiffs=[],a()}),this.channel._on(r.diff,{},i=>{const{onJoin:n,onLeave:o,onSync:a}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(i):(this.state=Ce.syncDiff(this.state,i,n,o),a())}),this.onJoin((i,n,o)=>{this.channel._trigger("presence",{event:"join",key:i,currentPresences:n,newPresences:o})}),this.onLeave((i,n,o)=>{this.channel._trigger("presence",{event:"leave",key:i,currentPresences:n,leftPresences:o})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,i){const n=this.cloneDeep(e),o=this.transformState(t),a={},c={};return this.map(n,(l,h)=>{o[l]||(c[l]=h)}),this.map(o,(l,h)=>{const u=n[l];if(u){const m=h.map(g=>g.presence_ref),p=u.map(g=>g.presence_ref),f=h.filter(g=>p.indexOf(g.presence_ref)<0),y=u.filter(g=>m.indexOf(g.presence_ref)<0);f.length>0&&(a[l]=f),y.length>0&&(c[l]=y)}else a[l]=h}),this.syncDiff(n,{joins:a,leaves:c},r,i)}static syncDiff(e,t,r,i){const{joins:n,leaves:o}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),i||(i=()=>{}),this.map(n,(a,c)=>{var l;const h=(l=e[a])!==null&&l!==void 0?l:[];if(e[a]=this.cloneDeep(c),h.length>0){const u=e[a].map(p=>p.presence_ref),m=h.filter(p=>u.indexOf(p.presence_ref)<0);e[a].unshift(...m)}r(a,h,c)}),this.map(o,(a,c)=>{let l=e[a];if(!l)return;const h=c.map(u=>u.presence_ref);l=l.filter(u=>h.indexOf(u.presence_ref)<0),e[a]=l,i(a,l,c),l.length===0&&delete e[a]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,r)=>{const i=e[r];return"metas"in i?t[r]=i.metas.map(n=>(n.presence_ref=n.phx_ref,delete n.phx_ref,delete n.phx_ref_prev,n)):t[r]=i,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var x;(function(s){s.abstime="abstime",s.bool="bool",s.date="date",s.daterange="daterange",s.float4="float4",s.float8="float8",s.int2="int2",s.int4="int4",s.int4range="int4range",s.int8="int8",s.int8range="int8range",s.json="json",s.jsonb="jsonb",s.money="money",s.numeric="numeric",s.oid="oid",s.reltime="reltime",s.text="text",s.time="time",s.timestamp="timestamp",s.timestamptz="timestamptz",s.timetz="timetz",s.tsrange="tsrange",s.tstzrange="tstzrange"})(x||(x={}));const Et=(s,e,t={})=>{var r;const i=(r=t.skipTypes)!==null&&r!==void 0?r:[];return Object.keys(e).reduce((n,o)=>(n[o]=Rr(o,s,e,i),n),{})},Rr=(s,e,t,r)=>{const i=e.find(a=>a.name===s),n=i==null?void 0:i.type,o=t[s];return n&&!r.includes(n)?xt(n,o):st(o)},xt=(s,e)=>{if(s.charAt(0)==="_"){const t=s.slice(1,s.length);return Dr(e,t)}switch(s){case x.bool:return Pr(e);case x.float4:case x.float8:case x.int2:case x.int4:case x.int8:case x.numeric:case x.oid:return Ur(e);case x.json:case x.jsonb:return Ir(e);case x.timestamp:return Lr(e);case x.abstime:case x.date:case x.daterange:case x.int4range:case x.int8range:case x.money:case x.reltime:case x.text:case x.time:case x.timestamptz:case x.timetz:case x.tsrange:case x.tstzrange:return st(e);default:return st(e)}},st=s=>s,Pr=s=>{switch(s){case"t":return!0;case"f":return!1;default:return s}},Ur=s=>{if(typeof s=="string"){const e=parseFloat(s);if(!Number.isNaN(e))return e}return s},Ir=s=>{if(typeof s=="string")try{return JSON.parse(s)}catch(e){return console.log(`JSON parse error: ${e}`),s}return s},Dr=(s,e)=>{if(typeof s!="string")return s;const t=s.length-1,r=s[t];if(s[0]==="{"&&r==="}"){let n;const o=s.slice(1,t);try{n=JSON.parse("["+o+"]")}catch{n=o?o.split(","):[]}return n.map(a=>xt(e,a))}return s},Lr=s=>typeof s=="string"?s.replace(" ","T"):s;var St=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(h){try{l(r.next(h))}catch(u){o(u)}}function c(h){try{l(r.throw(h))}catch(u){o(u)}}function l(h){h.done?n(h.value):i(h.value).then(a,c)}l((r=r.apply(s,e||[])).next())})},jt;(function(s){s.ALL="*",s.INSERT="INSERT",s.UPDATE="UPDATE",s.DELETE="DELETE"})(jt||(jt={}));var Ct;(function(s){s.BROADCAST="broadcast",s.PRESENCE="presence",s.POSTGRES_CHANGES="postgres_changes"})(Ct||(Ct={}));var At;(function(s){s.SUBSCRIBED="SUBSCRIBED",s.TIMED_OUT="TIMED_OUT",s.CLOSED="CLOSED",s.CHANNEL_ERROR="CHANNEL_ERROR"})(At||(At={}));class it{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=M.closed,this.joinedOnce=!1,this.pushBuffer=[],this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""}},t.config),this.timeout=this.socket.timeout,this.joinPush=new rt(this,K.join,this.params,this.timeout),this.rejoinTimer=new Tt(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=M.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(i=>i.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=M.closed,this.socket._remove(this)}),this._onError(i=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,i),this.state=M.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=M.errored,this.rejoinTimer.scheduleTimeout())}),this._on(K.reply,{},(i,n)=>{this._trigger(this._replyEventName(n),i)}),this.presence=new Ce(this)}subscribe(e,t=this.timeout){var r,i;if(this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:n,presence:o}}=this.params;this._onError(l=>e&&e("CHANNEL_ERROR",l)),this._onClose(()=>e&&e("CLOSED"));const a={},c={broadcast:n,presence:o,postgres_changes:(i=(r=this.bindings.postgres_changes)===null||r===void 0?void 0:r.map(l=>l.filter))!==null&&i!==void 0?i:[]};this.socket.accessToken&&(a.access_token=this.socket.accessToken),this.updateJoinPayload(Object.assign({config:c},a)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",({postgres_changes:l})=>{var h;if(this.socket.accessToken&&this.socket.setAuth(this.socket.accessToken),l===void 0){e&&e("SUBSCRIBED");return}else{const u=this.bindings.postgres_changes,m=(h=u==null?void 0:u.length)!==null&&h!==void 0?h:0,p=[];for(let f=0;f<m;f++){const y=u[f],{filter:{event:g,schema:j,table:H,filter:R}}=y,C=l&&l[f];if(C&&C.event===g&&C.schema===j&&C.table===H&&C.filter===R)p.push(Object.assign(Object.assign({},y),{id:C.id}));else{this.unsubscribe(),e&&e("CHANNEL_ERROR",new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=p,e&&e("SUBSCRIBED");return}}).receive("error",l=>{e&&e("CHANNEL_ERROR",new Error(JSON.stringify(Object.values(l).join(", ")||"error")))}).receive("timeout",()=>{e&&e("TIMED_OUT")})}return this}presenceState(){return this.presence.state}track(e,t={}){return St(this,void 0,void 0,function*(){return yield this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)})}untrack(e={}){return St(this,void 0,void 0,function*(){return yield this.send({type:"presence",event:"untrack"},e)})}on(e,t,r){return this._on(e,t,r)}send(e,t={}){return new Promise(r=>{var i,n,o;const a=this._push(e.type,e,t.timeout||this.timeout);a.rateLimited&&r("rate limited"),e.type==="broadcast"&&!(!((o=(n=(i=this.params)===null||i===void 0?void 0:i.config)===null||n===void 0?void 0:n.broadcast)===null||o===void 0)&&o.ack)&&r("ok"),a.receive("ok",()=>r("ok")),a.receive("timeout",()=>r("timed out"))})}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=M.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(K.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(r=>{const i=new rt(this,K.leave,{},e);i.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),i.send(),this._canPush()||i.trigger("ok",{})})}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let i=new rt(this,e,t,r);return this._canPush()?i.send():(i.startTimeout(),this.pushBuffer.push(i)),i}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var i,n;const o=e.toLocaleLowerCase(),{close:a,error:c,leave:l,join:h}=K;if(r&&[a,c,l,h].indexOf(o)>=0&&r!==this._joinRef())return;let m=this._onMessage(o,t,r);if(t&&!m)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?(i=this.bindings.postgres_changes)===null||i===void 0||i.filter(p=>{var f,y,g;return((f=p.filter)===null||f===void 0?void 0:f.event)==="*"||((g=(y=p.filter)===null||y===void 0?void 0:y.event)===null||g===void 0?void 0:g.toLocaleLowerCase())===o}).map(p=>p.callback(m,r)):(n=this.bindings[o])===null||n===void 0||n.filter(p=>{var f,y,g,j,H,R;if(["broadcast","presence","postgres_changes"].includes(o))if("id"in p){const C=p.id,Z=(f=p.filter)===null||f===void 0?void 0:f.event;return C&&((y=t.ids)===null||y===void 0?void 0:y.includes(C))&&(Z==="*"||(Z==null?void 0:Z.toLocaleLowerCase())===((g=t.data)===null||g===void 0?void 0:g.type.toLocaleLowerCase()))}else{const C=(H=(j=p==null?void 0:p.filter)===null||j===void 0?void 0:j.event)===null||H===void 0?void 0:H.toLocaleLowerCase();return C==="*"||C===((R=t==null?void 0:t.event)===null||R===void 0?void 0:R.toLocaleLowerCase())}else return p.type.toLocaleLowerCase()===o}).map(p=>{if(typeof m=="object"&&"ids"in m){const f=m.data,{schema:y,table:g,commit_timestamp:j,type:H,errors:R}=f;m=Object.assign(Object.assign({},{schema:y,table:g,commit_timestamp:j,eventType:H,new:{},old:{},errors:R}),this._getPayloadRecords(f))}p.callback(m,r)})}_isClosed(){return this.state===M.closed}_isJoined(){return this.state===M.joined}_isJoining(){return this.state===M.joining}_isLeaving(){return this.state===M.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){const i=e.toLocaleLowerCase(),n={type:i,filter:t,callback:r};return this.bindings[i]?this.bindings[i].push(n):this.bindings[i]=[n],this}_off(e,t){const r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(i=>{var n;return!(((n=i.type)===null||n===void 0?void 0:n.toLocaleLowerCase())===r&&it.isEqual(i.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(K.close,{},e)}_onError(e){this._on(K.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=M.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return(e.type==="INSERT"||e.type==="UPDATE")&&(t.new=Et(e.columns,e.record)),(e.type==="UPDATE"||e.type==="DELETE")&&(t.old=Et(e.columns,e.old_record)),t}}var Fr=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(h){try{l(r.next(h))}catch(u){o(u)}}function c(h){try{l(r.throw(h))}catch(u){o(u)}}function l(h){h.done?n(h.value):i(h.value).then(a,c)}l((r=r.apply(s,e||[])).next())})};const Br=()=>{};class Nr{constructor(e,t){var r;this.accessToken=null,this.channels=[],this.endPoint="",this.headers=Sr,this.params={},this.timeout=kt,this.transport=xr.w3cwebsocket,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=Br,this.conn=null,this.sendBuffer=[],this.serializer=new Ar,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.eventsPerSecondLimitMs=100,this.inThrottle=!1,this.endPoint=`${e}/${tt.websocket}`,t!=null&&t.params&&(this.params=t.params),t!=null&&t.headers&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),t!=null&&t.timeout&&(this.timeout=t.timeout),t!=null&&t.logger&&(this.logger=t.logger),t!=null&&t.transport&&(this.transport=t.transport),t!=null&&t.heartbeatIntervalMs&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const i=(r=t==null?void 0:t.params)===null||r===void 0?void 0:r.eventsPerSecond;i&&(this.eventsPerSecondLimitMs=Math.floor(1e3/i)),this.reconnectAfterMs=t!=null&&t.reconnectAfterMs?t.reconnectAfterMs:n=>[1e3,2e3,5e3,1e4][n-1]||1e4,this.encode=t!=null&&t.encode?t.encode:(n,o)=>o(JSON.stringify(n)),this.decode=t!=null&&t.decode?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new Tt(()=>Fr(this,void 0,void 0,function*(){this.disconnect(),this.connect()}),this.reconnectAfterMs)}connect(){this.conn||(this.conn=new this.transport(this._endPointURL(),[],null,this.headers),this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e)))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,t??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}removeChannel(e){return e.unsubscribe().then(t=>(this.channels.length===0&&this.disconnect(),t))}removeAllChannels(){return Promise.all(this.channels.map(e=>e.unsubscribe())).then(e=>(this.disconnect(),e))}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case je.connecting:return le.Connecting;case je.open:return le.Open;case je.closing:return le.Closing;default:return le.Closed}}isConnected(){return this.connectionState()===le.Open}channel(e,t={config:{}}){this.isConnected()||this.connect();const r=new it(`realtime:${e}`,t,this);return this.channels.push(r),r}push(e){const{topic:t,event:r,payload:i,ref:n}=e;let o=()=>{this.encode(e,a=>{var c;(c=this.conn)===null||c===void 0||c.send(a)})};if(this.log("push",`${t} ${r} (${n})`,i),this.isConnected())if(["broadcast","presence","postgres_changes"].includes(r)){if(this._throttle(o)())return"rate limited"}else o();else this.sendBuffer.push(o)}setAuth(e){this.accessToken=e,this.channels.forEach(t=>{e&&t.updateJoinPayload({access_token:e}),t.joinedOnce&&t._isJoined()&&t._push(K.access_token,{access_token:e})})}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(r=>r.topic===e&&(r._isJoined()||r._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t._joinRef()!==e._joinRef())}_endPointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:jr}))}_onConnMessage(e){this.decode(e.data,t=>{let{topic:r,event:i,payload:n,ref:o}=t;(o&&o===this.pendingHeartbeatRef||i===(n==null?void 0:n.type))&&(this.pendingHeartbeatRef=null),this.log("receive",`${n.status||""} ${r} ${i} ${o&&"("+o+")"||""}`,n),this.channels.filter(a=>a._isMember(r)).forEach(a=>a._trigger(i,n,o)),this.stateChangeCallbacks.message.forEach(a=>a(t))})}_onConnOpen(){this.log("transport",`connected to ${this._endPointURL()}`),this._flushSendBuffer(),this.reconnectTimer.reset(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this._sendHeartbeat(),this.heartbeatIntervalMs),this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(K.error))}_appendParams(e,t){if(Object.keys(t).length===0)return e;const r=e.match(/\?/)?"&":"?",i=new URLSearchParams(t);return`${e}${r}${i}`}_flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),(e=this.conn)===null||e===void 0||e.close(Cr,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth(this.accessToken)}}_throttle(e,t=this.eventsPerSecondLimitMs){return()=>this.inThrottle?!0:(e(),t>0&&(this.inThrottle=!0,setTimeout(()=>{this.inThrottle=!1},t)),!1)}}class Rt extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function B(s){return typeof s=="object"&&s!==null&&"__isStorageError"in s}class Mr extends Rt{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class qr extends Rt{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var Pt=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(h){try{l(r.next(h))}catch(u){o(u)}}function c(h){try{l(r.throw(h))}catch(u){o(u)}}function l(h){h.done?n(h.value):i(h.value).then(a,c)}l((r=r.apply(s,e||[])).next())})};const Ut=s=>{let e;return s?e=s:typeof fetch>"u"?e=(...t)=>Pt(void 0,void 0,void 0,function*(){return yield(yield Promise.resolve().then(()=>Fe)).fetch(...t)}):e=fetch,(...t)=>e(...t)},zr=()=>Pt(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield Promise.resolve().then(()=>Fe)).Response:Response});var be=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(h){try{l(r.next(h))}catch(u){o(u)}}function c(h){try{l(r.throw(h))}catch(u){o(u)}}function l(h){h.done?n(h.value):i(h.value).then(a,c)}l((r=r.apply(s,e||[])).next())})};const It=s=>s.msg||s.message||s.error_description||s.error||JSON.stringify(s),Hr=(s,e)=>be(void 0,void 0,void 0,function*(){const t=yield zr();s instanceof t?s.json().then(r=>{e(new Mr(It(r),s.status||500))}):e(new qr(It(s),s))}),Jr=(s,e,t,r)=>{const i={method:s,headers:(e==null?void 0:e.headers)||{}};return s==="GET"?i:(i.headers=Object.assign({"Content-Type":"application/json"},e==null?void 0:e.headers),i.body=JSON.stringify(r),Object.assign(Object.assign({},i),t))};function Be(s,e,t,r,i,n){return be(this,void 0,void 0,function*(){return new Promise((o,a)=>{s(t,Jr(e,r,i,n)).then(c=>{if(!c.ok)throw c;return r!=null&&r.noResolveJson?c:c.json()}).then(c=>o(c)).catch(c=>Hr(c,a))})})}function nt(s,e,t,r){return be(this,void 0,void 0,function*(){return Be(s,"GET",e,t,r)})}function ce(s,e,t,r,i){return be(this,void 0,void 0,function*(){return Be(s,"POST",e,r,i,t)})}function Gr(s,e,t,r,i){return be(this,void 0,void 0,function*(){return Be(s,"PUT",e,r,i,t)})}function Dt(s,e,t,r,i){return be(this,void 0,void 0,function*(){return Be(s,"DELETE",e,r,i,t)})}var X=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(h){try{l(r.next(h))}catch(u){o(u)}}function c(h){try{l(r.throw(h))}catch(u){o(u)}}function l(h){h.done?n(h.value):i(h.value).then(a,c)}l((r=r.apply(s,e||[])).next())})};const Vr={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Kr={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class Wr{constructor(e,t={},r,i){this.url=e,this.headers=t,this.bucketId=r,this.fetch=Ut(i)}uploadOrUpdate(e,t,r,i){return X(this,void 0,void 0,function*(){try{let n;const o=Object.assign(Object.assign({},Kr),i),a=Object.assign(Object.assign({},this.headers),e==="POST"&&{"x-upsert":String(o.upsert)});typeof Blob<"u"&&r instanceof Blob?(n=new FormData,n.append("cacheControl",o.cacheControl),n.append("",r)):typeof FormData<"u"&&r instanceof FormData?(n=r,n.append("cacheControl",o.cacheControl)):(n=r,a["cache-control"]=`max-age=${o.cacheControl}`,a["content-type"]=o.contentType);const c=this._removeEmptyFolders(t),l=this._getFinalPath(c),h=yield this.fetch(`${this.url}/object/${l}`,{method:e,body:n,headers:a});return h.ok?{data:{path:c},error:null}:{data:null,error:yield h.json()}}catch(n){if(B(n))return{data:null,error:n};throw n}})}upload(e,t,r){return X(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}update(e,t,r){return X(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t){return X(this,void 0,void 0,function*(){try{return{data:yield ce(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t},{headers:this.headers}),error:null}}catch(r){if(B(r))return{data:null,error:r};throw r}})}copy(e,t){return X(this,void 0,void 0,function*(){try{return{data:{path:(yield ce(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t},{headers:this.headers})).Key},error:null}}catch(r){if(B(r))return{data:null,error:r};throw r}})}createSignedUrl(e,t,r){return X(this,void 0,void 0,function*(){try{let i=this._getFinalPath(e),n=yield ce(this.fetch,`${this.url}/object/sign/${i}`,Object.assign({expiresIn:t},r!=null&&r.transform?{transform:r.transform}:{}),{headers:this.headers});const o=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return n={signedUrl:encodeURI(`${this.url}${n.signedURL}${o}`)},{data:n,error:null}}catch(i){if(B(i))return{data:null,error:i};throw i}})}createSignedUrls(e,t,r){return X(this,void 0,void 0,function*(){try{const i=yield ce(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),n=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return{data:i.map(o=>Object.assign(Object.assign({},o),{signedUrl:o.signedURL?encodeURI(`${this.url}${o.signedURL}${n}`):null})),error:null}}catch(i){if(B(i))return{data:null,error:i};throw i}})}download(e,t){return X(this,void 0,void 0,function*(){const i=typeof(t==null?void 0:t.transform)<"u"?"render/image/authenticated":"object",n=this.transformOptsToQueryString((t==null?void 0:t.transform)||{}),o=n?`?${n}`:"";try{const a=this._getFinalPath(e);return{data:yield(yield nt(this.fetch,`${this.url}/${i}/${a}${o}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(a){if(B(a))return{data:null,error:a};throw a}})}getPublicUrl(e,t){const r=this._getFinalPath(e),i=[],n=t!=null&&t.download?`download=${t.download===!0?"":t.download}`:"";n!==""&&i.push(n);const a=typeof(t==null?void 0:t.transform)<"u"?"render/image":"object",c=this.transformOptsToQueryString((t==null?void 0:t.transform)||{});c!==""&&i.push(c);let l=i.join("&");return l!==""&&(l=`?${l}`),{data:{publicUrl:encodeURI(`${this.url}/${a}/public/${r}${l}`)}}}remove(e){return X(this,void 0,void 0,function*(){try{return{data:yield Dt(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(B(t))return{data:null,error:t};throw t}})}list(e,t,r){return X(this,void 0,void 0,function*(){try{const i=Object.assign(Object.assign(Object.assign({},Vr),t),{prefix:e||""});return{data:yield ce(this.fetch,`${this.url}/object/list/${this.bucketId}`,i,{headers:this.headers},r),error:null}}catch(i){if(B(i))return{data:null,error:i};throw i}})}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const Qr={"X-Client-Info":"storage-js/2.3.0"};var we=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(h){try{l(r.next(h))}catch(u){o(u)}}function c(h){try{l(r.throw(h))}catch(u){o(u)}}function l(h){h.done?n(h.value):i(h.value).then(a,c)}l((r=r.apply(s,e||[])).next())})};class Xr{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},Qr),t),this.fetch=Ut(r)}listBuckets(){return we(this,void 0,void 0,function*(){try{return{data:yield nt(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(B(e))return{data:null,error:e};throw e}})}getBucket(e){return we(this,void 0,void 0,function*(){try{return{data:yield nt(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(B(t))return{data:null,error:t};throw t}})}createBucket(e,t={public:!1}){return we(this,void 0,void 0,function*(){try{return{data:yield ce(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public},{headers:this.headers}),error:null}}catch(r){if(B(r))return{data:null,error:r};throw r}})}updateBucket(e,t){return we(this,void 0,void 0,function*(){try{return{data:yield Gr(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public},{headers:this.headers}),error:null}}catch(r){if(B(r))return{data:null,error:r};throw r}})}emptyBucket(e){return we(this,void 0,void 0,function*(){try{return{data:yield ce(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(B(t))return{data:null,error:t};throw t}})}deleteBucket(e){return we(this,void 0,void 0,function*(){try{return{data:yield Dt(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(B(t))return{data:null,error:t};throw t}})}}class Yr extends Xr{constructor(e,t={},r){super(e,t,r)}from(e){return new Wr(this.url,this.headers,e,this.fetch)}}const Zr={"X-Client-Info":"supabase-js/2.7.1"};var es=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(h){try{l(r.next(h))}catch(u){o(u)}}function c(h){try{l(r.throw(h))}catch(u){o(u)}}function l(h){h.done?n(h.value):i(h.value).then(a,c)}l((r=r.apply(s,e||[])).next())})};const ts=s=>{let e;return s?e=s:typeof fetch>"u"?e=Ze:e=fetch,(...t)=>e(...t)},rs=()=>typeof Headers>"u"?ge.Headers:Headers,ss=(s,e,t)=>{const r=ts(t),i=rs();return(n,o)=>es(void 0,void 0,void 0,function*(){var a;const c=(a=yield e())!==null&&a!==void 0?a:s;let l=new i(o==null?void 0:o.headers);return l.has("apikey")||l.set("apikey",s),l.has("Authorization")||l.set("Authorization",`Bearer ${c}`),r(n,Object.assign(Object.assign({},o),{headers:l}))})};function is(s){return s.replace(/\/$/,"")}function ns(s,e){const{db:t,auth:r,realtime:i,global:n}=s,{db:o,auth:a,realtime:c,global:l}=e;return{db:Object.assign(Object.assign({},o),t),auth:Object.assign(Object.assign({},a),r),realtime:Object.assign(Object.assign({},c),i),global:Object.assign(Object.assign({},l),n)}}var Ae=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(h){try{l(r.next(h))}catch(u){o(u)}}function c(h){try{l(r.throw(h))}catch(u){o(u)}}function l(h){h.done?n(h.value):i(h.value).then(a,c)}l((r=r.apply(s,e||[])).next())})};function os(s){return Math.round(Date.now()/1e3)+s}function as(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(s){const e=Math.random()*16|0;return(s=="x"?e:e&3|8).toString(16)})}const ie=()=>typeof document<"u";function z(s,e){var t;e||(e=((t=window==null?void 0:window.location)===null||t===void 0?void 0:t.href)||""),s=s.replace(/[\[\]]/g,"\\$&");const r=new RegExp("[?&#]"+s+"(=([^&#]*)|&|#|$)"),i=r.exec(e);return i?i[2]?decodeURIComponent(i[2].replace(/\+/g," ")):"":null}const Lt=s=>{let e;return s?e=s:typeof fetch>"u"?e=(...t)=>Ae(void 0,void 0,void 0,function*(){return yield(yield Promise.resolve().then(()=>Fe)).fetch(...t)}):e=fetch,(...t)=>e(...t)},ls=s=>typeof s=="object"&&s!==null&&"status"in s&&"ok"in s&&"json"in s&&typeof s.json=="function",cs=(s,e,t)=>Ae(void 0,void 0,void 0,function*(){yield s.setItem(e,JSON.stringify(t))}),Ft=(s,e)=>Ae(void 0,void 0,void 0,function*(){const t=yield s.getItem(e);if(!t)return null;try{return JSON.parse(t)}catch{return t}}),hs=(s,e)=>Ae(void 0,void 0,void 0,function*(){yield s.removeItem(e)});function us(s){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";let t="",r,i,n,o,a,c,l,h=0;for(s=s.replace("-","+").replace("_","/");h<s.length;)o=e.indexOf(s.charAt(h++)),a=e.indexOf(s.charAt(h++)),c=e.indexOf(s.charAt(h++)),l=e.indexOf(s.charAt(h++)),r=o<<2|a>>4,i=(a&15)<<4|c>>2,n=(c&3)<<6|l,t=t+String.fromCharCode(r),c!=64&&i!=0&&(t=t+String.fromCharCode(i)),l!=64&&n!=0&&(t=t+String.fromCharCode(n));return t}class Ne{constructor(){this.promise=new Ne.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}Ne.promiseConstructor=Promise;function Bt(s){const e=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}=?$|[a-z0-9_-]{2}(==)?$)$/i,t=s.split(".");if(t.length!==3)throw new Error("JWT is not valid: not a JWT structure");if(!e.test(t[1]))throw new Error("JWT is not valid: payload is not in base64url format");const r=t[1];return JSON.parse(us(r))}function ds(s){return new Promise(e=>{setTimeout(()=>e(null),s)})}function fs(s,e){return new Promise((r,i)=>{Ae(this,void 0,void 0,function*(){for(let n=0;n<1/0;n++)try{const o=yield s(n);if(!e(n,null,o)){r(o);return}}catch(o){if(!e(n,o)){i(o);return}}})})}class ot extends Error{constructor(e,t){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t}}function T(s){return typeof s=="object"&&s!==null&&"__isAuthError"in s}class ps extends ot{constructor(e,t){super(e,t),this.name="AuthApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}function ms(s){return T(s)&&s.name==="AuthApiError"}class Nt extends ot{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class Me extends ot{constructor(e,t,r){super(e),this.name=t,this.status=r}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class Re extends Me{constructor(){super("Auth session missing!","AuthSessionMissingError",400)}}class at extends Me{constructor(e){super(e,"AuthInvalidCredentialsError",400)}}class ne extends Me{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class lt extends Me{constructor(e,t){super(e,"AuthRetryableFetchError",t)}}var ct=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(h){try{l(r.next(h))}catch(u){o(u)}}function c(h){try{l(r.throw(h))}catch(u){o(u)}}function l(h){h.done?n(h.value):i(h.value).then(a,c)}l((r=r.apply(s,e||[])).next())})},vs=globalThis&&globalThis.__rest||function(s,e){var t={};for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&e.indexOf(r)<0&&(t[r]=s[r]);if(s!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(s);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(s,r[i])&&(t[r[i]]=s[r[i]]);return t};const qe=s=>s.msg||s.message||s.error_description||s.error||JSON.stringify(s),_s=(s,e)=>ct(void 0,void 0,void 0,function*(){const t=[502,503,504];ls(s)?t.includes(s.status)?e(new lt(qe(s),s.status)):s.json().then(r=>{e(new ps(qe(r),s.status||500))}).catch(r=>{e(new Nt(qe(r),r))}):e(new lt(qe(s),0))}),gs=(s,e,t,r)=>{const i={method:s,headers:(e==null?void 0:e.headers)||{}};return s==="GET"?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},e==null?void 0:e.headers),i.body=JSON.stringify(r),Object.assign(Object.assign({},i),t))};function S(s,e,t,r){var i;return ct(this,void 0,void 0,function*(){const n=Object.assign({},r==null?void 0:r.headers);r!=null&&r.jwt&&(n.Authorization=`Bearer ${r.jwt}`);const o=(i=r==null?void 0:r.query)!==null&&i!==void 0?i:{};r!=null&&r.redirectTo&&(o.redirect_to=r.redirectTo);const a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",c=yield ys(s,e,t+a,{headers:n,noResolveJson:r==null?void 0:r.noResolveJson},{},r==null?void 0:r.body);return r!=null&&r.xform?r==null?void 0:r.xform(c):{data:Object.assign({},c),error:null}})}function ys(s,e,t,r,i,n){return ct(this,void 0,void 0,function*(){return new Promise((o,a)=>{s(t,gs(e,r,i,n)).then(c=>{if(!c.ok)throw c;return r!=null&&r.noResolveJson?c:c.json()}).then(c=>o(c)).catch(c=>_s(c,a))})})}function $e(s){var e;let t=null;ks(s)&&(t=Object.assign({},s),t.expires_at=os(s.expires_in));const r=(e=s.user)!==null&&e!==void 0?e:s;return{data:{session:t,user:r},error:null}}function he(s){var e;return{data:{user:(e=s.user)!==null&&e!==void 0?e:s},error:null}}function bs(s){return{data:s,error:null}}function ws(s){const{action_link:e,email_otp:t,hashed_token:r,redirect_to:i,verification_type:n}=s,o=vs(s,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),a={action_link:e,email_otp:t,hashed_token:r,redirect_to:i,verification_type:n},c=Object.assign({},o);return{data:{properties:a,user:c},error:null}}function $s(s){return s}function ks(s){return s.access_token&&s.refresh_token&&s.expires_in}var Y=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(h){try{l(r.next(h))}catch(u){o(u)}}function c(h){try{l(r.throw(h))}catch(u){o(u)}}function l(h){h.done?n(h.value):i(h.value).then(a,c)}l((r=r.apply(s,e||[])).next())})},Ts=globalThis&&globalThis.__rest||function(s,e){var t={};for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&e.indexOf(r)<0&&(t[r]=s[r]);if(s!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(s);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(s,r[i])&&(t[r[i]]=s[r[i]]);return t};class Os{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=Lt(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}signOut(e){return Y(this,void 0,void 0,function*(){try{return yield S(this.fetch,"POST",`${this.url}/logout`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(t){if(T(t))return{data:null,error:t};throw t}})}inviteUserByEmail(e,t={}){return Y(this,void 0,void 0,function*(){try{return yield S(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:he})}catch(r){if(T(r))return{data:{user:null},error:r};throw r}})}generateLink(e){return Y(this,void 0,void 0,function*(){try{const{options:t}=e,r=Ts(e,["options"]),i=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(i.new_email=r==null?void 0:r.newEmail,delete i.newEmail),yield S(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:ws,redirectTo:t==null?void 0:t.redirectTo})}catch(t){if(T(t))return{data:{properties:null,user:null},error:t};throw t}})}createUser(e){return Y(this,void 0,void 0,function*(){try{return yield S(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:he})}catch(t){if(T(t))return{data:{user:null},error:t};throw t}})}listUsers(e){var t,r,i,n,o,a,c;return Y(this,void 0,void 0,function*(){try{const l={nextPage:null,lastPage:0,total:0},h=yield S(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(r=(t=e==null?void 0:e.page)===null||t===void 0?void 0:t.toString())!==null&&r!==void 0?r:"",per_page:(n=(i=e==null?void 0:e.perPage)===null||i===void 0?void 0:i.toString())!==null&&n!==void 0?n:""},xform:$s});if(h.error)throw h.error;const u=yield h.json(),m=(o=h.headers.get("x-total-count"))!==null&&o!==void 0?o:0,p=(c=(a=h.headers.get("link"))===null||a===void 0?void 0:a.split(","))!==null&&c!==void 0?c:[];return p.length>0&&(p.forEach(f=>{const y=parseInt(f.split(";")[0].split("=")[1].substring(0,1)),g=JSON.parse(f.split(";")[1].split("=")[1]);l[`${g}Page`]=y}),l.total=parseInt(m)),{data:Object.assign(Object.assign({},u),l),error:null}}catch(l){if(T(l))return{data:{users:[]},error:l};throw l}})}getUserById(e){return Y(this,void 0,void 0,function*(){try{return yield S(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:he})}catch(t){if(T(t))return{data:{user:null},error:t};throw t}})}updateUserById(e,t){return Y(this,void 0,void 0,function*(){try{return yield S(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:he})}catch(r){if(T(r))return{data:{user:null},error:r};throw r}})}deleteUser(e,t=!1){return Y(this,void 0,void 0,function*(){try{return yield S(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:he})}catch(r){if(T(r))return{data:{user:null},error:r};throw r}})}_listFactors(e){return Y(this,void 0,void 0,function*(){try{const{data:t,error:r}=yield S(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:i=>({data:{factors:i},error:null})});return{data:t,error:r}}catch(t){if(T(t))return{data:null,error:t};throw t}})}_deleteFactor(e){return Y(this,void 0,void 0,function*(){try{return{data:yield S(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(T(t))return{data:null,error:t};throw t}})}}const Es="2.11.0",xs="http://localhost:9999",Ss="supabase.auth.token",js={"X-Client-Info":`gotrue-js/${Es}`},Cs=10,As={getItem:s=>ie()?globalThis.localStorage.getItem(s):null,setItem:(s,e)=>{ie()&&globalThis.localStorage.setItem(s,e)},removeItem:s=>{ie()&&globalThis.localStorage.removeItem(s)}};function Rs(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}var k=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(h){try{l(r.next(h))}catch(u){o(u)}}function c(h){try{l(r.throw(h))}catch(u){o(u)}}function l(h){h.done?n(h.value):i(h.value).then(a,c)}l((r=r.apply(s,e||[])).next())})};Rs();const Ps={url:xs,storageKey:Ss,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:js},ht=10*1e3,Us=3;class Is{constructor(e){this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.broadcastChannel=null;const t=Object.assign(Object.assign({},Ps),e);this.inMemorySession=null,this.storageKey=t.storageKey,this.autoRefreshToken=t.autoRefreshToken,this.persistSession=t.persistSession,this.storage=t.storage||As,this.admin=new Os({url:t.url,headers:t.headers,fetch:t.fetch}),this.url=t.url,this.headers=t.headers,this.fetch=Lt(t.fetch),this.detectSessionInUrl=t.detectSessionInUrl,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},ie()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey&&(this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey),this.broadcastChannel.addEventListener("message",r=>{this._notifyAllSubscribers(r.data.event,r.data.session,!1)})),this.initialize()}initialize(){return this.initializePromise||(this.initializePromise=this._initialize()),this.initializePromise}_initialize(){return k(this,void 0,void 0,function*(){if(this.initializePromise)return this.initializePromise;try{if(this.detectSessionInUrl&&this._isImplicitGrantFlow()){const{data:e,error:t}=yield this._getSessionFromUrl();if(t)return yield this._removeSession(),{error:t};const{session:r,redirectType:i}=e;return yield this._saveSession(r),this._notifyAllSubscribers("SIGNED_IN",r),i==="recovery"&&this._notifyAllSubscribers("PASSWORD_RECOVERY",r),{error:null}}return yield this._recoverAndRefresh(),{error:null}}catch(e){return T(e)?{error:e}:{error:new Nt("Unexpected error during initialization",e)}}finally{yield this._handleVisibilityChange()}})}signUp(e){var t,r;return k(this,void 0,void 0,function*(){try{yield this._removeSession();let i;if("email"in e){const{email:l,password:h,options:u}=e;i=yield S(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:u==null?void 0:u.emailRedirectTo,body:{email:l,password:h,data:(t=u==null?void 0:u.data)!==null&&t!==void 0?t:{},gotrue_meta_security:{captcha_token:u==null?void 0:u.captchaToken}},xform:$e})}else if("phone"in e){const{phone:l,password:h,options:u}=e;i=yield S(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:l,password:h,data:(r=u==null?void 0:u.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:u==null?void 0:u.captchaToken}},xform:$e})}else throw new at("You must provide either an email or phone number and a password");const{data:n,error:o}=i;if(o||!n)return{data:{user:null,session:null},error:o};const a=n.session,c=n.user;return n.session&&(yield this._saveSession(n.session),this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:c,session:a},error:null}}catch(i){if(T(i))return{data:{user:null,session:null},error:i};throw i}})}signInWithPassword(e){var t,r;return k(this,void 0,void 0,function*(){try{yield this._removeSession();let i;if("email"in e){const{email:a,password:c,options:l}=e;i=yield S(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:a,password:c,data:(t=l==null?void 0:l.data)!==null&&t!==void 0?t:{},gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken}},xform:$e})}else if("phone"in e){const{phone:a,password:c,options:l}=e;i=yield S(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:a,password:c,data:(r=l==null?void 0:l.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken}},xform:$e})}else throw new at("You must provide either an email or phone number and a password");const{data:n,error:o}=i;return o||!n?{data:{user:null,session:null},error:o}:(n.session&&(yield this._saveSession(n.session),this._notifyAllSubscribers("SIGNED_IN",n.session)),{data:n,error:o})}catch(i){if(T(i))return{data:{user:null,session:null},error:i};throw i}})}signInWithOAuth(e){var t,r,i,n;return k(this,void 0,void 0,function*(){return yield this._removeSession(),this._handleProviderSignIn(e.provider,{redirectTo:(t=e.options)===null||t===void 0?void 0:t.redirectTo,scopes:(r=e.options)===null||r===void 0?void 0:r.scopes,queryParams:(i=e.options)===null||i===void 0?void 0:i.queryParams,skipBrowserRedirect:(n=e.options)===null||n===void 0?void 0:n.skipBrowserRedirect})})}signInWithOtp(e){var t,r,i,n;return k(this,void 0,void 0,function*(){try{if(yield this._removeSession(),"email"in e){const{email:o,options:a}=e,{error:c}=yield S(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:o,data:(t=a==null?void 0:a.data)!==null&&t!==void 0?t:{},create_user:(r=a==null?void 0:a.shouldCreateUser)!==null&&r!==void 0?r:!0,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},redirectTo:a==null?void 0:a.emailRedirectTo});return{data:{user:null,session:null},error:c}}if("phone"in e){const{phone:o,options:a}=e,{error:c}=yield S(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:o,data:(i=a==null?void 0:a.data)!==null&&i!==void 0?i:{},create_user:(n=a==null?void 0:a.shouldCreateUser)!==null&&n!==void 0?n:!0,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}}});return{data:{user:null,session:null},error:c}}throw new at("You must provide either an email or phone number.")}catch(o){if(T(o))return{data:{user:null,session:null},error:o};throw o}})}verifyOtp(e){var t,r;return k(this,void 0,void 0,function*(){try{yield this._removeSession();const{data:i,error:n}=yield S(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:(t=e.options)===null||t===void 0?void 0:t.captchaToken}}),redirectTo:(r=e.options)===null||r===void 0?void 0:r.redirectTo,xform:$e});if(n)throw n;if(!i)throw"An error occurred on token verification.";const o=i.session,a=i.user;return o!=null&&o.access_token&&(yield this._saveSession(o),this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:a,session:o},error:null}}catch(i){if(T(i))return{data:{user:null,session:null},error:i};throw i}})}signInWithSSO(e){var t,r,i;return k(this,void 0,void 0,function*(){try{return yield this._removeSession(),yield S(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:(r=(t=e.options)===null||t===void 0?void 0:t.redirectTo)!==null&&r!==void 0?r:void 0}),!((i=e==null?void 0:e.options)===null||i===void 0)&&i.captchaToken?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0}),headers:this.headers,xform:bs})}catch(n){if(T(n))return{data:null,error:n};throw n}})}getSession(){return k(this,void 0,void 0,function*(){yield this.initializePromise;let e=null;if(this.persistSession){const n=yield Ft(this.storage,this.storageKey);n!==null&&(this._isValidSession(n)?e=n:yield this._removeSession())}else e=this.inMemorySession;if(!e)return{data:{session:null},error:null};if(!(e.expires_at?e.expires_at<=Date.now()/1e3:!1))return{data:{session:e},error:null};const{session:r,error:i}=yield this._callRefreshToken(e.refresh_token);return i?{data:{session:null},error:i}:{data:{session:r},error:null}})}getUser(e){var t,r;return k(this,void 0,void 0,function*(){try{if(!e){const{data:i,error:n}=yield this.getSession();if(n)throw n;e=(r=(t=i.session)===null||t===void 0?void 0:t.access_token)!==null&&r!==void 0?r:void 0}return yield S(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:he})}catch(i){if(T(i))return{data:{user:null},error:i};throw i}})}updateUser(e){return k(this,void 0,void 0,function*(){try{const{data:t,error:r}=yield this.getSession();if(r)throw r;if(!t.session)throw new Re;const i=t.session,{data:n,error:o}=yield S(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,body:e,jwt:i.access_token,xform:he});if(o)throw o;return i.user=n.user,yield this._saveSession(i),this._notifyAllSubscribers("USER_UPDATED",i),{data:{user:i.user},error:null}}catch(t){if(T(t))return{data:{user:null},error:t};throw t}})}_decodeJWT(e){return Bt(e)}setSession(e){return k(this,void 0,void 0,function*(){try{if(!e.access_token||!e.refresh_token)throw new Re;const t=Date.now()/1e3;let r=t,i=!0,n=null;const o=Bt(e.access_token);if(o.exp&&(r=o.exp,i=r<=t),i){const{session:a,error:c}=yield this._callRefreshToken(e.refresh_token);if(c)return{data:{user:null,session:null},error:c};if(!a)return{data:{user:null,session:null},error:null};n=a}else{const{data:a,error:c}=yield this.getUser(e.access_token);if(c)throw c;n={access_token:e.access_token,refresh_token:e.refresh_token,user:a.user,token_type:"bearer",expires_in:r-t,expires_at:r},yield this._saveSession(n),this._notifyAllSubscribers("SIGNED_IN",n)}return{data:{user:n.user,session:n},error:null}}catch(t){if(T(t))return{data:{session:null,user:null},error:t};throw t}})}refreshSession(e){var t;return k(this,void 0,void 0,function*(){try{if(!e){const{data:n,error:o}=yield this.getSession();if(o)throw o;e=(t=n.session)!==null&&t!==void 0?t:void 0}if(!(e!=null&&e.refresh_token))throw new Re;const{session:r,error:i}=yield this._callRefreshToken(e.refresh_token);return i?{data:{user:null,session:null},error:i}:r?{data:{user:r.user,session:r},error:null}:{data:{user:null,session:null},error:null}}catch(r){if(T(r))return{data:{user:null,session:null},error:r};throw r}})}_getSessionFromUrl(){return k(this,void 0,void 0,function*(){try{if(!ie())throw new ne("No browser detected.");if(!this._isImplicitGrantFlow())throw new ne("Not a valid implicit grant flow url.");const e=z("error_description");if(e){const y=z("error_code");if(!y)throw new ne("No error_code detected.");const g=z("error");throw g?new ne(e,{error:g,code:y}):new ne("No error detected.")}const t=z("provider_token"),r=z("provider_refresh_token"),i=z("access_token");if(!i)throw new ne("No access_token detected.");const n=z("expires_in");if(!n)throw new ne("No expires_in detected.");const o=z("refresh_token");if(!o)throw new ne("No refresh_token detected.");const a=z("token_type");if(!a)throw new ne("No token_type detected.");const l=Math.round(Date.now()/1e3)+parseInt(n),{data:h,error:u}=yield this.getUser(i);if(u)throw u;const m=h.user,p={provider_token:t,provider_refresh_token:r,access_token:i,expires_in:parseInt(n),expires_at:l,refresh_token:o,token_type:a,user:m},f=z("type");return window.location.hash="",{data:{session:p,redirectType:f},error:null}}catch(e){if(T(e))return{data:{session:null,redirectType:null},error:e};throw e}})}_isImplicitGrantFlow(){return ie()&&(Boolean(z("access_token"))||Boolean(z("error_description")))}signOut(){var e;return k(this,void 0,void 0,function*(){const{data:t,error:r}=yield this.getSession();if(r)return{error:r};const i=(e=t.session)===null||e===void 0?void 0:e.access_token;if(i){const{error:n}=yield this.admin.signOut(i);if(n&&!(ms(n)&&(n.status===404||n.status===401)))return{error:n}}return yield this._removeSession(),this._notifyAllSubscribers("SIGNED_OUT",null),{error:null}})}onAuthStateChange(e){const t=as(),r={id:t,callback:e,unsubscribe:()=>{this.stateChangeEmitters.delete(t)}};return this.stateChangeEmitters.set(t,r),{data:{subscription:r}}}resetPasswordForEmail(e,t={}){return k(this,void 0,void 0,function*(){try{return yield S(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(r){if(T(r))return{data:null,error:r};throw r}})}_refreshAccessToken(e){return k(this,void 0,void 0,function*(){try{const t=Date.now();return yield fs(r=>k(this,void 0,void 0,function*(){return yield ds(r*200),yield S(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:$e})}),(r,i,n)=>n&&n.error&&n.error instanceof lt&&Date.now()+(r+1)*200-t<ht)}catch(t){if(T(t))return{data:{session:null,user:null},error:t};throw t}})}_isValidSession(e){return typeof e=="object"&&e!==null&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}_handleProviderSignIn(e,t={}){const r=this._getUrlForProvider(e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return ie()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}_recoverAndRefresh(){var e;return k(this,void 0,void 0,function*(){try{const t=yield Ft(this.storage,this.storageKey);if(!this._isValidSession(t)){t!==null&&(yield this._removeSession());return}const r=Math.round(Date.now()/1e3);if(((e=t.expires_at)!==null&&e!==void 0?e:1/0)<r+Cs)if(this.autoRefreshToken&&t.refresh_token){const{error:i}=yield this._callRefreshToken(t.refresh_token);i&&(console.log(i.message),yield this._removeSession())}else yield this._removeSession();else this.persistSession&&(yield this._saveSession(t)),this._notifyAllSubscribers("SIGNED_IN",t)}catch(t){console.error(t);return}})}_callRefreshToken(e){var t,r;return k(this,void 0,void 0,function*(){if(this.refreshingDeferred)return this.refreshingDeferred.promise;try{if(this.refreshingDeferred=new Ne,!e)throw new Re;const{data:i,error:n}=yield this._refreshAccessToken(e);if(n)throw n;if(!i.session)throw new Re;yield this._saveSession(i.session),this._notifyAllSubscribers("TOKEN_REFRESHED",i.session);const o={session:i.session,error:null};return this.refreshingDeferred.resolve(o),o}catch(i){if(T(i)){const n={session:null,error:i};return(t=this.refreshingDeferred)===null||t===void 0||t.resolve(n),n}throw(r=this.refreshingDeferred)===null||r===void 0||r.reject(i),i}finally{this.refreshingDeferred=null}})}_notifyAllSubscribers(e,t,r=!0){this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t}),this.stateChangeEmitters.forEach(i=>i.callback(e,t))}_saveSession(e){return k(this,void 0,void 0,function*(){this.persistSession||(this.inMemorySession=e),this.persistSession&&e.expires_at&&(yield this._persistSession(e))})}_persistSession(e){return cs(this.storage,this.storageKey,e)}_removeSession(){return k(this,void 0,void 0,function*(){this.persistSession?yield hs(this.storage,this.storageKey):this.inMemorySession=null})}_removeVisibilityChangedCallback(){const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&ie()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){console.error("removing visibilitychange callback failed",t)}}_startAutoRefresh(){return k(this,void 0,void 0,function*(){yield this._stopAutoRefresh();const e=setInterval(()=>this._autoRefreshTokenTick(),ht);this.autoRefreshTicker=e,e&&typeof e=="object"&&typeof e.unref=="function"&&e.unref(),yield this._autoRefreshTokenTick()})}_stopAutoRefresh(){return k(this,void 0,void 0,function*(){const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)})}startAutoRefresh(){return k(this,void 0,void 0,function*(){this._removeVisibilityChangedCallback(),yield this._startAutoRefresh()})}stopAutoRefresh(){return k(this,void 0,void 0,function*(){this._removeVisibilityChangedCallback(),yield this._stopAutoRefresh()})}_autoRefreshTokenTick(){return k(this,void 0,void 0,function*(){const e=Date.now();try{const{data:{session:t},error:r}=yield this.getSession();if(!t||!t.refresh_token||!t.expires_at)return;Math.floor((t.expires_at*1e3-e)/ht)<Us&&(yield this._callRefreshToken(t.refresh_token))}catch(t){console.error("Auto refresh tick failed with error. This is likely a transient error.",t)}})}_handleVisibilityChange(){return k(this,void 0,void 0,function*(){if(!ie()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=()=>k(this,void 0,void 0,function*(){return yield this._onVisibilityChanged(!1)}),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),yield this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}})}_onVisibilityChanged(e){return k(this,void 0,void 0,function*(){document.visibilityState==="visible"?(e||(yield this.initializePromise,yield this._recoverAndRefresh()),this.autoRefreshToken&&this._startAutoRefresh()):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()})}_getUrlForProvider(e,t){const r=[`provider=${encodeURIComponent(e)}`];if(t!=null&&t.redirectTo&&r.push(`redirect_to=${encodeURIComponent(t.redirectTo)}`),t!=null&&t.scopes&&r.push(`scopes=${encodeURIComponent(t.scopes)}`),t!=null&&t.queryParams){const i=new URLSearchParams(t.queryParams);r.push(i.toString())}return`${this.url}/authorize?${r.join("&")}`}_unenroll(e){var t;return k(this,void 0,void 0,function*(){try{const{data:r,error:i}=yield this.getSession();return i?{data:null,error:i}:yield S(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:(t=r==null?void 0:r.session)===null||t===void 0?void 0:t.access_token})}catch(r){if(T(r))return{data:null,error:r};throw r}})}_enroll(e){var t,r;return k(this,void 0,void 0,function*(){try{const{data:i,error:n}=yield this.getSession();if(n)return{data:null,error:n};const{data:o,error:a}=yield S(this.fetch,"POST",`${this.url}/factors`,{body:{friendly_name:e.friendlyName,factor_type:e.factorType,issuer:e.issuer},headers:this.headers,jwt:(t=i==null?void 0:i.session)===null||t===void 0?void 0:t.access_token});return a?{data:null,error:a}:(!((r=o==null?void 0:o.totp)===null||r===void 0)&&r.qr_code&&(o.totp.qr_code=`data:image/svg+xml;utf-8,${o.totp.qr_code}`),{data:o,error:null})}catch(i){if(T(i))return{data:null,error:i};throw i}})}_verify(e){var t;return k(this,void 0,void 0,function*(){try{const{data:r,error:i}=yield this.getSession();if(i)return{data:null,error:i};const{data:n,error:o}=yield S(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:(t=r==null?void 0:r.session)===null||t===void 0?void 0:t.access_token});return o?{data:null,error:o}:(yield this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+n.expires_in},n)),this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",n),{data:n,error:o})}catch(r){if(T(r))return{data:null,error:r};throw r}})}_challenge(e){var t;return k(this,void 0,void 0,function*(){try{const{data:r,error:i}=yield this.getSession();return i?{data:null,error:i}:yield S(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{headers:this.headers,jwt:(t=r==null?void 0:r.session)===null||t===void 0?void 0:t.access_token})}catch(r){if(T(r))return{data:null,error:r};throw r}})}_challengeAndVerify(e){return k(this,void 0,void 0,function*(){const{data:t,error:r}=yield this._challenge({factorId:e.factorId});return r?{data:null,error:r}:yield this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})})}_listFactors(){return k(this,void 0,void 0,function*(){const{data:{user:e},error:t}=yield this.getUser();if(t)return{data:null,error:t};const r=(e==null?void 0:e.factors)||[],i=r.filter(n=>n.factor_type==="totp"&&n.status==="verified");return{data:{all:r,totp:i},error:null}})}_getAuthenticatorAssuranceLevel(){var e,t;return k(this,void 0,void 0,function*(){const{data:{session:r},error:i}=yield this.getSession();if(i)return{data:null,error:i};if(!r)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const n=this._decodeJWT(r.access_token);let o=null;n.aal&&(o=n.aal);let a=o;((t=(e=r.user.factors)===null||e===void 0?void 0:e.filter(h=>h.status==="verified"))!==null&&t!==void 0?t:[]).length>0&&(a="aal2");const l=n.amr||[];return{data:{currentLevel:o,nextLevel:a,currentAuthenticationMethods:l},error:null}})}}class Ds extends Is{constructor(e){super(e)}}var Ls=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(h){try{l(r.next(h))}catch(u){o(u)}}function c(h){try{l(r.throw(h))}catch(u){o(u)}}function l(h){h.done?n(h.value):i(h.value).then(a,c)}l((r=r.apply(s,e||[])).next())})};const Fs={headers:Zr},Bs={schema:"public"},Ns={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},Ms={};class qs{constructor(e,t,r){var i,n,o,a,c,l,h,u;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const m=is(e);if(this.realtimeUrl=`${m}/realtime/v1`.replace(/^http/i,"ws"),this.authUrl=`${m}/auth/v1`,this.storageUrl=`${m}/storage/v1`,m.match(/(supabase\.co)|(supabase\.in)/)){const j=m.split(".");this.functionsUrl=`${j[0]}.functions.${j[1]}.${j[2]}`}else this.functionsUrl=`${m}/functions/v1`;const f=`sb-${new URL(this.authUrl).hostname.split(".")[0]}-auth-token`,y={db:Bs,realtime:Ms,auth:Object.assign(Object.assign({},Ns),{storageKey:f}),global:Fs},g=ns(r??{},y);this.storageKey=(n=(i=g.auth)===null||i===void 0?void 0:i.storageKey)!==null&&n!==void 0?n:"",this.headers=(a=(o=g.global)===null||o===void 0?void 0:o.headers)!==null&&a!==void 0?a:{},this.auth=this._initSupabaseAuthClient((c=g.auth)!==null&&c!==void 0?c:{},this.headers,(l=g.global)===null||l===void 0?void 0:l.fetch),this.fetch=ss(t,this._getAccessToken.bind(this),(h=g.global)===null||h===void 0?void 0:h.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers},g.realtime)),this.rest=new kr(`${m}/rest/v1`,{headers:this.headers,schema:(u=g.db)===null||u===void 0?void 0:u.schema,fetch:this.fetch}),this._listenForAuthEvents()}get functions(){return new mr(this.functionsUrl,{headers:this.headers,customFetch:this.fetch})}get storage(){return new Yr(this.storageUrl,this.headers,this.fetch)}from(e){return this.rest.from(e)}rpc(e,t={},r){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return Ls(this,void 0,void 0,function*(){const{data:r}=yield this.auth.getSession();return(t=(e=r.session)===null||e===void 0?void 0:e.access_token)!==null&&t!==void 0?t:null})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,storageKey:n},o,a){const c={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Ds({url:this.authUrl,headers:Object.assign(Object.assign({},c),o),storageKey:n,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,fetch:a})}_initRealtimeClient(e){return new Nr(this.realtimeUrl,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},e==null?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((t,r)=>{this._handleTokenChanged(t,r==null?void 0:r.access_token,"CLIENT")})}_handleTokenChanged(e,t,r){(e==="TOKEN_REFRESHED"||e==="SIGNED_IN")&&this.changedAccessToken!==t?(this.realtime.setAuth(t??null),this.changedAccessToken=t):(e==="SIGNED_OUT"||e==="USER_DELETED")&&(this.realtime.setAuth(this.supabaseKey),r=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const zs=(s,e,t)=>new qs(s,e,t),Pe=new Proxy({},{get:function(s,e){switch(e){case"getItem":return function(t){return Hs(t)};case"setItem":return function(t,r){return Js(t,r)};case"removeItem":return function(t){return Gs(t)};default:return null}}});function Hs(s){try{const e=localStorage.getItem(s);return e?JSON.parse(e):null}catch(e){return console.warn(`Failed to retrieve value from localStorage: ${e}`),null}}function Js(s,e){try{return localStorage.setItem(s,JSON.stringify(e)),!0}catch(t){return console.warn(`Failed to store value in localStorage: ${t}`),!1}}function Gs(s){try{return localStorage.removeItem(s),!0}catch(e){return console.warn(`Failed to remove value from localStorage: ${e}`),!1}}const Mt=s=>zs(s.apiUrl,s.apiKey,{auth:{storage:Pe}});class Vs{constructor(){Qt(this,"page_size",100);this.messages=bt({loading:!1,isInitialized:!1,data:[]}),this.roomId=null,this.chatClient=null,this.fetchedCount=0,this.totalMessagesCount=0}subscribe(e){return this.messages.subscribe(e)}async initialize(e){this.roomId=e.roomId,this.chatClient=Mt(e),this.totalMessagesCount=await this.getTotalMessagesCount(),this.addListeners(),await this.fetch()}async getTotalMessagesCount(){const{count:e,error:t}=await this.chatClient.from("messages").select("*",{count:"exact",head:!0}).eq("room_id",this.roomId);return e}addListeners(){this.listenToNewMessages(),this.listenToMessageDeletions(),this.listenToUserBlockOperation()}listenToNewMessages(){this.chatClient.channel("new_messages").on("postgres_changes",{event:"INSERT",schema:"public",table:"messages",filter:`room_id=eq.${this.roomId}`},async e=>{e.new.user=await this.getUser(e.new.userid),this.addMessageToList(e.new)}).subscribe()}listenToMessageDeletions(){this.chatClient.channel("message_deletions").on("postgres_changes",{event:"UPDATE",schema:"public",table:"messages",filter:`room_id=eq.${this.roomId}`},async e=>{e.new.is_deleted&&this.removeMessage(e.new.id)}).subscribe()}addMessageToList(e){this.messages.update(t=>(t.data=[...t.data,e],t))}async getUser(e){let{data:t}=await this.chatClient.from("user").select("*").eq("id",e).maybeSingle();return t}listenToUserBlockOperation(){this.chatClient.channel("user_block").on("postgres_changes",{event:"UPDATE",schema:"public",table:"user",filter:`room_id=eq.${this.roomId}`},async e=>{this.updateUserInMessages(e.new)}).subscribe()}updateUserInMessages(e){this.messages.update(t=>(t.data.forEach(r=>{r.user.id===e.id&&(r.user={id:e.id,name:e.name,is_blocked:e.is_blocked})}),t))}async fetch(){if(!this.shouldFetch())return;this.loadingStart();const e=await this.load();this.loadingComplete(e)}shouldFetch(){const{loading:e}=Yt(this.messages),t=this.fetchedCount<this.totalMessagesCount;return!e&&t}loadingStart(){this.messages.update(e=>(e.loading=!0,e))}async load(){const e=this.fetchedCount,t=this.page_size+this.fetchedCount,{data:r}=await this.chatClient.from("messages").select("*, user (name, id, is_blocked, is_moderator)").eq("room_id",this.roomId).is("is_deleted",!1).range(e,t).order("timestamp",{ascending:!1});return this.fetchedCount+=r.length,r}loadingComplete(e){this.messages.update(t=>(t.loading=!1,t.data=[...e.reverse(),...t.data],t.isInitialized=!0,t))}removeMessage(e){this.messages.update(t=>(t.data=t.data.filter(r=>r.id!=e),t))}async post(e,t){return await this.chatClient.from("messages").insert({room_id:this.roomId,userid:e.user_id,content:t})}async delete(e){await this.chatClient.from("messages").update({is_deleted:!0}).eq("id",e)}async blockUser(e){await this.chatClient.from("user").update({is_blocked:!0}).eq("id",e)}}const ue=new Vs,Ks=s=>{var e=s.split(".")[1],t=e.replace(/-/g,"+").replace(/_/g,"/"),r=decodeURIComponent(window.atob(t).split("").map(function(i){return"%"+("00"+i.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(r)},Ws=s=>Ks(s).role;function Qs(){return([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,s=>(s^crypto.getRandomValues(new Uint8Array(1))[0]&15>>s/4).toString(16))}class Xs{constructor(){this.chat_room_id=null,this.user=bt({}),this.role="anon"}async initialize(e){this.role=Ws(e.apiKey),this.chat_room_id=e.roomId,this.chatClient=Mt(e);const t=await this.initializaUser(e);this.store(t)}subscribe(e){return this.user.subscribe(e)}async initializaUser(e){let t={};const r=e.username??Pe.getItem(`${this.chat_room_id}_name`),i=e.user_id??Pe.getItem(`${this.chat_room_id}_user_id`);return r&&i&&(t=await this.getOrCreateSupabaseUser(i,r)),t}async getOrCreateSupabaseUser(e,t){let r=await this.getUser(e);return r||(r=await this.createUser(e,t)),{name:r.name,user_id:r.id,is_moderator:r.is_moderator}}async getUser(e){let{data:t,error:r}=await this.chatClient.from("user").select("*").eq("user_id",e).eq("room_id",this.chat_room_id).maybeSingle();return t}async createUser(e,t){const{data:r,error:i}=await this.chatClient.from("user").insert([{user_id:e,name:t,room_id:this.chat_room_id,is_moderator:this.role=="service_role"}]).select().single();return r}store(e){this.user.set(e)}async setNameAndId(e,t=Qs()){Pe.setItem(`${this.chat_room_id}_name`,e),Pe.setItem(`${this.chat_room_id}_user_id`,t);const r=await this.getOrCreateSupabaseUser(t,e);this.store(r)}}const ke=new Xs;function qt(s){let e;return{c(){e=w("span"),e.textContent="Moderator",v(e,"class","mx-2 inline-flex items-center rounded-full bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700")},m(t,r){D(t,e,r)},d(t){t&&U(e)}}}function zt(s){let e,t,r,i,n,o=s[1]&&Ht(s);return{c(){e=w("div"),t=w("a"),t.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6"><path fill-rule="evenodd" d="M4.5 12a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm6 0a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm6 0a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z" clip-rule="evenodd"></path></svg>',r=F(),o&&o.c(),v(t,"href","#"),v(t,"class","cursor-pointer"),v(e,"class","mx-2 relative")},m(a,c){D(a,e,c),b(e,t),b(e,r),o&&o.m(e,null),i||(n=[J(t,"click",s[5]),J(e,"focusout",s[3])],i=!0)},p(a,c){a[1]?o?o.p(a,c):(o=Ht(a),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},d(a){a&&U(e),o&&o.d(),i=!1,te(n)}}}function Ht(s){let e,t,r,i,n,o,a;return{c(){e=w("div"),t=w("div"),r=w("a"),r.textContent="Delete message",i=F(),n=w("a"),n.textContent="Block user",v(r,"href","#"),v(r,"class","text-red-700 hover:bg-red-500 hover:text-white block px-4 py-2 text-sm"),v(r,"role","menuitem"),v(r,"tabindex","-1"),v(r,"id","menu-item-0"),v(n,"href","#"),v(n,"class","text-gray-700 hover:bg-gray-500 hover:text-white block px-4 py-2 text-sm"),v(n,"role","menuitem"),v(n,"tabindex","-1"),v(n,"id","menu-item-1"),v(t,"class","py-1"),v(t,"role","none"),v(e,"class","absolute left-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"),v(e,"role","menu"),v(e,"aria-orientation","vertical"),v(e,"aria-labelledby","menu-button"),v(e,"tabindex","-1")},m(c,l){D(c,e,l),b(e,t),b(t,r),b(t,i),b(t,n),o||(a=[J(r,"click",s[6]),J(n,"click",s[7])],o=!0)},p:O,d(c){c&&U(e),o=!1,te(a)}}}function Ys(s){var j,H;let e,t,r,i=((j=s[0].user)==null?void 0:j.name)+"",n,o,a,c,l,h,u=s[0].content+"",m,p,f,y=((H=s[0].user)==null?void 0:H.is_moderator)&&qt(),g=s[2].is_moderator&&zt(s);return{c(){e=w("li"),t=w("div"),r=w("p"),n=pe(i),o=F(),y&&y.c(),a=F(),g&&g.c(),c=F(),l=w("div"),h=w("p"),m=pe(u),v(r,"class","font-medium text-gray-900"),v(t,"class","flex "+(s[4]?"flex-row-reverse":"")+" text-sm"),v(h,"title",p=new Date(s[0].timestamp).toLocaleTimeString()),v(h,"class","w-4/6 flex "+(s[4]?"justify-end":"")),v(l,"class","mt-1 flex "+(s[4]?"flex-row-reverse":"")+" text-sm text-gray-700"),v(e,"class",f="py-2 px-2 rounded-md "+(s[4]&&s[0].user.is_blocked?"opacity-50":""))},m(R,C){D(R,e,C),b(e,t),b(t,r),b(r,n),b(t,o),y&&y.m(t,null),b(t,a),g&&g.m(t,null),b(e,c),b(e,l),b(l,h),b(h,m)},p(R,[C]){var Z,ze;C&1&&i!==(i=((Z=R[0].user)==null?void 0:Z.name)+"")&&Ge(n,i),(ze=R[0].user)!=null&&ze.is_moderator?y||(y=qt(),y.c(),y.m(t,a)):y&&(y.d(1),y=null),R[2].is_moderator?g?g.p(R,C):(g=zt(R),g.c(),g.m(t,null)):g&&(g.d(1),g=null),C&1&&u!==(u=R[0].content+"")&&Ge(m,u),C&1&&p!==(p=new Date(R[0].timestamp).toLocaleTimeString())&&v(h,"title",p),C&1&&f!==(f="py-2 px-2 rounded-md "+(R[4]&&R[0].user.is_blocked?"opacity-50":""))&&v(e,"class",f)},i:O,o:O,d(R){R&&U(e),y&&y.d(),g&&g.d()}}}function Zs(s,e,t){var u;let r;fe(s,ke,m=>t(2,r=m));let{chat:i}=e,n=!1;const o=({relatedTarget:m,currentTarget:p})=>{m instanceof HTMLElement&&p.contains(m)||t(1,n=!1)},a=r.user_id==((u=i.user)==null?void 0:u.id),c=()=>t(1,n=!n),l=()=>{ue.delete(i.id),t(1,n=!1)},h=()=>{ue.blockUser(i.user.id),t(1,n=!1)};return s.$$set=m=>{"chat"in m&&t(0,i=m.chat)},[i,n,r,o,a,c,l,h]}class Jt extends se{constructor(e){super(),re(this,e,Zs,Ys,W,{chat:0})}}function ei(s){let e,t,r,i,n;return{c(){e=w("div"),t=Je("svg"),r=Je("circle"),i=Je("path"),v(r,"class","opacity-25"),v(r,"cx","12"),v(r,"cy","12"),v(r,"r","10"),v(r,"stroke","currentColor"),v(r,"stroke-width","4"),v(i,"class","opacity-75"),v(i,"fill","currentColor"),v(i,"d","M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"),v(t,"class","animate-spin h-6 w-6 text-gray-900"),v(t,"xmlns","http://www.w3.org/2000/svg"),v(t,"fill","none"),v(t,"viewBox","0 0 24 24"),v(e,"class",n="flex flex-col justify-center items-center "+(s[0]?"h-full":""))},m(o,a){D(o,e,a),b(e,t),b(t,r),b(t,i)},p(o,[a]){a&1&&n!==(n="flex flex-col justify-center items-center "+(o[0]?"h-full":""))&&v(e,"class",n)},i:O,o:O,d(o){o&&U(e)}}}function ti(s,e,t){let{showFullScreen:r}=e;return s.$$set=i=>{"showFullScreen"in i&&t(0,r=i.showFullScreen)},[r]}class Gt extends se{constructor(e){super(),re(this,e,ti,ei,W,{showFullScreen:0})}}const ln="";function Vt(s,e,t){const r=s.slice();return r[6]=e[t],r}function Kt(s){let e,t;return e=new Gt({props:{showFullScreen:!1}}),{c(){Q(e.$$.fragment)},m(r,i){G(e,r,i),t=!0},i(r){t||(A(e.$$.fragment,r),t=!0)},o(r){P(e.$$.fragment,r),t=!1},d(r){V(e,r)}}}function ri(s){let e,t;return e=new Jt({props:{chat:s[6]}}),{c(){Q(e.$$.fragment)},m(r,i){G(e,r,i),t=!0},p(r,i){const n={};i&2&&(n.chat=r[6]),e.$set(n)},i(r){t||(A(e.$$.fragment,r),t=!0)},o(r){P(e.$$.fragment,r),t=!1},d(r){V(e,r)}}}function si(s){let e,t;return e=new Jt({props:{chat:s[6]}}),{c(){Q(e.$$.fragment)},m(r,i){G(e,r,i),t=!0},p(r,i){const n={};i&2&&(n.chat=r[6]),e.$set(n)},i(r){t||(A(e.$$.fragment,r),t=!0)},o(r){P(e.$$.fragment,r),t=!1},d(r){V(e,r)}}}function Wt(s){let e,t,r,i;const n=[si,ri],o=[];function a(c,l){return c[6].user.is_blocked?c[6].user.is_blocked&&c[6].user.id===c[2].user_id?1:-1:0}return~(e=a(s))&&(t=o[e]=n[e](s)),{c(){t&&t.c(),r=er()},m(c,l){~e&&o[e].m(c,l),D(c,r,l),i=!0},p(c,l){let h=e;e=a(c),e===h?~e&&o[e].p(c,l):(t&&(Ee(),P(o[h],1,1,()=>{o[h]=null}),xe()),~e?(t=o[e],t?t.p(c,l):(t=o[e]=n[e](c),t.c()),A(t,1),t.m(r.parentNode,r)):t=null)},i(c){i||(A(t),i=!0)},o(c){P(t),i=!1},d(c){~e&&o[e].d(c),c&&U(r)}}}function ii(s){let e,t,r,i,n,o,a=s[1].loading&&Kt(),c=s[1].data,l=[];for(let u=0;u<c.length;u+=1)l[u]=Wt(Vt(s,c,u));const h=u=>P(l[u],1,1,()=>{l[u]=null});return{c(){e=w("div"),a&&a.c(),t=F(),r=w("ul");for(let u=0;u<l.length;u+=1)l[u].c();v(r,"class","messages space-y-8 overflow-y-auto pr-4 svelte-s2zs04"),rr(r,"height","calc(100vh - 19rem)"),v(e,"class","px-4 py-6 sm:px-4")},m(u,m){D(u,e,m),a&&a.m(e,null),b(e,t),b(e,r);for(let p=0;p<l.length;p+=1)l[p].m(r,null);s[4](r),i=!0,n||(o=J(r,"scroll",s[3]),n=!0)},p(u,[m]){if(u[1].loading?a?m&2&&A(a,1):(a=Kt(),a.c(),A(a,1),a.m(e,t)):a&&(Ee(),P(a,1,1,()=>{a=null}),xe()),m&6){c=u[1].data;let p;for(p=0;p<c.length;p+=1){const f=Vt(u,c,p);l[p]?(l[p].p(f,m),A(l[p],1)):(l[p]=Wt(f),l[p].c(),A(l[p],1),l[p].m(r,null))}for(Ee(),p=c.length;p<l.length;p+=1)h(p);xe()}},i(u){if(!i){A(a);for(let m=0;m<c.length;m+=1)A(l[m]);i=!0}},o(u){P(a),l=l.filter(Boolean);for(let m=0;m<l.length;m+=1)P(l[m]);i=!1},d(u){u&&U(e),a&&a.d(),Zt(l,u),s[4](null),n=!1,o()}}}function ni(s,e,t){let r,i;fe(s,ue,l=>t(1,r=l)),fe(s,ke,l=>t(2,i=l));let n,o;_t(()=>{n.scroll({top:n.scrollHeight})}),sr(()=>{o=n&&n.offsetHeight+n.scrollTop>n.scrollHeight-50}),ir(()=>{o&&n.scroll({top:n.scrollHeight,behavior:"smooth"})});function a(){n.scrollTop<30&&ue.fetch()}function c(l){Ke[l?"unshift":"push"](()=>{n=l,t(0,n)})}return[n,r,i,a,c]}class oi extends se{constructor(e){super(),re(this,e,ni,ii,W,{})}}function ai(s){let e,t,r,i,n,o,a,c,l,h,u,m;return{c(){e=w("div"),t=w("div"),r=w("div"),i=w("div"),n=w("label"),n.textContent="About",o=F(),a=w("textarea"),c=F(),l=w("div"),h=w("button"),h.textContent="Send",v(n,"for","comment"),v(n,"class","sr-only"),v(a,"rows","3"),v(a,"class","block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"),v(a,"placeholder","Type a message..."),v(h,"type","button"),v(h,"class","inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),v(l,"class","mt-3 flex items-center justify-end"),v(r,"class","min-w-0 flex-1"),v(t,"class","flex space-x-3"),v(e,"class","bg-gray-50 px-4 py-6 sm:px-6")},m(p,f){D(p,e,f),b(e,t),b(t,r),b(r,i),b(i,n),b(i,o),b(i,a),Ie(a,s[0]),b(r,c),b(r,l),b(l,h),u||(m=[J(a,"keydown",s[2]),J(a,"input",s[3]),J(h,"click",s[1])],u=!0)},p(p,[f]){f&1&&Ie(a,p[0])},i:O,o:O,d(p){p&&U(e),u=!1,te(m)}}}function li(s,e,t){let r;fe(s,ke,c=>t(4,r=c));let i="";function n(){i&&(ue.post(r,i),t(0,i=""))}function o(c){c.ctrlKey&&c.key=="Enter"&&n()}function a(){i=this.value,t(0,i)}return[i,n,o,a]}class ci extends se{constructor(e){super(),re(this,e,li,ai,W,{})}}function hi(s){let e;return{c(){e=w("div"),e.innerHTML=`<svg class="h-16 w-16 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0011.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"></path></svg>
  <p class="mt-4 text-gray-600 text-xl font-medium">No chats yet...</p>
  <p class="text-gray-600 mt-0.5">Start conversing to see your messages here.</p>`,v(e,"class","flex flex-col justify-center items-center h-full")},m(t,r){D(t,e,r)},p:O,i:O,o:O,d(t){t&&U(e)}}}class ui extends se{constructor(e){super(),re(this,e,null,hi,W,{})}}function di(s){let e,t;return e=new oi({}),{c(){Q(e.$$.fragment)},m(r,i){G(e,r,i),t=!0},i(r){t||(A(e.$$.fragment,r),t=!0)},o(r){P(e.$$.fragment,r),t=!1},d(r){V(e,r)}}}function fi(s){let e,t;return e=new ui({}),{c(){Q(e.$$.fragment)},m(r,i){G(e,r,i),t=!0},i(r){t||(A(e.$$.fragment,r),t=!0)},o(r){P(e.$$.fragment,r),t=!1},d(r){V(e,r)}}}function pi(s){let e,t;return e=new Gt({props:{showFullScreen:!0}}),{c(){Q(e.$$.fragment)},m(r,i){G(e,r,i),t=!0},i(r){t||(A(e.$$.fragment,r),t=!0)},o(r){P(e.$$.fragment,r),t=!1},d(r){V(e,r)}}}function mi(s){let e,t,r,i,n;const o=[pi,fi,di],a=[];function c(l,h){return l[1]?0:l[0]?1:2}return e=c(s),t=a[e]=o[e](s),i=new ci({}),{c(){t.c(),r=F(),Q(i.$$.fragment)},m(l,h){a[e].m(l,h),D(l,r,h),G(i,l,h),n=!0},p(l,[h]){let u=e;e=c(l),e!==u&&(Ee(),P(a[u],1,1,()=>{a[u]=null}),xe(),t=a[e],t||(t=a[e]=o[e](l),t.c()),A(t,1),t.m(r.parentNode,r))},i(l){n||(A(t),A(i.$$.fragment,l),n=!0)},o(l){P(t),P(i.$$.fragment,l),n=!1},d(l){a[e].d(l),l&&U(r),V(i,l)}}}function vi(s,e,t){let r,i,n;return fe(s,ue,o=>t(2,n=o)),s.$$.update=()=>{s.$$.dirty&4&&t(1,r=n.loading&&!n.isInitialized),s.$$.dirty&4&&t(0,i=n.data.length==0)},[i,r,n]}class _i extends se{constructor(e){super(),re(this,e,vi,mi,W,{})}}function gi(s){let e,t,r,i,n,o,a,c,l,h,u,m,p;return{c(){e=w("div"),t=w("p"),t.textContent="Join to chat",r=F(),i=w("div"),n=w("label"),n.textContent="Name",o=F(),a=w("div"),c=w("input"),l=F(),h=w("div"),u=w("button"),u.textContent="Join",v(t,"class","text-center font-medium text-gray-600"),v(n,"for","name"),v(n,"class","block text-sm font-medium text-gray-700"),v(c,"id","name"),v(c,"name","name"),v(c,"type","text"),c.required=!0,v(c,"class","block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"),v(a,"class","mt-1"),v(i,"class","mt-2 mx-4"),v(u,"type","button"),v(u,"class","inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-8 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"),v(h,"class","mt-2 text-right mx-4"),v(e,"class","flex flex-col h-full justify-center w-full mx-auto")},m(f,y){D(f,e,y),b(e,t),b(e,r),b(e,i),b(i,n),b(i,o),b(i,a),b(a,c),Ie(c,s[0]),b(e,l),b(e,h),b(h,u),m||(p=[J(c,"keydown",s[2]),J(c,"input",s[3]),J(u,"click",s[1])],m=!0)},p(f,[y]){y&1&&c.value!==f[0]&&Ie(c,f[0])},i:O,o:O,d(f){f&&U(e),m=!1,te(p)}}}function yi(s,e,t){let r="";function i(){ke.setNameAndId(r)}function n(a){a.key=="Enter"&&r.length>0&&i()}function o(){r=this.value,t(0,r)}return[r,i,n,o]}class bi extends se{constructor(e){super(),re(this,e,yi,gi,W,{})}}function wi(s){let e;return{c(){e=pe("Chat")},m(t,r){D(t,e,r)},p:O,d(t){t&&U(e)}}}function $i(s){let e;return{c(){e=pe(s[0])},m(t,r){D(t,e,r)},p(t,r){r&1&&Ge(e,t[0])},d(t){t&&U(e)}}}function ki(s){let e,t;function r(o,a){return o[0]?$i:wi}let i=r(s),n=i(s);return{c(){e=w("div"),t=w("h2"),n.c(),v(t,"class","text-lg font-medium text-gray-900"),v(e,"class","px-4 py-5 sm:px-6")},m(o,a){D(o,e,a),b(e,t),n.m(t,null)},p(o,[a]){i===(i=r(o))&&n?n.p(o,a):(n.d(1),n=i(o),n&&(n.c(),n.m(t,null)))},i:O,o:O,d(o){o&&U(e),n.d()}}}function Ti(s,e,t){let{title:r}=e;return s.$$set=i=>{"title"in i&&t(0,r=i.title)},[r]}class Oi extends se{constructor(e){super(),re(this,e,Ti,ki,W,{title:0})}}function Ei(s){let e,t;return e=new bi({}),{c(){Q(e.$$.fragment)},m(r,i){G(e,r,i),t=!0},i(r){t||(A(e.$$.fragment,r),t=!0)},o(r){P(e.$$.fragment,r),t=!1},d(r){V(e,r)}}}function xi(s){let e,t;return e=new _i({}),{c(){Q(e.$$.fragment)},m(r,i){G(e,r,i),t=!0},i(r){t||(A(e.$$.fragment,r),t=!0)},o(r){P(e.$$.fragment,r),t=!1},d(r){V(e,r)}}}function Si(s){let e,t,r,i,n,o,a,c,l,h;o=new Oi({props:{title:s[0].title}});const u=[xi,Ei],m=[];function p(f,y){return f[1].name?0:1}return c=p(s),l=m[c]=u[c](s),{c(){e=w("link"),t=F(),r=w("div"),i=w("div"),n=w("div"),Q(o.$$.fragment),a=F(),l.c(),v(e,"rel","stylesheet"),v(e,"href","https://rsms.me/inter/inter.css"),v(n,"class","divide-y divide-gray-200 h-full flex flex-col"),v(i,"class","bg-white shadow sm:overflow-hidden sm:rounded-lg max-w-2xl mx-auto h-full"),v(r,"class","tp-chat bg-white absolute inset-0 w-full")},m(f,y){b(document.head,e),D(f,t,y),D(f,r,y),b(r,i),b(i,n),G(o,n,null),b(n,a),m[c].m(n,null),h=!0},p(f,[y]){const g={};y&1&&(g.title=f[0].title),o.$set(g);let j=c;c=p(f),c!==j&&(Ee(),P(m[j],1,1,()=>{m[j]=null}),xe(),l=m[c],l||(l=m[c]=u[c](f),l.c()),A(l,1),l.m(n,null))},i(f){h||(A(o.$$.fragment,f),A(l),h=!0)},o(f){P(o.$$.fragment,f),P(l),h=!1},d(f){U(e),f&&U(t),f&&U(r),V(o),m[c].d()}}}function ji(s,e,t){let r;fe(s,ke,n=>t(1,r=n));let{config:i}=e;return _t(()=>{ue.initialize(i),ke.initialize(i)}),s.$$set=n=>{"config"in n&&t(0,i=n.config)},[i,r]}class Ci extends se{constructor(e){super(),re(this,e,ji,Si,W,{config:0})}}const Ai=function(s,e){new Ci({target:s,props:{config:e}})};q.load=Ai,Object.defineProperty(q,Symbol.toStringTag,{value:"Module"})});
