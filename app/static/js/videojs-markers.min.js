/*! videojs-markers - v1.0.1 - 2018-02-03
* Copyright (c) 2018 ; Licensed  */

!function(e,r){if("function"==typeof define&&define.amd)define(["video.js"],r);else if("undefined"!=typeof exports)r(require("video.js"));else{var t={exports:{}};r(e.videojs),e.videojsMarkers=t.exports}}(this,function(e){"use strict";function r(){var e=(new Date).getTime();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(r){var t=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"==r?t:3&t|8).toString(16)})}function t(e){var r,t={top:0,bottom:0,left:0,width:0,height:0,right:0};try{r=e.getBoundingClientRect()}catch(e){r=t}return r}function i(e){function i(){M.sort(function(e,r){return j.markerTip.time(e)-j.markerTip.time(r)})}function u(e){e.forEach(function(e){e.key=r(),S.el().querySelector(".vjs-progress-holder").appendChild(s(e)),E[e.key]=e,M.push(e)}),i()}function c(e){return j.markerTip.time(e)/S.duration()*100}function f(e,r){r.className="vjs-marker "+(e.class||""),Object.keys(j.markerStyle).forEach(function(e){r.style[e]=j.markerStyle[e]});var i=e.time/S.duration();if((i<0||i>1)&&(r.style.display="none"),r.style.left=c(e)+"%",e.duration)r.style.width=e.duration/S.duration()*100+"%",r.style.marginLeft="0px";else{var n=t(r);r.style.marginLeft=n.width/2+"px"}}function s(e){var r=n.default.createEl("div",{},{"data-marker-key":e.key,"data-marker-time":j.markerTip.time(e)});return f(e,r),r.addEventListener("click",function(r){var t=!1;if("function"==typeof j.onMarkerClick&&(t=!1===j.onMarkerClick(e)),!t){var i=this.getAttribute("data-marker-key");S.currentTime(j.markerTip.time(E[i]))}}),j.markerTip.display&&v(r),r}function d(e){M.forEach(function(r){var t=S.el().querySelector(".vjs-marker[data-marker-key='"+r.key+"']"),i=j.markerTip.time(r);(e||t.getAttribute("data-marker-time")!==i)&&(f(r,t),t.setAttribute("data-marker-time",i))}),i()}function m(e){w&&(L=l,w.style.visibility="hidden"),O=l;var r=[];e.forEach(function(e){var t=M[e];if(t){delete E[t.key],r.push(e);var i=S.el().querySelector(".vjs-marker[data-marker-key='"+t.key+"']");i&&i.parentNode.removeChild(i)}}),r.reverse(),r.forEach(function(e){M.splice(e,1)}),i()}function v(e){e.addEventListener("mouseover",function(){var r=E[e.getAttribute("data-marker-key")];if(A){A.querySelector(".vjs-tip-inner").innerText=j.markerTip.text(r),A.style.left=c(r)+"%";var i=t(A),n=t(e);A.style.marginLeft=-parseFloat(i.width/2)+parseFloat(n.width/4)+"px",A.style.visibility="visible"}}),e.addEventListener("mouseout",function(){A&&(A.style.visibility="hidden")})}function y(){A=n.default.createEl("div",{className:"vjs-tip",innerHTML:"<div class='vjs-tip-arrow'></div><div class='vjs-tip-inner'></div>"}),S.el().querySelector(".vjs-progress-holder").appendChild(A)}function k(){if(j.breakOverlay.display&&!(O<0)){var e=S.currentTime(),r=M[O],t=j.markerTip.time(r);e>=t&&e<=t+j.breakOverlay.displayTime?(L!==O&&(L=O,w&&(w.querySelector(".vjs-break-overlay-text").innerHTML=j.breakOverlay.text(r))),w&&(w.style.visibility="visible")):(L=l,w&&(w.style.visibility="hidden"))}}function p(){w=n.default.createEl("div",{className:"vjs-break-overlay",innerHTML:"<div class='vjs-break-overlay-text'></div>"}),Object.keys(j.breakOverlay.style).forEach(function(e){w&&(w.style[e]=j.breakOverlay.style[e])}),S.el().appendChild(w),L=l}function h(){x(),k(),e.onTimeUpdateAfterMarkerUpdate&&e.onTimeUpdateAfterMarkerUpdate()}function x(){if(M.length){var r=function(e){return e<M.length-1?j.markerTip.time(M[e+1]):S.duration()},t=S.currentTime(),i=l;if(O!==l){var n=r(O);if(t>=j.markerTip.time(M[O])&&t<n)return;if(O===M.length-1&&t===S.duration())return}if(t<j.markerTip.time(M[0]))i=l;else for(var a=0;a<M.length;a++)if(n=r(a),t>=j.markerTip.time(M[a])&&t<n){i=a;break}i!==O&&(i!==l&&e.onMarkerReached&&e.onMarkerReached(M[i],i),O=i)}}function b(){j.markerTip.display&&y(),S.markers.removeAll(),u(j.markers),j.breakOverlay.display&&p(),h(),S.on("timeupdate",h),S.off("loadedmetadata")}if(!n.default.mergeOptions){var T=function(e){return!!e&&"object"===(void 0===e?"undefined":a(e))&&"[object Object]"===toString.call(e)&&e.constructor===Object},g=function e(r,t){var i={};return[r,t].forEach(function(r){r&&Object.keys(r).forEach(function(t){var n=r[t];T(n)?(T(i[t])||(i[t]={}),i[t]=e(i[t],n)):i[t]=n})}),i};n.default.mergeOptions=g}n.default.createEl||(n.default.createEl=function(e,r,t){var i=n.default.Player.prototype.createEl(e,r);return t&&Object.keys(t).forEach(function(e){i.setAttribute(e,t[e])}),i});var j=n.default.mergeOptions(o,e),E={},M=[],O=l,S=this,A=null,w=null,L=l;S.on("loadedmetadata",function(){b()}),S.markers={getMarkers:function(){return M},next:function(){for(var e=S.currentTime(),r=0;r<M.length;r++){var t=j.markerTip.time(M[r]);if(t>e){S.currentTime(t);break}}},prev:function(){for(var e=S.currentTime(),r=M.length-1;r>=0;r--){var t=j.markerTip.time(M[r]);if(t+.5<e)return void S.currentTime(t)}},add:function(e){u(e)},remove:function(e){m(e)},removeAll:function(){for(var e=[],r=0;r<M.length;r++)e.push(r);m(e)},updateTime:function(e){d(e)},reset:function(e){S.markers.removeAll(),u(e)},destroy:function(){S.markers.removeAll(),w&&w.remove(),A&&A.remove(),S.off("timeupdate",k),delete S.markers}}}var n=function(e){return e&&e.__esModule?e:{default:e}}(e),a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o={markerStyle:{width:"7px","border-radius":"30%","background-color":"red"},markerTip:{display:!0,text:function(e){return"Break: "+e.text},time:function(e){return e.time}},breakOverlay:{display:!1,displayTime:3,text:function(e){return"Break overlay: "+e.overlayText},style:{width:"100%",height:"20%","background-color":"rgba(0,0,0,0.7)",color:"white","font-size":"17px"}},onMarkerClick:function(e){},onMarkerReached:function(e,r){},markers:[]},l=-1;n.default.plugin("markers",i)});