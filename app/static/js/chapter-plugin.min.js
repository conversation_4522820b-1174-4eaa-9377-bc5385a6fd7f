(()=>{var e=window.videojs;class r extends(e.getComponent("Component")){constructor(e,r={}){super(e,r),this.resetMarkerChildren=this.resetMarkerChildren.bind(this),this.loadMarkers=this.loadMarkers.bind(this),this.onProgressBarHover=this.onProgressBarHover.bind(this),this.player().on("loadstart",this.resetMarkerChildren),this.player().on("loadeddata",this.loadMarkers),this.player().on("playerreset",this.resetMarkerChildren)}loadMarkers(){const r=Array.from(this.player().textTracks()).find((e=>"chapter-markers"===e.label));if(!r)return;const t=r.cues_,s=e.computedStyle(this.el(),"gap");this.createMarkerElements(t,s);const a=this.player().getChild("ControlBar").getChild("ProgressControl");this.addProgressBarHoverListeners(a)}createMarkerElements(e,r){e.forEach(((e,t,s)=>{this.initializeMarkerElement(e,t,s,r)}))}initializeMarkerElement(e,r,t,s){const a=e.startTime,i=JSON.parse(e.text).label;if(this.player().duration()>a){0===r&&a>0&&this.addChild("marker-empty",{className:"marker-empty",componentClass:"MarkerDisplay",endTime:a,gap:s,startTime:0,label:i});const l=t[r+1];this.addChild(`marker-${r}`,{className:`marker-${r}`,componentClass:"MarkerDisplay",endTime:e.endTime,gap:l?s:void 0,startTime:a,label:i})}}addProgressBarHoverListeners(e){e.on("mousemove",this.onProgressBarHover),e.on("mouseleave",(()=>{this.el().querySelectorAll(".cst-marker").forEach((e=>e.classList.remove("show-label")))}))}onProgressBarHover(e){const r=this.player().getChild("ControlBar").getChild("ProgressControl").getChild("SeekBar"),t=this.player().duration(),s=(e.pageX-r.el().getBoundingClientRect().left)/r.width()*t;this.el().querySelectorAll(".cst-marker").forEach((e=>e.classList.remove("show-label")));const a=Array.from(this.el().children).find((e=>{if(!e.options_)return!1;const{startTime:r,endTime:t}=e.options_;return r<=s&&s<=t}));a&&a.classList.add("show-label")}resetMarkerChildren(){this.children().forEach((e=>e.dispose())),this.children_=[],e.dom.emptyEl(this.el())}buildCSSClass(){return`cst-markers ${super.buildCSSClass()}`.trim()}createEl(){return e.dom.createEl("div",{className:this.buildCSSClass()})}dispose(){this.player().off("loadstart",this.resetMarkerChildren),this.player().off("loadeddata",this.loadMarkers),this.player().off("playerreset",this.resetMarkerChildren),this.resetMarkerChildren(),super.dispose()}}e.registerComponent("MarkersDisplay",r);class t extends(e.getComponent("Component")){constructor(e,r){super(e,r);const{gap:t}=r;this.updateMarkerPlayed=this.updateMarkerPlayed.bind(this),this.updateMarkerBuffered=this.updateMarkerBuffered.bind(this),this.setMarkerWidth(this.calculateMarkerWidth(),t),this.player().on("timeupdate",this.updateMarkerPlayed),this.player().on("progress",this.updateMarkerBuffered)}setMarkerWidth(e,r){const t=void 0!==r?`width: calc(${e}% - ${r})`:`width: ${e}%`;this.setAttribute("style",t)}calculateMarkerWidth(){const{endTime:e,startTime:r}=this.options();return(e-r)/this.player().duration()*100}updateMarker(e=0,r){if(!this.parentComponent_.el().getClientRects().length)return;const t=this.player().duration(),s=this.parentComponent_.el().getClientRects()[0].width,a=this.el().offsetLeft,i=t*a/s,l=t*(a+this.el().offsetWidth)/s;e>l?this.el().style.setProperty(r,"200%"):e<i?this.el().style.setProperty(r,"0%"):this.el().style.setProperty(r,100*Math.abs((e-i)/(l-i))+"%")}updateMarkerBuffered(){this.updateMarker(this.player().bufferedEnd(),"--_cst-marker-buffered")}updateMarkerPlayed(){this.updateMarker(this.player().currentTime(),"--_cst-marker-played")}buildCSSClass(){return`cst-marker ${super.buildCSSClass()}`.trim()}createEl(){const r=super.createEl("div",{className:this.buildCSSClass()}),{label:t}=this.options();if(t){const s=e.dom.createEl("span",{className:"marker-label",textContent:t});r.appendChild(s)}return r}dispose(){this.player().off("timeupdate",this.updateMarkerPlayed),this.player().off("progress",this.updateMarkerBuffered),super.dispose()}}e.registerComponent("MarkerDisplay",t);class s extends(e.getPlugin("plugin")){constructor(e,r){super(e,r),this.initializeMarkers(e,r),this.addMarkersToTrack=this.addMarkersToTrack.bind(this),e.on("loadedmetadata",this.addMarkersToTrack)}initializeMarkers(e,r){this.markers=r.markers||[],e.addClass("chapter-markers"),e.getChild("ControlBar").getChild("ProgressControl").getChild("SeekBar").addChild("MarkersDisplay",{componentClass:"MarkersDisplay"})}addMark(e=[]){if(!e.length)return;const r=this.player.addTextTrack("metadata","chapter-markers",this.player.language());e.forEach(((e,t,s)=>{const{startTime:a,label:i}=e,l=s[t+1],d={startTime:a,endTime:l?l.startTime:this.player().duration(),text:JSON.stringify(e)};r.addCue(d)}))}addMarkersToTrack(){this.markers.length&&(this.markersTrack=this.player.addTextTrack("metadata","chapter-markers",this.player.language()),this.markers.forEach(((e,r,t)=>{const{startTime:s,label:a}=e,i=t[r+1],l={startTime:s,endTime:i?i.startTime:this.player.duration(),text:JSON.stringify(e)};this.markersTrack.addCue(l)})))}}e.registerPlugin("chapter",s)})();
