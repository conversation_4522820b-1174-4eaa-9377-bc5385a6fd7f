(function(F,D){typeof exports=="object"&&typeof module<"u"?D(exports):typeof define=="function"&&define.amd?define(["exports"],D):(F=typeof globalThis<"u"?globalThis:F||self,D(F.TPStreamsChat={}))})(this,function(F){"use strict";var li=Object.defineProperty;var hi=(F,D,j)=>D in F?li(F,D,{enumerable:!0,configurable:!0,writable:!0,value:j}):F[D]=j;var Nt=(F,D,j)=>(hi(F,typeof D!="symbol"?D+"":D,j),j);function D(s,e){for(var t=0;t<e.length;t++){const r=e[t];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in s)){const n=Object.getOwnPropertyDescriptor(r,i);n&&Object.defineProperty(s,i,n.get?n:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(s,Symbol.toStringTag,{value:"Module"}))}function j(){}function lt(s){return s()}function ht(){return Object.create(null)}function X(s){s.forEach(lt)}function ut(s){return typeof s=="function"}function Y(s,e){return s!=s?e==e:s!==e||s&&typeof s=="object"||typeof s=="function"}function Mt(s){return Object.keys(s).length===0}function dt(s,...e){if(s==null)return j;const t=s.subscribe(...e);return t.unsubscribe?()=>t.unsubscribe():t}function ft(s,e,t){s.$$.on_destroy.push(dt(e,t))}function $(s,e){s.appendChild(e)}function q(s,e,t){s.insertBefore(e,t||null)}function B(s){s.parentNode&&s.parentNode.removeChild(s)}function Ht(s,e){for(let t=0;t<s.length;t+=1)s[t]&&s[t].d(e)}function T(s){return document.createElement(s)}function ye(s){return document.createTextNode(s)}function V(){return ye(" ")}function Z(s,e,t,r){return s.addEventListener(e,t,r),()=>s.removeEventListener(e,t,r)}function y(s,e,t){t==null?s.removeAttribute(e):s.getAttribute(e)!==t&&s.setAttribute(e,t)}function qt(s){return Array.from(s.childNodes)}function Ne(s,e){e=""+e,s.wholeText!==e&&(s.data=e)}function Ae(s,e){s.value=e??""}function Jt(s,e,t,r){t===null?s.style.removeProperty(e):s.style.setProperty(e,t,r?"important":"")}let be;function we(s){be=s}function Me(){if(!be)throw new Error("Function called outside component initialization");return be}function zt(s){Me().$$.before_update.push(s)}function Gt(s){Me().$$.on_mount.push(s)}function Kt(s){Me().$$.after_update.push(s)}const ae=[],He=[],Re=[],pt=[],Vt=Promise.resolve();let qe=!1;function Wt(){qe||(qe=!0,Vt.then(vt))}function Je(s){Re.push(s)}const ze=new Set;let ce=0;function vt(){if(ce!==0)return;const s=be;do{try{for(;ce<ae.length;){const e=ae[ce];ce++,we(e),Qt(e.$$)}}catch(e){throw ae.length=0,ce=0,e}for(we(null),ae.length=0,ce=0;He.length;)He.pop()();for(let e=0;e<Re.length;e+=1){const t=Re[e];ze.has(t)||(ze.add(t),t())}Re.length=0}while(ae.length);for(;pt.length;)pt.pop()();qe=!1,ze.clear(),we(s)}function Qt(s){if(s.fragment!==null){s.update(),X(s.before_update);const e=s.dirty;s.dirty=[-1],s.fragment&&s.fragment.p(s.ctx,e),s.after_update.forEach(Je)}}const Pe=new Set;let ee;function Ge(){ee={r:0,c:[],p:ee}}function Ke(){ee.r||X(ee.c),ee=ee.p}function N(s,e){s&&s.i&&(Pe.delete(s),s.i(e))}function J(s,e,t,r){if(s&&s.o){if(Pe.has(s))return;Pe.add(s),ee.c.push(()=>{Pe.delete(s),r&&(t&&s.d(1),r())}),s.o(e)}else r&&r()}function $e(s){s&&s.c()}function le(s,e,t,r){const{fragment:i,after_update:n}=s.$$;i&&i.m(e,t),r||Je(()=>{const o=s.$$.on_mount.map(lt).filter(ut);s.$$.on_destroy?s.$$.on_destroy.push(...o):X(o),s.$$.on_mount=[]}),n.forEach(Je)}function he(s,e){const t=s.$$;t.fragment!==null&&(X(t.on_destroy),t.fragment&&t.fragment.d(e),t.on_destroy=t.fragment=null,t.ctx=[])}function Xt(s,e){s.$$.dirty[0]===-1&&(ae.push(s),Wt(),s.$$.dirty.fill(0)),s.$$.dirty[e/31|0]|=1<<e%31}function ue(s,e,t,r,i,n,o,a=[-1]){const h=be;we(s);const c=s.$$={fragment:null,ctx:[],props:n,update:j,not_equal:i,bound:ht(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(h?h.$$.context:[])),callbacks:ht(),dirty:a,skip_bound:!1,root:e.target||h.$$.root};o&&o(c.root);let l=!1;if(c.ctx=t?t(s,e.props||{},(u,f,...m)=>{const p=m.length?m[0]:f;return c.ctx&&i(c.ctx[u],c.ctx[u]=p)&&(!c.skip_bound&&c.bound[u]&&c.bound[u](p),l&&Xt(s,u)),f}):[],c.update(),l=!0,X(c.before_update),c.fragment=r?r(c.ctx):!1,e.target){if(e.hydrate){const u=qt(e.target);c.fragment&&c.fragment.l(u),u.forEach(B)}else c.fragment&&c.fragment.c();e.intro&&N(s.$$.fragment),le(s,e.target,e.anchor,e.customElement),vt()}we(h)}class de{$destroy(){he(this,1),this.$destroy=j}$on(e,t){if(!ut(t))return j;const r=this.$$.callbacks[e]||(this.$$.callbacks[e]=[]);return r.push(t),()=>{const i=r.indexOf(t);i!==-1&&r.splice(i,1)}}$set(e){this.$$set&&!Mt(e)&&(this.$$.skip_bound=!0,this.$$set(e),this.$$.skip_bound=!1)}}const ui="";var Yt=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(l){try{c(r.next(l))}catch(u){o(u)}}function h(l){try{c(r.throw(l))}catch(u){o(u)}}function c(l){l.done?n(l.value):i(l.value).then(a,h)}c((r=r.apply(s,e||[])).next())})};const Zt=s=>{let e;return s?e=s:typeof fetch>"u"?e=(...t)=>Yt(void 0,void 0,void 0,function*(){return yield(yield Promise.resolve().then(()=>Ce)).fetch(...t)}):e=fetch,(...t)=>e(...t)};class Ve extends Error{constructor(e,t="FunctionsError",r){super(e),super.name=t,this.context=r}}class er extends Ve{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class tr extends Ve{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class rr extends Ve{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var sr=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(l){try{c(r.next(l))}catch(u){o(u)}}function h(l){try{c(r.throw(l))}catch(u){o(u)}}function c(l){l.done?n(l.value):i(l.value).then(a,h)}c((r=r.apply(s,e||[])).next())})};class ir{constructor(e,{headers:t={},customFetch:r}={}){this.url=e,this.headers=t,this.fetch=Zt(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r;return sr(this,void 0,void 0,function*(){try{const{headers:i,body:n}=t;let o={},a;n&&(i&&!Object.prototype.hasOwnProperty.call(i,"Content-Type")||!i)&&(typeof Blob<"u"&&n instanceof Blob||n instanceof ArrayBuffer?(o["Content-Type"]="application/octet-stream",a=n):typeof n=="string"?(o["Content-Type"]="text/plain",a=n):typeof FormData<"u"&&n instanceof FormData?a=n:(o["Content-Type"]="application/json",a=JSON.stringify(n)));const h=yield this.fetch(`${this.url}/${e}`,{method:"POST",headers:Object.assign(Object.assign(Object.assign({},o),this.headers),i),body:a}).catch(f=>{throw new er(f)}),c=h.headers.get("x-relay-error");if(c&&c==="true")throw new tr(h);if(!h.ok)throw new rr(h);let l=((r=h.headers.get("Content-Type"))!==null&&r!==void 0?r:"text/plain").split(";")[0].trim(),u;return l==="application/json"?u=yield h.json():l==="application/octet-stream"?u=yield h.blob():l==="multipart/form-data"?u=yield h.formData():u=yield h.text(),{data:u,error:null}}catch(i){return{data:null,error:i}}})}}var nr=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function or(s){return s&&s.__esModule&&Object.prototype.hasOwnProperty.call(s,"default")?s.default:s}var fe={},ar={get exports(){return fe},set exports(s){fe=s}};(function(s,e){var t=typeof self<"u"?self:nr,r=function(){function n(){this.fetch=!1,this.DOMException=t.DOMException}return n.prototype=t,new n}();(function(n){(function(o){var a={searchParams:"URLSearchParams"in n,iterable:"Symbol"in n&&"iterator"in Symbol,blob:"FileReader"in n&&"Blob"in n&&function(){try{return new Blob,!0}catch{return!1}}(),formData:"FormData"in n,arrayBuffer:"ArrayBuffer"in n};function h(d){return d&&DataView.prototype.isPrototypeOf(d)}if(a.arrayBuffer)var c=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],l=ArrayBuffer.isView||function(d){return d&&c.indexOf(Object.prototype.toString.call(d))>-1};function u(d){if(typeof d!="string"&&(d=String(d)),/[^a-z0-9\-#$%&'*+.^_`|~]/i.test(d))throw new TypeError("Invalid character in header field name");return d.toLowerCase()}function f(d){return typeof d!="string"&&(d=String(d)),d}function m(d){var v={next:function(){var b=d.shift();return{done:b===void 0,value:b}}};return a.iterable&&(v[Symbol.iterator]=function(){return v}),v}function p(d){this.map={},d instanceof p?d.forEach(function(v,b){this.append(b,v)},this):Array.isArray(d)?d.forEach(function(v){this.append(v[0],v[1])},this):d&&Object.getOwnPropertyNames(d).forEach(function(v){this.append(v,d[v])},this)}p.prototype.append=function(d,v){d=u(d),v=f(v);var b=this.map[d];this.map[d]=b?b+", "+v:v},p.prototype.delete=function(d){delete this.map[u(d)]},p.prototype.get=function(d){return d=u(d),this.has(d)?this.map[d]:null},p.prototype.has=function(d){return this.map.hasOwnProperty(u(d))},p.prototype.set=function(d,v){this.map[u(d)]=f(v)},p.prototype.forEach=function(d,v){for(var b in this.map)this.map.hasOwnProperty(b)&&d.call(v,this.map[b],b,this)},p.prototype.keys=function(){var d=[];return this.forEach(function(v,b){d.push(b)}),m(d)},p.prototype.values=function(){var d=[];return this.forEach(function(v){d.push(v)}),m(d)},p.prototype.entries=function(){var d=[];return this.forEach(function(v,b){d.push([b,v])}),m(d)},a.iterable&&(p.prototype[Symbol.iterator]=p.prototype.entries);function _(d){if(d.bodyUsed)return Promise.reject(new TypeError("Already read"));d.bodyUsed=!0}function g(d){return new Promise(function(v,b){d.onload=function(){v(d.result)},d.onerror=function(){b(d.error)}})}function A(d){var v=new FileReader,b=g(v);return v.readAsArrayBuffer(d),b}function O(d){var v=new FileReader,b=g(v);return v.readAsText(d),b}function P(d){for(var v=new Uint8Array(d),b=new Array(v.length),I=0;I<v.length;I++)b[I]=String.fromCharCode(v[I]);return b.join("")}function R(d){if(d.slice)return d.slice(0);var v=new Uint8Array(d.byteLength);return v.set(new Uint8Array(d)),v.buffer}function ne(){return this.bodyUsed=!1,this._initBody=function(d){this._bodyInit=d,d?typeof d=="string"?this._bodyText=d:a.blob&&Blob.prototype.isPrototypeOf(d)?this._bodyBlob=d:a.formData&&FormData.prototype.isPrototypeOf(d)?this._bodyFormData=d:a.searchParams&&URLSearchParams.prototype.isPrototypeOf(d)?this._bodyText=d.toString():a.arrayBuffer&&a.blob&&h(d)?(this._bodyArrayBuffer=R(d.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):a.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(d)||l(d))?this._bodyArrayBuffer=R(d):this._bodyText=d=Object.prototype.toString.call(d):this._bodyText="",this.headers.get("content-type")||(typeof d=="string"?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):a.searchParams&&URLSearchParams.prototype.isPrototypeOf(d)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},a.blob&&(this.blob=function(){var d=_(this);if(d)return d;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){return this._bodyArrayBuffer?_(this)||Promise.resolve(this._bodyArrayBuffer):this.blob().then(A)}),this.text=function(){var d=_(this);if(d)return d;if(this._bodyBlob)return O(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(P(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},a.formData&&(this.formData=function(){return this.text().then(oi)}),this.json=function(){return this.text().then(JSON.parse)},this}var ii=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function ni(d){var v=d.toUpperCase();return ii.indexOf(v)>-1?v:d}function oe(d,v){v=v||{};var b=v.body;if(d instanceof oe){if(d.bodyUsed)throw new TypeError("Already read");this.url=d.url,this.credentials=d.credentials,v.headers||(this.headers=new p(d.headers)),this.method=d.method,this.mode=d.mode,this.signal=d.signal,!b&&d._bodyInit!=null&&(b=d._bodyInit,d.bodyUsed=!0)}else this.url=String(d);if(this.credentials=v.credentials||this.credentials||"same-origin",(v.headers||!this.headers)&&(this.headers=new p(v.headers)),this.method=ni(v.method||this.method||"GET"),this.mode=v.mode||this.mode||null,this.signal=v.signal||this.signal,this.referrer=null,(this.method==="GET"||this.method==="HEAD")&&b)throw new TypeError("Body not allowed for GET or HEAD requests");this._initBody(b)}oe.prototype.clone=function(){return new oe(this,{body:this._bodyInit})};function oi(d){var v=new FormData;return d.trim().split("&").forEach(function(b){if(b){var I=b.split("="),C=I.shift().replace(/\+/g," "),x=I.join("=").replace(/\+/g," ");v.append(decodeURIComponent(C),decodeURIComponent(x))}}),v}function ai(d){var v=new p,b=d.replace(/\r?\n[\t ]+/g," ");return b.split(/\r?\n/).forEach(function(I){var C=I.split(":"),x=C.shift().trim();if(x){var Be=C.join(":").trim();v.append(x,Be)}}),v}ne.call(oe.prototype);function K(d,v){v||(v={}),this.type="default",this.status=v.status===void 0?200:v.status,this.ok=this.status>=200&&this.status<300,this.statusText="statusText"in v?v.statusText:"OK",this.headers=new p(v.headers),this.url=v.url||"",this._initBody(d)}ne.call(K.prototype),K.prototype.clone=function(){return new K(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new p(this.headers),url:this.url})},K.error=function(){var d=new K(null,{status:0,statusText:""});return d.type="error",d};var ci=[301,302,303,307,308];K.redirect=function(d,v){if(ci.indexOf(v)===-1)throw new RangeError("Invalid status code");return new K(null,{status:v,headers:{location:d}})},o.DOMException=n.DOMException;try{new o.DOMException}catch{o.DOMException=function(v,b){this.message=v,this.name=b;var I=Error(v);this.stack=I.stack},o.DOMException.prototype=Object.create(Error.prototype),o.DOMException.prototype.constructor=o.DOMException}function at(d,v){return new Promise(function(b,I){var C=new oe(d,v);if(C.signal&&C.signal.aborted)return I(new o.DOMException("Aborted","AbortError"));var x=new XMLHttpRequest;function Be(){x.abort()}x.onload=function(){var je={status:x.status,statusText:x.statusText,headers:ai(x.getAllResponseHeaders()||"")};je.url="responseURL"in x?x.responseURL:je.headers.get("X-Request-URL");var ct="response"in x?x.response:x.responseText;b(new K(ct,je))},x.onerror=function(){I(new TypeError("Network request failed"))},x.ontimeout=function(){I(new TypeError("Network request failed"))},x.onabort=function(){I(new o.DOMException("Aborted","AbortError"))},x.open(C.method,C.url,!0),C.credentials==="include"?x.withCredentials=!0:C.credentials==="omit"&&(x.withCredentials=!1),"responseType"in x&&a.blob&&(x.responseType="blob"),C.headers.forEach(function(je,ct){x.setRequestHeader(ct,je)}),C.signal&&(C.signal.addEventListener("abort",Be),x.onreadystatechange=function(){x.readyState===4&&C.signal.removeEventListener("abort",Be)}),x.send(typeof C._bodyInit>"u"?null:C._bodyInit)})}return at.polyfill=!0,n.fetch||(n.fetch=at,n.Headers=p,n.Request=oe,n.Response=K),o.Headers=p,o.Request=oe,o.Response=K,o.fetch=at,Object.defineProperty(o,"__esModule",{value:!0}),o})({})})(r),r.fetch.ponyfill=!0,delete r.fetch.polyfill;var i=r;e=i.fetch,e.default=i.fetch,e.fetch=i.fetch,e.Headers=i.Headers,e.Request=i.Request,e.Response=i.Response,s.exports=e})(ar,fe);const We=or(fe),Ce=D({__proto__:null,default:We},[fe]);class cr{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.allowEmpty=e.allowEmpty,e.fetch?this.fetch=e.fetch:typeof fetch>"u"?this.fetch=We:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}then(e,t){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const r=this.fetch;let i=r(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async n=>{var o,a,h;let c=null,l=null,u=null,f=n.status,m=n.statusText;if(n.ok){if(this.method!=="HEAD"){const A=await n.text();A===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?l=A:l=JSON.parse(A))}const _=(o=this.headers.Prefer)===null||o===void 0?void 0:o.match(/count=(exact|planned|estimated)/),g=(a=n.headers.get("content-range"))===null||a===void 0?void 0:a.split("/");_&&g&&g.length>1&&(u=parseInt(g[1]))}else{const _=await n.text();try{c=JSON.parse(_),Array.isArray(c)&&n.status===404&&(l=[],c=null,f=200,m="OK")}catch{n.status===404&&_===""?(f=204,m="No Content"):c={message:_}}if(c&&this.allowEmpty&&(!((h=c==null?void 0:c.details)===null||h===void 0)&&h.includes("Results contain 0 rows"))&&(c=null,f=200,m="OK"),c&&this.shouldThrowOnError)throw c}return{error:c,data:l,count:u,status:f,statusText:m}});return this.shouldThrowOnError||(i=i.catch(n=>({error:{message:`FetchError: ${n.message}`,details:"",hint:"",code:n.code||""},data:null,count:null,status:0,statusText:""}))),i.then(e,t)}}class lr extends cr{select(e){let t=!1;const r=(e??"*").split("").map(i=>/\s/.test(i)&&!t?"":(i==='"'&&(t=!t),i)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:i}={}){const n=i?`${i}.order`:"order",o=this.url.searchParams.get(n);return this.url.searchParams.set(n,`${o?`${o},`:""}${e}.${t?"asc":"desc"}${r===void 0?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t}={}){const r=typeof t>"u"?"limit":`${t}.limit`;return this.url.searchParams.set(r,`${e}`),this}range(e,t,{foreignTable:r}={}){const i=typeof r>"u"?"offset":`${r}.offset`,n=typeof r>"u"?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${e}`),this.url.searchParams.set(n,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.headers.Accept="application/vnd.pgrst.object+json",this.allowEmpty=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:i=!1,wal:n=!1,format:o="text"}={}){const a=[e?"analyze":null,t?"verbose":null,r?"settings":null,i?"buffers":null,n?"wal":null].filter(Boolean).join("|"),h=this.headers.Accept;return this.headers.Accept=`application/vnd.pgrst.plan+${o}; for="${h}"; options=${a};`,o==="json"?this:this}rollback(){var e;return((e=this.headers.Prefer)!==null&&e!==void 0?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}class pe extends lr{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){const r=t.map(i=>typeof i=="string"&&new RegExp("[,()]").test(i)?`"${i}"`:`${i}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return typeof t=="string"?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return typeof t=="string"?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return typeof t=="string"?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:i}={}){let n="";i==="plain"?n="pl":i==="phrase"?n="ph":i==="websearch"&&(n="w");const o=r===void 0?"":`(${r})`;return this.url.searchParams.append(e,`${n}fts${o}.${t}`),this}match(e){return Object.entries(e).forEach(([t,r])=>{this.url.searchParams.append(t,`eq.${r}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t}={}){const r=t?`${t}.or`:"or";return this.url.searchParams.append(r,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}}class hr{constructor(e,{headers:t={},schema:r,fetch:i}){this.url=e,this.headers=t,this.schema=r,this.fetch=i}select(e,{head:t=!1,count:r}={}){const i=t?"HEAD":"GET";let n=!1;const o=(e??"*").split("").map(a=>/\s/.test(a)&&!n?"":(a==='"'&&(n=!n),a)).join("");return this.url.searchParams.set("select",o),r&&(this.headers.Prefer=`count=${r}`),new pe({method:i,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t}={}){const r="POST",i=[],n=e;if(t&&i.push(`count=${t}`),this.headers.Prefer&&i.unshift(this.headers.Prefer),this.headers.Prefer=i.join(","),Array.isArray(e)){const o=e.reduce((a,h)=>a.concat(Object.keys(h)),[]);if(o.length>0){const a=[...new Set(o)].map(h=>`"${h}"`);this.url.searchParams.set("columns",a.join(","))}}return new pe({method:r,url:this.url,headers:this.headers,schema:this.schema,body:n,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:i}={}){const n="POST",o=[`resolution=${r?"ignore":"merge"}-duplicates`];t!==void 0&&this.url.searchParams.set("on_conflict",t);const a=e;return i&&o.push(`count=${i}`),this.headers.Prefer&&o.unshift(this.headers.Prefer),this.headers.Prefer=o.join(","),new pe({method:n,url:this.url,headers:this.headers,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){const r="PATCH",i=[],n=e;return t&&i.push(`count=${t}`),this.headers.Prefer&&i.unshift(this.headers.Prefer),this.headers.Prefer=i.join(","),new pe({method:r,url:this.url,headers:this.headers,schema:this.schema,body:n,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){const t="DELETE",r=[];return e&&r.push(`count=${e}`),this.headers.Prefer&&r.unshift(this.headers.Prefer),this.headers.Prefer=r.join(","),new pe({method:t,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}const ur={"X-Client-Info":"postgrest-js/1.4.0"};class dr{constructor(e,{headers:t={},schema:r,fetch:i}={}){this.url=e,this.headers=Object.assign(Object.assign({},ur),t),this.schema=r,this.fetch=i}from(e){const t=new URL(`${this.url}/${e}`);return new hr(t,{headers:Object.assign({},this.headers),schema:this.schema,fetch:this.fetch})}rpc(e,t={},{head:r=!1,count:i}={}){let n;const o=new URL(`${this.url}/rpc/${e}`);let a;r?(n="HEAD",Object.entries(t).forEach(([c,l])=>{o.searchParams.append(c,`${l}`)})):(n="POST",a=t);const h=Object.assign({},this.headers);return i&&(h.Prefer=`count=${i}`),new pe({method:n,url:o,headers:h,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}}var Qe,mt;function fr(){if(mt)return Qe;mt=1;var s=function(){if(typeof self=="object"&&self)return self;if(typeof window=="object"&&window)return window;throw new Error("Unable to resolve global `this`")};return Qe=function(){if(this)return this;if(typeof globalThis=="object"&&globalThis)return globalThis;try{Object.defineProperty(Object.prototype,"__global__",{get:function(){return this},configurable:!0})}catch{return s()}try{return __global__||s()}finally{delete Object.prototype.__global__}}(),Qe}var pr={name:"websocket",description:"Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.",keywords:["websocket","websockets","socket","networking","comet","push","RFC-6455","realtime","server","client"],author:"Brian McKelvey <<EMAIL>> (https://github.com/theturtle32)",contributors:["Iñaki Baz Castillo <<EMAIL>> (http://dev.sipdoc.net)"],version:"1.0.34",repository:{type:"git",url:"https://github.com/theturtle32/WebSocket-Node.git"},homepage:"https://github.com/theturtle32/WebSocket-Node",engines:{node:">=4.0.0"},dependencies:{bufferutil:"^4.0.1",debug:"^2.2.0","es5-ext":"^0.10.50","typedarray-to-buffer":"^3.1.5","utf-8-validate":"^5.0.2",yaeti:"^0.0.6"},devDependencies:{"buffer-equal":"^1.0.0",gulp:"^4.0.2","gulp-jshint":"^2.0.4","jshint-stylish":"^2.2.1",jshint:"^2.0.0",tape:"^4.9.1"},config:{verbose:!1},scripts:{test:"tape test/unit/*.js",gulp:"gulp"},main:"index",directories:{lib:"./lib"},browser:"lib/browser.js",license:"Apache-2.0"}.version,te;if(typeof globalThis=="object")te=globalThis;else try{te=fr()}catch{}finally{if(!te&&typeof window<"u"&&(te=window),!te)throw new Error("Could not determine global this")}var Te=te.WebSocket||te.MozWebSocket,vr=pr;function _t(s,e){var t;return e?t=new Te(s,e):t=new Te(s),t}Te&&["CONNECTING","OPEN","CLOSING","CLOSED"].forEach(function(s){Object.defineProperty(_t,s,{get:function(){return Te[s]}})});var mr={w3cwebsocket:Te?_t:null,version:vr};const _r={"X-Client-Info":"realtime-js/2.6.0"},gr="1.0.0",gt=1e4,yr=1e3;var Ee;(function(s){s[s.connecting=0]="connecting",s[s.open=1]="open",s[s.closing=2]="closing",s[s.closed=3]="closed"})(Ee||(Ee={}));var L;(function(s){s.closed="closed",s.errored="errored",s.joined="joined",s.joining="joining",s.leaving="leaving"})(L||(L={}));var H;(function(s){s.close="phx_close",s.error="phx_error",s.join="phx_join",s.reply="phx_reply",s.leave="phx_leave",s.access_token="access_token"})(H||(H={}));var Xe;(function(s){s.websocket="websocket"})(Xe||(Xe={}));var re;(function(s){s.Connecting="connecting",s.Open="open",s.Closing="closing",s.Closed="closed"})(re||(re={}));class yt{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}class br{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t(typeof e=="string"?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){const i=t.getUint8(1),n=t.getUint8(2);let o=this.HEADER_LENGTH+2;const a=r.decode(e.slice(o,o+i));o=o+i;const h=r.decode(e.slice(o,o+n));o=o+n;const c=JSON.parse(r.decode(e.slice(o,e.byteLength)));return{ref:null,topic:a,event:h,payload:c}}}class Ye{constructor(e,t,r={},i=gt){this.channel=e,this.event=t,this.payload=r,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null,this.rateLimited=!1}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){if(this._hasReceived("timeout"))return;this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()})==="rate limited"&&(this.rateLimited=!0)}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t((r=this.receivedResp)===null||r===void 0?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const e=t=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=t,this._matchReceive(t)};this.channel._on(this.refEvent,{},e),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(r=>r.status===e).forEach(r=>r.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var bt;(function(s){s.SYNC="sync",s.JOIN="join",s.LEAVE="leave"})(bt||(bt={}));class Oe{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(t==null?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},i=>{const{onJoin:n,onLeave:o,onSync:a}=this.caller;this.joinRef=this.channel._joinRef(),this.state=Oe.syncState(this.state,i,n,o),this.pendingDiffs.forEach(h=>{this.state=Oe.syncDiff(this.state,h,n,o)}),this.pendingDiffs=[],a()}),this.channel._on(r.diff,{},i=>{const{onJoin:n,onLeave:o,onSync:a}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(i):(this.state=Oe.syncDiff(this.state,i,n,o),a())}),this.onJoin((i,n,o)=>{this.channel._trigger("presence",{event:"join",key:i,currentPresences:n,newPresences:o})}),this.onLeave((i,n,o)=>{this.channel._trigger("presence",{event:"leave",key:i,currentPresences:n,leftPresences:o})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,i){const n=this.cloneDeep(e),o=this.transformState(t),a={},h={};return this.map(n,(c,l)=>{o[c]||(h[c]=l)}),this.map(o,(c,l)=>{const u=n[c];if(u){const f=l.map(g=>g.presence_ref),m=u.map(g=>g.presence_ref),p=l.filter(g=>m.indexOf(g.presence_ref)<0),_=u.filter(g=>f.indexOf(g.presence_ref)<0);p.length>0&&(a[c]=p),_.length>0&&(h[c]=_)}else a[c]=l}),this.syncDiff(n,{joins:a,leaves:h},r,i)}static syncDiff(e,t,r,i){const{joins:n,leaves:o}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),i||(i=()=>{}),this.map(n,(a,h)=>{var c;const l=(c=e[a])!==null&&c!==void 0?c:[];if(e[a]=this.cloneDeep(h),l.length>0){const u=e[a].map(m=>m.presence_ref),f=l.filter(m=>u.indexOf(m.presence_ref)<0);e[a].unshift(...f)}r(a,l,h)}),this.map(o,(a,h)=>{let c=e[a];if(!c)return;const l=h.map(u=>u.presence_ref);c=c.filter(u=>l.indexOf(u.presence_ref)<0),e[a]=c,i(a,c,h),c.length===0&&delete e[a]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,r)=>{const i=e[r];return"metas"in i?t[r]=i.metas.map(n=>(n.presence_ref=n.phx_ref,delete n.phx_ref,delete n.phx_ref_prev,n)):t[r]=i,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var k;(function(s){s.abstime="abstime",s.bool="bool",s.date="date",s.daterange="daterange",s.float4="float4",s.float8="float8",s.int2="int2",s.int4="int4",s.int4range="int4range",s.int8="int8",s.int8range="int8range",s.json="json",s.jsonb="jsonb",s.money="money",s.numeric="numeric",s.oid="oid",s.reltime="reltime",s.text="text",s.time="time",s.timestamp="timestamp",s.timestamptz="timestamptz",s.timetz="timetz",s.tsrange="tsrange",s.tstzrange="tstzrange"})(k||(k={}));const wt=(s,e,t={})=>{var r;const i=(r=t.skipTypes)!==null&&r!==void 0?r:[];return Object.keys(e).reduce((n,o)=>(n[o]=wr(o,s,e,i),n),{})},wr=(s,e,t,r)=>{const i=e.find(a=>a.name===s),n=i==null?void 0:i.type,o=t[s];return n&&!r.includes(n)?$t(n,o):Ze(o)},$t=(s,e)=>{if(s.charAt(0)==="_"){const t=s.slice(1,s.length);return Or(e,t)}switch(s){case k.bool:return $r(e);case k.float4:case k.float8:case k.int2:case k.int4:case k.int8:case k.numeric:case k.oid:return Tr(e);case k.json:case k.jsonb:return Er(e);case k.timestamp:return xr(e);case k.abstime:case k.date:case k.daterange:case k.int4range:case k.int8range:case k.money:case k.reltime:case k.text:case k.time:case k.timestamptz:case k.timetz:case k.tsrange:case k.tstzrange:return Ze(e);default:return Ze(e)}},Ze=s=>s,$r=s=>{switch(s){case"t":return!0;case"f":return!1;default:return s}},Tr=s=>{if(typeof s=="string"){const e=parseFloat(s);if(!Number.isNaN(e))return e}return s},Er=s=>{if(typeof s=="string")try{return JSON.parse(s)}catch(e){return console.log(`JSON parse error: ${e}`),s}return s},Or=(s,e)=>{if(typeof s!="string")return s;const t=s.length-1,r=s[t];if(s[0]==="{"&&r==="}"){let n;const o=s.slice(1,t);try{n=JSON.parse("["+o+"]")}catch{n=o?o.split(","):[]}return n.map(a=>$t(e,a))}return s},xr=s=>typeof s=="string"?s.replace(" ","T"):s;var Tt=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(l){try{c(r.next(l))}catch(u){o(u)}}function h(l){try{c(r.throw(l))}catch(u){o(u)}}function c(l){l.done?n(l.value):i(l.value).then(a,h)}c((r=r.apply(s,e||[])).next())})},Et;(function(s){s.ALL="*",s.INSERT="INSERT",s.UPDATE="UPDATE",s.DELETE="DELETE"})(Et||(Et={}));var Ot;(function(s){s.BROADCAST="broadcast",s.PRESENCE="presence",s.POSTGRES_CHANGES="postgres_changes"})(Ot||(Ot={}));var xt;(function(s){s.SUBSCRIBED="SUBSCRIBED",s.TIMED_OUT="TIMED_OUT",s.CLOSED="CLOSED",s.CHANNEL_ERROR="CHANNEL_ERROR"})(xt||(xt={}));class et{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=L.closed,this.joinedOnce=!1,this.pushBuffer=[],this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""}},t.config),this.timeout=this.socket.timeout,this.joinPush=new Ye(this,H.join,this.params,this.timeout),this.rejoinTimer=new yt(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=L.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(i=>i.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=L.closed,this.socket._remove(this)}),this._onError(i=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,i),this.state=L.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=L.errored,this.rejoinTimer.scheduleTimeout())}),this._on(H.reply,{},(i,n)=>{this._trigger(this._replyEventName(n),i)}),this.presence=new Oe(this)}subscribe(e,t=this.timeout){var r,i;if(this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:n,presence:o}}=this.params;this._onError(c=>e&&e("CHANNEL_ERROR",c)),this._onClose(()=>e&&e("CLOSED"));const a={},h={broadcast:n,presence:o,postgres_changes:(i=(r=this.bindings.postgres_changes)===null||r===void 0?void 0:r.map(c=>c.filter))!==null&&i!==void 0?i:[]};this.socket.accessToken&&(a.access_token=this.socket.accessToken),this.updateJoinPayload(Object.assign({config:h},a)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",({postgres_changes:c})=>{var l;if(this.socket.accessToken&&this.socket.setAuth(this.socket.accessToken),c===void 0){e&&e("SUBSCRIBED");return}else{const u=this.bindings.postgres_changes,f=(l=u==null?void 0:u.length)!==null&&l!==void 0?l:0,m=[];for(let p=0;p<f;p++){const _=u[p],{filter:{event:g,schema:A,table:O,filter:P}}=_,R=c&&c[p];if(R&&R.event===g&&R.schema===A&&R.table===O&&R.filter===P)m.push(Object.assign(Object.assign({},_),{id:R.id}));else{this.unsubscribe(),e&&e("CHANNEL_ERROR",new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=m,e&&e("SUBSCRIBED");return}}).receive("error",c=>{e&&e("CHANNEL_ERROR",new Error(JSON.stringify(Object.values(c).join(", ")||"error")))}).receive("timeout",()=>{e&&e("TIMED_OUT")})}return this}presenceState(){return this.presence.state}track(e,t={}){return Tt(this,void 0,void 0,function*(){return yield this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)})}untrack(e={}){return Tt(this,void 0,void 0,function*(){return yield this.send({type:"presence",event:"untrack"},e)})}on(e,t,r){return this._on(e,t,r)}send(e,t={}){return new Promise(r=>{var i,n,o;const a=this._push(e.type,e,t.timeout||this.timeout);a.rateLimited&&r("rate limited"),e.type==="broadcast"&&!(!((o=(n=(i=this.params)===null||i===void 0?void 0:i.config)===null||n===void 0?void 0:n.broadcast)===null||o===void 0)&&o.ack)&&r("ok"),a.receive("ok",()=>r("ok")),a.receive("timeout",()=>r("timed out"))})}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=L.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(H.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(r=>{const i=new Ye(this,H.leave,{},e);i.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),i.send(),this._canPush()||i.trigger("ok",{})})}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let i=new Ye(this,e,t,r);return this._canPush()?i.send():(i.startTimeout(),this.pushBuffer.push(i)),i}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var i,n;const o=e.toLocaleLowerCase(),{close:a,error:h,leave:c,join:l}=H;if(r&&[a,h,c,l].indexOf(o)>=0&&r!==this._joinRef())return;let f=this._onMessage(o,t,r);if(t&&!f)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?(i=this.bindings.postgres_changes)===null||i===void 0||i.filter(m=>{var p,_,g;return((p=m.filter)===null||p===void 0?void 0:p.event)==="*"||((g=(_=m.filter)===null||_===void 0?void 0:_.event)===null||g===void 0?void 0:g.toLocaleLowerCase())===o}).map(m=>m.callback(f,r)):(n=this.bindings[o])===null||n===void 0||n.filter(m=>{var p,_,g,A,O,P;if(["broadcast","presence","postgres_changes"].includes(o))if("id"in m){const R=m.id,ne=(p=m.filter)===null||p===void 0?void 0:p.event;return R&&((_=t.ids)===null||_===void 0?void 0:_.includes(R))&&(ne==="*"||(ne==null?void 0:ne.toLocaleLowerCase())===((g=t.data)===null||g===void 0?void 0:g.type.toLocaleLowerCase()))}else{const R=(O=(A=m==null?void 0:m.filter)===null||A===void 0?void 0:A.event)===null||O===void 0?void 0:O.toLocaleLowerCase();return R==="*"||R===((P=t==null?void 0:t.event)===null||P===void 0?void 0:P.toLocaleLowerCase())}else return m.type.toLocaleLowerCase()===o}).map(m=>{if(typeof f=="object"&&"ids"in f){const p=f.data,{schema:_,table:g,commit_timestamp:A,type:O,errors:P}=p;f=Object.assign(Object.assign({},{schema:_,table:g,commit_timestamp:A,eventType:O,new:{},old:{},errors:P}),this._getPayloadRecords(p))}m.callback(f,r)})}_isClosed(){return this.state===L.closed}_isJoined(){return this.state===L.joined}_isJoining(){return this.state===L.joining}_isLeaving(){return this.state===L.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){const i=e.toLocaleLowerCase(),n={type:i,filter:t,callback:r};return this.bindings[i]?this.bindings[i].push(n):this.bindings[i]=[n],this}_off(e,t){const r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(i=>{var n;return!(((n=i.type)===null||n===void 0?void 0:n.toLocaleLowerCase())===r&&et.isEqual(i.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(H.close,{},e)}_onError(e){this._on(H.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=L.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return(e.type==="INSERT"||e.type==="UPDATE")&&(t.new=wt(e.columns,e.record)),(e.type==="UPDATE"||e.type==="DELETE")&&(t.old=wt(e.columns,e.old_record)),t}}var kr=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(l){try{c(r.next(l))}catch(u){o(u)}}function h(l){try{c(r.throw(l))}catch(u){o(u)}}function c(l){l.done?n(l.value):i(l.value).then(a,h)}c((r=r.apply(s,e||[])).next())})};const Sr=()=>{};class jr{constructor(e,t){var r;this.accessToken=null,this.channels=[],this.endPoint="",this.headers=_r,this.params={},this.timeout=gt,this.transport=mr.w3cwebsocket,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=Sr,this.conn=null,this.sendBuffer=[],this.serializer=new br,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.eventsPerSecondLimitMs=100,this.inThrottle=!1,this.endPoint=`${e}/${Xe.websocket}`,t!=null&&t.params&&(this.params=t.params),t!=null&&t.headers&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),t!=null&&t.timeout&&(this.timeout=t.timeout),t!=null&&t.logger&&(this.logger=t.logger),t!=null&&t.transport&&(this.transport=t.transport),t!=null&&t.heartbeatIntervalMs&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const i=(r=t==null?void 0:t.params)===null||r===void 0?void 0:r.eventsPerSecond;i&&(this.eventsPerSecondLimitMs=Math.floor(1e3/i)),this.reconnectAfterMs=t!=null&&t.reconnectAfterMs?t.reconnectAfterMs:n=>[1e3,2e3,5e3,1e4][n-1]||1e4,this.encode=t!=null&&t.encode?t.encode:(n,o)=>o(JSON.stringify(n)),this.decode=t!=null&&t.decode?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new yt(()=>kr(this,void 0,void 0,function*(){this.disconnect(),this.connect()}),this.reconnectAfterMs)}connect(){this.conn||(this.conn=new this.transport(this._endPointURL(),[],null,this.headers),this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e)))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,t??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}removeChannel(e){return e.unsubscribe().then(t=>(this.channels.length===0&&this.disconnect(),t))}removeAllChannels(){return Promise.all(this.channels.map(e=>e.unsubscribe())).then(e=>(this.disconnect(),e))}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case Ee.connecting:return re.Connecting;case Ee.open:return re.Open;case Ee.closing:return re.Closing;default:return re.Closed}}isConnected(){return this.connectionState()===re.Open}channel(e,t={config:{}}){this.isConnected()||this.connect();const r=new et(`realtime:${e}`,t,this);return this.channels.push(r),r}push(e){const{topic:t,event:r,payload:i,ref:n}=e;let o=()=>{this.encode(e,a=>{var h;(h=this.conn)===null||h===void 0||h.send(a)})};if(this.log("push",`${t} ${r} (${n})`,i),this.isConnected())if(["broadcast","presence","postgres_changes"].includes(r)){if(this._throttle(o)())return"rate limited"}else o();else this.sendBuffer.push(o)}setAuth(e){this.accessToken=e,this.channels.forEach(t=>{e&&t.updateJoinPayload({access_token:e}),t.joinedOnce&&t._isJoined()&&t._push(H.access_token,{access_token:e})})}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(r=>r.topic===e&&(r._isJoined()||r._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t._joinRef()!==e._joinRef())}_endPointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:gr}))}_onConnMessage(e){this.decode(e.data,t=>{let{topic:r,event:i,payload:n,ref:o}=t;(o&&o===this.pendingHeartbeatRef||i===(n==null?void 0:n.type))&&(this.pendingHeartbeatRef=null),this.log("receive",`${n.status||""} ${r} ${i} ${o&&"("+o+")"||""}`,n),this.channels.filter(a=>a._isMember(r)).forEach(a=>a._trigger(i,n,o)),this.stateChangeCallbacks.message.forEach(a=>a(t))})}_onConnOpen(){this.log("transport",`connected to ${this._endPointURL()}`),this._flushSendBuffer(),this.reconnectTimer.reset(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this._sendHeartbeat(),this.heartbeatIntervalMs),this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(H.error))}_appendParams(e,t){if(Object.keys(t).length===0)return e;const r=e.match(/\?/)?"&":"?",i=new URLSearchParams(t);return`${e}${r}${i}`}_flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),(e=this.conn)===null||e===void 0||e.close(yr,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth(this.accessToken)}}_throttle(e,t=this.eventsPerSecondLimitMs){return()=>this.inThrottle?!0:(e(),t>0&&(this.inThrottle=!0,setTimeout(()=>{this.inThrottle=!1},t)),!1)}}class kt extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function U(s){return typeof s=="object"&&s!==null&&"__isStorageError"in s}class Ar extends kt{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class Rr extends kt{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var St=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(l){try{c(r.next(l))}catch(u){o(u)}}function h(l){try{c(r.throw(l))}catch(u){o(u)}}function c(l){l.done?n(l.value):i(l.value).then(a,h)}c((r=r.apply(s,e||[])).next())})};const jt=s=>{let e;return s?e=s:typeof fetch>"u"?e=(...t)=>St(void 0,void 0,void 0,function*(){return yield(yield Promise.resolve().then(()=>Ce)).fetch(...t)}):e=fetch,(...t)=>e(...t)},Pr=()=>St(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield Promise.resolve().then(()=>Ce)).Response:Response});var ve=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(l){try{c(r.next(l))}catch(u){o(u)}}function h(l){try{c(r.throw(l))}catch(u){o(u)}}function c(l){l.done?n(l.value):i(l.value).then(a,h)}c((r=r.apply(s,e||[])).next())})};const At=s=>s.msg||s.message||s.error_description||s.error||JSON.stringify(s),Cr=(s,e)=>ve(void 0,void 0,void 0,function*(){const t=yield Pr();s instanceof t?s.json().then(r=>{e(new Ar(At(r),s.status||500))}):e(new Rr(At(s),s))}),Ir=(s,e,t,r)=>{const i={method:s,headers:(e==null?void 0:e.headers)||{}};return s==="GET"?i:(i.headers=Object.assign({"Content-Type":"application/json"},e==null?void 0:e.headers),i.body=JSON.stringify(r),Object.assign(Object.assign({},i),t))};function Ie(s,e,t,r,i,n){return ve(this,void 0,void 0,function*(){return new Promise((o,a)=>{s(t,Ir(e,r,i,n)).then(h=>{if(!h.ok)throw h;return r!=null&&r.noResolveJson?h:h.json()}).then(h=>o(h)).catch(h=>Cr(h,a))})})}function tt(s,e,t,r){return ve(this,void 0,void 0,function*(){return Ie(s,"GET",e,t,r)})}function se(s,e,t,r,i){return ve(this,void 0,void 0,function*(){return Ie(s,"POST",e,r,i,t)})}function Ur(s,e,t,r,i){return ve(this,void 0,void 0,function*(){return Ie(s,"PUT",e,r,i,t)})}function Rt(s,e,t,r,i){return ve(this,void 0,void 0,function*(){return Ie(s,"DELETE",e,r,i,t)})}var z=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(l){try{c(r.next(l))}catch(u){o(u)}}function h(l){try{c(r.throw(l))}catch(u){o(u)}}function c(l){l.done?n(l.value):i(l.value).then(a,h)}c((r=r.apply(s,e||[])).next())})};const Dr={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Lr={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class Fr{constructor(e,t={},r,i){this.url=e,this.headers=t,this.bucketId=r,this.fetch=jt(i)}uploadOrUpdate(e,t,r,i){return z(this,void 0,void 0,function*(){try{let n;const o=Object.assign(Object.assign({},Lr),i),a=Object.assign(Object.assign({},this.headers),e==="POST"&&{"x-upsert":String(o.upsert)});typeof Blob<"u"&&r instanceof Blob?(n=new FormData,n.append("cacheControl",o.cacheControl),n.append("",r)):typeof FormData<"u"&&r instanceof FormData?(n=r,n.append("cacheControl",o.cacheControl)):(n=r,a["cache-control"]=`max-age=${o.cacheControl}`,a["content-type"]=o.contentType);const h=this._removeEmptyFolders(t),c=this._getFinalPath(h),l=yield this.fetch(`${this.url}/object/${c}`,{method:e,body:n,headers:a});return l.ok?{data:{path:h},error:null}:{data:null,error:yield l.json()}}catch(n){if(U(n))return{data:null,error:n};throw n}})}upload(e,t,r){return z(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}update(e,t,r){return z(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t){return z(this,void 0,void 0,function*(){try{return{data:yield se(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t},{headers:this.headers}),error:null}}catch(r){if(U(r))return{data:null,error:r};throw r}})}copy(e,t){return z(this,void 0,void 0,function*(){try{return{data:{path:(yield se(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t},{headers:this.headers})).Key},error:null}}catch(r){if(U(r))return{data:null,error:r};throw r}})}createSignedUrl(e,t,r){return z(this,void 0,void 0,function*(){try{let i=this._getFinalPath(e),n=yield se(this.fetch,`${this.url}/object/sign/${i}`,Object.assign({expiresIn:t},r!=null&&r.transform?{transform:r.transform}:{}),{headers:this.headers});const o=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return n={signedUrl:encodeURI(`${this.url}${n.signedURL}${o}`)},{data:n,error:null}}catch(i){if(U(i))return{data:null,error:i};throw i}})}createSignedUrls(e,t,r){return z(this,void 0,void 0,function*(){try{const i=yield se(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),n=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return{data:i.map(o=>Object.assign(Object.assign({},o),{signedUrl:o.signedURL?encodeURI(`${this.url}${o.signedURL}${n}`):null})),error:null}}catch(i){if(U(i))return{data:null,error:i};throw i}})}download(e,t){return z(this,void 0,void 0,function*(){const i=typeof(t==null?void 0:t.transform)<"u"?"render/image/authenticated":"object",n=this.transformOptsToQueryString((t==null?void 0:t.transform)||{}),o=n?`?${n}`:"";try{const a=this._getFinalPath(e);return{data:yield(yield tt(this.fetch,`${this.url}/${i}/${a}${o}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(a){if(U(a))return{data:null,error:a};throw a}})}getPublicUrl(e,t){const r=this._getFinalPath(e),i=[],n=t!=null&&t.download?`download=${t.download===!0?"":t.download}`:"";n!==""&&i.push(n);const a=typeof(t==null?void 0:t.transform)<"u"?"render/image":"object",h=this.transformOptsToQueryString((t==null?void 0:t.transform)||{});h!==""&&i.push(h);let c=i.join("&");return c!==""&&(c=`?${c}`),{data:{publicUrl:encodeURI(`${this.url}/${a}/public/${r}${c}`)}}}remove(e){return z(this,void 0,void 0,function*(){try{return{data:yield Rt(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(U(t))return{data:null,error:t};throw t}})}list(e,t,r){return z(this,void 0,void 0,function*(){try{const i=Object.assign(Object.assign(Object.assign({},Dr),t),{prefix:e||""});return{data:yield se(this.fetch,`${this.url}/object/list/${this.bucketId}`,i,{headers:this.headers},r),error:null}}catch(i){if(U(i))return{data:null,error:i};throw i}})}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const Br={"X-Client-Info":"storage-js/2.3.0"};var me=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(l){try{c(r.next(l))}catch(u){o(u)}}function h(l){try{c(r.throw(l))}catch(u){o(u)}}function c(l){l.done?n(l.value):i(l.value).then(a,h)}c((r=r.apply(s,e||[])).next())})};class Nr{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},Br),t),this.fetch=jt(r)}listBuckets(){return me(this,void 0,void 0,function*(){try{return{data:yield tt(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(U(e))return{data:null,error:e};throw e}})}getBucket(e){return me(this,void 0,void 0,function*(){try{return{data:yield tt(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(U(t))return{data:null,error:t};throw t}})}createBucket(e,t={public:!1}){return me(this,void 0,void 0,function*(){try{return{data:yield se(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public},{headers:this.headers}),error:null}}catch(r){if(U(r))return{data:null,error:r};throw r}})}updateBucket(e,t){return me(this,void 0,void 0,function*(){try{return{data:yield Ur(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public},{headers:this.headers}),error:null}}catch(r){if(U(r))return{data:null,error:r};throw r}})}emptyBucket(e){return me(this,void 0,void 0,function*(){try{return{data:yield se(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(U(t))return{data:null,error:t};throw t}})}deleteBucket(e){return me(this,void 0,void 0,function*(){try{return{data:yield Rt(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(U(t))return{data:null,error:t};throw t}})}}class Mr extends Nr{constructor(e,t={},r){super(e,t,r)}from(e){return new Fr(this.url,this.headers,e,this.fetch)}}const Hr={"X-Client-Info":"supabase-js/2.7.1"};var qr=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(l){try{c(r.next(l))}catch(u){o(u)}}function h(l){try{c(r.throw(l))}catch(u){o(u)}}function c(l){l.done?n(l.value):i(l.value).then(a,h)}c((r=r.apply(s,e||[])).next())})};const Jr=s=>{let e;return s?e=s:typeof fetch>"u"?e=We:e=fetch,(...t)=>e(...t)},zr=()=>typeof Headers>"u"?fe.Headers:Headers,Gr=(s,e,t)=>{const r=Jr(t),i=zr();return(n,o)=>qr(void 0,void 0,void 0,function*(){var a;const h=(a=yield e())!==null&&a!==void 0?a:s;let c=new i(o==null?void 0:o.headers);return c.has("apikey")||c.set("apikey",s),c.has("Authorization")||c.set("Authorization",`Bearer ${h}`),r(n,Object.assign(Object.assign({},o),{headers:c}))})};function Kr(s){return s.replace(/\/$/,"")}function Vr(s,e){const{db:t,auth:r,realtime:i,global:n}=s,{db:o,auth:a,realtime:h,global:c}=e;return{db:Object.assign(Object.assign({},o),t),auth:Object.assign(Object.assign({},a),r),realtime:Object.assign(Object.assign({},h),i),global:Object.assign(Object.assign({},c),n)}}var xe=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(l){try{c(r.next(l))}catch(u){o(u)}}function h(l){try{c(r.throw(l))}catch(u){o(u)}}function c(l){l.done?n(l.value):i(l.value).then(a,h)}c((r=r.apply(s,e||[])).next())})};function Wr(s){return Math.round(Date.now()/1e3)+s}function Qr(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(s){const e=Math.random()*16|0;return(s=="x"?e:e&3|8).toString(16)})}const W=()=>typeof document<"u";function M(s,e){var t;e||(e=((t=window==null?void 0:window.location)===null||t===void 0?void 0:t.href)||""),s=s.replace(/[\[\]]/g,"\\$&");const r=new RegExp("[?&#]"+s+"(=([^&#]*)|&|#|$)"),i=r.exec(e);return i?i[2]?decodeURIComponent(i[2].replace(/\+/g," ")):"":null}const Pt=s=>{let e;return s?e=s:typeof fetch>"u"?e=(...t)=>xe(void 0,void 0,void 0,function*(){return yield(yield Promise.resolve().then(()=>Ce)).fetch(...t)}):e=fetch,(...t)=>e(...t)},Xr=s=>typeof s=="object"&&s!==null&&"status"in s&&"ok"in s&&"json"in s&&typeof s.json=="function",Yr=(s,e,t)=>xe(void 0,void 0,void 0,function*(){yield s.setItem(e,JSON.stringify(t))}),Ct=(s,e)=>xe(void 0,void 0,void 0,function*(){const t=yield s.getItem(e);if(!t)return null;try{return JSON.parse(t)}catch{return t}}),Zr=(s,e)=>xe(void 0,void 0,void 0,function*(){yield s.removeItem(e)});function es(s){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";let t="",r,i,n,o,a,h,c,l=0;for(s=s.replace("-","+").replace("_","/");l<s.length;)o=e.indexOf(s.charAt(l++)),a=e.indexOf(s.charAt(l++)),h=e.indexOf(s.charAt(l++)),c=e.indexOf(s.charAt(l++)),r=o<<2|a>>4,i=(a&15)<<4|h>>2,n=(h&3)<<6|c,t=t+String.fromCharCode(r),h!=64&&i!=0&&(t=t+String.fromCharCode(i)),c!=64&&n!=0&&(t=t+String.fromCharCode(n));return t}class Ue{constructor(){this.promise=new Ue.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}Ue.promiseConstructor=Promise;function It(s){const e=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}=?$|[a-z0-9_-]{2}(==)?$)$/i,t=s.split(".");if(t.length!==3)throw new Error("JWT is not valid: not a JWT structure");if(!e.test(t[1]))throw new Error("JWT is not valid: payload is not in base64url format");const r=t[1];return JSON.parse(es(r))}function ts(s){return new Promise(e=>{setTimeout(()=>e(null),s)})}function rs(s,e){return new Promise((r,i)=>{xe(this,void 0,void 0,function*(){for(let n=0;n<1/0;n++)try{const o=yield s(n);if(!e(n,null,o)){r(o);return}}catch(o){if(!e(n,o)){i(o);return}}})})}class rt extends Error{constructor(e,t){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t}}function E(s){return typeof s=="object"&&s!==null&&"__isAuthError"in s}class ss extends rt{constructor(e,t){super(e,t),this.name="AuthApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}function is(s){return E(s)&&s.name==="AuthApiError"}class Ut extends rt{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class De extends rt{constructor(e,t,r){super(e),this.name=t,this.status=r}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class ke extends De{constructor(){super("Auth session missing!","AuthSessionMissingError",400)}}class st extends De{constructor(e){super(e,"AuthInvalidCredentialsError",400)}}class Q extends De{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class it extends De{constructor(e,t){super(e,"AuthRetryableFetchError",t)}}var nt=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(l){try{c(r.next(l))}catch(u){o(u)}}function h(l){try{c(r.throw(l))}catch(u){o(u)}}function c(l){l.done?n(l.value):i(l.value).then(a,h)}c((r=r.apply(s,e||[])).next())})},ns=globalThis&&globalThis.__rest||function(s,e){var t={};for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&e.indexOf(r)<0&&(t[r]=s[r]);if(s!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(s);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(s,r[i])&&(t[r[i]]=s[r[i]]);return t};const Le=s=>s.msg||s.message||s.error_description||s.error||JSON.stringify(s),os=(s,e)=>nt(void 0,void 0,void 0,function*(){const t=[502,503,504];Xr(s)?t.includes(s.status)?e(new it(Le(s),s.status)):s.json().then(r=>{e(new ss(Le(r),s.status||500))}).catch(r=>{e(new Ut(Le(r),r))}):e(new it(Le(s),0))}),as=(s,e,t,r)=>{const i={method:s,headers:(e==null?void 0:e.headers)||{}};return s==="GET"?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},e==null?void 0:e.headers),i.body=JSON.stringify(r),Object.assign(Object.assign({},i),t))};function S(s,e,t,r){var i;return nt(this,void 0,void 0,function*(){const n=Object.assign({},r==null?void 0:r.headers);r!=null&&r.jwt&&(n.Authorization=`Bearer ${r.jwt}`);const o=(i=r==null?void 0:r.query)!==null&&i!==void 0?i:{};r!=null&&r.redirectTo&&(o.redirect_to=r.redirectTo);const a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",h=yield cs(s,e,t+a,{headers:n,noResolveJson:r==null?void 0:r.noResolveJson},{},r==null?void 0:r.body);return r!=null&&r.xform?r==null?void 0:r.xform(h):{data:Object.assign({},h),error:null}})}function cs(s,e,t,r,i,n){return nt(this,void 0,void 0,function*(){return new Promise((o,a)=>{s(t,as(e,r,i,n)).then(h=>{if(!h.ok)throw h;return r!=null&&r.noResolveJson?h:h.json()}).then(h=>o(h)).catch(h=>os(h,a))})})}function _e(s){var e;let t=null;ds(s)&&(t=Object.assign({},s),t.expires_at=Wr(s.expires_in));const r=(e=s.user)!==null&&e!==void 0?e:s;return{data:{session:t,user:r},error:null}}function ie(s){var e;return{data:{user:(e=s.user)!==null&&e!==void 0?e:s},error:null}}function ls(s){return{data:s,error:null}}function hs(s){const{action_link:e,email_otp:t,hashed_token:r,redirect_to:i,verification_type:n}=s,o=ns(s,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),a={action_link:e,email_otp:t,hashed_token:r,redirect_to:i,verification_type:n},h=Object.assign({},o);return{data:{properties:a,user:h},error:null}}function us(s){return s}function ds(s){return s.access_token&&s.refresh_token&&s.expires_in}var G=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(l){try{c(r.next(l))}catch(u){o(u)}}function h(l){try{c(r.throw(l))}catch(u){o(u)}}function c(l){l.done?n(l.value):i(l.value).then(a,h)}c((r=r.apply(s,e||[])).next())})},fs=globalThis&&globalThis.__rest||function(s,e){var t={};for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&e.indexOf(r)<0&&(t[r]=s[r]);if(s!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(s);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(s,r[i])&&(t[r[i]]=s[r[i]]);return t};class ps{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=Pt(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}signOut(e){return G(this,void 0,void 0,function*(){try{return yield S(this.fetch,"POST",`${this.url}/logout`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(t){if(E(t))return{data:null,error:t};throw t}})}inviteUserByEmail(e,t={}){return G(this,void 0,void 0,function*(){try{return yield S(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:ie})}catch(r){if(E(r))return{data:{user:null},error:r};throw r}})}generateLink(e){return G(this,void 0,void 0,function*(){try{const{options:t}=e,r=fs(e,["options"]),i=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(i.new_email=r==null?void 0:r.newEmail,delete i.newEmail),yield S(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:hs,redirectTo:t==null?void 0:t.redirectTo})}catch(t){if(E(t))return{data:{properties:null,user:null},error:t};throw t}})}createUser(e){return G(this,void 0,void 0,function*(){try{return yield S(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:ie})}catch(t){if(E(t))return{data:{user:null},error:t};throw t}})}listUsers(e){var t,r,i,n,o,a,h;return G(this,void 0,void 0,function*(){try{const c={nextPage:null,lastPage:0,total:0},l=yield S(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(r=(t=e==null?void 0:e.page)===null||t===void 0?void 0:t.toString())!==null&&r!==void 0?r:"",per_page:(n=(i=e==null?void 0:e.perPage)===null||i===void 0?void 0:i.toString())!==null&&n!==void 0?n:""},xform:us});if(l.error)throw l.error;const u=yield l.json(),f=(o=l.headers.get("x-total-count"))!==null&&o!==void 0?o:0,m=(h=(a=l.headers.get("link"))===null||a===void 0?void 0:a.split(","))!==null&&h!==void 0?h:[];return m.length>0&&(m.forEach(p=>{const _=parseInt(p.split(";")[0].split("=")[1].substring(0,1)),g=JSON.parse(p.split(";")[1].split("=")[1]);c[`${g}Page`]=_}),c.total=parseInt(f)),{data:Object.assign(Object.assign({},u),c),error:null}}catch(c){if(E(c))return{data:{users:[]},error:c};throw c}})}getUserById(e){return G(this,void 0,void 0,function*(){try{return yield S(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:ie})}catch(t){if(E(t))return{data:{user:null},error:t};throw t}})}updateUserById(e,t){return G(this,void 0,void 0,function*(){try{return yield S(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:ie})}catch(r){if(E(r))return{data:{user:null},error:r};throw r}})}deleteUser(e,t=!1){return G(this,void 0,void 0,function*(){try{return yield S(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:ie})}catch(r){if(E(r))return{data:{user:null},error:r};throw r}})}_listFactors(e){return G(this,void 0,void 0,function*(){try{const{data:t,error:r}=yield S(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:i=>({data:{factors:i},error:null})});return{data:t,error:r}}catch(t){if(E(t))return{data:null,error:t};throw t}})}_deleteFactor(e){return G(this,void 0,void 0,function*(){try{return{data:yield S(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(E(t))return{data:null,error:t};throw t}})}}const vs="2.11.0",ms="http://localhost:9999",_s="supabase.auth.token",gs={"X-Client-Info":`gotrue-js/${vs}`},ys=10,bs={getItem:s=>W()?globalThis.localStorage.getItem(s):null,setItem:(s,e)=>{W()&&globalThis.localStorage.setItem(s,e)},removeItem:s=>{W()&&globalThis.localStorage.removeItem(s)}};function ws(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}var w=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(l){try{c(r.next(l))}catch(u){o(u)}}function h(l){try{c(r.throw(l))}catch(u){o(u)}}function c(l){l.done?n(l.value):i(l.value).then(a,h)}c((r=r.apply(s,e||[])).next())})};ws();const $s={url:ms,storageKey:_s,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:gs},ot=10*1e3,Ts=3;class Es{constructor(e){this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.broadcastChannel=null;const t=Object.assign(Object.assign({},$s),e);this.inMemorySession=null,this.storageKey=t.storageKey,this.autoRefreshToken=t.autoRefreshToken,this.persistSession=t.persistSession,this.storage=t.storage||bs,this.admin=new ps({url:t.url,headers:t.headers,fetch:t.fetch}),this.url=t.url,this.headers=t.headers,this.fetch=Pt(t.fetch),this.detectSessionInUrl=t.detectSessionInUrl,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},W()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey&&(this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey),this.broadcastChannel.addEventListener("message",r=>{this._notifyAllSubscribers(r.data.event,r.data.session,!1)})),this.initialize()}initialize(){return this.initializePromise||(this.initializePromise=this._initialize()),this.initializePromise}_initialize(){return w(this,void 0,void 0,function*(){if(this.initializePromise)return this.initializePromise;try{if(this.detectSessionInUrl&&this._isImplicitGrantFlow()){const{data:e,error:t}=yield this._getSessionFromUrl();if(t)return yield this._removeSession(),{error:t};const{session:r,redirectType:i}=e;return yield this._saveSession(r),this._notifyAllSubscribers("SIGNED_IN",r),i==="recovery"&&this._notifyAllSubscribers("PASSWORD_RECOVERY",r),{error:null}}return yield this._recoverAndRefresh(),{error:null}}catch(e){return E(e)?{error:e}:{error:new Ut("Unexpected error during initialization",e)}}finally{yield this._handleVisibilityChange()}})}signUp(e){var t,r;return w(this,void 0,void 0,function*(){try{yield this._removeSession();let i;if("email"in e){const{email:c,password:l,options:u}=e;i=yield S(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:u==null?void 0:u.emailRedirectTo,body:{email:c,password:l,data:(t=u==null?void 0:u.data)!==null&&t!==void 0?t:{},gotrue_meta_security:{captcha_token:u==null?void 0:u.captchaToken}},xform:_e})}else if("phone"in e){const{phone:c,password:l,options:u}=e;i=yield S(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:c,password:l,data:(r=u==null?void 0:u.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:u==null?void 0:u.captchaToken}},xform:_e})}else throw new st("You must provide either an email or phone number and a password");const{data:n,error:o}=i;if(o||!n)return{data:{user:null,session:null},error:o};const a=n.session,h=n.user;return n.session&&(yield this._saveSession(n.session),this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:h,session:a},error:null}}catch(i){if(E(i))return{data:{user:null,session:null},error:i};throw i}})}signInWithPassword(e){var t,r;return w(this,void 0,void 0,function*(){try{yield this._removeSession();let i;if("email"in e){const{email:a,password:h,options:c}=e;i=yield S(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:a,password:h,data:(t=c==null?void 0:c.data)!==null&&t!==void 0?t:{},gotrue_meta_security:{captcha_token:c==null?void 0:c.captchaToken}},xform:_e})}else if("phone"in e){const{phone:a,password:h,options:c}=e;i=yield S(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:a,password:h,data:(r=c==null?void 0:c.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:c==null?void 0:c.captchaToken}},xform:_e})}else throw new st("You must provide either an email or phone number and a password");const{data:n,error:o}=i;return o||!n?{data:{user:null,session:null},error:o}:(n.session&&(yield this._saveSession(n.session),this._notifyAllSubscribers("SIGNED_IN",n.session)),{data:n,error:o})}catch(i){if(E(i))return{data:{user:null,session:null},error:i};throw i}})}signInWithOAuth(e){var t,r,i,n;return w(this,void 0,void 0,function*(){return yield this._removeSession(),this._handleProviderSignIn(e.provider,{redirectTo:(t=e.options)===null||t===void 0?void 0:t.redirectTo,scopes:(r=e.options)===null||r===void 0?void 0:r.scopes,queryParams:(i=e.options)===null||i===void 0?void 0:i.queryParams,skipBrowserRedirect:(n=e.options)===null||n===void 0?void 0:n.skipBrowserRedirect})})}signInWithOtp(e){var t,r,i,n;return w(this,void 0,void 0,function*(){try{if(yield this._removeSession(),"email"in e){const{email:o,options:a}=e,{error:h}=yield S(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:o,data:(t=a==null?void 0:a.data)!==null&&t!==void 0?t:{},create_user:(r=a==null?void 0:a.shouldCreateUser)!==null&&r!==void 0?r:!0,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},redirectTo:a==null?void 0:a.emailRedirectTo});return{data:{user:null,session:null},error:h}}if("phone"in e){const{phone:o,options:a}=e,{error:h}=yield S(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:o,data:(i=a==null?void 0:a.data)!==null&&i!==void 0?i:{},create_user:(n=a==null?void 0:a.shouldCreateUser)!==null&&n!==void 0?n:!0,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}}});return{data:{user:null,session:null},error:h}}throw new st("You must provide either an email or phone number.")}catch(o){if(E(o))return{data:{user:null,session:null},error:o};throw o}})}verifyOtp(e){var t,r;return w(this,void 0,void 0,function*(){try{yield this._removeSession();const{data:i,error:n}=yield S(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:(t=e.options)===null||t===void 0?void 0:t.captchaToken}}),redirectTo:(r=e.options)===null||r===void 0?void 0:r.redirectTo,xform:_e});if(n)throw n;if(!i)throw"An error occurred on token verification.";const o=i.session,a=i.user;return o!=null&&o.access_token&&(yield this._saveSession(o),this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:a,session:o},error:null}}catch(i){if(E(i))return{data:{user:null,session:null},error:i};throw i}})}signInWithSSO(e){var t,r,i;return w(this,void 0,void 0,function*(){try{return yield this._removeSession(),yield S(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:(r=(t=e.options)===null||t===void 0?void 0:t.redirectTo)!==null&&r!==void 0?r:void 0}),!((i=e==null?void 0:e.options)===null||i===void 0)&&i.captchaToken?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0}),headers:this.headers,xform:ls})}catch(n){if(E(n))return{data:null,error:n};throw n}})}getSession(){return w(this,void 0,void 0,function*(){yield this.initializePromise;let e=null;if(this.persistSession){const n=yield Ct(this.storage,this.storageKey);n!==null&&(this._isValidSession(n)?e=n:yield this._removeSession())}else e=this.inMemorySession;if(!e)return{data:{session:null},error:null};if(!(e.expires_at?e.expires_at<=Date.now()/1e3:!1))return{data:{session:e},error:null};const{session:r,error:i}=yield this._callRefreshToken(e.refresh_token);return i?{data:{session:null},error:i}:{data:{session:r},error:null}})}getUser(e){var t,r;return w(this,void 0,void 0,function*(){try{if(!e){const{data:i,error:n}=yield this.getSession();if(n)throw n;e=(r=(t=i.session)===null||t===void 0?void 0:t.access_token)!==null&&r!==void 0?r:void 0}return yield S(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:ie})}catch(i){if(E(i))return{data:{user:null},error:i};throw i}})}updateUser(e){return w(this,void 0,void 0,function*(){try{const{data:t,error:r}=yield this.getSession();if(r)throw r;if(!t.session)throw new ke;const i=t.session,{data:n,error:o}=yield S(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,body:e,jwt:i.access_token,xform:ie});if(o)throw o;return i.user=n.user,yield this._saveSession(i),this._notifyAllSubscribers("USER_UPDATED",i),{data:{user:i.user},error:null}}catch(t){if(E(t))return{data:{user:null},error:t};throw t}})}_decodeJWT(e){return It(e)}setSession(e){return w(this,void 0,void 0,function*(){try{if(!e.access_token||!e.refresh_token)throw new ke;const t=Date.now()/1e3;let r=t,i=!0,n=null;const o=It(e.access_token);if(o.exp&&(r=o.exp,i=r<=t),i){const{session:a,error:h}=yield this._callRefreshToken(e.refresh_token);if(h)return{data:{user:null,session:null},error:h};if(!a)return{data:{user:null,session:null},error:null};n=a}else{const{data:a,error:h}=yield this.getUser(e.access_token);if(h)throw h;n={access_token:e.access_token,refresh_token:e.refresh_token,user:a.user,token_type:"bearer",expires_in:r-t,expires_at:r},yield this._saveSession(n),this._notifyAllSubscribers("SIGNED_IN",n)}return{data:{user:n.user,session:n},error:null}}catch(t){if(E(t))return{data:{session:null,user:null},error:t};throw t}})}refreshSession(e){var t;return w(this,void 0,void 0,function*(){try{if(!e){const{data:n,error:o}=yield this.getSession();if(o)throw o;e=(t=n.session)!==null&&t!==void 0?t:void 0}if(!(e!=null&&e.refresh_token))throw new ke;const{session:r,error:i}=yield this._callRefreshToken(e.refresh_token);return i?{data:{user:null,session:null},error:i}:r?{data:{user:r.user,session:r},error:null}:{data:{user:null,session:null},error:null}}catch(r){if(E(r))return{data:{user:null,session:null},error:r};throw r}})}_getSessionFromUrl(){return w(this,void 0,void 0,function*(){try{if(!W())throw new Q("No browser detected.");if(!this._isImplicitGrantFlow())throw new Q("Not a valid implicit grant flow url.");const e=M("error_description");if(e){const _=M("error_code");if(!_)throw new Q("No error_code detected.");const g=M("error");throw g?new Q(e,{error:g,code:_}):new Q("No error detected.")}const t=M("provider_token"),r=M("provider_refresh_token"),i=M("access_token");if(!i)throw new Q("No access_token detected.");const n=M("expires_in");if(!n)throw new Q("No expires_in detected.");const o=M("refresh_token");if(!o)throw new Q("No refresh_token detected.");const a=M("token_type");if(!a)throw new Q("No token_type detected.");const c=Math.round(Date.now()/1e3)+parseInt(n),{data:l,error:u}=yield this.getUser(i);if(u)throw u;const f=l.user,m={provider_token:t,provider_refresh_token:r,access_token:i,expires_in:parseInt(n),expires_at:c,refresh_token:o,token_type:a,user:f},p=M("type");return window.location.hash="",{data:{session:m,redirectType:p},error:null}}catch(e){if(E(e))return{data:{session:null,redirectType:null},error:e};throw e}})}_isImplicitGrantFlow(){return W()&&(Boolean(M("access_token"))||Boolean(M("error_description")))}signOut(){var e;return w(this,void 0,void 0,function*(){const{data:t,error:r}=yield this.getSession();if(r)return{error:r};const i=(e=t.session)===null||e===void 0?void 0:e.access_token;if(i){const{error:n}=yield this.admin.signOut(i);if(n&&!(is(n)&&(n.status===404||n.status===401)))return{error:n}}return yield this._removeSession(),this._notifyAllSubscribers("SIGNED_OUT",null),{error:null}})}onAuthStateChange(e){const t=Qr(),r={id:t,callback:e,unsubscribe:()=>{this.stateChangeEmitters.delete(t)}};return this.stateChangeEmitters.set(t,r),{data:{subscription:r}}}resetPasswordForEmail(e,t={}){return w(this,void 0,void 0,function*(){try{return yield S(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(r){if(E(r))return{data:null,error:r};throw r}})}_refreshAccessToken(e){return w(this,void 0,void 0,function*(){try{const t=Date.now();return yield rs(r=>w(this,void 0,void 0,function*(){return yield ts(r*200),yield S(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:_e})}),(r,i,n)=>n&&n.error&&n.error instanceof it&&Date.now()+(r+1)*200-t<ot)}catch(t){if(E(t))return{data:{session:null,user:null},error:t};throw t}})}_isValidSession(e){return typeof e=="object"&&e!==null&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}_handleProviderSignIn(e,t={}){const r=this._getUrlForProvider(e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return W()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}_recoverAndRefresh(){var e;return w(this,void 0,void 0,function*(){try{const t=yield Ct(this.storage,this.storageKey);if(!this._isValidSession(t)){t!==null&&(yield this._removeSession());return}const r=Math.round(Date.now()/1e3);if(((e=t.expires_at)!==null&&e!==void 0?e:1/0)<r+ys)if(this.autoRefreshToken&&t.refresh_token){const{error:i}=yield this._callRefreshToken(t.refresh_token);i&&(console.log(i.message),yield this._removeSession())}else yield this._removeSession();else this.persistSession&&(yield this._saveSession(t)),this._notifyAllSubscribers("SIGNED_IN",t)}catch(t){console.error(t);return}})}_callRefreshToken(e){var t,r;return w(this,void 0,void 0,function*(){if(this.refreshingDeferred)return this.refreshingDeferred.promise;try{if(this.refreshingDeferred=new Ue,!e)throw new ke;const{data:i,error:n}=yield this._refreshAccessToken(e);if(n)throw n;if(!i.session)throw new ke;yield this._saveSession(i.session),this._notifyAllSubscribers("TOKEN_REFRESHED",i.session);const o={session:i.session,error:null};return this.refreshingDeferred.resolve(o),o}catch(i){if(E(i)){const n={session:null,error:i};return(t=this.refreshingDeferred)===null||t===void 0||t.resolve(n),n}throw(r=this.refreshingDeferred)===null||r===void 0||r.reject(i),i}finally{this.refreshingDeferred=null}})}_notifyAllSubscribers(e,t,r=!0){this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t}),this.stateChangeEmitters.forEach(i=>i.callback(e,t))}_saveSession(e){return w(this,void 0,void 0,function*(){this.persistSession||(this.inMemorySession=e),this.persistSession&&e.expires_at&&(yield this._persistSession(e))})}_persistSession(e){return Yr(this.storage,this.storageKey,e)}_removeSession(){return w(this,void 0,void 0,function*(){this.persistSession?yield Zr(this.storage,this.storageKey):this.inMemorySession=null})}_removeVisibilityChangedCallback(){const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&W()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){console.error("removing visibilitychange callback failed",t)}}_startAutoRefresh(){return w(this,void 0,void 0,function*(){yield this._stopAutoRefresh();const e=setInterval(()=>this._autoRefreshTokenTick(),ot);this.autoRefreshTicker=e,e&&typeof e=="object"&&typeof e.unref=="function"&&e.unref(),yield this._autoRefreshTokenTick()})}_stopAutoRefresh(){return w(this,void 0,void 0,function*(){const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)})}startAutoRefresh(){return w(this,void 0,void 0,function*(){this._removeVisibilityChangedCallback(),yield this._startAutoRefresh()})}stopAutoRefresh(){return w(this,void 0,void 0,function*(){this._removeVisibilityChangedCallback(),yield this._stopAutoRefresh()})}_autoRefreshTokenTick(){return w(this,void 0,void 0,function*(){const e=Date.now();try{const{data:{session:t},error:r}=yield this.getSession();if(!t||!t.refresh_token||!t.expires_at)return;Math.floor((t.expires_at*1e3-e)/ot)<Ts&&(yield this._callRefreshToken(t.refresh_token))}catch(t){console.error("Auto refresh tick failed with error. This is likely a transient error.",t)}})}_handleVisibilityChange(){return w(this,void 0,void 0,function*(){if(!W()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=()=>w(this,void 0,void 0,function*(){return yield this._onVisibilityChanged(!1)}),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),yield this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}})}_onVisibilityChanged(e){return w(this,void 0,void 0,function*(){document.visibilityState==="visible"?(e||(yield this.initializePromise,yield this._recoverAndRefresh()),this.autoRefreshToken&&this._startAutoRefresh()):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()})}_getUrlForProvider(e,t){const r=[`provider=${encodeURIComponent(e)}`];if(t!=null&&t.redirectTo&&r.push(`redirect_to=${encodeURIComponent(t.redirectTo)}`),t!=null&&t.scopes&&r.push(`scopes=${encodeURIComponent(t.scopes)}`),t!=null&&t.queryParams){const i=new URLSearchParams(t.queryParams);r.push(i.toString())}return`${this.url}/authorize?${r.join("&")}`}_unenroll(e){var t;return w(this,void 0,void 0,function*(){try{const{data:r,error:i}=yield this.getSession();return i?{data:null,error:i}:yield S(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:(t=r==null?void 0:r.session)===null||t===void 0?void 0:t.access_token})}catch(r){if(E(r))return{data:null,error:r};throw r}})}_enroll(e){var t,r;return w(this,void 0,void 0,function*(){try{const{data:i,error:n}=yield this.getSession();if(n)return{data:null,error:n};const{data:o,error:a}=yield S(this.fetch,"POST",`${this.url}/factors`,{body:{friendly_name:e.friendlyName,factor_type:e.factorType,issuer:e.issuer},headers:this.headers,jwt:(t=i==null?void 0:i.session)===null||t===void 0?void 0:t.access_token});return a?{data:null,error:a}:(!((r=o==null?void 0:o.totp)===null||r===void 0)&&r.qr_code&&(o.totp.qr_code=`data:image/svg+xml;utf-8,${o.totp.qr_code}`),{data:o,error:null})}catch(i){if(E(i))return{data:null,error:i};throw i}})}_verify(e){var t;return w(this,void 0,void 0,function*(){try{const{data:r,error:i}=yield this.getSession();if(i)return{data:null,error:i};const{data:n,error:o}=yield S(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:(t=r==null?void 0:r.session)===null||t===void 0?void 0:t.access_token});return o?{data:null,error:o}:(yield this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+n.expires_in},n)),this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",n),{data:n,error:o})}catch(r){if(E(r))return{data:null,error:r};throw r}})}_challenge(e){var t;return w(this,void 0,void 0,function*(){try{const{data:r,error:i}=yield this.getSession();return i?{data:null,error:i}:yield S(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{headers:this.headers,jwt:(t=r==null?void 0:r.session)===null||t===void 0?void 0:t.access_token})}catch(r){if(E(r))return{data:null,error:r};throw r}})}_challengeAndVerify(e){return w(this,void 0,void 0,function*(){const{data:t,error:r}=yield this._challenge({factorId:e.factorId});return r?{data:null,error:r}:yield this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})})}_listFactors(){return w(this,void 0,void 0,function*(){const{data:{user:e},error:t}=yield this.getUser();if(t)return{data:null,error:t};const r=(e==null?void 0:e.factors)||[],i=r.filter(n=>n.factor_type==="totp"&&n.status==="verified");return{data:{all:r,totp:i},error:null}})}_getAuthenticatorAssuranceLevel(){var e,t;return w(this,void 0,void 0,function*(){const{data:{session:r},error:i}=yield this.getSession();if(i)return{data:null,error:i};if(!r)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const n=this._decodeJWT(r.access_token);let o=null;n.aal&&(o=n.aal);let a=o;((t=(e=r.user.factors)===null||e===void 0?void 0:e.filter(l=>l.status==="verified"))!==null&&t!==void 0?t:[]).length>0&&(a="aal2");const c=n.amr||[];return{data:{currentLevel:o,nextLevel:a,currentAuthenticationMethods:c},error:null}})}}class Os extends Es{constructor(e){super(e)}}var xs=globalThis&&globalThis.__awaiter||function(s,e,t,r){function i(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(l){try{c(r.next(l))}catch(u){o(u)}}function h(l){try{c(r.throw(l))}catch(u){o(u)}}function c(l){l.done?n(l.value):i(l.value).then(a,h)}c((r=r.apply(s,e||[])).next())})};const ks={headers:Hr},Ss={schema:"public"},js={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},As={};class Rs{constructor(e,t,r){var i,n,o,a,h,c,l,u;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const f=Kr(e);if(this.realtimeUrl=`${f}/realtime/v1`.replace(/^http/i,"ws"),this.authUrl=`${f}/auth/v1`,this.storageUrl=`${f}/storage/v1`,f.match(/(supabase\.co)|(supabase\.in)/)){const A=f.split(".");this.functionsUrl=`${A[0]}.functions.${A[1]}.${A[2]}`}else this.functionsUrl=`${f}/functions/v1`;const p=`sb-${new URL(this.authUrl).hostname.split(".")[0]}-auth-token`,_={db:Ss,realtime:As,auth:Object.assign(Object.assign({},js),{storageKey:p}),global:ks},g=Vr(r??{},_);this.storageKey=(n=(i=g.auth)===null||i===void 0?void 0:i.storageKey)!==null&&n!==void 0?n:"",this.headers=(a=(o=g.global)===null||o===void 0?void 0:o.headers)!==null&&a!==void 0?a:{},this.auth=this._initSupabaseAuthClient((h=g.auth)!==null&&h!==void 0?h:{},this.headers,(c=g.global)===null||c===void 0?void 0:c.fetch),this.fetch=Gr(t,this._getAccessToken.bind(this),(l=g.global)===null||l===void 0?void 0:l.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers},g.realtime)),this.rest=new dr(`${f}/rest/v1`,{headers:this.headers,schema:(u=g.db)===null||u===void 0?void 0:u.schema,fetch:this.fetch}),this._listenForAuthEvents()}get functions(){return new ir(this.functionsUrl,{headers:this.headers,customFetch:this.fetch})}get storage(){return new Mr(this.storageUrl,this.headers,this.fetch)}from(e){return this.rest.from(e)}rpc(e,t={},r){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return xs(this,void 0,void 0,function*(){const{data:r}=yield this.auth.getSession();return(t=(e=r.session)===null||e===void 0?void 0:e.access_token)!==null&&t!==void 0?t:null})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,storageKey:n},o,a){const h={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Os({url:this.authUrl,headers:Object.assign(Object.assign({},h),o),storageKey:n,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,fetch:a})}_initRealtimeClient(e){return new jr(this.realtimeUrl,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},e==null?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((t,r)=>{this._handleTokenChanged(t,r==null?void 0:r.access_token,"CLIENT")})}_handleTokenChanged(e,t,r){(e==="TOKEN_REFRESHED"||e==="SIGNED_IN")&&this.changedAccessToken!==t?(this.realtime.setAuth(t??null),this.changedAccessToken=t):(e==="SIGNED_OUT"||e==="USER_DELETED")&&(this.realtime.setAuth(this.supabaseKey),r=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const Ps=(s,e,t)=>new Rs(s,e,t),ge=[];function Dt(s,e=j){let t;const r=new Set;function i(a){if(Y(s,a)&&(s=a,t)){const h=!ge.length;for(const c of r)c[1](),ge.push(c,s);if(h){for(let c=0;c<ge.length;c+=2)ge[c][0](ge[c+1]);ge.length=0}}}function n(a){i(a(s))}function o(a,h=j){const c=[a,h];return r.add(c),r.size===1&&(t=e(i)||j),a(s),()=>{r.delete(c),r.size===0&&(t(),t=null)}}return{set:i,update:n,subscribe:o}}let Cs=class{constructor(){Nt(this,"page_size",100);this.messages=Dt([]),this.roomId=null,this.supabase=null,this.fetched_count=0,this.isFetching=!1,this.total_messages=0}subscribe(e){return this.messages.subscribe(e)}async getTotalMessagesCount(){const{count:e,error:t}=await this.supabase.from("messages").select("*",{count:"exact",head:!0}).eq("room_id",this.roomId);return e}async initialize(e){this.roomId=e.roomId,this.supabase=Ps(e.apiUrl,e.apiKey),this.total_messages=await this.getTotalMessagesCount(),this.supabase.channel("any").on("postgres_changes",{event:"INSERT",schema:"public",table:"messages",filter:`room_id=eq.${this.roomId}`},t=>{this.messages.update(r=>[...r,t.new])}).subscribe()}async fetch(){if(this.isFetching)return;this.isFetching=!0;const{data:e}=await this.supabase.from("messages").select().eq("room_id",this.roomId).range(0,this.page_size).order("timestamp",{ascending:!1});return this.isFetching=!1,this.fetched_count=e.length,this.messages.set(e.reverse())}async fetchOlder(){if(this.isFetching)return;this.isFetching=!0;const{data:e}=await this.supabase.from("messages").select().eq("room_id",this.roomId).range(this.fetched_count,this.page_size+this.fetched_count+1).order("timestamp",{ascending:!1});return this.isFetching=!1,this.fetched_count+=e.length,this.messages.update(t=>[...e.reverse(),...t])}async post(e,t){return await this.supabase.from("messages").insert({username:e.name,user_id:e.user_id,room_id:this.roomId,content:t})}};const Se=new Cs;function Is(s){let e;return{c(){e=T("div"),e.innerHTML=`<svg class="h-16 w-16 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0011.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"></path></svg>
  <p class="mt-4 text-gray-600 text-xl font-medium">No chats yet...</p>
  <p class="text-gray-600 mt-0.5">Start conversing to see your messages here.</p>`,y(e,"class","flex flex-col justify-center items-center h-full")},m(t,r){q(t,e,r)},p:j,i:j,o:j,d(t){t&&B(e)}}}class Us extends de{constructor(e){super(),ue(this,e,null,Is,Y,{})}}function Ds(s){let e,t,r,i=s[0].username+"",n,o,a,h,c,l=s[0].content+"",u,f,m;return{c(){e=T("li"),t=T("div"),r=T("p"),n=ye(i),a=V(),h=T("div"),c=T("p"),u=ye(l),y(r,"class","font-medium text-gray-900"),y(t,"class",o="flex "+(s[1]?"flex-row-reverse":"")+" text-sm"),y(c,"class",f="w-4/6 flex "+(s[1]?"justify-end":"")),y(h,"class",m="mt-1 flex "+(s[1]?"flex-row-reverse":"")+" text-sm text-gray-700")},m(p,_){q(p,e,_),$(e,t),$(t,r),$(r,n),$(e,a),$(e,h),$(h,c),$(c,u)},p(p,[_]){_&1&&i!==(i=p[0].username+"")&&Ne(n,i),_&2&&o!==(o="flex "+(p[1]?"flex-row-reverse":"")+" text-sm")&&y(t,"class",o),_&1&&l!==(l=p[0].content+"")&&Ne(u,l),_&2&&f!==(f="w-4/6 flex "+(p[1]?"justify-end":""))&&y(c,"class",f),_&2&&m!==(m="mt-1 flex "+(p[1]?"flex-row-reverse":"")+" text-sm text-gray-700")&&y(h,"class",m)},i:j,o:j,d(p){p&&B(e)}}}function Ls(s,e,t){let{chat:r,isCurrentUser:i}=e;return s.$$set=n=>{"chat"in n&&t(0,r=n.chat),"isCurrentUser"in n&&t(1,i=n.isCurrentUser)},[r,i]}class Fs extends de{constructor(e){super(),ue(this,e,Ls,Ds,Y,{chat:0,isCurrentUser:1})}}const Ii="";function Lt(s,e,t){const r=s.slice();return r[7]=e[t],r}function Ft(s){let e,t;return e=new Us({}),{c(){$e(e.$$.fragment)},m(r,i){le(e,r,i),t=!0},i(r){t||(N(e.$$.fragment,r),t=!0)},o(r){J(e.$$.fragment,r),t=!1},d(r){he(e,r)}}}function Bt(s){let e,t;return e=new Fs({props:{chat:s[7],isCurrentUser:s[0].user_id==s[7].user_id}}),{c(){$e(e.$$.fragment)},m(r,i){le(e,r,i),t=!0},p(r,i){const n={};i&8&&(n.chat=r[7]),i&9&&(n.isCurrentUser=r[0].user_id==r[7].user_id),e.$set(n)},i(r){t||(N(e.$$.fragment,r),t=!0)},o(r){J(e.$$.fragment,r),t=!1},d(r){he(e,r)}}}function Bs(s){let e,t,r,i,n,o=s[3],a=[];for(let l=0;l<o.length;l+=1)a[l]=Bt(Lt(s,o,l));const h=l=>J(a[l],1,1,()=>{a[l]=null});let c=null;return o.length||(c=Ft()),{c(){e=T("div"),t=T("ul");for(let l=0;l<a.length;l+=1)a[l].c();c&&c.c(),y(t,"class","messages space-y-8 overflow-y-auto pr-4 svelte-s2zs04"),Jt(t,"height","calc(100vh - 19rem)"),y(e,"class","px-4 py-6 sm:px-6")},m(l,u){q(l,e,u),$(e,t);for(let f=0;f<a.length;f+=1)a[f].m(t,null);c&&c.m(t,null),s[5](t),r=!0,i||(n=Z(t,"scroll",s[4]),i=!0)},p(l,[u]){if(u&9){o=l[3];let f;for(f=0;f<o.length;f+=1){const m=Lt(l,o,f);a[f]?(a[f].p(m,u),N(a[f],1)):(a[f]=Bt(m),a[f].c(),N(a[f],1),a[f].m(t,null))}for(Ge(),f=o.length;f<a.length;f+=1)h(f);Ke(),o.length?c&&(Ge(),J(c,1,1,()=>{c=null}),Ke()):c||(c=Ft(),c.c(),N(c,1),c.m(t,null))}},i(l){if(!r){for(let u=0;u<o.length;u+=1)N(a[u]);r=!0}},o(l){a=a.filter(Boolean);for(let u=0;u<a.length;u+=1)J(a[u]);r=!1},d(l){l&&B(e),Ht(a,l),c&&c.d(),s[5](null),i=!1,n()}}}function Ns(s,e,t){let r,i=j,n=()=>(i(),i=dt(a,f=>t(3,r=f)),a);s.$$.on_destroy.push(()=>i());let{user:o,chats:a}=e;n();let h,c;zt(()=>{c=h&&h.offsetHeight+h.scrollTop>h.scrollHeight-50}),Kt(()=>{c&&h.scroll({top:h.scrollHeight,behavior:"smooth"})});function l(){h.scrollTop===0&&Se.fetchOlder()}function u(f){He[f?"unshift":"push"](()=>{h=f,t(2,h)})}return s.$$set=f=>{"user"in f&&t(0,o=f.user),"chats"in f&&n(t(1,a=f.chats))},[o,a,h,r,l,u]}class Ms extends de{constructor(e){super(),ue(this,e,Ns,Bs,Y,{user:0,chats:1})}}function Hs(){return([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,s=>(s^crypto.getRandomValues(new Uint8Array(1))[0]&15>>s/4).toString(16))}class qs{constructor(){let e={};localStorage.getItem("name")&&localStorage.getItem("user_id")&&(e={name:localStorage.getItem("name"),user_id:localStorage.getItem("user_id")}),this.user=Dt(e)}subscribe(e){return this.user.subscribe(e)}setName(e){return localStorage.setItem("name",e),this.user.update(t=>(t.name=e,t)),this}setId(e=Hs()){return localStorage.setItem("user_id",e),this.user.update(t=>(t.user_id=e,t)),this}}const Fe=new qs;function Js(s){let e,t,r,i,n,o,a,h,c,l,u,f,m;return{c(){e=T("div"),t=T("div"),r=T("div"),i=T("form"),n=T("div"),o=T("label"),o.textContent="About",a=V(),h=T("textarea"),c=V(),l=T("div"),u=T("button"),u.textContent="Submit",y(o,"for","comment"),y(o,"class","sr-only"),y(h,"rows","3"),y(h,"class","block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"),y(h,"placeholder","Add a note"),y(u,"class","inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),y(l,"class","mt-3 flex items-center justify-end"),y(i,"action","#"),y(r,"class","min-w-0 flex-1"),y(t,"class","flex space-x-3"),y(e,"class","bg-gray-50 px-4 py-6 sm:px-6")},m(p,_){q(p,e,_),$(e,t),$(t,r),$(r,i),$(i,n),$(n,o),$(n,a),$(n,h),Ae(h,s[0]),$(i,c),$(i,l),$(l,u),f||(m=[Z(h,"keydown",s[2]),Z(h,"input",s[3]),Z(u,"click",s[1])],f=!0)},p(p,[_]){_&1&&Ae(h,p[0])},i:j,o:j,d(p){p&&B(e),f=!1,X(m)}}}function zs(s,e,t){let r;ft(s,Fe,h=>t(4,r=h));let i="";function n(){i&&(Se.post(r,i),t(0,i=""))}function o(h){h.ctrlKey&&h.key=="Enter"&&n()}function a(){i=this.value,t(0,i)}return[i,n,o,a]}class Gs extends de{constructor(e){super(),ue(this,e,zs,Js,Y,{})}}function Ks(s){let e,t,r,i,n,o,a,h,c,l,u,f,m;return{c(){e=T("div"),t=T("p"),t.textContent="Join to chat",r=V(),i=T("div"),n=T("label"),n.textContent="Name",o=V(),a=T("div"),h=T("input"),c=V(),l=T("div"),u=T("button"),u.textContent="Join",y(t,"class","text-center font-medium text-gray-600"),y(n,"for","name"),y(n,"class","block text-sm font-medium text-gray-700"),y(h,"id","name"),y(h,"name","name"),y(h,"type","text"),h.required=!0,y(h,"class","block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"),y(a,"class","mt-1"),y(i,"class","mt-2 mx-4"),y(u,"type","button"),y(u,"class","inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-8 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"),y(l,"class","mt-2 text-right mx-4"),y(e,"class","flex flex-col h-full justify-center w-full mx-auto")},m(p,_){q(p,e,_),$(e,t),$(e,r),$(e,i),$(i,n),$(i,o),$(i,a),$(a,h),Ae(h,s[0]),$(e,c),$(e,l),$(l,u),f||(m=[Z(h,"keydown",s[2]),Z(h,"input",s[3]),Z(u,"click",s[1])],f=!0)},p(p,[_]){_&1&&h.value!==p[0]&&Ae(h,p[0])},i:j,o:j,d(p){p&&B(e),f=!1,X(m)}}}function Vs(s,e,t){let r="";function i(){Fe.setName(r).setId()}function n(a){a.key=="Enter"&&r.length>0&&i()}function o(){r=this.value,t(0,r)}return[r,i,n,o]}class Ws extends de{constructor(e){super(),ue(this,e,Vs,Ks,Y,{})}}function Qs(s){let e;return{c(){e=ye("Chat")},m(t,r){q(t,e,r)},p:j,d(t){t&&B(e)}}}function Xs(s){let e=s[0].title+"",t;return{c(){t=ye(e)},m(r,i){q(r,t,i)},p(r,i){i&1&&e!==(e=r[0].title+"")&&Ne(t,e)},d(r){r&&B(t)}}}function Ys(s){let e,t;return e=new Ws({}),{c(){$e(e.$$.fragment)},m(r,i){le(e,r,i),t=!0},p:j,i(r){t||(N(e.$$.fragment,r),t=!0)},o(r){J(e.$$.fragment,r),t=!1},d(r){he(e,r)}}}function Zs(s){let e,t,r,i;return e=new Ms({props:{user:s[1],chats:Se}}),r=new Gs({}),{c(){$e(e.$$.fragment),t=V(),$e(r.$$.fragment)},m(n,o){le(e,n,o),q(n,t,o),le(r,n,o),i=!0},p(n,o){const a={};o&2&&(a.user=n[1]),e.$set(a)},i(n){i||(N(e.$$.fragment,n),N(r.$$.fragment,n),i=!0)},o(n){J(e.$$.fragment,n),J(r.$$.fragment,n),i=!1},d(n){he(e,n),n&&B(t),he(r,n)}}}function ei(s){let e,t,r,i,n,o,a,h,c,l,u;function f(O,P){return O[0].title?Xs:Qs}let m=f(s),p=m(s);const _=[Zs,Ys],g=[];function A(O,P){return O[1].name?0:1}return c=A(s),l=g[c]=_[c](s),{c(){e=T("link"),t=V(),r=T("div"),i=T("div"),n=T("div"),o=T("div"),a=T("h2"),p.c(),h=V(),l.c(),y(e,"rel","stylesheet"),y(e,"href","https://rsms.me/inter/inter.css"),y(a,"class","text-lg font-medium text-gray-900"),y(o,"class","px-4 py-5 sm:px-6"),y(n,"class","divide-y divide-gray-200 h-full flex flex-col"),y(i,"class","bg-white shadow sm:overflow-hidden sm:rounded-lg max-w-2xl mx-auto h-full"),y(r,"class","tp-chat bg-white absolute inset-0 w-full")},m(O,P){$(document.head,e),q(O,t,P),q(O,r,P),$(r,i),$(i,n),$(n,o),$(o,a),p.m(a,null),$(n,h),g[c].m(n,null),u=!0},p(O,[P]){m===(m=f(O))&&p?p.p(O,P):(p.d(1),p=m(O),p&&(p.c(),p.m(a,null)));let R=c;c=A(O),c===R?g[c].p(O,P):(Ge(),J(g[R],1,1,()=>{g[R]=null}),Ke(),l=g[c],l?l.p(O,P):(l=g[c]=_[c](O),l.c()),N(l,1),l.m(n,null))},i(O){u||(N(l),u=!0)},o(O){J(l),u=!1},d(O){B(e),O&&B(t),O&&B(r),p.d(),g[c].d()}}}function ti(s,e,t){let r;ft(s,Fe,n=>t(1,r=n));let{config:i}=e;return Gt(()=>{Se.initialize(i),Se.fetch(),i.username&&Fe.setName(i.username).setId(i.user_id)}),s.$$set=n=>{"config"in n&&t(0,i=n.config)},[i,r]}class ri extends de{constructor(e){super(),ue(this,e,ti,ei,Y,{config:0})}}const si=function(s,e){new ri({target:s,props:{config:e}})};F.load=si,Object.defineProperty(F,Symbol.toStringTag,{value:"Module"})});
