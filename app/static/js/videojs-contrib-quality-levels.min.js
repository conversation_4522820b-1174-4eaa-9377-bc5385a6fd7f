/*! @name videojs-contrib-quality-levels @version 2.2.1 @license Apache-2.0 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("video.js")):"function"==typeof define&&define.amd?define(["video.js"],t):e.videojsContribQualityLevels=t(e.videojs)}(this,function(e){"use strict";function t(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}var i=function(e){var i,n;function l(){var i,n=t(t(i=e.call(this)||this));return n.levels_=[],n.selectedIndex_=-1,Object.defineProperty(n,"selectedIndex",{get:function(){return n.selectedIndex_}}),Object.defineProperty(n,"length",{get:function(){return n.levels_.length}}),n||t(i)}n=e,(i=l).prototype=Object.create(n.prototype),i.prototype.constructor=i,i.__proto__=n;var r=l.prototype;return r.addQualityLevel=function(e){var t=this.getQualityLevelById(e.id);if(t)return t;var i=this.levels_.length;return t=new function(e){var t=this;return t.id=e.id,t.label=t.id,t.width=e.width,t.height=e.height,t.bitrate=e.bandwidth,t.frameRate=e.frameRate,t.enabled_=e.enabled,Object.defineProperty(t,"enabled",{get:function(){return t.enabled_()},set:function(e){t.enabled_(e)}}),t}(e),""+i in this||Object.defineProperty(this,i,{get:function(){return this.levels_[i]}}),this.levels_.push(t),this.trigger({qualityLevel:t,type:"addqualitylevel"}),t},r.removeQualityLevel=function(e){for(var t=null,i=0,n=this.length;i<n;i++)if(this[i]===e){t=this.levels_.splice(i,1)[0],this.selectedIndex_===i?this.selectedIndex_=-1:this.selectedIndex_>i&&this.selectedIndex_--;break}return t&&this.trigger({qualityLevel:e,type:"removequalitylevel"}),t},r.getQualityLevelById=function(e){for(var t=0,i=this.length;t<i;t++){var n=this[t];if(n.id===e)return n}return null},r.dispose=function(){this.selectedIndex_=-1,this.levels_.length=0},l}((e=e&&e.hasOwnProperty("default")?e.default:e).EventTarget);for(var n in i.prototype.allowedEvents_={change:"change",addqualitylevel:"addqualitylevel",removequalitylevel:"removequalitylevel"},i.prototype.allowedEvents_)i.prototype["on"+n]=null;var l=function(t){return n=this,e.mergeOptions({},t),l=n.qualityLevels,r=new i,n.on("dispose",function e(){r.dispose(),n.qualityLevels=l,n.off("dispose",e)}),n.qualityLevels=function(){return r},n.qualityLevels.VERSION="2.2.1",r;var n,l,r};return(e.registerPlugin||e.plugin)("qualityLevels",l),l.VERSION="2.2.1",l});
