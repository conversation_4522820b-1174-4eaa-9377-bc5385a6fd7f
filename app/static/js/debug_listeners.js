// Add event listener for 'play' event
player.on('play', function() {
    console.log("Player is playing. Current time:", player.currentTime());
});

// Add event listener for 'timeupdate' event
player.on('timeupdate', function() {
    console.log("Player current time has been updated. Current time:", player.currentTime());
});

// Add event listener for 'seeked' event
player.on('seeked', function() {
    console.log("Player seeked to a new time position. Current time:", player.currentTime());
});

// Add event listener for 'ended' event
player.on('ended', function() {
    console.log("Player playback has ended. Current time:", player.currentTime());
});

// Add event listener for 'pause' event
player.on('pause', function() {
    console.log("Player playback has been paused. Current time:", player.currentTime());
});

// Add event listener for 'error' event
player.on('error', function() {
    console.log("Player encountered an error. Current time:", player.currentTime());
});
