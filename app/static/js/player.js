
function base64EncodeUint8Array(input) {
  return btoa(String.fromCharCode.apply(null, input));
}


class PlayerBuilder {
    settings = {}
    hlsSettings = {}
    url
    type
    drmURL
    fairplayCertificateURL
    csrftoken

    enableLiveUI(enable) {
      this.settings["liveui"] = enable
      return this
    }

    useAWSIVFSDK(enable) {
      if (enable) {
        this.settings["techOrder"] = ["AmazonIVS"]
      }
      return this
    }

    setPlaybackRates(playbackRates) {
      this.settings["playbackRates"] = playbackRates
      return this
    }

    enableDoubleClick(enable) {
      this.settings["userActions"] = {
        doubleClick: enable
      }
      return this
    }

    enableResponsive(enable) {
      this.settings["responsive"] = enable
      return this
    }

    enableFluidUI(enable) {
      this.settings["fluid"] = enable
      return this
    }



    setBackgroundMode(enable) {
      this.setMute(enable);
      this.setAutoplay(enable);
      this.setLoop(enable);
      this.setControls(!enable);
      return this;
    }

    setControls(enable) {
      this.settings["controls"] = enable
      if (!enable) {
        this.settings["userActions"] = {
          hotkeys: true,
        }
      }
      return this
    }

    enableHotkeys() {
      this.settings["plugins"] = {
        hotkeys: {
          customKeys: {
            spaceKey: {
              key: function(event) {
                return event.which === 32;
              },
              handler: function(player, options, event) {
                player.paused() ? player.play() : player.pause();
              }
            }
          }
        }
      };
      return this;
    }

    setMute(enable) {
      this.settings["muted"] = enable;
      return this;
    }

    setAutoplay(enable) {
      this.settings["autoplay"] = enable;
      return this;
    }

    setLoop(enable) {
      this.settings["loop"] = enable;
      return this;
    }

    setPictureInPicture(enable) {
      this.settings["disablePictureInPicture"] = !enable;
      return this;
    }

    enableCachingEncryptionKeys(enable){
      this.hlsSettings["cacheEncryptionKeys"] = enable
      return this
    }

    usePlayerDimensionsForAdaptiveness(enable) {
      this.hlsSettings["limitRenditionByPlayerDimensions"] = enable
      return this
    }

    setVideoDetails(url, type) {
      this.url = url
      this.type = type
      return this
    }

    setDRMURL(url) {
      this.drmURL = url
      return this
    }

    setFairplayCertificateURL(url) {
      this.fairplayCertificateURL = url
      return this
    }

    setCSRFToken(token) {
      this.csrftoken = token
      return this
    }

    getWidevineKeySystemOptions() {
      return {
        getLicense: (emeOptions, keyMessage, callback) => {
          let headers = {"Content-type": "application/octet-stream"};
          this.makeDRMRequest(headers, keyMessage, callback);
        },
      }
    }

    getFairplayKeySystemOptions() {
      return {
        certificateUri: this.fairplayCertificateURL,

        getContentId: (emeOptions, initData) => {
          return new TextDecoder('utf-16').decode(initData.slice(16));
        },

        getLicense: (emeOptions, contentId, keyMessage, callback) => {
          const headers = {"Content-type": "application/json"};
          const body = JSON.stringify({
            spc: base64EncodeUint8Array(keyMessage),
          });
          this.makeDRMRequest(headers, body, callback, "fairplay");
        }
      }
    }

    makeDRMRequest(headers, body, callback, drm_type="widevine") {
      if (drm_type === "fairplay" &&  window.drmLicenseCallCount > 0 ) {
        return
      }
      window.drmLicenseCallCount++;
      const sendAuthentication = (xhr) => {
        if (this.csrftoken) {
          xhr.setRequestHeader('X-CSRFToken', this.csrftoken);
        }
      };

      videojs.xhr({
        url: `${this.drmURL}&drm_type=${drm_type}`,
        method: 'POST',
        body: body,
        responseType: 'arraybuffer',
        headers: headers,
        beforeSend: sendAuthentication,
      }, (err, response, responseBody) => {
        if (err) {
          callback(err);
          return;
        }
        if (response.statusCode >= 400 && response.statusCode <= 599) {
          const message = new TextDecoder().decode(responseBody);
          player.error({
            code: 3,
            message: message
          });
          callback({});
          return;
        }

        callback(null, responseBody);
      })
    }

    build() {
      this.settings.html5 = {hls: this.hlsSettings}
      const player = videojs("video", this.settings)
      player.eme()

      // Variable to track the number of DRM license calls specifically for logging in Sentry
      window.drmLicenseCallCount = 0;

      player.src({
        src: this.url,
        type: this.type,
        keySystems: {
          "com.widevine.alpha": this.getWidevineKeySystemOptions(),
          "com.apple.fps.1_0": this.getFairplayKeySystemOptions()
        }
      })
      return player
    }
}
