!function(){var e={};!function(){"use strict";var t=e;t.detectIncognito=void 0,t.detectIncognito=function(){return new Promise((function(e,t){var o,n,r="Unknown";function i(t){e({isPrivate:t,browserName:r})}function a(e){return e===eval.toString().length}void 0!==(n=navigator.vendor)&&0===n.indexOf("Apple")&&a(37)?(r="Safari",void 0!==navigator.maxTouchPoints?function(){var e=String(Math.random());try{window.indexedDB.open(e,1).onupgradeneeded=function(t){var o,n,r=null===(o=t.target)||void 0===o?void 0:o.result;try{r.createObjectStore("test",{autoIncrement:!0}).put(new Blob),i(!1)}catch(e){var a=e;return e instanceof Error&&(a=null!==(n=e.message)&&void 0!==n?n:e),i("string"==typeof a&&/BlobURLs are not yet supported/.test(a))}finally{r.close(),window.indexedDB.deleteDatabase(e)}}}catch(e){return i(!1)}}():function(){var e=window.openDatabase,t=window.localStorage;try{e(null,null,null,null)}catch(e){return i(!0)}try{t.setItem("test","1"),t.removeItem("test")}catch(e){return i(!0)}i(!1)}()):function(){var e=navigator.vendor;return void 0!==e&&0===e.indexOf("Google")&&a(33)}()?(o=navigator.userAgent,r=o.match(/Chrome/)?void 0!==navigator.brave?"Brave":o.match(/Edg/)?"Edge":o.match(/OPR/)?"Opera":"Chrome":"Chromium",void 0!==self.Promise&&void 0!==self.Promise.allSettled?navigator.webkitTemporaryStorage.queryUsageAndQuota((function(e,t){var o;i(Math.round(t/1048576)<2*Math.round((void 0!==(o=window).performance&&void 0!==o.performance.memory&&void 0!==o.performance.memory.jsHeapSizeLimit?performance.memory.jsHeapSizeLimit:1073741824)/1048576))}),(function(e){t(new Error("detectIncognito somehow failed to query storage quota: "+e.message))})):(0,window.webkitRequestFileSystem)(0,1,(function(){i(!1)}),(function(){i(!0)}))):void 0!==document.documentElement&&void 0!==document.documentElement.style.MozAppearance&&a(37)?(r="Firefox",i(void 0===navigator.serviceWorker)):void 0!==navigator.msSaveBlob&&a(39)?(r="Internet Explorer",i(void 0===window.indexedDB)):t(new Error("detectIncognito cannot determine the browser"))}))}}(),detectIncognito=e.detectIncognito}();
