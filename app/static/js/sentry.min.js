/*! @sentry/apm & @sentry/browser 5.20.1 (0df0db1b) | https://github.com/getsentry/sentry-javascript */
var Sentry=function(t){var n=function(t,r){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var r in n)n.hasOwnProperty(r)&&(t[r]=n[r])})(t,r)};function r(t,r){function e(){this.constructor=t}n(t,r),t.prototype=null===r?Object.create(r):(e.prototype=r.prototype,new e)}var e,i,o,u=function(){return(u=Object.assign||function(t){for(var n,r=1,e=arguments.length;r<e;r++)for(var i in n=arguments[r])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)};function a(t){var n="function"==typeof Symbol&&t[Symbol.iterator],r=0;return n?n.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}}function c(t,n){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var e,i,o=r.call(t),u=[];try{for(;(void 0===n||n-- >0)&&!(e=o.next()).done;)u.push(e.value)}catch(t){i={error:t}}finally{try{e&&!e.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return u}function s(){for(var t=[],n=0;n<arguments.length;n++)t=t.concat(c(arguments[n]));return t}!function(t){t[t.None=0]="None",t[t.Error=1]="Error",t[t.Debug=2]="Debug",t[t.Verbose=3]="Verbose"}(e||(e={})),(i=t.Severity||(t.Severity={})).Fatal="fatal",i.Error="error",i.Warning="warning",i.Log="log",i.Info="info",i.Debug="debug",i.Critical="critical",function(t){t.fromString=function(n){switch(n){case"debug":return t.Debug;case"info":return t.Info;case"warn":case"warning":return t.Warning;case"error":return t.Error;case"fatal":return t.Fatal;case"critical":return t.Critical;case"log":default:return t.Log}}}(t.Severity||(t.Severity={})),(o=t.Status||(t.Status={})).Unknown="unknown",o.Skipped="skipped",o.Success="success",o.RateLimit="rate_limit",o.Invalid="invalid",o.Failed="failed",function(t){t.fromHttpCode=function(n){return n>=200&&n<300?t.Success:429===n?t.RateLimit:n>=400&&n<500?t.Invalid:n>=500?t.Failed:t.Unknown}}(t.Status||(t.Status={}));var f=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,n){return t.__proto__=n,t}:function(t,n){for(var r in n)t.hasOwnProperty(r)||(t[r]=n[r]);return t});var h=function(t){function n(n){var r=this.constructor,e=t.call(this,n)||this;return e.message=n,e.name=r.prototype.constructor.name,f(e,r.prototype),e}return r(n,t),n}(Error);function v(t){switch(Object.prototype.toString.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return T(t,Error)}}function l(t){return"[object ErrorEvent]"===Object.prototype.toString.call(t)}function d(t){return"[object DOMError]"===Object.prototype.toString.call(t)}function p(t){return"[object String]"===Object.prototype.toString.call(t)}function m(t){return null===t||"object"!=typeof t&&"function"!=typeof t}function y(t){return"[object Object]"===Object.prototype.toString.call(t)}function g(t){return"undefined"!=typeof Event&&T(t,Event)}function b(t){return"undefined"!=typeof Element&&T(t,Element)}function w(t){return Boolean(t&&t.then&&"function"==typeof t.then)}function T(t,n){try{return t instanceof n}catch(t){return!1}}function E(t,n){return void 0===n&&(n=0),"string"!=typeof t||0===n?t:t.length<=n?t:t.substr(0,n)+"..."}function x(t,n){if(!Array.isArray(t))return"";for(var r=[],e=0;e<t.length;e++){var i=t[e];try{r.push(String(i))}catch(t){r.push("[value cannot be serialized]")}}return r.join(n)}function _(t,n){return!!p(t)&&(r=n,"[object RegExp]"===Object.prototype.toString.call(r)?n.test(t):"string"==typeof n&&-1!==t.indexOf(n));var r}function j(){return"[object process]"===Object.prototype.toString.call("undefined"!=typeof process?process:0)}var k={};function O(){return j()?global:"undefined"!=typeof window?window:"undefined"!=typeof self?self:k}function S(){var t=O(),n=t.crypto||t.msCrypto;if(void 0!==n&&n.getRandomValues){var r=new Uint16Array(8);n.getRandomValues(r),r[3]=4095&r[3]|16384,r[4]=16383&r[4]|32768;var e=function(t){for(var n=t.toString(16);n.length<4;)n="0"+n;return n};return e(r[0])+e(r[1])+e(r[2])+e(r[3])+e(r[4])+e(r[5])+e(r[6])+e(r[7])}return"xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g,function(t){var n=16*Math.random()|0;return("x"===t?n:3&n|8).toString(16)})}function D(t){if(!t)return{};var n=t.match(/^(([^:\/?#]+):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!n)return{};var r=n[6]||"",e=n[8]||"";return{host:n[4],path:n[5],protocol:n[2],relative:n[5]+r+e}}function I(t){if(t.message)return t.message;if(t.exception&&t.exception.values&&t.exception.values[0]){var n=t.exception.values[0];return n.type&&n.value?n.type+": "+n.value:n.type||n.value||t.event_id||"<unknown>"}return t.event_id||"<unknown>"}function R(t){var n=O();if(!("console"in n))return t();var r=n.console,e={};["debug","info","warn","error","log","assert"].forEach(function(t){t in n.console&&r[t].__sentry_original__&&(e[t]=r[t],r[t]=r[t].__sentry_original__)});var i=t();return Object.keys(e).forEach(function(t){r[t]=e[t]}),i}function N(t,n,r){t.exception=t.exception||{},t.exception.values=t.exception.values||[],t.exception.values[0]=t.exception.values[0]||{},t.exception.values[0].value=t.exception.values[0].value||n||"",t.exception.values[0].type=t.exception.values[0].type||r||"Error"}function A(t,n){void 0===n&&(n={});try{t.exception.values[0].mechanism=t.exception.values[0].mechanism||{},Object.keys(n).forEach(function(r){t.exception.values[0].mechanism[r]=n[r]})}catch(t){}}function C(t){try{for(var n=t,r=[],e=0,i=0,o=" > ".length,u=void 0;n&&e++<5&&!("html"===(u=M(n))||e>1&&i+r.length*o+u.length>=80);)r.push(u),i+=u.length,n=n.parentNode;return r.reverse().join(" > ")}catch(t){return"<unknown>"}}function M(t){var n,r,e,i,o,u=t,a=[];if(!u||!u.tagName)return"";if(a.push(u.tagName.toLowerCase()),u.id&&a.push("#"+u.id),(n=u.className)&&p(n))for(r=n.split(/\s+/),o=0;o<r.length;o++)a.push("."+r[o]);var c=["type","name","title","alt"];for(o=0;o<c.length;o++)e=c[o],(i=u.getAttribute(e))&&a.push("["+e+'="'+i+'"]');return a.join("")}var q=Date.now(),L=0,U={now:function(){var t=Date.now()-q;return t<L&&(t=L),L=t,t},timeOrigin:q},P=function(){if(j())try{return(t=module,n="perf_hooks",t.require(n)).performance}catch(t){return U}var t,n,r=O().performance;return r&&r.now?(void 0===r.timeOrigin&&(r.timeOrigin=r.timing&&r.timing.navigationStart||q),r):U}();function H(){return(P.timeOrigin+P.now())/1e3}var F=6e4;function X(t,n){if(!n)return F;var r=parseInt(""+n,10);if(!isNaN(r))return 1e3*r;var e=Date.parse(""+n);return isNaN(e)?F:e-t}var B="<anonymous>";function W(t){try{return t&&"function"==typeof t&&t.name||B}catch(t){return B}}var $=O(),J="Sentry Logger ",G=function(){function t(){this.t=!1}return t.prototype.disable=function(){this.t=!1},t.prototype.enable=function(){this.t=!0},t.prototype.log=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this.t&&R(function(){$.console.log(J+"[Log]: "+t.join(" "))})},t.prototype.warn=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this.t&&R(function(){$.console.warn(J+"[Warn]: "+t.join(" "))})},t.prototype.error=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this.t&&R(function(){$.console.error(J+"[Error]: "+t.join(" "))})},t}();$.__SENTRY__=$.__SENTRY__||{};var z,V=$.__SENTRY__.logger||($.__SENTRY__.logger=new G),K=function(){function t(){this.i="function"==typeof WeakSet,this.o=this.i?new WeakSet:[]}return t.prototype.memoize=function(t){if(this.i)return!!this.o.has(t)||(this.o.add(t),!1);for(var n=0;n<this.o.length;n++){if(this.o[n]===t)return!0}return this.o.push(t),!1},t.prototype.unmemoize=function(t){if(this.i)this.o.delete(t);else for(var n=0;n<this.o.length;n++)if(this.o[n]===t){this.o.splice(n,1);break}},t}();function Y(t,n,r){if(n in t){var e=t[n],i=r(e);if("function"==typeof i)try{i.prototype=i.prototype||{},Object.defineProperties(i,{__sentry_original__:{enumerable:!1,value:e}})}catch(t){}t[n]=i}}function Q(t){if(v(t)){var n=t,r={message:n.message,name:n.name,stack:n.stack};for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(r[e]=n[e]);return r}if(g(t)){var i=t,o={};o.type=i.type;try{o.target=b(i.target)?C(i.target):Object.prototype.toString.call(i.target)}catch(t){o.target="<unknown>"}try{o.currentTarget=b(i.currentTarget)?C(i.currentTarget):Object.prototype.toString.call(i.currentTarget)}catch(t){o.currentTarget="<unknown>"}for(var e in"undefined"!=typeof CustomEvent&&T(t,CustomEvent)&&(o.detail=i.detail),i)Object.prototype.hasOwnProperty.call(i,e)&&(o[e]=i);return o}return t}function Z(t){return function(t){return~-encodeURI(t).split(/%..|./).length}(JSON.stringify(t))}function tt(t,n,r){void 0===n&&(n=3),void 0===r&&(r=102400);var e=et(t,n);return Z(e)>r?tt(t,n-1,r):e}function nt(t,n){return"domain"===n&&t&&"object"==typeof t&&t.u?"[Domain]":"domainEmitter"===n?"[DomainEmitter]":"undefined"!=typeof global&&t===global?"[Global]":"undefined"!=typeof window&&t===window?"[Window]":"undefined"!=typeof document&&t===document?"[Document]":y(r=t)&&"nativeEvent"in r&&"preventDefault"in r&&"stopPropagation"in r?"[SyntheticEvent]":"number"==typeof t&&t!=t?"[NaN]":void 0===t?"[undefined]":"function"==typeof t?"[Function: "+W(t)+"]":t;var r}function rt(t,n,r,e){if(void 0===r&&(r=1/0),void 0===e&&(e=new K),0===r)return function(t){var n=Object.prototype.toString.call(t);if("string"==typeof t)return t;if("[object Object]"===n)return"[Object]";if("[object Array]"===n)return"[Array]";var r=nt(t);return m(r)?r:n}(n);if(null!=n&&"function"==typeof n.toJSON)return n.toJSON();var i=nt(n,t);if(m(i))return i;var o=Q(n),u=Array.isArray(n)?[]:{};if(e.memoize(n))return"[Circular ~]";for(var a in o)Object.prototype.hasOwnProperty.call(o,a)&&(u[a]=rt(a,o[a],r-1,e));return e.unmemoize(n),u}function et(t,n){try{return JSON.parse(JSON.stringify(t,function(t,r){return rt(t,r,n)}))}catch(t){return"**non-serializable**"}}function it(t,n){void 0===n&&(n=40);var r=Object.keys(Q(t));if(r.sort(),!r.length)return"[object has no keys]";if(r[0].length>=n)return E(r[0],n);for(var e=r.length;e>0;e--){var i=r.slice(0,e).join(", ");if(!(i.length>n))return e===r.length?i:E(i,n)}return""}function ot(t){var n,r;if(y(t)){var e=t,i={};try{for(var o=a(Object.keys(e)),u=o.next();!u.done;u=o.next()){var c=u.value;void 0!==e[c]&&(i[c]=ot(e[c]))}}catch(t){n={error:t}}finally{try{u&&!u.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return i}return Array.isArray(t)?t.map(ot):t}!function(t){t.PENDING="PENDING",t.RESOLVED="RESOLVED",t.REJECTED="REJECTED"}(z||(z={}));var ut=function(){function t(t){var n=this;this.s=z.PENDING,this.h=[],this.v=function(t){n.l(z.RESOLVED,t)},this.p=function(t){n.l(z.REJECTED,t)},this.l=function(t,r){n.s===z.PENDING&&(w(r)?r.then(n.v,n.p):(n.s=t,n.m=r,n.g()))},this.T=function(t){n.h=n.h.concat(t),n.g()},this.g=function(){if(n.s!==z.PENDING){var t=n.h.slice();n.h=[],t.forEach(function(t){t.done||(n.s===z.RESOLVED&&t.onfulfilled&&t.onfulfilled(n.m),n.s===z.REJECTED&&t.onrejected&&t.onrejected(n.m),t.done=!0)})}};try{t(this.v,this.p)}catch(t){this.p(t)}}return t.prototype.toString=function(){return"[object SyncPromise]"},t.resolve=function(n){return new t(function(t){t(n)})},t.reject=function(n){return new t(function(t,r){r(n)})},t.all=function(n){return new t(function(r,e){if(Array.isArray(n))if(0!==n.length){var i=n.length,o=[];n.forEach(function(n,u){t.resolve(n).then(function(t){o[u]=t,0===(i-=1)&&r(o)}).then(null,e)})}else r([]);else e(new TypeError("Promise.all requires an array as input."))})},t.prototype.then=function(n,r){var e=this;return new t(function(t,i){e.T({done:!1,onfulfilled:function(r){if(n)try{return void t(n(r))}catch(t){return void i(t)}else t(r)},onrejected:function(n){if(r)try{return void t(r(n))}catch(t){return void i(t)}else i(n)}})})},t.prototype.catch=function(t){return this.then(function(t){return t},t)},t.prototype.finally=function(n){var r=this;return new t(function(t,e){var i,o;return r.then(function(t){o=!1,i=t,n&&n()},function(t){o=!0,i=t,n&&n()}).then(function(){o?e(i):t(i)})})},t}(),at=function(){function t(t){this._=t,this.j=[]}return t.prototype.isReady=function(){return void 0===this._||this.length()<this._},t.prototype.add=function(t){var n=this;return this.isReady()?(-1===this.j.indexOf(t)&&this.j.push(t),t.then(function(){return n.remove(t)}).then(null,function(){return n.remove(t).then(null,function(){})}),t):ut.reject(new h("Not adding Promise due to buffer limit reached."))},t.prototype.remove=function(t){return this.j.splice(this.j.indexOf(t),1)[0]},t.prototype.length=function(){return this.j.length},t.prototype.drain=function(t){var n=this;return new ut(function(r){var e=setTimeout(function(){t&&t>0&&r(!1)},t);ut.all(n.j).then(function(){clearTimeout(e),r(!0)}).then(null,function(){r(!0)})})},t}();function ct(){if(!("fetch"in O()))return!1;try{return new Headers,new Request(""),new Response,!0}catch(t){return!1}}function st(t){return t&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function ft(){if(!ct())return!1;var t=O();if(st(t.fetch))return!0;var n=!1,r=t.document;if(r&&"function"==typeof r.createElement)try{var e=r.createElement("iframe");e.hidden=!0,r.head.appendChild(e),e.contentWindow&&e.contentWindow.fetch&&(n=st(e.contentWindow.fetch)),r.head.removeChild(e)}catch(t){V.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",t)}return n}function ht(){if(!ct())return!1;try{return new Request("_",{referrerPolicy:"origin"}),!0}catch(t){return!1}}var vt,lt=O(),dt={},pt={};function mt(t){if(!pt[t])switch(pt[t]=!0,t){case"console":!function(){if(!("console"in lt))return;["debug","info","warn","error","log","assert"].forEach(function(t){t in lt.console&&Y(lt.console,t,function(n){return function(){for(var r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];gt("console",{args:r,level:t}),n&&Function.prototype.apply.call(n,lt.console,r)}})})}();break;case"dom":!function(){if(!("document"in lt))return;lt.document.addEventListener("click",jt("click",gt.bind(null,"dom")),!1),lt.document.addEventListener("keypress",kt(gt.bind(null,"dom")),!1),["EventTarget","Node"].forEach(function(t){var n=lt[t]&&lt[t].prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&(Y(n,"addEventListener",function(t){return function(n,r,e){return r&&r.handleEvent?("click"===n&&Y(r,"handleEvent",function(t){return function(n){return jt("click",gt.bind(null,"dom"))(n),t.call(this,n)}}),"keypress"===n&&Y(r,"handleEvent",function(t){return function(n){return kt(gt.bind(null,"dom"))(n),t.call(this,n)}})):("click"===n&&jt("click",gt.bind(null,"dom"),!0)(this),"keypress"===n&&kt(gt.bind(null,"dom"))(this)),t.call(this,n,r,e)}}),Y(n,"removeEventListener",function(t){return function(n,r,e){try{t.call(this,n,r.__sentry_wrapped__,e)}catch(t){}return t.call(this,n,r,e)}}))})}();break;case"xhr":!function(){if(!("XMLHttpRequest"in lt))return;var t=XMLHttpRequest.prototype;Y(t,"open",function(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var e=this,i=n[1];e.__sentry_xhr__={method:p(n[0])?n[0].toUpperCase():n[0],url:n[1]},p(i)&&"POST"===e.__sentry_xhr__.method&&i.match(/sentry_key/)&&(e.__sentry_own_request__=!0);var o=function(){if(4===e.readyState){try{e.__sentry_xhr__&&(e.__sentry_xhr__.status_code=e.status)}catch(t){}gt("xhr",{args:n,endTimestamp:Date.now(),startTimestamp:Date.now(),xhr:e})}};return"onreadystatechange"in e&&"function"==typeof e.onreadystatechange?Y(e,"onreadystatechange",function(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return o(),t.apply(e,n)}}):e.addEventListener("readystatechange",o),t.apply(e,n)}}),Y(t,"send",function(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return gt("xhr",{args:n,startTimestamp:Date.now(),xhr:this}),t.apply(this,n)}})}();break;case"fetch":!function(){if(!ft())return;Y(lt,"fetch",function(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var e={args:n,fetchData:{method:bt(n),url:wt(n)},startTimestamp:Date.now()};return gt("fetch",u({},e)),t.apply(lt,n).then(function(t){return gt("fetch",u({},e,{endTimestamp:Date.now(),response:t})),t},function(t){throw gt("fetch",u({},e,{endTimestamp:Date.now(),error:t})),t})}})}();break;case"history":!function(){if(t=O(),n=t.chrome,r=n&&n.app&&n.app.runtime,e="history"in t&&!!t.history.pushState&&!!t.history.replaceState,r||!e)return;var t,n,r,e;var i=lt.onpopstate;function o(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var e=n.length>2?n[2]:void 0;if(e){var i=vt,o=String(e);vt=o,gt("history",{from:i,to:o})}return t.apply(this,n)}}lt.onpopstate=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=lt.location.href,e=vt;if(vt=r,gt("history",{from:e,to:r}),i)return i.apply(this,t)},Y(lt.history,"pushState",o),Y(lt.history,"replaceState",o)}();break;case"error":Ot=lt.onerror,lt.onerror=function(t,n,r,e,i){return gt("error",{column:e,error:i,line:r,msg:t,url:n}),!!Ot&&Ot.apply(this,arguments)};break;case"unhandledrejection":St=lt.onunhandledrejection,lt.onunhandledrejection=function(t){return gt("unhandledrejection",t),!St||St.apply(this,arguments)};break;default:V.warn("unknown instrumentation type:",t)}}function yt(t){t&&"string"==typeof t.type&&"function"==typeof t.callback&&(dt[t.type]=dt[t.type]||[],dt[t.type].push(t.callback),mt(t.type))}function gt(t,n){var r,e;if(t&&dt[t])try{for(var i=a(dt[t]||[]),o=i.next();!o.done;o=i.next()){var u=o.value;try{u(n)}catch(n){V.error("Error while triggering instrumentation handler.\nType: "+t+"\nName: "+W(u)+"\nError: "+n)}}}catch(t){r={error:t}}finally{try{o&&!o.done&&(e=i.return)&&e.call(i)}finally{if(r)throw r.error}}}function bt(t){return void 0===t&&(t=[]),"Request"in lt&&T(t[0],Request)&&t[0].method?String(t[0].method).toUpperCase():t[1]&&t[1].method?String(t[1].method).toUpperCase():"GET"}function wt(t){return void 0===t&&(t=[]),"string"==typeof t[0]?t[0]:"Request"in lt&&T(t[0],Request)?t[0].url:String(t[0])}var Tt,Et,xt=1e3,_t=0;function jt(t,n,r){return void 0===r&&(r=!1),function(e){Tt=void 0,e&&Et!==e&&(Et=e,_t&&clearTimeout(_t),r?_t=setTimeout(function(){n({event:e,name:t})}):n({event:e,name:t}))}}function kt(t){return function(n){var r;try{r=n.target}catch(t){return}var e=r&&r.tagName;e&&("INPUT"===e||"TEXTAREA"===e||r.isContentEditable)&&(Tt||jt("input",t)(n),clearTimeout(Tt),Tt=setTimeout(function(){Tt=void 0},xt))}}var Ot=null;var St=null;var Dt=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+))?@)([\w\.-]+)(?::(\d+))?\/(.+)/,It=function(){function t(t){"string"==typeof t?this.k(t):this.O(t),this.S()}return t.prototype.toString=function(t){void 0===t&&(t=!1);var n=this,r=n.host,e=n.path,i=n.pass,o=n.port,u=n.projectId;return n.protocol+"://"+n.user+(t&&i?":"+i:"")+"@"+r+(o?":"+o:"")+"/"+(e?e+"/":e)+u},t.prototype.k=function(t){var n=Dt.exec(t);if(!n)throw new h("Invalid Dsn");var r=c(n.slice(1),6),e=r[0],i=r[1],o=r[2],u=void 0===o?"":o,a=r[3],s=r[4],f=void 0===s?"":s,v="",l=r[5],d=l.split("/");if(d.length>1&&(v=d.slice(0,-1).join("/"),l=d.pop()),l){var p=l.match(/^\d+/);p&&(l=p[0])}this.O({host:a,pass:u,path:v,projectId:l,port:f,protocol:e,user:i})},t.prototype.O=function(t){this.protocol=t.protocol,this.user=t.user,this.pass=t.pass||"",this.host=t.host,this.port=t.port||"",this.path=t.path||"",this.projectId=t.projectId},t.prototype.S=function(){var t=this;if(["protocol","user","host","projectId"].forEach(function(n){if(!t[n])throw new h("Invalid Dsn: "+n+" missing")}),!this.projectId.match(/^\d+$/))throw new h("Invalid Dsn: Invalid projectId "+this.projectId);if("http"!==this.protocol&&"https"!==this.protocol)throw new h("Invalid Dsn: Invalid protocol "+this.protocol);if(this.port&&isNaN(parseInt(this.port,10)))throw new h("Invalid Dsn: Invalid port "+this.port)},t}(),Rt=function(){function t(){this.D=!1,this.I=[],this.R=[],this.N=[],this.A={},this.C={},this.M={},this.q={}}return t.prototype.addScopeListener=function(t){this.I.push(t)},t.prototype.addEventProcessor=function(t){return this.R.push(t),this},t.prototype.L=function(){var t=this;this.D||(this.D=!0,setTimeout(function(){t.I.forEach(function(n){n(t)}),t.D=!1}))},t.prototype.U=function(t,n,r,e){var i=this;return void 0===e&&(e=0),new ut(function(o,a){var c=t[e];if(null===n||"function"!=typeof c)o(n);else{var s=c(u({},n),r);w(s)?s.then(function(n){return i.U(t,n,r,e+1).then(o)}).then(null,a):i.U(t,s,r,e+1).then(o).then(null,a)}})},t.prototype.setUser=function(t){return this.A=t||{},this.L(),this},t.prototype.setTags=function(t){return this.C=u({},this.C,t),this.L(),this},t.prototype.setTag=function(t,n){var r;return this.C=u({},this.C,((r={})[t]=n,r)),this.L(),this},t.prototype.setExtras=function(t){return this.M=u({},this.M,t),this.L(),this},t.prototype.setExtra=function(t,n){var r;return this.M=u({},this.M,((r={})[t]=n,r)),this.L(),this},t.prototype.setFingerprint=function(t){return this.P=t,this.L(),this},t.prototype.setLevel=function(t){return this.H=t,this.L(),this},t.prototype.setTransactionName=function(t){return this.F=t,this.L(),this},t.prototype.setTransaction=function(t){return this.setTransactionName(t)},t.prototype.setContext=function(t,n){var r;return this.q=u({},this.q,((r={})[t]=n,r)),this.L(),this},t.prototype.setSpan=function(t){return this.X=t,this.L(),this},t.prototype.getSpan=function(){return this.X},t.prototype.getTransaction=function(){var t=this.getSpan();if(t&&t.spanRecorder&&t.spanRecorder.spans[0])return t.spanRecorder.spans[0]},t.clone=function(n){var r=new t;return n&&(r.N=s(n.N),r.C=u({},n.C),r.M=u({},n.M),r.q=u({},n.q),r.A=n.A,r.H=n.H,r.X=n.X,r.F=n.F,r.P=n.P,r.R=s(n.R)),r},t.prototype.update=function(n){if(!n)return this;if("function"==typeof n){var r=n(this);return r instanceof t?r:this}return n instanceof t?(this.C=u({},this.C,n.C),this.M=u({},this.M,n.M),this.q=u({},this.q,n.q),n.A&&(this.A=n.A),n.H&&(this.H=n.H),n.P&&(this.P=n.P)):y(n)&&(n=n,this.C=u({},this.C,n.tags),this.M=u({},this.M,n.extra),this.q=u({},this.q,n.contexts),n.user&&(this.A=n.user),n.level&&(this.H=n.level),n.fingerprint&&(this.P=n.fingerprint)),this},t.prototype.clear=function(){return this.N=[],this.C={},this.M={},this.A={},this.q={},this.H=void 0,this.F=void 0,this.P=void 0,this.X=void 0,this.L(),this},t.prototype.addBreadcrumb=function(t,n){var r=u({timestamp:H()},t);return this.N=void 0!==n&&n>=0?s(this.N,[r]).slice(-n):s(this.N,[r]),this.L(),this},t.prototype.clearBreadcrumbs=function(){return this.N=[],this.L(),this},t.prototype.B=function(t){t.fingerprint=t.fingerprint?Array.isArray(t.fingerprint)?t.fingerprint:[t.fingerprint]:[],this.P&&(t.fingerprint=t.fingerprint.concat(this.P)),t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint},t.prototype.applyToEvent=function(t,n){return this.M&&Object.keys(this.M).length&&(t.extra=u({},this.M,t.extra)),this.C&&Object.keys(this.C).length&&(t.tags=u({},this.C,t.tags)),this.A&&Object.keys(this.A).length&&(t.user=u({},this.A,t.user)),this.q&&Object.keys(this.q).length&&(t.contexts=u({},this.q,t.contexts)),this.H&&(t.level=this.H),this.F&&(t.transaction=this.F),this.X&&(t.contexts=u({trace:this.X.getTraceContext()},t.contexts)),this.B(t),t.breadcrumbs=s(t.breadcrumbs||[],this.N),t.breadcrumbs=t.breadcrumbs.length>0?t.breadcrumbs:void 0,this.U(s(Nt(),this.R),t,n)},t}();function Nt(){var t=O();return t.__SENTRY__=t.__SENTRY__||{},t.__SENTRY__.globalEventProcessors=t.__SENTRY__.globalEventProcessors||[],t.__SENTRY__.globalEventProcessors}function At(t){Nt().push(t)}var Ct=3,Mt=function(){function t(t,n,r){void 0===n&&(n=new Rt),void 0===r&&(r=Ct),this.W=r,this.$=[],this.$.push({client:t,scope:n}),this.bindClient(t)}return t.prototype.J=function(t){for(var n,r=[],e=1;e<arguments.length;e++)r[e-1]=arguments[e];var i=this.getStackTop();i&&i.client&&i.client[t]&&(n=i.client)[t].apply(n,s(r,[i.scope]))},t.prototype.isOlderThan=function(t){return this.W<t},t.prototype.bindClient=function(t){this.getStackTop().client=t,t&&t.setupIntegrations&&t.setupIntegrations()},t.prototype.pushScope=function(){var t=this.getStack(),n=t.length>0?t[t.length-1].scope:void 0,r=Rt.clone(n);return this.getStack().push({client:this.getClient(),scope:r}),r},t.prototype.popScope=function(){return void 0!==this.getStack().pop()},t.prototype.withScope=function(t){var n=this.pushScope();try{t(n)}finally{this.popScope()}},t.prototype.getClient=function(){return this.getStackTop().client},t.prototype.getScope=function(){return this.getStackTop().scope},t.prototype.getStack=function(){return this.$},t.prototype.getStackTop=function(){return this.$[this.$.length-1]},t.prototype.captureException=function(t,n){var r=this.G=S(),e=n;if(!n){var i=void 0;try{throw new Error("Sentry syntheticException")}catch(t){i=t}e={originalException:t,syntheticException:i}}return this.J("captureException",t,u({},e,{event_id:r})),r},t.prototype.captureMessage=function(t,n,r){var e=this.G=S(),i=r;if(!r){var o=void 0;try{throw new Error(t)}catch(t){o=t}i={originalException:t,syntheticException:o}}return this.J("captureMessage",t,n,u({},i,{event_id:e})),e},t.prototype.captureEvent=function(t,n){var r=this.G=S();return this.J("captureEvent",t,u({},n,{event_id:r})),r},t.prototype.lastEventId=function(){return this.G},t.prototype.addBreadcrumb=function(t,n){var r=this.getStackTop();if(r.scope&&r.client){var e=r.client.getOptions&&r.client.getOptions()||{},i=e.beforeBreadcrumb,o=void 0===i?null:i,a=e.maxBreadcrumbs,c=void 0===a?100:a;if(!(c<=0)){var s=H(),f=u({timestamp:s},t),h=o?R(function(){return o(f,n)}):f;null!==h&&r.scope.addBreadcrumb(h,Math.min(c,100))}}},t.prototype.setUser=function(t){var n=this.getStackTop();n.scope&&n.scope.setUser(t)},t.prototype.setTags=function(t){var n=this.getStackTop();n.scope&&n.scope.setTags(t)},t.prototype.setExtras=function(t){var n=this.getStackTop();n.scope&&n.scope.setExtras(t)},t.prototype.setTag=function(t,n){var r=this.getStackTop();r.scope&&r.scope.setTag(t,n)},t.prototype.setExtra=function(t,n){var r=this.getStackTop();r.scope&&r.scope.setExtra(t,n)},t.prototype.setContext=function(t,n){var r=this.getStackTop();r.scope&&r.scope.setContext(t,n)},t.prototype.configureScope=function(t){var n=this.getStackTop();n.scope&&n.client&&t(n.scope)},t.prototype.run=function(t){var n=Lt(this);try{t(this)}finally{Lt(n)}},t.prototype.getIntegration=function(t){var n=this.getClient();if(!n)return null;try{return n.getIntegration(t)}catch(n){return V.warn("Cannot retrieve integration "+t.id+" from the current Hub"),null}},t.prototype.startSpan=function(t){return this.V("startSpan",t)},t.prototype.startTransaction=function(t){return this.V("startTransaction",t)},t.prototype.traceHeaders=function(){return this.V("traceHeaders")},t.prototype.V=function(t){for(var n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];var e=qt().__SENTRY__;if(e&&e.extensions&&"function"==typeof e.extensions[t])return e.extensions[t].apply(this,n);V.warn("Extension method "+t+" couldn't be found, doing nothing.")},t}();function qt(){var t=O();return t.__SENTRY__=t.__SENTRY__||{extensions:{},hub:void 0},t}function Lt(t){var n=qt(),r=Ht(n);return Ft(n,t),r}function Ut(){var t=qt();return Pt(t)&&!Ht(t).isOlderThan(Ct)||Ft(t,new Mt),j()?function(t){try{var n=qt(),r=n.__SENTRY__;if(!r||!r.extensions||!r.extensions.domain)return Ht(t);var e=r.extensions.domain,i=e.active;if(!i)return Ht(t);if(!Pt(i)||Ht(i).isOlderThan(Ct)){var o=Ht(t).getStackTop();Ft(i,new Mt(o.client,Rt.clone(o.scope)))}return Ht(i)}catch(n){return Ht(t)}}(t):Ht(t)}function Pt(t){return!!(t&&t.__SENTRY__&&t.__SENTRY__.hub)}function Ht(t){return t&&t.__SENTRY__&&t.__SENTRY__.hub?t.__SENTRY__.hub:(t.__SENTRY__=t.__SENTRY__||{},t.__SENTRY__.hub=new Mt,t.__SENTRY__.hub)}function Ft(t,n){return!!t&&(t.__SENTRY__=t.__SENTRY__||{},t.__SENTRY__.hub=n,!0)}function Xt(t){for(var n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];var e=Ut();if(e&&e[t])return e[t].apply(e,s(n));throw new Error("No hub defined or "+t+" was not found on the hub, please open a bug report.")}function captureException(t,n){var r;try{throw new Error("Sentry syntheticException")}catch(t){r=t}return Xt("captureException",t,{captureContext:n,originalException:t,syntheticException:r})}function Bt(t){Xt("withScope",t)}var Wt=function(){function t(t){this.dsn=t,this.K=new It(t)}return t.prototype.getDsn=function(){return this.K},t.prototype.getBaseApiEndpoint=function(){var t=this.K,n=t.protocol?t.protocol+":":"",r=t.port?":"+t.port:"";return n+"//"+t.host+r+(t.path?"/"+t.path:"")+"/api/"},t.prototype.getStoreEndpoint=function(){return this.Y("store")},t.prototype.Z=function(){return this.Y("envelope")},t.prototype.Y=function(t){return""+this.getBaseApiEndpoint()+this.K.projectId+"/"+t+"/"},t.prototype.getStoreEndpointWithUrlEncodedAuth=function(){return this.getStoreEndpoint()+"?"+this.tt()},t.prototype.getEnvelopeEndpointWithUrlEncodedAuth=function(){return this.Z()+"?"+this.tt()},t.prototype.tt=function(){var t,n={sentry_key:this.K.user,sentry_version:"7"};return t=n,Object.keys(t).map(function(n){return encodeURIComponent(n)+"="+encodeURIComponent(t[n])}).join("&")},t.prototype.getStoreEndpointPath=function(){var t=this.K;return(t.path?"/"+t.path:"")+"/api/"+t.projectId+"/store/"},t.prototype.getRequestHeaders=function(t,n){var r=this.K,e=["Sentry sentry_version=7"];return e.push("sentry_client="+t+"/"+n),e.push("sentry_key="+r.user),r.pass&&e.push("sentry_secret="+r.pass),{"Content-Type":"application/json","X-Sentry-Auth":e.join(", ")}},t.prototype.getReportDialogEndpoint=function(t){void 0===t&&(t={});var n=this.K,r=this.getBaseApiEndpoint()+"embed/error-page/",e=[];for(var i in e.push("dsn="+n.toString()),t)if("user"===i){if(!t.user)continue;t.user.name&&e.push("name="+encodeURIComponent(t.user.name)),t.user.email&&e.push("email="+encodeURIComponent(t.user.email))}else e.push(encodeURIComponent(i)+"="+encodeURIComponent(t[i]));return e.length?r+"?"+e.join("&"):r},t}(),$t=[];function Jt(t){var n={};return function(t){var n=t.defaultIntegrations&&s(t.defaultIntegrations)||[],r=t.integrations,e=[];if(Array.isArray(r)){var i=r.map(function(t){return t.name}),o=[];n.forEach(function(t){-1===i.indexOf(t.name)&&-1===o.indexOf(t.name)&&(e.push(t),o.push(t.name))}),r.forEach(function(t){-1===o.indexOf(t.name)&&(e.push(t),o.push(t.name))})}else"function"==typeof r?(e=r(n),e=Array.isArray(e)?e:[e]):e=s(n);var u=e.map(function(t){return t.name});return-1!==u.indexOf("Debug")&&e.push.apply(e,s(e.splice(u.indexOf("Debug"),1))),e}(t).forEach(function(t){n[t.name]=t,function(t){-1===$t.indexOf(t.name)&&(t.setupOnce(At,Ut),$t.push(t.name),V.log("Integration installed: "+t.name))}(t)}),n}var Gt,zt=function(){function t(t,n){this.nt={},this.rt=!1,this.et=new t(n),this.it=n,n.dsn&&(this.ot=new It(n.dsn))}return t.prototype.captureException=function(t,n,r){var e=this,i=n&&n.event_id;return this.rt=!0,this.ut().eventFromException(t,n).then(function(t){i=e.captureEvent(t,n,r)}),i},t.prototype.captureMessage=function(t,n,r,e){var i=this,o=r&&r.event_id;return this.rt=!0,(m(t)?this.ut().eventFromMessage(""+t,n,r):this.ut().eventFromException(t,r)).then(function(t){o=i.captureEvent(t,r,e)}),o},t.prototype.captureEvent=function(t,n,r){var e=this,i=n&&n.event_id;return this.rt=!0,this.at(t,n,r).then(function(t){i=t&&t.event_id,e.rt=!1}).then(null,function(t){V.error(t),e.rt=!1}),i},t.prototype.getDsn=function(){return this.ot},t.prototype.getOptions=function(){return this.it},t.prototype.flush=function(t){var n=this;return this.ct(t).then(function(r){return clearInterval(r.interval),n.ut().getTransport().close(t).then(function(t){return r.ready&&t})})},t.prototype.close=function(t){var n=this;return this.flush(t).then(function(t){return n.getOptions().enabled=!1,t})},t.prototype.setupIntegrations=function(){this.st()&&(this.nt=Jt(this.it))},t.prototype.getIntegration=function(t){try{return this.nt[t.id]||null}catch(n){return V.warn("Cannot retrieve integration "+t.id+" from the current Client"),null}},t.prototype.ct=function(t){var n=this;return new ut(function(r){var e=0,i=0;clearInterval(i),i=setInterval(function(){n.rt?(e+=1,t&&e>=t&&r({interval:i,ready:!1})):r({interval:i,ready:!0})},1)})},t.prototype.ut=function(){return this.et},t.prototype.st=function(){return!1!==this.getOptions().enabled&&void 0!==this.ot},t.prototype.ft=function(t,n,r){var e=this,i=this.getOptions().normalizeDepth,o=void 0===i?3:i,a=u({},t,{event_id:t.event_id||(r&&r.event_id?r.event_id:S()),timestamp:t.timestamp||H()});this.ht(a),this.vt(a);var c=n;r&&r.captureContext&&(c=Rt.clone(c).update(r.captureContext));var s=ut.resolve(a);return c&&(s=c.applyToEvent(a,r)),s.then(function(t){return"number"==typeof o&&o>0?e.lt(t,o):t})},t.prototype.lt=function(t,n){if(!t)return null;var r=u({},t,t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map(function(t){return u({},t,t.data&&{data:et(t.data,n)})})},t.user&&{user:et(t.user,n)},t.contexts&&{contexts:et(t.contexts,n)},t.extra&&{extra:et(t.extra,n)});return t.contexts&&t.contexts.trace&&(r.contexts.trace=t.contexts.trace),r},t.prototype.ht=function(t){var n=this.getOptions(),r=n.environment,e=n.release,i=n.dist,o=n.maxValueLength,u=void 0===o?250:o;void 0===t.environment&&void 0!==r&&(t.environment=r),void 0===t.release&&void 0!==e&&(t.release=e),void 0===t.dist&&void 0!==i&&(t.dist=i),t.message&&(t.message=E(t.message,u));var a=t.exception&&t.exception.values&&t.exception.values[0];a&&a.value&&(a.value=E(a.value,u));var c=t.request;c&&c.url&&(c.url=E(c.url,u))},t.prototype.vt=function(t){var n=t.sdk,r=Object.keys(this.nt);n&&r.length>0&&(n.integrations=r)},t.prototype.dt=function(t){this.ut().sendEvent(t)},t.prototype.at=function(t,n,r){var e=this,i=this.getOptions(),o=i.beforeSend,u=i.sampleRate;if(!this.st())return ut.reject("SDK not enabled, will not send event.");var a="transaction"===t.type;return!a&&"number"==typeof u&&Math.random()>u?ut.reject("This event has been sampled, will not send event."):new ut(function(i,u){e.ft(t,r,n).then(function(t){if(null!==t){var r=t;if(n&&n.data&&!0===n.data.__sentry__||!o||a)return e.dt(r),void i(r);var c=o(t,n);if(void 0===c)V.error("`beforeSend` method has to return `null` or a valid event.");else if(w(c))e.pt(c,i,u);else{if(null===(r=c))return V.log("`beforeSend` returned `null`, will not send event."),void i(null);e.dt(r),i(r)}}else u("An event processor returned null, will not send event.")}).then(null,function(t){e.captureException(t,{data:{__sentry__:!0},originalException:t}),u("Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\nReason: "+t)})})},t.prototype.pt=function(t,n,r){var e=this;t.then(function(t){null!==t?(e.dt(t),n(t)):r("`beforeSend` returned `null`, will not send event.")}).then(null,function(t){r("beforeSend rejected with "+t)})},t}(),Vt=function(){function n(){}return n.prototype.sendEvent=function(n){return ut.resolve({reason:"NoopTransport: Event has been skipped because no Dsn is configured.",status:t.Status.Skipped})},n.prototype.close=function(t){return ut.resolve(!0)},n}(),Kt=function(){function t(t){this.it=t,this.it.dsn||V.warn("No DSN provided, backend will not do anything."),this.yt=this.gt()}return t.prototype.gt=function(){return new Vt},t.prototype.eventFromException=function(t,n){throw new h("Backend has to implement `eventFromException` method")},t.prototype.eventFromMessage=function(t,n,r){throw new h("Backend has to implement `eventFromMessage` method")},t.prototype.sendEvent=function(t){this.yt.sendEvent(t).then(null,function(t){V.error("Error while sending event: "+t)})},t.prototype.getTransport=function(){return this.yt},t}();function Yt(t,n){var r="transaction"===t.type,e={body:JSON.stringify(t),url:r?n.getEnvelopeEndpointWithUrlEncodedAuth():n.getStoreEndpointWithUrlEncodedAuth()};if(r){var i=JSON.stringify({event_id:t.event_id,sent_at:new Date(1e3*H()).toISOString()})+"\n"+JSON.stringify({type:t.type})+"\n"+e.body;e.body=i}return e}var Qt=function(){function t(){this.name=t.id}return t.prototype.setupOnce=function(){Gt=Function.prototype.toString,Function.prototype.toString=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=this.__sentry_original__||this;return Gt.apply(r,t)}},t.id="FunctionToString",t}(),Zt=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/],tn=function(){function t(n){void 0===n&&(n={}),this.it=n,this.name=t.id}return t.prototype.setupOnce=function(){At(function(n){var r=Ut();if(!r)return n;var e=r.getIntegration(t);if(e){var i=r.getClient(),o=i?i.getOptions():{},u=e.bt(o);if(e.wt(n,u))return null}return n})},t.prototype.wt=function(t,n){return this.Tt(t,n)?(V.warn("Event dropped due to being internal Sentry Error.\nEvent: "+I(t)),!0):this.Et(t,n)?(V.warn("Event dropped due to being matched by `ignoreErrors` option.\nEvent: "+I(t)),!0):this.xt(t,n)?(V.warn("Event dropped due to being matched by `denyUrls` option.\nEvent: "+I(t)+".\nUrl: "+this._t(t)),!0):!this.jt(t,n)&&(V.warn("Event dropped due to not being matched by `allowUrls` option.\nEvent: "+I(t)+".\nUrl: "+this._t(t)),!0)},t.prototype.Tt=function(t,n){if(!n.ignoreInternal)return!1;try{return t&&t.exception&&t.exception.values&&t.exception.values[0]&&"SentryError"===t.exception.values[0].type||!1}catch(t){return!1}},t.prototype.Et=function(t,n){return!(!n.ignoreErrors||!n.ignoreErrors.length)&&this.kt(t).some(function(t){return n.ignoreErrors.some(function(n){return _(t,n)})})},t.prototype.xt=function(t,n){if(!n.denyUrls||!n.denyUrls.length)return!1;var r=this._t(t);return!!r&&n.denyUrls.some(function(t){return _(r,t)})},t.prototype.jt=function(t,n){if(!n.allowUrls||!n.allowUrls.length)return!0;var r=this._t(t);return!r||n.allowUrls.some(function(t){return _(r,t)})},t.prototype.bt=function(t){return void 0===t&&(t={}),{allowUrls:s(this.it.whitelistUrls||[],this.it.allowUrls||[],t.whitelistUrls||[],t.allowUrls||[]),denyUrls:s(this.it.blacklistUrls||[],this.it.denyUrls||[],t.blacklistUrls||[],t.denyUrls||[]),ignoreErrors:s(this.it.ignoreErrors||[],t.ignoreErrors||[],Zt),ignoreInternal:void 0===this.it.ignoreInternal||this.it.ignoreInternal}},t.prototype.kt=function(t){if(t.message)return[t.message];if(t.exception)try{var n=t.exception.values&&t.exception.values[0]||{},r=n.type,e=void 0===r?"":r,i=n.value,o=void 0===i?"":i;return[""+o,e+": "+o]}catch(n){return V.error("Cannot extract message for event "+I(t)),[]}return[]},t.prototype._t=function(t){try{if(t.stacktrace){var n=t.stacktrace.frames;return n&&n[n.length-1].filename||null}if(t.exception){var r=t.exception.values&&t.exception.values[0].stacktrace&&t.exception.values[0].stacktrace.frames;return r&&r[r.length-1].filename||null}return null}catch(n){return V.error("Cannot extract url for event "+I(t)),null}},t.id="InboundFilters",t}(),nn=Object.freeze({__proto__:null,FunctionToString:Qt,InboundFilters:tn}),rn="?",en=/^\s*at (?:(.*?) ?\()?((?:file|https?|blob|chrome-extension|address|native|eval|webpack|<anonymous>|[-a-z]+:|.*bundle|\/).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,on=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:file|https?|blob|chrome|webpack|resource|moz-extension).*?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js))(?::(\d+))?(?::(\d+))?\s*$/i,un=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,an=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,cn=/\((\S*)(?::(\d+))(?::(\d+))\)/,sn=/Minified React error #\d+;/i;function fn(t){var n=null,r=0;t&&("number"==typeof t.framesToPop?r=t.framesToPop:sn.test(t.message)&&(r=1));try{if(n=function(t){if(!t||!t.stacktrace)return null;for(var n,r=t.stacktrace,e=/ line (\d+).*script (?:in )?(\S+)(?:: in function (\S+))?$/i,i=/ line (\d+), column (\d+)\s*(?:in (?:<anonymous function: ([^>]+)>|([^\)]+))\((.*)\))? in (.*):\s*$/i,o=r.split("\n"),u=[],a=0;a<o.length;a+=2){var c=null;(n=e.exec(o[a]))?c={url:n[2],func:n[3],args:[],line:+n[1],column:null}:(n=i.exec(o[a]))&&(c={url:n[6],func:n[3]||n[4],args:n[5]?n[5].split(","):[],line:+n[1],column:+n[2]}),c&&(!c.func&&c.line&&(c.func=rn),u.push(c))}if(!u.length)return null;return{message:vn(t),name:t.name,stack:u}}(t))return hn(n,r)}catch(t){}try{if(n=function(t){if(!t||!t.stack)return null;for(var n,r,e,i=[],o=t.stack.split("\n"),u=0;u<o.length;++u){if(r=en.exec(o[u])){var a=r[2]&&0===r[2].indexOf("native");r[2]&&0===r[2].indexOf("eval")&&(n=cn.exec(r[2]))&&(r[2]=n[1],r[3]=n[2],r[4]=n[3]),e={url:r[2]&&0===r[2].indexOf("address at ")?r[2].substr("address at ".length):r[2],func:r[1]||rn,args:a?[r[2]]:[],line:r[3]?+r[3]:null,column:r[4]?+r[4]:null}}else if(r=un.exec(o[u]))e={url:r[2],func:r[1]||rn,args:[],line:+r[3],column:r[4]?+r[4]:null};else{if(!(r=on.exec(o[u])))continue;r[3]&&r[3].indexOf(" > eval")>-1&&(n=an.exec(r[3]))?(r[1]=r[1]||"eval",r[3]=n[1],r[4]=n[2],r[5]=""):0!==u||r[5]||void 0===t.columnNumber||(i[0].column=t.columnNumber+1),e={url:r[3],func:r[1]||rn,args:r[2]?r[2].split(","):[],line:r[4]?+r[4]:null,column:r[5]?+r[5]:null}}!e.func&&e.line&&(e.func=rn),i.push(e)}if(!i.length)return null;return{message:vn(t),name:t.name,stack:i}}(t))return hn(n,r)}catch(t){}return{message:vn(t),name:t&&t.name,stack:[],failed:!0}}function hn(t,n){try{return u({},t,{stack:t.stack.slice(n)})}catch(n){return t}}function vn(t){var n=t&&t.message;return n?n.error&&"string"==typeof n.error.message?n.error.message:n:"No error message"}var ln=50;function dn(t){var n=mn(t.stack),r={type:t.name,value:t.message};return n&&n.length&&(r.stacktrace={frames:n}),void 0===r.type&&""===r.value&&(r.value="Unrecoverable error caught"),r}function pn(t){return{exception:{values:[dn(t)]}}}function mn(t){if(!t||!t.length)return[];var n=t,r=n[0].func||"",e=n[n.length-1].func||"";return-1===r.indexOf("captureMessage")&&-1===r.indexOf("captureException")||(n=n.slice(1)),-1!==e.indexOf("sentryWrapped")&&(n=n.slice(0,-1)),n.slice(0,ln).map(function(t){return{colno:null===t.column?void 0:t.column,filename:t.url||n[0].url,function:t.func||"?",in_app:!0,lineno:null===t.line?void 0:t.line}}).reverse()}function yn(t,n,r){var e,i;if(void 0===r&&(r={}),l(t)&&t.error)return e=pn(fn(t=t.error));if(d(t)||(i=t,"[object DOMException]"===Object.prototype.toString.call(i))){var o=t,u=o.name||(d(o)?"DOMError":"DOMException"),a=o.message?u+": "+o.message:u;return N(e=gn(a,n,r),a),e}return v(t)?e=pn(fn(t)):y(t)||g(t)?(A(e=function(t,n,r){var e={exception:{values:[{type:g(t)?t.constructor.name:r?"UnhandledRejection":"Error",value:"Non-Error "+(r?"promise rejection":"exception")+" captured with keys: "+it(t)}]},extra:{__serialized__:tt(t)}};if(n){var i=mn(fn(n).stack);e.stacktrace={frames:i}}return e}(t,n,r.rejection),{synthetic:!0}),e):(N(e=gn(t,n,r),""+t,void 0),A(e,{synthetic:!0}),e)}function gn(t,n,r){void 0===r&&(r={});var e={message:t};if(r.attachStacktrace&&n){var i=mn(fn(n).stack);e.stacktrace={frames:i}}return e}var bn=function(){function t(t){this.options=t,this.j=new at(30),this.Ot=new Wt(this.options.dsn),this.url=this.Ot.getStoreEndpointWithUrlEncodedAuth()}return t.prototype.sendEvent=function(t){throw new h("Transport Class has to implement `sendEvent` method")},t.prototype.close=function(t){return this.j.drain(t)},t}(),wn=O(),Tn=function(n){function e(){var t=null!==n&&n.apply(this,arguments)||this;return t.St=new Date(Date.now()),t}return r(e,n),e.prototype.sendEvent=function(n){var r=this;if(new Date(Date.now())<this.St)return Promise.reject({event:n,reason:"Transport locked till "+this.St+" due to too many requests.",status:429});var e=Yt(n,this.Ot),i={body:e.body,method:"POST",referrerPolicy:ht()?"origin":""};return void 0!==this.options.fetchParameters&&Object.assign(i,this.options.fetchParameters),void 0!==this.options.headers&&(i.headers=this.options.headers),this.j.add(new ut(function(n,o){wn.fetch(e.url,i).then(function(e){var i=t.Status.fromHttpCode(e.status);if(i!==t.Status.Success){if(i===t.Status.RateLimit){var u=Date.now();r.St=new Date(u+X(u,e.headers.get("Retry-After"))),V.warn("Too many requests, backing off till: "+r.St)}o(e)}else n({status:i})}).catch(o)}))},e}(bn),En=function(n){function e(){var t=null!==n&&n.apply(this,arguments)||this;return t.St=new Date(Date.now()),t}return r(e,n),e.prototype.sendEvent=function(n){var r=this;if(new Date(Date.now())<this.St)return Promise.reject({event:n,reason:"Transport locked till "+this.St+" due to too many requests.",status:429});var e=Yt(n,this.Ot);return this.j.add(new ut(function(n,i){var o=new XMLHttpRequest;for(var u in o.onreadystatechange=function(){if(4===o.readyState){var e=t.Status.fromHttpCode(o.status);if(e!==t.Status.Success){if(e===t.Status.RateLimit){var u=Date.now();r.St=new Date(u+X(u,o.getResponseHeader("Retry-After"))),V.warn("Too many requests, backing off till: "+r.St)}i(o)}else n({status:e})}},o.open("POST",e.url),r.options.headers)r.options.headers.hasOwnProperty(u)&&o.setRequestHeader(u,r.options.headers[u]);o.send(e.body)}))},e}(bn),xn=Object.freeze({__proto__:null,BaseTransport:bn,FetchTransport:Tn,XHRTransport:En}),_n=function(n){function e(){return null!==n&&n.apply(this,arguments)||this}return r(e,n),e.prototype.gt=function(){if(!this.it.dsn)return n.prototype.gt.call(this);var t=u({},this.it.transportOptions,{dsn:this.it.dsn});return this.it.transport?new this.it.transport(t):ct()?new Tn(t):new En(t)},e.prototype.eventFromException=function(n,r){return function(n,r,e){var i=yn(r,e&&e.syntheticException||void 0,{attachStacktrace:n.attachStacktrace});return A(i,{handled:!0,type:"generic"}),i.level=t.Severity.Error,e&&e.event_id&&(i.event_id=e.event_id),ut.resolve(i)}(this.it,n,r)},e.prototype.eventFromMessage=function(n,r,e){return void 0===r&&(r=t.Severity.Info),function(n,r,e,i){void 0===e&&(e=t.Severity.Info);var o=gn(r,i&&i.syntheticException||void 0,{attachStacktrace:n.attachStacktrace});return o.level=e,i&&i.event_id&&(o.event_id=i.event_id),ut.resolve(o)}(this.it,n,r,e)},e}(Kt),jn=0;function kn(){return jn>0}function On(t,n,r){if(void 0===n&&(n={}),"function"!=typeof t)return t;try{if(t.__sentry__)return t;if(t.__sentry_wrapped__)return t.__sentry_wrapped__}catch(n){return t}var sentryWrapped=function(){var e=Array.prototype.slice.call(arguments);try{r&&"function"==typeof r&&r.apply(this,arguments);var i=e.map(function(t){return On(t,n)});return t.handleEvent?t.handleEvent.apply(this,i):t.apply(this,i)}catch(t){throw jn+=1,setTimeout(function(){jn-=1}),Bt(function(r){r.addEventProcessor(function(t){var r=u({},t);return n.mechanism&&(N(r,void 0,void 0),A(r,n.mechanism)),r.extra=u({},r.extra,{arguments:e}),r}),captureException(t)}),t}};try{for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&(sentryWrapped[e]=t[e])}catch(t){}t.prototype=t.prototype||{},sentryWrapped.prototype=t.prototype,Object.defineProperty(t,"__sentry_wrapped__",{enumerable:!1,value:sentryWrapped}),Object.defineProperties(sentryWrapped,{__sentry__:{enumerable:!1,value:!0},__sentry_original__:{enumerable:!1,value:t}});try{Object.getOwnPropertyDescriptor(sentryWrapped,"name").configurable&&Object.defineProperty(sentryWrapped,"name",{get:function(){return t.name}})}catch(t){}return sentryWrapped}function Sn(t){if(void 0===t&&(t={}),t.eventId)if(t.dsn){var n=document.createElement("script");n.async=!0,n.src=new Wt(t.dsn).getReportDialogEndpoint(t),t.onLoad&&(n.onload=t.onLoad),(document.head||document.body).appendChild(n)}else V.error("Missing dsn option in showReportDialog call");else V.error("Missing eventId option in showReportDialog call")}var Dn=function(){function n(t){this.name=n.id,this.Dt=!1,this.It=!1,this.it=u({onerror:!0,onunhandledrejection:!0},t)}return n.prototype.setupOnce=function(){Error.stackTraceLimit=50,this.it.onerror&&(V.log("Global Handler attached: onerror"),this.Rt()),this.it.onunhandledrejection&&(V.log("Global Handler attached: onunhandledrejection"),this.Nt())},n.prototype.Rt=function(){var t=this;this.Dt||(yt({callback:function(r){var e=r.error,i=Ut(),o=i.getIntegration(n),u=e&&!0===e.__sentry_own_request__;if(o&&!kn()&&!u){var a=i.getClient(),c=m(e)?t.At(r.msg,r.url,r.line,r.column):t.Ct(yn(e,void 0,{attachStacktrace:a&&a.getOptions().attachStacktrace,rejection:!1}),r.url,r.line,r.column);A(c,{handled:!1,type:"onerror"}),i.captureEvent(c,{originalException:e})}},type:"error"}),this.Dt=!0)},n.prototype.Nt=function(){var r=this;this.It||(yt({callback:function(e){var i=e;try{"reason"in e?i=e.reason:"detail"in e&&"reason"in e.detail&&(i=e.detail.reason)}catch(t){}var o=Ut(),u=o.getIntegration(n),a=i&&!0===i.__sentry_own_request__;if(!u||kn()||a)return!0;var c=o.getClient(),s=m(i)?r.Mt(i):yn(i,void 0,{attachStacktrace:c&&c.getOptions().attachStacktrace,rejection:!0});s.level=t.Severity.Error,A(s,{handled:!1,type:"onunhandledrejection"}),o.captureEvent(s,{originalException:i})},type:"unhandledrejection"}),this.It=!0)},n.prototype.At=function(t,n,r,e){var i,o=l(t)?t.message:t;if(p(o)){var u=o.match(/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i);u&&(i=u[1],o=u[2])}var a={exception:{values:[{type:i||"Error",value:o}]}};return this.Ct(a,n,r,e)},n.prototype.Mt=function(t){return{exception:{values:[{type:"UnhandledRejection",value:"Non-Error promise rejection captured with value: "+t}]}}},n.prototype.Ct=function(t,n,r,e){t.exception=t.exception||{},t.exception.values=t.exception.values||[],t.exception.values[0]=t.exception.values[0]||{},t.exception.values[0].stacktrace=t.exception.values[0].stacktrace||{},t.exception.values[0].stacktrace.frames=t.exception.values[0].stacktrace.frames||[];var i=isNaN(parseInt(e,10))?void 0:e,o=isNaN(parseInt(r,10))?void 0:r,u=p(n)&&n.length>0?n:function(){try{return document.location.href}catch(t){return""}}();return 0===t.exception.values[0].stacktrace.frames.length&&t.exception.values[0].stacktrace.frames.push({colno:i,filename:u,function:"?",in_app:!0,lineno:o}),t},n.id="GlobalHandlers",n}(),In=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],Rn=function(){function t(n){this.name=t.id,this.it=u({XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0},n)}return t.prototype.qt=function(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var e=n[0];return n[0]=On(e,{mechanism:{data:{function:W(t)},handled:!0,type:"instrument"}}),t.apply(this,n)}},t.prototype.Lt=function(t){return function(n){return t.call(this,On(n,{mechanism:{data:{function:"requestAnimationFrame",handler:W(t)},handled:!0,type:"instrument"}}))}},t.prototype.Ut=function(t){var n=O(),r=n[t]&&n[t].prototype;r&&r.hasOwnProperty&&r.hasOwnProperty("addEventListener")&&(Y(r,"addEventListener",function(n){return function(r,e,i){try{"function"==typeof e.handleEvent&&(e.handleEvent=On(e.handleEvent.bind(e),{mechanism:{data:{function:"handleEvent",handler:W(e),target:t},handled:!0,type:"instrument"}}))}catch(t){}return n.call(this,r,On(e,{mechanism:{data:{function:"addEventListener",handler:W(e),target:t},handled:!0,type:"instrument"}}),i)}}),Y(r,"removeEventListener",function(t){return function(n,r,e){try{t.call(this,n,r.__sentry_wrapped__,e)}catch(t){}return t.call(this,n,r,e)}}))},t.prototype.Pt=function(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var e=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(function(t){t in e&&"function"==typeof e[t]&&Y(e,t,function(n){var r={mechanism:{data:{function:t,handler:W(n)},handled:!0,type:"instrument"}};return n.__sentry_original__&&(r.mechanism.data.handler=W(n.__sentry_original__)),On(n,r)})}),t.apply(this,n)}},t.prototype.setupOnce=function(){var t=O();(this.it.setTimeout&&Y(t,"setTimeout",this.qt.bind(this)),this.it.setInterval&&Y(t,"setInterval",this.qt.bind(this)),this.it.requestAnimationFrame&&Y(t,"requestAnimationFrame",this.Lt.bind(this)),this.it.XMLHttpRequest&&"XMLHttpRequest"in t&&Y(XMLHttpRequest.prototype,"send",this.Pt.bind(this)),this.it.eventTarget)&&(Array.isArray(this.it.eventTarget)?this.it.eventTarget:In).forEach(this.Ut.bind(this))},t.id="TryCatch",t}(),Nn=function(){function n(t){this.name=n.id,this.it=u({console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0},t)}return n.prototype.addSentryBreadcrumb=function(t){this.it.sentry&&Ut().addBreadcrumb({category:"sentry."+("transaction"===t.type?"transaction":"event"),event_id:t.event_id,level:t.level,message:I(t)},{event:t})},n.prototype.Ht=function(n){var r={category:"console",data:{arguments:n.args,logger:"console"},level:t.Severity.fromString(n.level),message:x(n.args," ")};if("assert"===n.level){if(!1!==n.args[0])return;r.message="Assertion failed: "+(x(n.args.slice(1)," ")||"console.assert"),r.data.arguments=n.args.slice(1)}Ut().addBreadcrumb(r,{input:n.args,level:n.level})},n.prototype.Ft=function(t){var n;try{n=t.event.target?C(t.event.target):C(t.event)}catch(t){n="<unknown>"}0!==n.length&&Ut().addBreadcrumb({category:"ui."+t.name,message:n},{event:t.event,name:t.name})},n.prototype.Xt=function(t){if(t.endTimestamp){if(t.xhr.__sentry_own_request__)return;Ut().addBreadcrumb({category:"xhr",data:t.xhr.__sentry_xhr__,type:"http"},{xhr:t.xhr})}else;},n.prototype.Bt=function(n){n.endTimestamp&&(n.fetchData.url.match(/sentry_key/)&&"POST"===n.fetchData.method||(n.error?Ut().addBreadcrumb({category:"fetch",data:n.fetchData,level:t.Severity.Error,type:"http"},{data:n.error,input:n.args}):Ut().addBreadcrumb({category:"fetch",data:u({},n.fetchData,{status_code:n.response.status}),type:"http"},{input:n.args,response:n.response})))},n.prototype.Wt=function(t){var n=O(),r=t.from,e=t.to,i=D(n.location.href),o=D(r),u=D(e);o.path||(o=i),i.protocol===u.protocol&&i.host===u.host&&(e=u.relative),i.protocol===o.protocol&&i.host===o.host&&(r=o.relative),Ut().addBreadcrumb({category:"navigation",data:{from:r,to:e}})},n.prototype.setupOnce=function(){var t=this;this.it.console&&yt({callback:function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];t.Ht.apply(t,s(n))},type:"console"}),this.it.dom&&yt({callback:function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];t.Ft.apply(t,s(n))},type:"dom"}),this.it.xhr&&yt({callback:function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];t.Xt.apply(t,s(n))},type:"xhr"}),this.it.fetch&&yt({callback:function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];t.Bt.apply(t,s(n))},type:"fetch"}),this.it.history&&yt({callback:function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];t.Wt.apply(t,s(n))},type:"history"})},n.id="Breadcrumbs",n}(),An="cause",Cn=5,Mn=function(){function t(n){void 0===n&&(n={}),this.name=t.id,this.$t=n.key||An,this._=n.limit||Cn}return t.prototype.setupOnce=function(){At(function(n,r){var e=Ut().getIntegration(t);return e?e.Jt(n,r):n})},t.prototype.Jt=function(t,n){if(!(t.exception&&t.exception.values&&n&&T(n.originalException,Error)))return t;var r=this.Gt(n.originalException,this.$t);return t.exception.values=s(r,t.exception.values),t},t.prototype.Gt=function(t,n,r){if(void 0===r&&(r=[]),!T(t[n],Error)||r.length+1>=this._)return r;var e=dn(fn(t[n]));return this.Gt(t[n],n,s([e],r))},t.id="LinkedErrors",t}(),qn=O(),Ln=function(){function t(){this.name=t.id}return t.prototype.setupOnce=function(){At(function(n){if(Ut().getIntegration(t)){if(!qn.navigator||!qn.location)return n;var r=n.request||{};return r.url=r.url||qn.location.href,r.headers=r.headers||{},r.headers["User-Agent"]=qn.navigator.userAgent,u({},n,{request:r})}return n})},t.id="UserAgent",t}(),Un=Object.freeze({__proto__:null,GlobalHandlers:Dn,TryCatch:Rn,Breadcrumbs:Nn,LinkedErrors:Mn,UserAgent:Ln}),Pn="sentry.javascript.browser",Hn=function(t){function n(n){return void 0===n&&(n={}),t.call(this,_n,n)||this}return r(n,t),n.prototype.ft=function(n,r,e){return n.platform=n.platform||"javascript",n.sdk=u({},n.sdk,{name:Pn,packages:s(n.sdk&&n.sdk.packages||[],[{name:"npm:@sentry/browser",version:"5.20.1"}]),version:"5.20.1"}),t.prototype.ft.call(this,n,r,e)},n.prototype.dt=function(n){var r=this.getIntegration(Nn);r&&r.addSentryBreadcrumb(n),t.prototype.dt.call(this,n)},n.prototype.showReportDialog=function(t){void 0===t&&(t={}),O().document&&(this.st()?Sn(u({},t,{dsn:t.dsn||this.getDsn()})):V.error("Trying to call showReportDialog with Sentry Client disabled"))},n}(zt),Fn=[new tn,new Qt,new Rn,new Nn,new Dn,new Mn,new Ln];var Xn={},Bn=O();Bn.Sentry&&Bn.Sentry.Integrations&&(Xn=Bn.Sentry.Integrations);var Wn,$n=u({},Xn,nn,Un);!function(t){t.Ok="ok",t.DeadlineExceeded="deadline_exceeded",t.Unauthenticated="unauthenticated",t.PermissionDenied="permission_denied",t.NotFound="not_found",t.ResourceExhausted="resource_exhausted",t.InvalidArgument="invalid_argument",t.Unimplemented="unimplemented",t.Unavailable="unavailable",t.InternalError="internal_error",t.UnknownError="unknown_error",t.Cancelled="cancelled",t.AlreadyExists="already_exists",t.FailedPrecondition="failed_precondition",t.Aborted="aborted",t.OutOfRange="out_of_range",t.DataLoss="data_loss"}(Wn||(Wn={})),function(t){t.fromHttpCode=function(n){if(n<400)return t.Ok;if(n>=400&&n<500)switch(n){case 401:return t.Unauthenticated;case 403:return t.PermissionDenied;case 404:return t.NotFound;case 409:return t.AlreadyExists;case 413:return t.FailedPrecondition;case 429:return t.ResourceExhausted;default:return t.InvalidArgument}if(n>=500&&n<600)switch(n){case 501:return t.Unimplemented;case 503:return t.Unavailable;case 504:return t.DeadlineExceeded;default:return t.InternalError}return t.UnknownError}}(Wn||(Wn={}));var Jn=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$"),Gn=function(){function t(t){if(this.traceId=S(),this.spanId=S().substring(16),this.startTimestamp=H(),this.tags={},this.data={},!t)return this;t.traceId&&(this.traceId=t.traceId),t.spanId&&(this.spanId=t.spanId),t.parentSpanId&&(this.parentSpanId=t.parentSpanId),"sampled"in t&&(this.sampled=t.sampled),t.op&&(this.op=t.op),t.description&&(this.description=t.description),t.data&&(this.data=t.data),t.tags&&(this.tags=t.tags),t.status&&(this.status=t.status),t.startTimestamp&&(this.startTimestamp=t.startTimestamp),t.endTimestamp&&(this.endTimestamp=t.endTimestamp)}return t.prototype.child=function(t){return this.startChild(t)},t.prototype.startChild=function(n){var r=new t(u({},n,{parentSpanId:this.spanId,sampled:this.sampled,traceId:this.traceId}));return r.spanRecorder=this.spanRecorder,r.spanRecorder&&r.spanRecorder.add(r),r},t.fromTraceparent=function(n,r){var e=n.match(Jn);if(e){var i=void 0;return"1"===e[3]?i=!0:"0"===e[3]&&(i=!1),new t(u({},r,{parentSpanId:e[2],sampled:i,traceId:e[1]}))}},t.prototype.setTag=function(t,n){var r;return this.tags=u({},this.tags,((r={})[t]=n,r)),this},t.prototype.setData=function(t,n){var r;return this.data=u({},this.data,((r={})[t]=n,r)),this},t.prototype.setStatus=function(t){return this.status=t,this},t.prototype.setHttpStatus=function(t){this.setTag("http.status_code",String(t));var n=Wn.fromHttpCode(t);return n!==Wn.UnknownError&&this.setStatus(n),this},t.prototype.isSuccess=function(){return this.status===Wn.Ok},t.prototype.finish=function(t){this.endTimestamp="number"==typeof t?t:H()},t.prototype.toTraceparent=function(){var t="";return void 0!==this.sampled&&(t=this.sampled?"-1":"-0"),this.traceId+"-"+this.spanId+t},t.prototype.getTraceContext=function(){return ot({data:Object.keys(this.data).length>0?this.data:void 0,description:this.description,op:this.op,parent_span_id:this.parentSpanId,span_id:this.spanId,status:this.status,tags:Object.keys(this.tags).length>0?this.tags:void 0,trace_id:this.traceId})},t.prototype.toJSON=function(){return ot({data:Object.keys(this.data).length>0?this.data:void 0,description:this.description,op:this.op,parent_span_id:this.parentSpanId,span_id:this.spanId,start_timestamp:this.startTimestamp,status:this.status,tags:Object.keys(this.tags).length>0?this.tags:void 0,timestamp:this.endTimestamp,trace_id:this.traceId})},t}(),zn=function(){function t(t){void 0===t&&(t=1e3),this.spans=[],this.zt=t}return t.prototype.add=function(t){this.spans.length>this.zt?t.spanRecorder=void 0:this.spans.push(t)},t}(),Vn=function(t){function n(n,r){var e=t.call(this,n)||this;return e.Vt=Ut(),T(r,Mt)&&(e.Vt=r),n.name&&(e.name=n.name),e.Kt=n.trimEnd,e}return r(n,t),n.prototype.setName=function(t){this.name=t},n.prototype.initSpanRecorder=function(t){void 0===t&&(t=1e3),this.spanRecorder||(this.spanRecorder=new zn(t)),this.spanRecorder.add(this)},n.prototype.finish=function(n){var r=this;if(void 0===this.endTimestamp){if(this.name||(V.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this.name="<unlabeled transaction>"),t.prototype.finish.call(this,n),!0===this.sampled){var e=this.spanRecorder?this.spanRecorder.spans.filter(function(t){return t!==r&&t.endTimestamp}):[];return this.Kt&&e.length>0&&(this.endTimestamp=e.reduce(function(t,n){return t.endTimestamp&&n.endTimestamp?t.endTimestamp>n.endTimestamp?t:n:t}).endTimestamp),this.Vt.captureEvent({contexts:{trace:this.getTraceContext()},spans:e,start_timestamp:this.startTimestamp,tags:this.tags,timestamp:this.endTimestamp,transaction:this.name,type:"transaction"})}V.warn("Discarding transaction because it was not chosen to be sampled.")}},n}(Gn);function Kn(){var t=this.getScope();if(t){var n=t.getSpan();if(n)return{"sentry-trace":n.toTraceparent()}}return{}}function Yn(t){var n=new Vn(t,this),r=this.getClient();if(void 0===n.sampled){var e=r&&r.getOptions().tracesSampleRate||0;n.sampled=Math.random()<e}if(n.sampled){var i=r&&r.getOptions().Yt||{};n.initSpanRecorder(i.maxSpans)}return n}function Qn(t){var n=t;if(void 0!==n.transaction&&(n.name=n.transaction),void 0!==n.name)return V.warn("Deprecated: Use startTransaction to start transactions and Transaction.startChild to start spans."),this.startTransaction(n);var r=this.getScope();if(r){var e=r.getSpan();if(e)return e.startChild(t)}return new Gn(t)}var Zn=O(),tr=["localhost",/^\//],nr=function(){function n(t){this.name=n.id,this.Qt=!1,Zn.performance&&(Zn.performance.mark&&Zn.performance.mark("sentry-tracing-init"),n.Zt());var r={beforeNavigate:function(t){return t.pathname},debug:{spanDebugTimingInfo:!1,writeAsBreadcrumbs:!1},idleTimeout:500,markBackgroundTransactions:!0,maxTransactionDuration:600,shouldCreateSpanForRequest:function(n){return(t&&t.tracingOrigins||tr).some(function(t){return _(n,t)})&&!_(n,"sentry_key")},startTransactionOnLocationChange:!0,startTransactionOnPageLoad:!0,traceFetch:!0,traceXHR:!0,tracingOrigins:tr};t&&Array.isArray(t.tracingOrigins)&&0!==t.tracingOrigins.length||(this.Qt=!0),n.options=u({},r,t)}return n.prototype.setupOnce=function(t,r){n.tn=r,this.Qt&&(V.warn("[Tracing] You need to define `tracingOrigins` in the options. Set an array of urls or patterns to trace."),V.warn("[Tracing] We added a reasonable default for you: "+tr)),Zn.location&&n.options&&n.options.startTransactionOnPageLoad&&n.startIdleTransaction({name:n.options.beforeNavigate(window.location),op:"pageload"}),this.nn(),this.rn(),this.en(),this.in(),this.on(),n.un(),t(function(t){if(!r().getIntegration(n))return t;var e=t.timestamp&&t.start_timestamp&&(t.timestamp-t.start_timestamp>n.options.maxTransactionDuration||t.timestamp-t.start_timestamp<0);return 0!==n.options.maxTransactionDuration&&"transaction"===t.type&&e&&(n.an("[Tracing] Transaction: "+Wn.Cancelled+" since it maxed out maxTransactionDuration"),t.contexts&&t.contexts.trace&&(t.contexts.trace=u({},t.contexts.trace,{status:Wn.DeadlineExceeded}),t.tags=u({},t.tags,{maxTransactionDurationExceeded:"true"}))),t})},n.cn=function(t,r){var e,i,o,a=n.sn("sentry-trace");if(a){var c=Gn.fromTraceparent(a);c&&(e=c.traceId,i=c.parentSpanId,o=c.sampled,n.an("[Tracing] found 'sentry-meta' '<meta />' continuing trace with: trace_id: "+e+" span_id: "+i))}return t.startTransaction(u({parentSpanId:i,sampled:o,traceId:e,trimEnd:!0},r))},n.sn=function(t){var n=document.querySelector("meta[name="+t+"]");return n?n.getAttribute("content"):null},n.un=function(){n.fn=setTimeout(function(){n.hn()},5e3)},n.hn=function(){clearTimeout(n.fn);var t=Object.keys(n.vn);if(t.length){var r=t.reduce(function(t,n){return t+n});r===n.ln?n.dn++:n.dn=0,n.dn>=3&&n.pn&&(n.an("[Tracing] Transaction: "+Wn.Cancelled+" -> Heartbeat safeguard kicked in since content hasn't changed for 3 beats"),n.pn.setStatus(Wn.DeadlineExceeded),n.pn.setTag("heartbeat","failed"),n.finishIdleTransaction(H())),n.ln=r}n.un()},n.prototype.on=function(){n.options&&n.options.markBackgroundTransactions&&Zn.document&&document.addEventListener("visibilitychange",function(){document.hidden&&n.pn&&(n.an("[Tracing] Transaction: "+Wn.Cancelled+" -> since tab moved to the background"),n.pn.setStatus(Wn.Cancelled),n.pn.setTag("visibilitychange","document.hidden"),n.finishIdleTransaction(H()))})},n.mn=function(){var t=n.tn;if(t){var r=t().getScope();r&&r.getSpan()===n.pn&&r.setSpan(void 0)}n.pn=void 0,n.vn={}},n.prototype.en=function(){n.options.startTransactionOnLocationChange&&yt({callback:ir,type:"history"})},n.prototype.rn=function(){n.options.traceFetch&&ft()&&yt({callback:er,type:"fetch"})},n.prototype.nn=function(){n.options.traceXHR&&yt({callback:rr,type:"xhr"})},n.prototype.in=function(){function t(){n.pn&&(n.an("[Tracing] Transaction: "+Wn.InternalError+" -> Global error occured"),n.pn.setStatus(Wn.InternalError))}yt({callback:t,type:"error"}),yt({callback:t,type:"unhandledrejection"})},n.an=function(){for(var r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];if(n.options&&n.options.debug&&n.options.debug.writeAsBreadcrumbs){var i=n.tn;i&&i().addBreadcrumb({category:"tracing",level:t.Severity.Debug,message:x(r," "),type:"debug"})}V.log.apply(V,s(r))},n.startIdleTransaction=function(t){n.an("[Tracing] startIdleTransaction");var r=n.tn;if(r){var e=r();if(e){n.pn=n.cn(e,t),e.configureScope(function(t){return t.setSpan(n.pn)});var i=n.pushActivity("idleTransactionStarted");return setTimeout(function(){n.popActivity(i)},n.options&&n.options.idleTimeout||100),n.pn}}},n.finishIdleTransaction=function(t){var r=n.pn;r?(n.an("[Tracing] finishing IdleTransaction",new Date(1e3*t).toISOString()),n.yn(r),r.spanRecorder&&(r.spanRecorder.spans=r.spanRecorder.spans.filter(function(e){if(e.spanId===r.spanId)return e;e.endTimestamp||(e.endTimestamp=t,e.setStatus(Wn.Cancelled),n.an("[Tracing] cancelling span since transaction ended early",JSON.stringify(e,void 0,2)));var i=e.startTimestamp<t;return i||n.an("[Tracing] discarding Span since it happened after Transaction was finished",JSON.stringify(e,void 0,2)),i})),n.an("[Tracing] flushing IdleTransaction"),r.finish(),n.mn()):n.an("[Tracing] No active IdleTransaction")},n.yn=function(t){if(Zn.performance&&Zn.performance.getEntries){n.an("[Tracing] Adding & adjusting spans using Performance API"),"pageload"===t.op&&(n.gn(),n.bn&&t.setData("_sentry_web_vitals",{LCP:n.bn}));var r,e,i,o=n.wn(performance.timeOrigin);if(Zn.document)for(var u=0;u<document.scripts.length;u++)if("true"===document.scripts[u].dataset.entry){r=document.scripts[u].src;break}performance.getEntries().slice(n.Tn).forEach(function(u){var c=n.wn(u.startTime),s=n.wn(u.duration);if(!("navigation"===t.op&&o+c<t.startTimestamp))switch(u.entryType){case"navigation":a(t,u,"unloadEvent"),a(t,u,"domContentLoadedEvent"),a(t,u,"loadEvent"),a(t,u,"connect"),a(t,u,"domainLookup"),function(t,r){or(t,{description:"request",endTimestamp:o+n.wn(r.responseEnd),op:"browser",startTimestamp:o+n.wn(r.requestStart)}),or(t,{description:"response",endTimestamp:o+n.wn(r.responseEnd),op:"browser",startTimestamp:o+n.wn(r.responseStart)})}(t,u);break;case"mark":case"paint":case"measure":var f=or(t,{description:u.name,endTimestamp:o+c+s,op:u.entryType,startTimestamp:o+c});void 0===i&&"sentry-tracing-init"===u.name&&(i=f.startTimestamp);break;case"resource":var h=u.name.replace(window.location.origin,"");if("xmlhttprequest"!==u.initiatorType&&"fetch"!==u.initiatorType){var v=or(t,{description:u.initiatorType+" "+h,endTimestamp:o+c+s,op:"resource",startTimestamp:o+c});void 0===e&&(r||"").indexOf(h)>-1&&(e=v.endTimestamp)}}}),void 0!==e&&void 0!==i&&or(t,{description:"evaluation",endTimestamp:i,op:"script",startTimestamp:e}),n.Tn=Math.max(performance.getEntries().length-1,0)}function a(t,r,e){or(t,{description:e,endTimestamp:o+n.wn(r[e+"End"]),op:"browser",startTimestamp:o+n.wn(r[e+"Start"])})}},n.Zt=function(){try{var t="hidden"===document.visibilityState?0:1/0;document.addEventListener("visibilitychange",function(n){t=Math.min(t,n.timeStamp)},{once:!0});var r=function(r){r.startTime<t&&(n.bn=u({},r.id&&{elementId:r.id},r.size&&{elementSize:r.size},{value:r.startTime}))},e=new PerformanceObserver(function(t){t.getEntries().forEach(r)});e.observe({buffered:!0,type:"largest-contentful-paint"}),n.gn=function(){e.takeRecords().forEach(r)}}catch(t){}},n.setTransactionStatus=function(t){var r=n.pn;r&&(n.an("[Tracing] setTransactionStatus",t),r.setStatus(t))},n.getTransaction=function(){return n.pn},n.wn=function(t){return t/1e3},n.En=function(t){var n={};Zn.performance?(n.performance=!0,n["performance.timeOrigin"]=Zn.performance.timeOrigin,n["performance.now"]=Zn.performance.now(),Zn.performance.timing&&(n["performance.timing.navigationStart"]=performance.timing.navigationStart)):n.performance=!1,n["Date.now()"]=Date.now(),t.setData("sentry_debug",n)},n.pushActivity=function(t,r,e){var i=n.pn;if(!i)return n.an("[Tracing] Not pushing activity "+t+" since there is no active transaction"),0;var o=n.tn;if(r&&o){if(o()){var u=i.startChild(r);n.vn[n.xn]={name:t,span:u}}}else n.vn[n.xn]={name:t};if(n.an("[Tracing] pushActivity: "+t+"#"+n.xn),n.an("[Tracing] activies count",Object.keys(n.vn).length),e&&"number"==typeof e.autoPopAfter){n.an("[Tracing] auto pop of: "+t+"#"+n.xn+" in "+e.autoPopAfter+"ms");var a=n.xn;setTimeout(function(){n.popActivity(a,{autoPop:!0,status:Wn.DeadlineExceeded})},e.autoPopAfter)}return n.xn++},n.popActivity=function(t,r){if(t){var e=n.vn[t];if(e){n.an("[Tracing] popActivity "+e.name+"#"+t);var i=e.span;i&&(r&&Object.keys(r).forEach(function(t){i.setData(t,r[t]),"status_code"===t&&i.setHttpStatus(r[t]),"status"===t&&i.setStatus(r[t])}),n.options&&n.options.debug&&n.options.debug.spanDebugTimingInfo&&n.En(i),i.finish()),delete n.vn[t]}var o=Object.keys(n.vn).length;if(n.an("[Tracing] activies count",o),0===o&&n.pn){var u=n.options&&n.options.idleTimeout;n.an("[Tracing] Flushing Transaction in "+u+"ms");var a=H()+u/1e3;setTimeout(function(){n.finishIdleTransaction(a)},u)}}},n.getActivitySpan=function(t){if(t){var r=n.vn[t];return r?r.span:void 0}},n.id="Tracing",n.xn=1,n.vn={},n.Tn=0,n.fn=0,n.dn=0,n.gn=function(){},n}();function rr(t){if(nr.options.traceXHR&&t&&t.xhr&&t.xhr.__sentry_xhr__){var n=t.xhr.__sentry_xhr__;if(nr.options.shouldCreateSpanForRequest(n.url)&&!t.xhr.__sentry_own_request__)if(t.endTimestamp&&t.xhr.__sentry_xhr_activity_id__)nr.popActivity(t.xhr.__sentry_xhr_activity_id__,t.xhr.__sentry_xhr__);else{t.xhr.__sentry_xhr_activity_id__=nr.pushActivity("xhr",{data:u({},n.data,{type:"xhr"}),description:n.method+" "+n.url,op:"http"});var r=nr.vn[t.xhr.__sentry_xhr_activity_id__];if(r){var e=r.span;if(e&&t.xhr.setRequestHeader)try{t.xhr.setRequestHeader("sentry-trace",e.toTraceparent())}catch(t){}}}}}function er(t){if(nr.options.traceFetch&&nr.options.shouldCreateSpanForRequest(t.fetchData.url))if(t.endTimestamp&&t.fetchData.__activity)nr.popActivity(t.fetchData.__activity,t.fetchData);else{t.fetchData.__activity=nr.pushActivity("fetch",{data:u({},t.fetchData,{type:"fetch"}),description:t.fetchData.method+" "+t.fetchData.url,op:"http"});var n=nr.vn[t.fetchData.__activity];if(n){var r=n.span;if(r){var e=t.args[0]=t.args[0],i=t.args[1]=t.args[1]||{},o=i.headers;T(e,Request)&&(o=e.headers),o?"function"==typeof o.append?o.append("sentry-trace",r.toTraceparent()):o=Array.isArray(o)?s(o,[["sentry-trace",r.toTraceparent()]]):u({},o,{"sentry-trace":r.toTraceparent()}):o={"sentry-trace":r.toTraceparent()},i.headers=o}}}}function ir(t){nr.options.startTransactionOnLocationChange&&Zn&&Zn.location&&(nr.finishIdleTransaction(H()),nr.startIdleTransaction({name:nr.options.beforeNavigate(window.location),op:"navigation"}))}function or(t,n){var r=n.startTimestamp,e=function(t,n){var r={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&n.indexOf(e)<0&&(r[e]=t[e]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(e=Object.getOwnPropertySymbols(t);i<e.length;i++)n.indexOf(e[i])<0&&(r[e[i]]=t[e[i]])}return r}(n,["startTimestamp"]);return r&&t.startTimestamp>r&&(t.startTimestamp=r),t.startChild(u({startTimestamp:r},e))}var ur={},ar=O();ar.Sentry&&ar.Sentry.Integrations&&(ur=ar.Sentry.Integrations);var cr,sr=u({},ur,$n,{Tracing:nr});return(cr=qt()).__SENTRY__&&(cr.__SENTRY__.extensions=cr.__SENTRY__.extensions||{},cr.__SENTRY__.extensions.startTransaction||(cr.__SENTRY__.extensions.startTransaction=Yn),cr.__SENTRY__.extensions.startSpan||(cr.__SENTRY__.extensions.startSpan=Qn),cr.__SENTRY__.extensions.traceHeaders||(cr.__SENTRY__.extensions.traceHeaders=Kn)),t.BrowserClient=Hn,t.Hub=Mt,t.Integrations=sr,t.SDK_NAME=Pn,t.SDK_VERSION="5.20.1",t.Scope=Rt,t.Span=Gn,t.TRACEPARENT_REGEXP=Jn,t.Transports=xn,t.addBreadcrumb=function(t){Xt("addBreadcrumb",t)},t.addGlobalEventProcessor=At,t.captureEvent=function(t){return Xt("captureEvent",t)},t.captureException=captureException,t.captureMessage=function(t,n){var r;try{throw new Error(t)}catch(t){r=t}return Xt("captureMessage",t,"string"==typeof n?n:void 0,u({originalException:t,syntheticException:r},"string"!=typeof n?{captureContext:n}:void 0))},t.close=function(t){var n=Ut().getClient();return n?n.close(t):ut.reject(!1)},t.configureScope=function(t){Xt("configureScope",t)},t.defaultIntegrations=Fn,t.flush=function(t){var n=Ut().getClient();return n?n.flush(t):ut.reject(!1)},t.forceLoad=function(){},t.getCurrentHub=Ut,t.getHubFromCarrier=Ht,t.init=function(t){if(void 0===t&&(t={}),void 0===t.defaultIntegrations&&(t.defaultIntegrations=Fn),void 0===t.release){var n=O();n.SENTRY_RELEASE&&n.SENTRY_RELEASE.id&&(t.release=n.SENTRY_RELEASE.id)}!function(t,n){!0===n.debug&&V.enable();var r=Ut(),e=new t(n);r.bindClient(e)}(Hn,t)},t.lastEventId=function(){return Ut().lastEventId()},t.onLoad=function(t){t()},t.setContext=function(t,n){Xt("setContext",t,n)},t.setExtra=function(t,n){Xt("setExtra",t,n)},t.setExtras=function(t){Xt("setExtras",t)},t.setTag=function(t,n){Xt("setTag",t,n)},t.setTags=function(t){Xt("setTags",t)},t.setUser=function(t){Xt("setUser",t)},t.showReportDialog=function(t){void 0===t&&(t={}),t.eventId||(t.eventId=Ut().lastEventId());var n=Ut().getClient();n&&n.showReportDialog(t)},t.startTransaction=function(t){return Xt("startTransaction",u({},t))},t.withScope=Bt,t.wrap=function(t){return On(t)()},t}({});
