class DoubleTapToSeekHandler {
  video
  lastTouchTime = 0
  forwardSeekWidget
  rewindSeekWidget
  rewoundDuration = 0
  forwardedDuration = 0
  resetDurationTracker = debounce(() => {
    this.rewoundDuration = 0
    this.forwardedDuration = 0
  }, 2000)

  constructor() {
    this.appendSeekWidgetsToPlayer();
    this.initializeProperties();
    this.initListeners();
  }

  appendSeekWidgetsToPlayer() {
    const videoContainer = document.querySelector("#video")
    const seekViewsTemplate = document.querySelector('#seek-views');
    videoContainer.appendChild(seekViewsTemplate.content.cloneNode(true))
  }

  initializeProperties() {
    this.video = document.querySelector('video');
    this.forwardSeekWidget = document.querySelector('.forward-widget');
    this.rewindSeekWidget = document.querySelector('.rewind-widget');
  }

  initListeners() {
    this.video.addEventListener('dblclick', this.handleDoubleClick);
    this.video.addEventListener('touchend', this.handleTouch);
    document.querySelectorAll('.seek').forEach((element) => {
      element.addEventListener('animationend', this.endAnimation);
    });
  }

  handleDoubleClick = (evt) => {
    const videoWidth = this.video.offsetWidth;
    (evt.offsetX < videoWidth / 2) ? this.rewindVideo() : this.forwardVideo();
  }

  handleTouch = (evt) => {
    let now = Date.now();
    let timeSinceLastTouch = now - this.lastTouchTime;
    if (timeSinceLastTouch < 300) {
      evt.preventDefault();
      this.handleDoubleTouch(evt);
    }
    this.lastTouchTime = now;
  }

  endAnimation = (evt) => {
    evt.target.classList.remove('animate-in');
  }

  handleDoubleTouch(evt) {
    let x_offset = evt.changedTouches[0].clientX - evt.target.getBoundingClientRect().left;
    (x_offset < this.video.offsetWidth / 2) ? this.rewindVideo() : this.forwardVideo();
  }

  rewindVideo = () => this.seek(-10);

  forwardVideo = () => this.seek(10);

  seek(delta) {
    let isRewinding = delta < 0;
    this.updateCurrentTime(delta)
    this.trackSeekedDuration(delta, isRewinding)
    this.showSeekWidget(isRewinding)
    this.resetDurationTracker()
  }

  updateCurrentTime = (seconds) => this.video.currentTime += seconds;

  trackSeekedDuration(delta, isRewinding) {
    this.rewoundDuration = isRewinding ? this.rewoundDuration + delta : 0;
    this.forwardedDuration = isRewinding ? 0 : this.forwardedDuration + delta;
  }

  showSeekWidget(isRewinding) {
    let seekWidget = isRewinding ? this.rewindSeekWidget : this.forwardSeekWidget;
    const duration = isRewinding ? this.rewoundDuration : this.forwardedDuration;
    seekWidget.querySelector(".duration").innerHTML = `${Math.abs(duration)} seconds`;
    this.startAnimation(seekWidget);
  }

  startAnimation(seekWidget) {
    seekWidget.classList.add('animate-in');
  }
}

function debounce(func, wait) {
  let timeout;
  return (...args) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      timeout = null;
      func(...args);
    }, wait);
  };
}
