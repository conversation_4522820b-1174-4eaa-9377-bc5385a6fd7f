/*! For license information please see amazon-ivs-wasmworker.min.js.LICENSE.txt */
!function(){var e={648:function(e,t,n){var r=n(288).default;function i(){"use strict";e.exports=i=function(){return t},e.exports.__esModule=!0,e.exports.default=e.exports;var t={},n=Object.prototype,o=n.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},s="function"==typeof Symbol?Symbol:{},u=s.iterator||"@@iterator",c=s.asyncIterator||"@@asyncIterator",f=s.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,n){return e[t]=n}}function d(e,t,n,r){var i=t&&t.prototype instanceof v?t:v,o=Object.create(i.prototype),s=new P(r||[]);return a(o,"_invoke",{value:T(e,n,s)}),o}function h(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var p={};function v(){}function g(){}function y(){}var m={};l(m,u,(function(){return this}));var b=Object.getPrototypeOf,E=b&&b(b(O([])));E&&E!==n&&o.call(E,u)&&(m=E);var w=y.prototype=v.prototype=Object.create(m);function C(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){function n(i,a,s,u){var c=h(e[i],e,a);if("throw"!==c.type){var f=c.arg,l=f.value;return l&&"object"==r(l)&&o.call(l,"__await")?t.resolve(l.__await).then((function(e){n("next",e,s,u)}),(function(e){n("throw",e,s,u)})):t.resolve(l).then((function(e){f.value=e,s(f)}),(function(e){return n("throw",e,s,u)}))}u(c.arg)}var i;a(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,i){n(e,r,t,i)}))}return i=i?i.then(o,o):o()}})}function T(e,t,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return{value:void 0,done:!0}}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var s=S(a,n);if(s){if(s===p)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var u=h(e,t,n);if("normal"===u.type){if(r=n.done?"completed":"suspendedYield",u.arg===p)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(r="completed",n.method="throw",n.arg=u.arg)}}}function S(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;var i=h(r,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,p;var o=i.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,p):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,p)}function k(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function A(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(k,this),this.reset(!0)}function O(e){if(e){var t=e[u];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,r=function t(){for(;++n<e.length;)if(o.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:R}}function R(){return{value:void 0,done:!0}}return g.prototype=y,a(w,"constructor",{value:y,configurable:!0}),a(y,"constructor",{value:g,configurable:!0}),g.displayName=l(y,f,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,l(e,f,"GeneratorFunction")),e.prototype=Object.create(w),e},t.awrap=function(e){return{__await:e}},C(_.prototype),l(_.prototype,c,(function(){return this})),t.AsyncIterator=_,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var a=new _(d(e,n,r,i),o);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},C(w),l(w,f,"Generator"),l(w,u,(function(){return this})),l(w,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=O,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(A),!e)for(var t in this)"t"===t.charAt(0)&&o.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var s=o.call(i,"catchLoc"),u=o.call(i,"finallyLoc");if(s&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),p},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),A(n),p}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;A(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:O(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},t}e.exports=i,e.exports.__esModule=!0,e.exports.default=e.exports},288:function(e){function t(n){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},357:function(e,t,n){var r=n(648)();e.exports=r;try{regeneratorRuntime=r}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=r:Function("r","regeneratorRuntime = r")(r)}}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";!function(e){if("performance"in e||(e.performance={}),!("now"in e.performance)){var t=Date.now();e.performance={now:function(){return Date.now()-t}}}}(self)}(),function(){"use strict";var e,t=(e="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,function(t){var r,i;(t=void 0!==(t=t||{})?t:{}).ready=new Promise((function(e,t){r=e,i=t}));var o,a={};for(o in t)t.hasOwnProperty(o)&&(a[o]=t[o]);var s,u=[],c="./this.program",f="";f=self.location.href,e&&(f=e),f=0!==f.indexOf("blob:")?f.substr(0,f.lastIndexOf("/")+1):"",s=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)};var l,d,h=t.print||console.log.bind(console),p=t.printErr||console.warn.bind(console);for(o in a)a.hasOwnProperty(o)&&(t[o]=a[o]);function v(e){v.shown||(v.shown={}),v.shown[e]||(v.shown[e]=1,p(e))}a=null,t.arguments&&(u=t.arguments),t.thisProgram&&(c=t.thisProgram),t.quit&&t.quit,t.wasmBinary&&(l=t.wasmBinary),t.noExitRuntime&&t.noExitRuntime,"object"!=typeof WebAssembly&&z("no native wasm support detected");var g=!1,y="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function m(e,t,n){for(var r=t+n,i=t;e[i]&&!(i>=r);)++i;if(i-t>16&&e.subarray&&y)return y.decode(e.subarray(t,i));for(var o="";t<i;){var a=e[t++];if(128&a){var s=63&e[t++];if(192!=(224&a)){var u=63&e[t++];if((a=224==(240&a)?(15&a)<<12|s<<6|u:(7&a)<<18|s<<12|u<<6|63&e[t++])<65536)o+=String.fromCharCode(a);else{var c=a-65536;o+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else o+=String.fromCharCode((31&a)<<6|s)}else o+=String.fromCharCode(a)}return o}function b(e,t){return e?m(T,e,t):""}function E(e,t,n,r){if(!(r>0))return 0;for(var i=n,o=n+r-1,a=0;a<e.length;++a){var s=e.charCodeAt(a);if(s>=55296&&s<=57343&&(s=65536+((1023&s)<<10)|1023&e.charCodeAt(++a)),s<=127){if(n>=o)break;t[n++]=s}else if(s<=2047){if(n+1>=o)break;t[n++]=192|s>>6,t[n++]=128|63&s}else if(s<=65535){if(n+2>=o)break;t[n++]=224|s>>12,t[n++]=128|s>>6&63,t[n++]=128|63&s}else{if(n+3>=o)break;t[n++]=240|s>>18,t[n++]=128|s>>12&63,t[n++]=128|s>>6&63,t[n++]=128|63&s}}return t[n]=0,n-i}function w(e){for(var t=0,n=0;n<e.length;++n){var r=e.charCodeAt(n);r>=55296&&r<=57343&&(r=65536+((1023&r)<<10)|1023&e.charCodeAt(++n)),r<=127?++t:t+=r<=2047?2:r<=65535?3:4}return t}var C,_,T,S,k,A,P,O,R,D="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function M(e,t){for(var n=e,r=n>>1,i=r+t/2;!(r>=i)&&k[r];)++r;if((n=r<<1)-e>32&&D)return D.decode(T.subarray(e,n));for(var o="",a=0;!(a>=t/2);++a){var s=S[e+2*a>>1];if(0==s)break;o+=String.fromCharCode(s)}return o}function $(e,t,n){if(void 0===n&&(n=2147483647),n<2)return 0;for(var r=t,i=(n-=2)<2*e.length?n/2:e.length,o=0;o<i;++o){var a=e.charCodeAt(o);S[t>>1]=a,t+=2}return S[t>>1]=0,t-r}function I(e){return 2*e.length}function N(e,t){for(var n=0,r="";!(n>=t/4);){var i=A[e+4*n>>2];if(0==i)break;if(++n,i>=65536){var o=i-65536;r+=String.fromCharCode(55296|o>>10,56320|1023&o)}else r+=String.fromCharCode(i)}return r}function x(e,t,n){if(void 0===n&&(n=2147483647),n<4)return 0;for(var r=t,i=r+n-4,o=0;o<e.length;++o){var a=e.charCodeAt(o);if(a>=55296&&a<=57343&&(a=65536+((1023&a)<<10)|1023&e.charCodeAt(++o)),A[t>>2]=a,(t+=4)+4>i)break}return A[t>>2]=0,t-r}function j(e){for(var t=0,n=0;n<e.length;++n){var r=e.charCodeAt(n);r>=55296&&r<=57343&&++n,t+=4}return t}function F(e){var t=w(e)+1,n=wt(t);return n&&E(e,_,n,t),n}function L(e){C=e,t.HEAP8=_=new Int8Array(e),t.HEAP16=S=new Int16Array(e),t.HEAP32=A=new Int32Array(e),t.HEAPU8=T=new Uint8Array(e),t.HEAPU16=k=new Uint16Array(e),t.HEAPU32=P=new Uint32Array(e),t.HEAPF32=O=new Float32Array(e),t.HEAPF64=R=new Float64Array(e)}t.INITIAL_MEMORY;var U,G=[],B=[],H=[],W=[];B.push({func:function(){Et()}});var Y=0,V=null,q=null;function z(e){t.onAbort&&t.onAbort(e),p(e+=""),g=!0,e="abort("+e+"). Build with -s ASSERTIONS=1 for more info.";var n=new WebAssembly.RuntimeError(e);throw i(n),n}t.preloadedImages={},t.preloadedAudios={};function K(e){return t=e,n="data:application/octet-stream;base64,",String.prototype.startsWith?t.startsWith(n):0===t.indexOf(n);var t,n}var X,J="amazon-ivs-wasmworker.min.wasm";function Q(e){try{if(e==J&&l)return new Uint8Array(l);if(s)return s(e);throw"both async and sync fetching of the wasm failed"}catch(e){z(e)}}function Z(e){for(;e.length>0;){var n=e.shift();if("function"!=typeof n){var r=n.func;"number"==typeof r?void 0===n.arg?U.get(r)():U.get(r)(n.arg):r(void 0===n.arg?null:n.arg)}else n(t)}}K(J)||(X=J,J=t.locateFile?t.locateFile(X,f):f+X);var ee={};function te(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function ne(e){return this.fromWireType(P[e>>2])}var re={},ie={},oe={},ae=48,se=57;function ue(e){if(void 0===e)return"_unknown";var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return t>=ae&&t<=se?"_"+e:e}function ce(e,t){return e=ue(e),function(){return t.apply(this,arguments)}}function fe(e,t){var n=ce(t,(function(e){this.name=t,this.message=e;var n=new Error(e).stack;void 0!==n&&(this.stack=this.toString()+"\n"+n.replace(/^Error(:[^\n]*)?\n/,""))}));return n.prototype=Object.create(e.prototype),n.prototype.constructor=n,n.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},n}var le=void 0;function de(e){throw new le(e)}function he(e,t,n){function r(t){var r=n(t);r.length!==e.length&&de("Mismatched type converter count");for(var i=0;i<e.length;++i)be(e[i],r[i])}e.forEach((function(e){oe[e]=t}));var i=new Array(t.length),o=[],a=0;t.forEach((function(e,t){ie.hasOwnProperty(e)?i[t]=ie[e]:(o.push(e),re.hasOwnProperty(e)||(re[e]=[]),re[e].push((function(){i[t]=ie[e],++a===o.length&&r(i)})))})),0===o.length&&r(i)}function pe(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var ve=void 0;function ge(e){for(var t="",n=e;T[n];)t+=ve[T[n++]];return t}var ye=void 0;function me(e){throw new ye(e)}function be(e,t,n){if(n=n||{},!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var r=t.name;if(e||me('type "'+r+'" must have a positive integer typeid pointer'),ie.hasOwnProperty(e)){if(n.ignoreDuplicateRegistrations)return;me("Cannot register type '"+r+"' twice")}if(ie[e]=t,delete oe[e],re.hasOwnProperty(e)){var i=re[e];delete re[e],i.forEach((function(e){e()}))}}function Ee(e){me(function(e){return e.$$.ptrType.registeredClass.name}(e)+" instance already deleted")}var we=!1;function Ce(e){}function _e(e){e.count.value-=1,0===e.count.value&&function(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}(e)}function Te(e){return"undefined"==typeof FinalizationGroup?(Te=function(e){return e},e):(we=new FinalizationGroup((function(e){for(var t=e.next();!t.done;t=e.next()){var n=t.value;n.ptr?_e(n):console.warn("object already deleted: "+n.ptr)}})),Te=function(e){return we.register(e,e.$$,e.$$),e},Ce=function(e){we.unregister(e.$$)},Te(e))}var Se=void 0,ke=[];function Ae(){for(;ke.length;){var e=ke.pop();e.$$.deleteScheduled=!1,e.delete()}}function Pe(){}var Oe={};function Re(e,t,n){if(void 0===e[t].overloadTable){var r=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||me("Function '"+n+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].overloadTable+")!"),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[r.argCount]=r}}function De(e,t,n,r,i,o,a,s){this.name=e,this.constructor=t,this.instancePrototype=n,this.rawDestructor=r,this.baseClass=i,this.getActualType=o,this.upcast=a,this.downcast=s,this.pureVirtualFunctions=[]}function Me(e,t,n){for(;t!==n;)t.upcast||me("Expected null or instance of "+n.name+", got an instance of "+t.name),e=t.upcast(e),t=t.baseClass;return e}function $e(e,t){if(null===t)return this.isReference&&me("null is not a valid "+this.name),0;t.$$||me('Cannot pass "'+Ke(t)+'" as a '+this.name),t.$$.ptr||me("Cannot pass deleted object as a pointer of type "+this.name);var n=t.$$.ptrType.registeredClass;return Me(t.$$.ptr,n,this.registeredClass)}function Ie(e,t){var n;if(null===t)return this.isReference&&me("null is not a valid "+this.name),this.isSmartPointer?(n=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,n),n):0;t.$$||me('Cannot pass "'+Ke(t)+'" as a '+this.name),t.$$.ptr||me("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&t.$$.ptrType.isConst&&me("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);var r=t.$$.ptrType.registeredClass;if(n=Me(t.$$.ptr,r,this.registeredClass),this.isSmartPointer)switch(void 0===t.$$.smartPtr&&me("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?n=t.$$.smartPtr:me("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:n=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)n=t.$$.smartPtr;else{var i=t.clone();n=this.rawShare(n,ze((function(){i.delete()}))),null!==e&&e.push(this.rawDestructor,n)}break;default:me("Unsupporting sharing policy")}return n}function Ne(e,t){if(null===t)return this.isReference&&me("null is not a valid "+this.name),0;t.$$||me('Cannot pass "'+Ke(t)+'" as a '+this.name),t.$$.ptr||me("Cannot pass deleted object as a pointer of type "+this.name),t.$$.ptrType.isConst&&me("Cannot convert argument of type "+t.$$.ptrType.name+" to parameter type "+this.name);var n=t.$$.ptrType.registeredClass;return Me(t.$$.ptr,n,this.registeredClass)}function xe(e,t,n){if(t===n)return e;if(void 0===n.baseClass)return null;var r=xe(e,t,n.baseClass);return null===r?null:n.downcast(r)}var je={};function Fe(e,t){return t.ptrType&&t.ptr||de("makeClassHandle requires ptr and ptrType"),!!t.smartPtrType!=!!t.smartPtr&&de("Both smartPtrType and smartPtr must be specified"),t.count={value:1},Te(Object.create(e,{$$:{value:t}}))}function Le(e,t,n,r,i,o,a,s,u,c,f){this.name=e,this.registeredClass=t,this.isReference=n,this.isConst=r,this.isSmartPointer=i,this.pointeeType=o,this.sharingPolicy=a,this.rawGetPointee=s,this.rawConstructor=u,this.rawShare=c,this.rawDestructor=f,i||void 0!==t.baseClass?this.toWireType=Ie:r?(this.toWireType=$e,this.destructorFunction=null):(this.toWireType=Ne,this.destructorFunction=null)}function Ue(e,n){var r,i,o,a=-1!=(e=ge(e)).indexOf("j")?(r=e,i=n,o=[],function(){o.length=arguments.length;for(var e=0;e<arguments.length;e++)o[e]=arguments[e];return function(e,n,r){return-1!=e.indexOf("j")?function(e,n,r){var i=t["dynCall_"+e];return r&&r.length?i.apply(null,[n].concat(r)):i.call(null,n)}(e,n,r):U.get(n).apply(null,r)}(r,i,o)}):U.get(n);return"function"!=typeof a&&me("unknown function pointer with signature "+e+": "+n),a}var Ge=void 0;function Be(e){var t=Ct(e),n=ge(t);return Tt(t),n}function He(e,t){var n=[],r={};throw t.forEach((function e(t){r[t]||ie[t]||(oe[t]?oe[t].forEach(e):(n.push(t),r[t]=!0))})),new Ge(e+": "+n.map(Be).join([", "]))}function We(e,t){for(var n=[],r=0;r<e;r++)n.push(A[(t>>2)+r]);return n}var Ye=[],Ve=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function qe(e){e>4&&0==--Ve[e].refcount&&(Ve[e]=void 0,Ye.push(e))}function ze(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var t=Ye.length?Ye.pop():Ve.length;return Ve[t]={refcount:1,value:e},t}}function Ke(e){if(null===e)return"null";var t=typeof e;return"object"===t||"array"===t||"function"===t?e.toString():""+e}function Xe(e,t){switch(t){case 2:return function(e){return this.fromWireType(O[e>>2])};case 3:return function(e){return this.fromWireType(R[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function Je(e,t,n){switch(t){case 0:return n?function(e){return _[e]}:function(e){return T[e]};case 1:return n?function(e){return S[e>>1]}:function(e){return k[e>>1]};case 2:return n?function(e){return A[e>>2]}:function(e){return P[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}function Qe(e){return e||me("Cannot use deleted val. handle = "+e),Ve[e].value}function Ze(e,t){var n=ie[e];return void 0===n&&me(t+" has unknown type "+Be(e)),n}function et(e,t){for(var n=new Array(e),r=0;r<e;++r)n[r]=Ze(A[(t>>2)+r],"parameter "+r);return n}var tt={};function nt(e){var t=tt[e];return void 0===t?ge(e):t}var rt,it=[];function ot(){if("object"==typeof globalThis)return globalThis;function e(e){e.$$$embind_global$$$=e;var t="object"==typeof $$$embind_global$$$&&e.$$$embind_global$$$===e;return t||delete e.$$$embind_global$$$,t}if("object"==typeof $$$embind_global$$$)return $$$embind_global$$$;if("object"==typeof n.g&&e(n.g)?$$$embind_global$$$=n.g:"object"==typeof self&&e(self)&&($$$embind_global$$$=self),"object"==typeof $$$embind_global$$$)return $$$embind_global$$$;throw Error("unable to get global object.")}function at(e){if(!e||!e.callee||!e.callee.name)return[null,"",""];e.callee.toString();var t=e.callee.name,n="(",r=!0;for(var i in e){var o=e[i];r||(n+=", "),r=!1,n+="number"==typeof o||"string"==typeof o?o:"("+typeof o+")"}n+=")";var a=e.callee.caller;return r&&(n=""),[e=a?a.arguments:[],t,n]}function st(e){try{return d.grow(e-C.byteLength+65535>>>16),L(d.buffer),1}catch(e){}}rt=function(){return performance.now()},t._emscripten_log_js=function(e,t){24&e&&(t=t.replace(/\s+$/,""),t+=(t.length>0?"\n":"")+function(e){var t=function(){var e=new Error;if(!e.stack){try{throw new Error}catch(t){e=t}if(!e.stack)return"(no stack trace available)"}return e.stack.toString()}(),n=t.lastIndexOf("_emscripten_log"),r=t.lastIndexOf("_emscripten_get_callstack"),i=t.indexOf("\n",Math.max(n,r))+1;t=t.slice(i),32&e&&v("EM_LOG_DEMANGLE is deprecated; ignoring"),8&e&&"undefined"==typeof emscripten_source_map&&(v('Source map information is not available, emscripten_log with EM_LOG_C_STACK will be ignored. Build with "--pre-js $EMSCRIPTEN/src/emscripten-source-map.min.js" linker flag to add source map loading to code.'),e^=8,e|=16);var o=null;if(128&e)for(o=at(arguments);o[1].indexOf("_emscripten_")>=0;)o=at(o[0]);var a=t.split("\n");t="";var s=new RegExp("\\s*(.*?)@(.*?):([0-9]+):([0-9]+)"),u=new RegExp("\\s*(.*?)@(.*):(.*)(:(.*))?"),c=new RegExp("\\s*at (.*?) \\((.*):(.*):(.*)\\)");for(var f in a){var l=a[f],d="",h="",p=0,g=0,y=c.exec(l);if(y&&5==y.length)d=y[1],h=y[2],p=y[3],g=y[4];else{if((y=s.exec(l))||(y=u.exec(l)),!(y&&y.length>=4)){t+=l+"\n";continue}d=y[1],h=y[2],p=y[3],g=0|y[4]}var m=!1;if(8&e){var b=emscripten_source_map.originalPositionFor({line:p,column:g});(m=b&&b.source)&&(64&e&&(b.source=b.source.substring(b.source.replace(/\\/g,"/").lastIndexOf("/")+1)),t+="    at "+d+" ("+b.source+":"+b.line+":"+b.column+")\n")}(16&e||!m)&&(64&e&&(h=h.substring(h.replace(/\\/g,"/").lastIndexOf("/")+1)),t+=(m?"     = "+d:"    at "+d)+" ("+h+":"+p+":"+g+")\n"),128&e&&o[0]&&(o[1]==d&&o[2].length>0&&(t=t.replace(/\s+$/,""),t+=" with values: "+o[1]+o[2]+"\n"),o=at(o[0]))}return t.replace(/\s+$/,"")}(e)),1&e?4&e?console.error(t):2&e?console.warn(t):512&e?console.info(t):256&e?console.debug(t):console.log(t):6&e?p(t):h(t)};var ut={};function ct(){if(!ct.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:c||"./this.program"};for(var t in ut)e[t]=ut[t];var n=[];for(var t in e)n.push(t+"="+e[t]);ct.strings=n}return ct.strings}var ft={mappings:{},buffers:[null,[],[]],printChar:function(e,t){var n=ft.buffers[e];0===t||10===t?((1===e?h:p)(m(n,0)),n.length=0):n.push(t)},varargs:void 0,get:function(){return ft.varargs+=4,A[ft.varargs-4>>2]},getStr:function(e){return b(e)},get64:function(e,t){return e}};function lt(){if(!lt.called){lt.called=!0;var e=(new Date).getFullYear(),t=new Date(e,0,1),n=new Date(e,6,1),r=t.getTimezoneOffset(),i=n.getTimezoneOffset(),o=Math.max(r,i);A[At()>>2]=60*o,A[kt()>>2]=Number(r!=i);var a=f(t),s=f(n),u=F(a),c=F(s);i<r?(A[St()>>2]=u,A[St()+4>>2]=c):(A[St()>>2]=c,A[St()+4>>2]=u)}function f(e){var t=e.toTimeString().match(/\(([A-Za-z ]+)\)$/);return t?t[1]:"GMT"}}function dt(e){return e%4==0&&(e%100!=0||e%400==0)}function ht(e,t){for(var n=0,r=0;r<=t;n+=e[r++]);return n}var pt=[31,29,31,30,31,30,31,31,30,31,30,31],vt=[31,28,31,30,31,30,31,31,30,31,30,31];function gt(e,t){for(var n=new Date(e.getTime());t>0;){var r=dt(n.getFullYear()),i=n.getMonth(),o=(r?pt:vt)[i];if(!(t>o-n.getDate()))return n.setDate(n.getDate()+t),n;t-=o-n.getDate()+1,n.setDate(1),i<11?n.setMonth(i+1):(n.setMonth(0),n.setFullYear(n.getFullYear()+1))}return n}function yt(e,t,n,r){var i=A[r+40>>2],o={tm_sec:A[r>>2],tm_min:A[r+4>>2],tm_hour:A[r+8>>2],tm_mday:A[r+12>>2],tm_mon:A[r+16>>2],tm_year:A[r+20>>2],tm_wday:A[r+24>>2],tm_yday:A[r+28>>2],tm_isdst:A[r+32>>2],tm_gmtoff:A[r+36>>2],tm_zone:i?b(i):""},a=b(n),s={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var u in s)a=a.replace(new RegExp(u,"g"),s[u]);var c=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],f=["January","February","March","April","May","June","July","August","September","October","November","December"];function l(e,t,n){for(var r="number"==typeof e?e.toString():e||"";r.length<t;)r=n[0]+r;return r}function d(e,t){return l(e,t,"0")}function h(e,t){function n(e){return e<0?-1:e>0?1:0}var r;return 0===(r=n(e.getFullYear()-t.getFullYear()))&&0===(r=n(e.getMonth()-t.getMonth()))&&(r=n(e.getDate()-t.getDate())),r}function p(e){switch(e.getDay()){case 0:return new Date(e.getFullYear()-1,11,29);case 1:return e;case 2:return new Date(e.getFullYear(),0,3);case 3:return new Date(e.getFullYear(),0,2);case 4:return new Date(e.getFullYear(),0,1);case 5:return new Date(e.getFullYear()-1,11,31);case 6:return new Date(e.getFullYear()-1,11,30)}}function v(e){var t=gt(new Date(e.tm_year+1900,0,1),e.tm_yday),n=new Date(t.getFullYear(),0,4),r=new Date(t.getFullYear()+1,0,4),i=p(n),o=p(r);return h(i,t)<=0?h(o,t)<=0?t.getFullYear()+1:t.getFullYear():t.getFullYear()-1}var g={"%a":function(e){return c[e.tm_wday].substring(0,3)},"%A":function(e){return c[e.tm_wday]},"%b":function(e){return f[e.tm_mon].substring(0,3)},"%B":function(e){return f[e.tm_mon]},"%C":function(e){return d((e.tm_year+1900)/100|0,2)},"%d":function(e){return d(e.tm_mday,2)},"%e":function(e){return l(e.tm_mday,2," ")},"%g":function(e){return v(e).toString().substring(2)},"%G":function(e){return v(e)},"%H":function(e){return d(e.tm_hour,2)},"%I":function(e){var t=e.tm_hour;return 0==t?t=12:t>12&&(t-=12),d(t,2)},"%j":function(e){return d(e.tm_mday+ht(dt(e.tm_year+1900)?pt:vt,e.tm_mon-1),3)},"%m":function(e){return d(e.tm_mon+1,2)},"%M":function(e){return d(e.tm_min,2)},"%n":function(){return"\n"},"%p":function(e){return e.tm_hour>=0&&e.tm_hour<12?"AM":"PM"},"%S":function(e){return d(e.tm_sec,2)},"%t":function(){return"\t"},"%u":function(e){return e.tm_wday||7},"%U":function(e){var t=new Date(e.tm_year+1900,0,1),n=0===t.getDay()?t:gt(t,7-t.getDay()),r=new Date(e.tm_year+1900,e.tm_mon,e.tm_mday);if(h(n,r)<0){var i=ht(dt(r.getFullYear())?pt:vt,r.getMonth()-1)-31,o=31-n.getDate()+i+r.getDate();return d(Math.ceil(o/7),2)}return 0===h(n,t)?"01":"00"},"%V":function(e){var t,n=new Date(e.tm_year+1900,0,4),r=new Date(e.tm_year+1901,0,4),i=p(n),o=p(r),a=gt(new Date(e.tm_year+1900,0,1),e.tm_yday);return h(a,i)<0?"53":h(o,a)<=0?"01":(t=i.getFullYear()<e.tm_year+1900?e.tm_yday+32-i.getDate():e.tm_yday+1-i.getDate(),d(Math.ceil(t/7),2))},"%w":function(e){return e.tm_wday},"%W":function(e){var t=new Date(e.tm_year,0,1),n=1===t.getDay()?t:gt(t,0===t.getDay()?1:7-t.getDay()+1),r=new Date(e.tm_year+1900,e.tm_mon,e.tm_mday);if(h(n,r)<0){var i=ht(dt(r.getFullYear())?pt:vt,r.getMonth()-1)-31,o=31-n.getDate()+i+r.getDate();return d(Math.ceil(o/7),2)}return 0===h(n,t)?"01":"00"},"%y":function(e){return(e.tm_year+1900).toString().substring(2)},"%Y":function(e){return e.tm_year+1900},"%z":function(e){var t=e.tm_gmtoff,n=t>=0;return t=(t=Math.abs(t)/60)/60*100+t%60,(n?"+":"-")+String("0000"+t).slice(-4)},"%Z":function(e){return e.tm_zone},"%%":function(){return"%"}};for(var u in g)a.indexOf(u)>=0&&(a=a.replace(new RegExp(u,"g"),g[u](o)));var y,m,C,T=(!1,m=w(y=a)+1,E(y,C=new Array(m),0,C.length),C);return T.length>t?0:(function(e,t){_.set(e,t)}(T,e),T.length-1)}le=t.InternalError=fe(Error,"InternalError"),function(){for(var e=new Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);ve=e}(),ye=t.BindingError=fe(Error,"BindingError"),Pe.prototype.isAliasOf=function(e){if(!(this instanceof Pe))return!1;if(!(e instanceof Pe))return!1;for(var t=this.$$.ptrType.registeredClass,n=this.$$.ptr,r=e.$$.ptrType.registeredClass,i=e.$$.ptr;t.baseClass;)n=t.upcast(n),t=t.baseClass;for(;r.baseClass;)i=r.upcast(i),r=r.baseClass;return t===r&&n===i},Pe.prototype.clone=function(){if(this.$$.ptr||Ee(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,t=Te(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return t.$$.count.value+=1,t.$$.deleteScheduled=!1,t},Pe.prototype.delete=function(){this.$$.ptr||Ee(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&me("Object already scheduled for deletion"),Ce(this),_e(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},Pe.prototype.isDeleted=function(){return!this.$$.ptr},Pe.prototype.deleteLater=function(){return this.$$.ptr||Ee(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&me("Object already scheduled for deletion"),ke.push(this),1===ke.length&&Se&&Se(Ae),this.$$.deleteScheduled=!0,this},Le.prototype.getPointee=function(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e},Le.prototype.destructor=function(e){this.rawDestructor&&this.rawDestructor(e)},Le.prototype.argPackAdvance=8,Le.prototype.readValueFromPointer=ne,Le.prototype.deleteObject=function(e){null!==e&&e.delete()},Le.prototype.fromWireType=function(e){var t=this.getPointee(e);if(!t)return this.destructor(e),null;var n=function(e,t){return t=function(e,t){for(void 0===t&&me("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}(e,t),je[t]}(this.registeredClass,t);if(void 0!==n){if(0===n.$$.count.value)return n.$$.ptr=t,n.$$.smartPtr=e,n.clone();var r=n.clone();return this.destructor(e),r}function i(){return this.isSmartPointer?Fe(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):Fe(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var o,a=this.registeredClass.getActualType(t),s=Oe[a];if(!s)return i.call(this);o=this.isConst?s.constPointerType:s.pointerType;var u=xe(t,this.registeredClass,o.registeredClass);return null===u?i.call(this):this.isSmartPointer?Fe(o.registeredClass.instancePrototype,{ptrType:o,ptr:u,smartPtrType:this,smartPtr:e}):Fe(o.registeredClass.instancePrototype,{ptrType:o,ptr:u})},t.getInheritedInstanceCount=function(){return Object.keys(je).length},t.getLiveInheritedInstances=function(){var e=[];for(var t in je)je.hasOwnProperty(t)&&e.push(je[t]);return e},t.flushPendingDeletes=Ae,t.setDelayFunction=function(e){Se=e,ke.length&&Se&&Se(Ae)},Ge=t.UnboundTypeError=fe(Error,"UnboundTypeError"),t.count_emval_handles=function(){for(var e=0,t=5;t<Ve.length;++t)void 0!==Ve[t]&&++e;return e},t.get_first_emval=function(){for(var e=5;e<Ve.length;++e)if(void 0!==Ve[e])return Ve[e];return null};var mt,bt={u:function(e,t){},x:function(e){var t=ee[e];delete ee[e];var n=t.rawConstructor,r=t.rawDestructor,i=t.fields;he([e],i.map((function(e){return e.getterReturnType})).concat(i.map((function(e){return e.setterArgumentType}))),(function(e){var o={};return i.forEach((function(t,n){var r=t.fieldName,a=e[n],s=t.getter,u=t.getterContext,c=e[n+i.length],f=t.setter,l=t.setterContext;o[r]={read:function(e){return a.fromWireType(s(u,e))},write:function(e,t){var n=[];f(l,e,c.toWireType(n,t)),te(n)}}})),[{name:t.name,fromWireType:function(e){var t={};for(var n in o)t[n]=o[n].read(e);return r(e),t},toWireType:function(e,t){for(var i in o)if(!(i in t))throw new TypeError('Missing field:  "'+i+'"');var a=n();for(i in o)o[i].write(a,t[i]);return null!==e&&e.push(r,a),a},argPackAdvance:8,readValueFromPointer:ne,destructorFunction:r}]}))},R:function(e,t,n,r,i){var o=pe(n);be(e,{name:t=ge(t),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?r:i},argPackAdvance:8,readValueFromPointer:function(e){var r;if(1===n)r=_;else if(2===n)r=S;else{if(4!==n)throw new TypeError("Unknown boolean type size: "+t);r=A}return this.fromWireType(r[e>>o])},destructorFunction:null})},v:function(e,n,r,i,o,a,s,u,c,f,l,d,h){l=ge(l),a=Ue(o,a),u&&(u=Ue(s,u)),f&&(f=Ue(c,f)),h=Ue(d,h);var p=ue(l);!function(e,n,r){t.hasOwnProperty(e)?(me("Cannot register public name '"+e+"' twice"),Re(t,e,e),t.hasOwnProperty(r)&&me("Cannot register multiple overloads of a function with the same number of arguments ("+r+")!"),t[e].overloadTable[void 0]=n):t[e]=n}(p,(function(){He("Cannot construct "+l+" due to unbound types",[i])})),he([e,n,r],i?[i]:[],(function(n){var r,o;n=n[0],o=i?(r=n.registeredClass).instancePrototype:Pe.prototype;var s=ce(p,(function(){if(Object.getPrototypeOf(this)!==c)throw new ye("Use 'new' to construct "+l);if(void 0===d.constructor_body)throw new ye(l+" has no accessible constructor");var e=d.constructor_body[arguments.length];if(void 0===e)throw new ye("Tried to invoke ctor of "+l+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(d.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)})),c=Object.create(o,{constructor:{value:s}});s.prototype=c;var d=new De(l,s,c,h,r,a,u,f),v=new Le(l,d,!0,!1,!1),g=new Le(l+"*",d,!1,!1,!1),y=new Le(l+" const*",d,!1,!0,!1);return Oe[e]={pointerType:g,constPointerType:y},function(e,n,r){t.hasOwnProperty(e)||de("Replacing nonexistant public symbol"),t[e].overloadTable,t[e]=n,t[e].argCount=r}(p,s),[v,g,y]}))},I:function(e,t,n,r,i,o){t>0||z("Assertion failed: "+undefined);var a=We(t,n);i=Ue(r,i);var s=[o],u=[];he([],[e],(function(e){var n="constructor "+(e=e[0]).name;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[t-1])throw new ye("Cannot register multiple constructors with identical number of parameters ("+(t-1)+") for class '"+e.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return e.registeredClass.constructor_body[t-1]=function(){He("Cannot construct "+e.name+" due to unbound types",a)},he([],a,(function(r){return e.registeredClass.constructor_body[t-1]=function(){arguments.length!==t-1&&me(n+" called with "+arguments.length+" arguments, expected "+(t-1)),u.length=0,s.length=t;for(var e=1;e<t;++e)s[e]=r[e].toWireType(u,arguments[e-1]);var o=i.apply(null,s);return te(u),r[0].fromWireType(o)},[]})),[]}))},g:function(e,t,n,r,i,o,a,s){var u=We(n,r);t=ge(t),o=Ue(i,o),he([],[e],(function(e){var r=(e=e[0]).name+"."+t;function i(){He("Cannot call "+r+" due to unbound types",u)}s&&e.registeredClass.pureVirtualFunctions.push(t);var c=e.registeredClass.instancePrototype,f=c[t];return void 0===f||void 0===f.overloadTable&&f.className!==e.name&&f.argCount===n-2?(i.argCount=n-2,i.className=e.name,c[t]=i):(Re(c,t,r),c[t].overloadTable[n-2]=i),he([],u,(function(i){var s=function(e,t,n,r,i){var o=t.length;o<2&&me("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var a=null!==t[1]&&null!==n,s=!1,u=1;u<t.length;++u)if(null!==t[u]&&void 0===t[u].destructorFunction){s=!0;break}var c="void"!==t[0].name,f=o-2,l=new Array(f),d=[],h=[];return function(){var n;arguments.length!==f&&me("function "+e+" called with "+arguments.length+" arguments, expected "+f+" args!"),h.length=0,d.length=a?2:1,d[0]=i,a&&(n=t[1].toWireType(h,this),d[1]=n);for(var o=0;o<f;++o)l[o]=t[o+2].toWireType(h,arguments[o]),d.push(l[o]);var u=r.apply(null,d);if(s)te(h);else for(o=a?1:2;o<t.length;o++){var p=1===o?n:l[o-2];null!==t[o].destructorFunction&&t[o].destructorFunction(p)}if(c)return t[0].fromWireType(u)}}(r,i,e,o,a);return void 0===c[t].overloadTable?(s.argCount=n-2,c[t]=s):c[t].overloadTable[n-2]=s,[]})),[]}))},Q:function(e,t){be(e,{name:t=ge(t),fromWireType:function(e){var t=Ve[e].value;return qe(e),t},toWireType:function(e,t){return ze(t)},argPackAdvance:8,readValueFromPointer:ne,destructorFunction:null})},E:function(e,t,n){var r=pe(n);be(e,{name:t=ge(t),fromWireType:function(e){return e},toWireType:function(e,t){if("number"!=typeof t&&"boolean"!=typeof t)throw new TypeError('Cannot convert "'+Ke(t)+'" to '+this.name);return t},argPackAdvance:8,readValueFromPointer:Xe(t,r),destructorFunction:null})},p:function(e,t,n,r,i){t=ge(t),-1===i&&(i=4294967295);var o=pe(n),a=function(e){return e};if(0===r){var s=32-8*n;a=function(e){return e<<s>>>s}}var u=-1!=t.indexOf("unsigned");be(e,{name:t,fromWireType:a,toWireType:function(e,n){if("number"!=typeof n&&"boolean"!=typeof n)throw new TypeError('Cannot convert "'+Ke(n)+'" to '+this.name);if(n<r||n>i)throw new TypeError('Passing a number "'+Ke(n)+'" from JS side to C/C++ side to an argument of type "'+t+'", which is outside the valid range ['+r+", "+i+"]!");return u?n>>>0:0|n},argPackAdvance:8,readValueFromPointer:Je(t,o,0!==r),destructorFunction:null})},o:function(e,t,n){var r=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];function i(e){var t=P,n=t[e>>=2],i=t[e+1];return new r(C,i,n)}be(e,{name:n=ge(n),fromWireType:i,argPackAdvance:8,readValueFromPointer:i},{ignoreDuplicateRegistrations:!0})},F:function(e,t){var n="std::string"===(t=ge(t));be(e,{name:t,fromWireType:function(e){var t,r=P[e>>2];if(n)for(var i=e+4,o=0;o<=r;++o){var a=e+4+o;if(o==r||0==T[a]){var s=b(i,a-i);void 0===t?t=s:(t+=String.fromCharCode(0),t+=s),i=a+1}}else{var u=new Array(r);for(o=0;o<r;++o)u[o]=String.fromCharCode(T[e+4+o]);t=u.join("")}return Tt(e),t},toWireType:function(e,t){t instanceof ArrayBuffer&&(t=new Uint8Array(t));var r="string"==typeof t;r||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||me("Cannot pass non-string to std::string");var i=(n&&r?function(){return w(t)}:function(){return t.length})(),o=wt(4+i+1);if(P[o>>2]=i,n&&r)E(t,T,o+4,i+1);else if(r)for(var a=0;a<i;++a){var s=t.charCodeAt(a);s>255&&(Tt(o),me("String has UTF-16 code units that do not fit in 8 bits")),T[o+4+a]=s}else for(a=0;a<i;++a)T[o+4+a]=t[a];return null!==e&&e.push(Tt,o),o},argPackAdvance:8,readValueFromPointer:ne,destructorFunction:function(e){Tt(e)}})},A:function(e,t,n){var r,i,o,a,s;n=ge(n),2===t?(r=M,i=$,a=I,o=function(){return k},s=1):4===t&&(r=N,i=x,a=j,o=function(){return P},s=2),be(e,{name:n,fromWireType:function(e){for(var n,i=P[e>>2],a=o(),u=e+4,c=0;c<=i;++c){var f=e+4+c*t;if(c==i||0==a[f>>s]){var l=r(u,f-u);void 0===n?n=l:(n+=String.fromCharCode(0),n+=l),u=f+t}}return Tt(e),n},toWireType:function(e,r){"string"!=typeof r&&me("Cannot pass non-string to C++ string type "+n);var o=a(r),u=wt(4+o+t);return P[u>>2]=o>>s,i(r,u+4,o+t),null!==e&&e.push(Tt,u),u},argPackAdvance:8,readValueFromPointer:ne,destructorFunction:function(e){Tt(e)}})},z:function(e,t,n,r,i,o){ee[e]={name:ge(t),rawConstructor:Ue(n,r),rawDestructor:Ue(i,o),fields:[]}},m:function(e,t,n,r,i,o,a,s,u,c){ee[e].fields.push({fieldName:ge(t),getterReturnType:n,getter:Ue(r,i),getterContext:o,setterArgumentType:a,setter:Ue(s,u),setterContext:c})},S:function(e,t){be(e,{isVoid:!0,name:t=ge(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,t){}})},f:function(e,t,n){e=Qe(e),t=Ze(t,"emval::as");var r=[],i=ze(r);return A[n>>2]=i,t.toWireType(r,e)},w:function(e,t,n,r){e=Qe(e);for(var i=et(t,n),o=new Array(t),a=0;a<t;++a){var s=i[a];o[a]=s.readValueFromPointer(r),r+=s.argPackAdvance}return ze(e.apply(void 0,o))},t:function(e,t,n,r,i){return(e=it[e])(t=Qe(t),n=nt(n),function(e){var t=[];return A[e>>2]=ze(t),t}(r),i)},i:function(e,t,n,r){(e=it[e])(t=Qe(t),n=nt(n),null,r)},a:qe,q:function(e){return 0===e?ze(ot()):(e=nt(e),ze(ot()[e]))},h:function(e,t){var n,r,i=et(e,t),o=i[0],a=new Array(e-1);return n=function(t,n,r,s){for(var u=0,c=0;c<e-1;++c)a[c]=i[c+1].readValueFromPointer(s+u),u+=i[c+1].argPackAdvance;var f=t[n].apply(t,a);for(c=0;c<e-1;++c)i[c+1].deleteObject&&i[c+1].deleteObject(a[c]);if(!o.isVoid)return o.toWireType(r,f)},r=it.length,it.push(n),r},B:function(e){return e=nt(e),ze(t[e])},e:function(e,t){return ze((e=Qe(e))[t=Qe(t)])},l:function(e){e>4&&(Ve[e].refcount+=1)},s:function(){return ze([])},b:function(e){return ze(nt(e))},r:function(){return ze({})},c:function(e){te(Ve[e].value),qe(e)},k:function(e,t,n){e=Qe(e),t=Qe(t),n=Qe(n),e[t]=n},j:function(e,t){return ze((e=Ze(e,"_emval_take_value")).readValueFromPointer(t))},n:function(e){return ze(typeof(e=Qe(e)))},d:function(){z()},C:function(e,t){var n;if(0===e)n=Date.now();else{if(1!==e&&4!==e)return 28,A[_t()>>2]=28,-1;n=rt()}return A[t>>2]=n/1e3|0,A[t+4>>2]=n%1e3*1e3*1e3|0,0},K:function(e,t,n){T.copyWithin(e,t,t+n)},L:function(e){e>>>=0;var t=T.length,n=2147483648;if(e>n)return!1;for(var r,i=1;i<=4;i*=2){var o=t*(1+.2/i);if(o=Math.min(o,e+100663296),st(Math.min(n,((r=Math.max(16777216,e,o))%65536>0&&(r+=65536-r%65536),r))))return!0}return!1},N:function(e,t){var n=0;return ct().forEach((function(r,i){var o=t+n;A[e+4*i>>2]=o,function(e,t,n){for(var r=0;r<e.length;++r)_[t++>>0]=e.charCodeAt(r);_[t>>0]=0}(r,o),n+=r.length+1})),0},O:function(e,t){var n=ct();A[e>>2]=n.length;var r=0;return n.forEach((function(e){r+=e.length+1})),A[t>>2]=r,0},P:function(e){return 0},J:function(e,t,n,r,i){},D:function(e,t,n,r){for(var i=0,o=0;o<n;o++){for(var a=A[t+8*o>>2],s=A[t+(8*o+4)>>2],u=0;u<s;u++)ft.printChar(e,T[a+u]);i+=s}return A[r>>2]=i,0},H:function e(t,n){var r=new Date(1e3*A[t>>2]);A[n>>2]=r.getUTCSeconds(),A[n+4>>2]=r.getUTCMinutes(),A[n+8>>2]=r.getUTCHours(),A[n+12>>2]=r.getUTCDate(),A[n+16>>2]=r.getUTCMonth(),A[n+20>>2]=r.getUTCFullYear()-1900,A[n+24>>2]=r.getUTCDay(),A[n+36>>2]=0,A[n+32>>2]=0;var i=Date.UTC(r.getUTCFullYear(),0,1,0,0,0,0),o=(r.getTime()-i)/864e5|0;return A[n+28>>2]=o,e.GMTString||(e.GMTString=F("GMT")),A[n+40>>2]=e.GMTString,n},V:function(e,t){lt();var n=new Date(1e3*A[e>>2]);A[t>>2]=n.getSeconds(),A[t+4>>2]=n.getMinutes(),A[t+8>>2]=n.getHours(),A[t+12>>2]=n.getDate(),A[t+16>>2]=n.getMonth(),A[t+20>>2]=n.getFullYear()-1900,A[t+24>>2]=n.getDay();var r=new Date(n.getFullYear(),0,1),i=(n.getTime()-r.getTime())/864e5|0;A[t+28>>2]=i,A[t+36>>2]=-60*n.getTimezoneOffset();var o=new Date(n.getFullYear(),6,1).getTimezoneOffset(),a=r.getTimezoneOffset(),s=0|(o!=a&&n.getTimezoneOffset()==Math.min(a,o));A[t+32>>2]=s;var u=A[St()+(s?4:0)>>2];return A[t+40>>2]=u,t},G:function(e){lt();var t=new Date(A[e+20>>2]+1900,A[e+16>>2],A[e+12>>2],A[e+8>>2],A[e+4>>2],A[e>>2],0),n=A[e+32>>2],r=t.getTimezoneOffset(),i=new Date(t.getFullYear(),0,1),o=new Date(t.getFullYear(),6,1).getTimezoneOffset(),a=i.getTimezoneOffset(),s=Math.min(a,o);if(n<0)A[e+32>>2]=Number(o!=a&&s==r);else if(n>0!=(s==r)){var u=Math.max(a,o),c=n>0?s:u;t.setTime(t.getTime()+6e4*(c-r))}A[e+24>>2]=t.getDay();var f=(t.getTime()-i.getTime())/864e5|0;return A[e+28>>2]=f,A[e>>2]=t.getSeconds(),A[e+4>>2]=t.getMinutes(),A[e+8>>2]=t.getHours(),A[e+12>>2]=t.getDate(),A[e+16>>2]=t.getMonth(),t.getTime()/1e3|0},y:function(e){},T:yt,M:function(e,t,n,r){return yt(e,t,n,r)},U:function(e){var t=Date.now()/1e3|0;return e&&(A[e>>2]=t),t}},Et=(function(){var e={a:bt};function n(e,n){var r=e.exports;t.asm=r,L((d=t.asm.W).buffer),U=t.asm.Z,function(e){if(Y--,t.monitorRunDependencies&&t.monitorRunDependencies(Y),0==Y&&(null!==V&&(clearInterval(V),V=null),q)){var n=q;q=null,n()}}()}function r(e){n(e.instance)}function o(t){return(l||"function"!=typeof fetch?Promise.resolve().then((function(){return Q(J)})):fetch(J,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+J+"'";return e.arrayBuffer()})).catch((function(){return Q(J)}))).then((function(t){return WebAssembly.instantiate(t,e)})).then(t,(function(e){p("failed to asynchronously prepare wasm: "+e),z(e)}))}if(Y++,t.monitorRunDependencies&&t.monitorRunDependencies(Y),t.instantiateWasm)try{return t.instantiateWasm(e,n)}catch(e){return p("Module.instantiateWasm callback failed with error: "+e),!1}(l||"function"!=typeof WebAssembly.instantiateStreaming||K(J)||"function"!=typeof fetch?o(r):fetch(J,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,e).then(r,(function(e){return p("wasm streaming compile failed: "+e),p("falling back to ArrayBuffer instantiation"),o(r)}))}))).catch(i)}(),t.___wasm_call_ctors=function(){return(Et=t.___wasm_call_ctors=t.asm.X).apply(null,arguments)}),wt=t._malloc=function(){return(wt=t._malloc=t.asm.Y).apply(null,arguments)},Ct=t.___getTypeName=function(){return(Ct=t.___getTypeName=t.asm._).apply(null,arguments)},_t=(t.___embind_register_native_and_builtin_types=function(){return(t.___embind_register_native_and_builtin_types=t.asm.$).apply(null,arguments)},t.___errno_location=function(){return(_t=t.___errno_location=t.asm.aa).apply(null,arguments)}),Tt=t._free=function(){return(Tt=t._free=t.asm.ba).apply(null,arguments)},St=t.__get_tzname=function(){return(St=t.__get_tzname=t.asm.ca).apply(null,arguments)},kt=t.__get_daylight=function(){return(kt=t.__get_daylight=t.asm.da).apply(null,arguments)},At=t.__get_timezone=function(){return(At=t.__get_timezone=t.asm.ea).apply(null,arguments)};function Pt(e){function n(){mt||(mt=!0,t.calledRun=!0,g||(Z(B),Z(H),r(t),t.onRuntimeInitialized&&t.onRuntimeInitialized(),function(){if(t.postRun)for("function"==typeof t.postRun&&(t.postRun=[t.postRun]);t.postRun.length;)e=t.postRun.shift(),W.unshift(e);var e;Z(W)}()))}e=e||u,Y>0||(function(){if(t.preRun)for("function"==typeof t.preRun&&(t.preRun=[t.preRun]);t.preRun.length;)e=t.preRun.shift(),G.unshift(e);var e;Z(G)}(),Y>0||(t.setStatus?(t.setStatus("Running..."),setTimeout((function(){setTimeout((function(){t.setStatus("")}),1),n()}),1)):n()))}if(t.dynCall_vij=function(){return(t.dynCall_vij=t.asm.fa).apply(null,arguments)},t.dynCall_viiij=function(){return(t.dynCall_viiij=t.asm.ga).apply(null,arguments)},t.dynCall_viijii=function(){return(t.dynCall_viijii=t.asm.ha).apply(null,arguments)},t.dynCall_j=function(){return(t.dynCall_j=t.asm.ia).apply(null,arguments)},t.dynCall_ji=function(){return(t.dynCall_ji=t.asm.ja).apply(null,arguments)},t.dynCall_iijiiiii=function(){return(t.dynCall_iijiiiii=t.asm.ka).apply(null,arguments)},t.dynCall_jiii=function(){return(t.dynCall_jiii=t.asm.la).apply(null,arguments)},t.dynCall_vijii=function(){return(t.dynCall_vijii=t.asm.ma).apply(null,arguments)},t.dynCall_jiji=function(){return(t.dynCall_jiji=t.asm.na).apply(null,arguments)},t.dynCall_iiiiij=function(){return(t.dynCall_iiiiij=t.asm.oa).apply(null,arguments)},t.dynCall_iiiiijj=function(){return(t.dynCall_iiiiijj=t.asm.pa).apply(null,arguments)},t.dynCall_iiiiiijj=function(){return(t.dynCall_iiiiiijj=t.asm.qa).apply(null,arguments)},q=function e(){mt||Pt(),mt||(q=e)},t.run=Pt,t.preInit)for("function"==typeof t.preInit&&(t.preInit=[t.preInit]);t.preInit.length>0;)t.preInit.pop()();return Pt(),t.ready}),r=t,i=function(e){return e.GENERIC="Error",e.NOT_SUPPORTED="ErrorNotSupported",e.NO_SOURCE="ErrorNoSource",e.INVALID_DATA="ErrorInvalidData",e.INVALID_STATE="ErrorInvalidState",e.INVALID_PARAMETER="ErrorInvalidParameter",e.TIMEOUT="ErrorTimeout",e.NETWORK="ErrorNetwork",e.NETWORK_IO="ErrorNetworkIO",e.AUTHORIZATION="ErrorAuthorization",e.NOT_AVAILABLE="ErrorNotAvailable",e}({}),o=function(e){return e.INITIALIZED="PlayerInitialized",e.QUALITY_CHANGED="PlayerQualityChanged",e.DURATION_CHANGED="PlayerDurationChanged",e.VOLUME_CHANGED="PlayerVolumeChanged",e.MUTED_CHANGED="PlayerMutedChanged",e.PLAYBACK_RATE_CHANGED="PlayerPlaybackRateChanged",e.REBUFFERING="PlayerRebuffering",e.AUDIO_BLOCKED="PlayerAudioBlocked",e.PLAYBACK_BLOCKED="PlayerPlaybackBlocked",e.ERROR="PlayerError",e.RECOVERABLE_ERROR="PlayerRecoverableError",e.ANALYTICS_EVENT="PlayerAnalyticsEvent",e.TIME_UPDATE="PlayerTimeUpdate",e.SYNC_TIME_UPDATE="PlayerSyncTimeUpdate",e.BUFFER_UPDATE="PlayerBufferUpdate",e.SEEK_COMPLETED="PlayerSeekCompleted",e.SESSION_DATA="PlayerSessionData",e.STATE_CHANGED="PlayerStateChanged",e.WORKER_ERROR="PlayerWorkerError",e.METADATA="PlayerMetadata",e.TEXT_CUE="PlayerTextCue",e.TEXT_METADATA_CUE="PlayerTextMetadataCue",e.AD_CUE="PlayerAdCue",e.STREAM_SOURCE_CUE="PlayerStreamSourceCue",e.NETWORK_UNAVAILABLE="PlayerNetworkUnavailable",e.SEGMENT_DISCONTINUITY="PlayerSegmentDiscontinuity",e.SEGMENT_METADATA="PlayerSegmentMetadata",e}({}),a=function(e){return e[e.STATE_CHANGED=0]="STATE_CHANGED",e[e.CONFIGURE=1]="CONFIGURE",e[e.RESET=2]="RESET",e[e.ADD_CUE=3]="ADD_CUE",e[e.GET_DECODE_INFO=4]="GET_DECODE_INFO",e[e.MEDIA_SINK_RPC=5]="MEDIA_SINK_RPC",e[e.GET_EXPERIMENTS=6]="GET_EXPERIMENTS",e[e.LOG_MESSAGE=7]="LOG_MESSAGE",e[e.DATA_CHANNEL_CREATE=8]="DATA_CHANNEL_CREATE",e[e.DATA_CHANNEL_CLOSE=9]="DATA_CHANNEL_CLOSE",e[e.DATA_CHANNEL_SEND=10]="DATA_CHANNEL_SEND",e[e.RTC_SET_REMOTE_DESCRIPTION=11]="RTC_SET_REMOTE_DESCRIPTION",e[e.PROPERTY_CHANGED=12]="PROPERTY_CHANGED",e[e.SYNC_TIME_CHANGED=13]="SYNC_TIME_CHANGED",e[e.BUFFERED_RANGES=14]="BUFFERED_RANGES",e[e.DESTROY=15]="DESTROY",e}({});function s(e,t){this.name="AggregateError",this.errors=e,this.message=t||""}s.prototype=Error.prototype;var u=setTimeout;function c(e){return Boolean(e&&void 0!==e.length)}function f(){}function l(e){if(!(this instanceof l))throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],y(e,this)}function d(e,t){for(;3===e._state;)e=e._value;0!==e._state?(e._handled=!0,l._immediateFn((function(){var n=1===e._state?t.onFulfilled:t.onRejected;if(null!==n){var r;try{r=n(e._value)}catch(e){return void p(t.promise,e)}h(t.promise,r)}else(1===e._state?h:p)(t.promise,e._value)}))):e._deferreds.push(t)}function h(e,t){try{if(t===e)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if(t instanceof l)return e._state=3,e._value=t,void v(e);if("function"==typeof n)return void y((r=n,i=t,function(){r.apply(i,arguments)}),e)}e._state=1,e._value=t,v(e)}catch(t){p(e,t)}var r,i}function p(e,t){e._state=2,e._value=t,v(e)}function v(e){2===e._state&&0===e._deferreds.length&&l._immediateFn((function(){e._handled||l._unhandledRejectionFn(e._value)}));for(var t=0,n=e._deferreds.length;t<n;t++)d(e,e._deferreds[t]);e._deferreds=null}function g(e,t,n){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.promise=n}function y(e,t){var n=!1;try{e((function(e){n||(n=!0,h(t,e))}),(function(e){n||(n=!0,p(t,e))}))}catch(e){if(n)return;n=!0,p(t,e)}}l.prototype.catch=function(e){return this.then(null,e)},l.prototype.then=function(e,t){var n=new this.constructor(f);return d(this,new g(e,t,n)),n},l.prototype.finally=function(e){var t=this.constructor;return this.then((function(n){return t.resolve(e()).then((function(){return n}))}),(function(n){return t.resolve(e()).then((function(){return t.reject(n)}))}))},l.all=function(e){return new l((function(t,n){if(!c(e))return n(new TypeError("Promise.all accepts an array"));var r=Array.prototype.slice.call(e);if(0===r.length)return t([]);var i=r.length;function o(e,a){try{if(a&&("object"==typeof a||"function"==typeof a)){var s=a.then;if("function"==typeof s)return void s.call(a,(function(t){o(e,t)}),n)}r[e]=a,0==--i&&t(r)}catch(e){n(e)}}for(var a=0;a<r.length;a++)o(a,r[a])}))},l.any=function(e){var t=this;return new t((function(n,r){if(!e||void 0===e.length)return r(new TypeError("Promise.any accepts an array"));var i=Array.prototype.slice.call(e);if(0===i.length)return r();for(var o=[],a=0;a<i.length;a++)try{t.resolve(i[a]).then(n).catch((function(e){o.push(e),o.length===i.length&&r(new s(o,"All promises were rejected"))}))}catch(e){r(e)}}))},l.allSettled=function(e){return new this((function(t,n){if(!e||void 0===e.length)return n(new TypeError(typeof e+" "+e+" is not iterable(cannot read property Symbol(Symbol.iterator))"));var r=Array.prototype.slice.call(e);if(0===r.length)return t([]);var i=r.length;function o(e,n){if(n&&("object"==typeof n||"function"==typeof n)){var a=n.then;if("function"==typeof a)return void a.call(n,(function(t){o(e,t)}),(function(n){r[e]={status:"rejected",reason:n},0==--i&&t(r)}))}r[e]={status:"fulfilled",value:n},0==--i&&t(r)}for(var a=0;a<r.length;a++)o(a,r[a])}))},l.resolve=function(e){return e&&"object"==typeof e&&e.constructor===l?e:new l((function(t){t(e)}))},l.reject=function(e){return new l((function(t,n){n(e)}))},l.race=function(e){return new l((function(t,n){if(!c(e))return n(new TypeError("Promise.race accepts an array"));for(var r=0,i=e.length;r<i;r++)l.resolve(e[r]).then(t,n)}))},l._immediateFn="function"==typeof setImmediate&&function(e){setImmediate(e)}||function(e){u(e,0)},l._unhandledRejectionFn=function(e){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)};var m=l,b=("undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==n.g?n.g:void 0).Promise||m,E=function(){function e(){this.buffer=void 0,this.head=void 0,this.tail=void 0,this.buffer=[],this.head=0,this.tail=0}var t=e.prototype;return t.push=function(e){this.tail===this.buffer.length?this.buffer.push(e):this.buffer[this.tail]=e,this.tail++},t.pop=function(){var e,t=null!=(e=this.buffer[this.head])?e:null;return this.buffer[this.head]=null,this.head++,this.empty()&&(this.head=0,this.tail=0),t},t.size=function(){return this.tail-this.head},t.empty=function(){return this.head>=this.tail},e}(),w=function(e){return e[e.Readable=0]="Readable",e[e.Closed=1]="Closed",e[e.Errored=2]="Errored",e}({}),C=function(){function e(e){this.state=void 0,this.queuedChunks=void 0,this.readRequest=void 0,this.storedError=void 0,this.onCancel=void 0,this.state=w.Readable,this.queuedChunks=new E,this.readRequest=null,this.storedError=null,this.onCancel=e}var t=e.prototype;return t.read=function(){var t=this;switch(this.state){case w.Readable:return this.queuedChunks.empty()?new b((function(e,n){t.readRequest={resolve:e,reject:n}})):this.queuedChunks.pop();case w.Closed:return this.queuedChunks.empty()?b.resolve(e.DONE_CHUNK):this.queuedChunks.pop();case w.Errored:return b.reject(this.storedError)}},t.cancel=function(){this.onCancel(),this.close()},t.error=function(e){this.state===w.Readable&&(this.state=w.Errored,this.storedError=e,this.readRequest&&(this.readRequest.reject(e),this.readRequest=null),this.queuedChunks=new E)},t.write=function(e){if(this.state===w.Readable){var t={done:!1,value:e};this.readRequest?(this.readRequest.resolve(t),this.readRequest=null):this.queuedChunks.push(b.resolve(t))}},t.close=function(){this.state===w.Readable&&(this.readRequest&&(this.readRequest.resolve(e.DONE_CHUNK),this.readRequest=null),this.state=w.Closed)},e}();C.DONE_CHUNK={done:!0,value:void 0};var _=function(){function e(e,t){this.reader=void 0,this.reader=new C(e.abort.bind(e)),this.initReadableStreamShim(e,t)}var t=e.prototype;return t.getReader=function(){return this.reader},t.initReadableStreamShim=function(e,t){var n=this;switch(e.responseType=t,t){case"moz-chunked-arraybuffer":e.addEventListener("progress",(function(){n.reader.write(new Uint8Array(e.response))})),e.addEventListener("load",this.reader.close.bind(this.reader));break;case"ms-stream":e.addEventListener("readystatechange",(function(){if(e.readyState===e.LOADING){var t=new self.MSStreamReader,r=0;t.onprogress=function(){r>=t.result.byteLength||(n.reader.write(new Uint8Array(t.result,r)),r=t.result.byteLength)},t.onload=n.reader.close.bind(n.reader),t.readAsArrayBuffer(e.response)}}));break;case"arraybuffer":e.addEventListener("progress",this.reader.write.bind(this.reader,new Uint8Array(0))),e.addEventListener("load",(function(){e.response&&n.reader.write(new Uint8Array(e.response)),n.reader.close()}))}},e}(),T=self.fetch&&self.ReadableStream?self.fetch.bind(self):function(e,t){return void 0===t&&(t={}),new b((function(n,r){var i=new XMLHttpRequest;for(var o in i.open(t.method||"GET",e),t.headers)Object.prototype.hasOwnProperty.call(t.headers,o)&&i.setRequestHeader(o,t.headers[o]);"include"===t.credentials&&(i.withCredentials=!0);var a=new _(i,S);i.addEventListener("readystatechange",(function e(){2===i.readyState&&(i.removeEventListener("readystatechange",e),n(new k(i,a)))})),t.signal&&(t.signal.onabort=function(){i.abort();var e=new Error("request aborted");e.name="AbortError",a.getReader().error(e),r(e)}),i.addEventListener("error",(function(){var e=new Error("network error");a.getReader().error(e),r(e)})),i.send(t.body||null)}))},S=self.fetch&&self.ReadableStream?"arraybuffer":P("moz-chunked-arraybuffer")||P("ms-stream")||"arraybuffer";var k=function(e,t){this.body=void 0,this.status=void 0,this.headers=void 0,this.body=t,this.status=e.status,this.headers=new A(e)},A=function(){function e(e){this.xhr=void 0,this.xhr=e}var t=e.prototype;return t.has=function(e){return null!==this.xhr.getResponseHeader(e)},t.get=function(e){return this.xhr.getResponseHeader(e)},e}();function P(e){try{var t=new XMLHttpRequest;return t.open("GET","https://twitch.tv"),t.responseType=e,t.responseType===e?e:""}catch(e){return""}}var O=function(){function e(e,t){this.cancelled=void 0,this.module=void 0,this.pendingAbort=void 0,this.response=void 0,this.reader=void 0,this.abortController=void 0,this.cancelled=!1,this.module=e,this.pendingAbort=!1,this.response=null,this.reader=null,this.abortController=t,this.readBody=this.readBody.bind(this)}var t=e.prototype;return t.setResponse=function(e){this.response=e,this.pendingAbort&&(this.pendingAbort=!1,this.getReader().cancel())},t.abort=function(){this.response?this.getReader().cancel():this.abortController?this.abortController.abort():this.pendingAbort=!0},t.cancel=function(){this.cancelled=!0,this.abort()},t.getHeader=function(e){var t,n;return null!=(t=this.response)&&t.headers.has(e)&&null!=(n=this.response.headers.get(e))?n:""},t.getStatus=function(){var e,t;return null!=(e=null==(t=this.response)?void 0:t.status)?e:0},t.readBody=function(e,t){var n=this,r=performance.now(),i=t>0?self.setTimeout((function o(){var a=performance.now()-r;a<t&&t>0?i=self.setTimeout(o,t-a):(n.abort(),e.error(!0,"Read response timeout"))}),t):-1;this.getReader().read().then((function t(i){var o=i.done,a=i.value;if(!n.cancelled){if(!o){var s=null==a?void 0:a.byteLength;return s&&e.read(n.module.copyUint8ArrayToEmscriptenHeap(a),s),r=performance.now(),n.getReader().read().then(t)}e.end()}})).catch((function(t){e.error(!1,t.message)})).then((function(){-1!==i&&clearTimeout(i),e.delete()}))},t.getReader=function(){if(!this.reader)try{this.reader=this.response.body.getReader()}catch(e){this.reader=new R}return this.reader},e}(),R=function(){function e(){this.closed=void 0,this.closed=b.resolve()}var t=e.prototype;return t.read=function(){return b.resolve({done:!0})},t.cancel=function(){return b.resolve()},t.releaseLock=function(){},e}();function D(e,t,n,r,i,o,a){try{var s=e[o](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,i)}function M(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){D(o,r,i,a,s,"next",e)}function s(e){D(o,r,i,a,s,"throw",e)}a(void 0)}))}}var $=n(357),I=n.n($),N=function(e){return e.ID3="MetaID3",e.CAPTION="MetaCaption",e}({});function x(){return x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},x.apply(this,arguments)}function j(e){return j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},j(e)}function F(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,o=void 0,o=function(e,t){if("object"!==j(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==j(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i),"symbol"===j(o)?o:String(o)),r)}var i,o}function L(e,t,n){return t&&F(e.prototype,t),n&&F(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function U(e,t){return U=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},U(e,t)}var G=1<<30,B={audio:1936684398,video:1986618469};function H(e,t,n){return e.addEventListener(t,n),function(){e.removeEventListener(t,n)}}function W(e){var t=(e||"").match(/codecs="([^.]+)\./);return t&&t[1]?t[1]:""}var Y=function(){function e(e,t,n,r,i){this.rawCodec=t,this.group=n,this.isProtected=r,this.onError=i,this.pending=void 0,this.unsubscribers=[],this.srcBuf=void 0,this.blocked=!1,this.srcBuf=e,this.pending=new E,this.unsubscribers.push(H(e,"updateend",this.process.bind(this)))}var t=e.prototype;return t.getBufferedRanges=function(){try{var e=[];if(this.srcBuf)for(var t=this.srcBuf.buffered,n=0;n<t.length;n++)e.push({start:t.start(n),end:t.end(n)});return e}catch(e){return[]}},t.abort=function(){this.schedule((function(e){e.abort()}))},t.changeType=function(e){this.rawCodec=function(e){var t=(e||"").match(/codecs=".+"/);return t&&t[0]?t[0]:'codecs=""'}(e),this.schedule((function(t){t.changeType(e)}))},t.appendBuffer=function(e){this.schedule((function(t){try{t.appendBuffer(e)}catch(e){if("QuotaExceededError"!==e.name)throw e;var n=t.buffered,r=n.start(0),i=n.end(n.length-1),o=(r+i)/2;t.remove(o,i)}}))},t.setTimestampOffset=function(e){this.schedule((function(t){t.timestampOffset=e}))},t.remove=function(e,t){this.schedule((function(n){var r=n.buffered;if(r.length){var i=Math.max(e,r.start(0)),o=Math.min(t,r.end(r.length-1));i<o&&n.remove(i,o)}}))},t.block=function(){var e=this;return new Promise((function(t){e.schedule((function(){e.blocked=!0,t()}))}))},t.unblock=function(){this.blocked=!1,this.process()},t.destroy=function(){this.pending=new E,this.unsubscribers.forEach((function(e){return e()})),this.srcBuf=void 0},t.schedule=function(e){this.pending.empty()&&this.canProcess()?this.safeExecute(e):(this.pending.push(e),this.process())},t.safeExecute=function(e){try{if(!this.srcBuf)throw new Error("srcBuf is undefined");e(this.srcBuf)}catch(e){this.onError(e,!1)}},t.process=function(){for(;!this.pending.empty()&&this.canProcess();)this.safeExecute(this.pending.pop())},t.canProcess=function(){return!(!this.srcBuf||this.srcBuf.updating||this.blocked)},L(e,[{key:"buffer",get:function(){return this.srcBuf}},{key:"codec",get:function(){return this.rawCodec}},{key:"timestampOffset",get:function(){return this.buffer?this.buffer.timestampOffset:0}}]),e}(),V=function(){function e(e,t,n){this.mediaSource=e,this.onEnded=t,this.onError=n,this.sourceBuffers=Object.create(null),this.unsubscribers=[],this.unsubscribers.push(H(e,"sourceended",this.onEnded))}e.isSupported=function(){return void 0!==self.MediaSource},e.isSupportedInWorker=function(){return e.isSupported()&&MediaSource.canConstructInDedicatedWorker&&"function"==typeof MediaSourceHandle},e.create=function(t,n){var r=new MediaSource,i=new Promise((function(i,o){var a=H(r,"sourceopen",(function(){"open"===r.readyState?(i(new e(r,t,n)),a()):o("The MediaSource was closed upon opening")}))}));return{ms:r,sink:i}};var t=e.prototype;return t.getBufferedRanges=function(e){var t,n;return null!=(t=null==(n=this.sourceBuffers[B[e]])?void 0:n.getBufferedRanges())?t:[]},t.addTrack=function(e,t,n,r){var i=this.mediaSource,o=this.sourceBuffers,a=function(e){return"video/mp4;"+e}(t);if(o[e]){var s,u=W(o[e].codec);return W(t)!==u&&o[e].changeType(a),null!=(s=o[e].buffer)?s:null}try{var c=i.addSourceBuffer(a);return o[e]=new Y(c,t,n,r,this.handleError.bind(this)),c}catch(e){this.handleError(e,"open"===i.readyState)}return null},t.append=function(e,t){var n;null==(n=this.sourceBuffers[e])||n.appendBuffer(t)},t.remove=function(e,t){for(var n=this.sourceBuffers,r=0,i=Object.keys(n);r<i.length;r++)n[i[r]].remove(e,t)},t.setTimestampOffset=function(e,t){var n=this.sourceBuffers[e];n&&(n.abort(),n.setTimestampOffset(t))},t.setDuration=function(e){var t=this;this.scheduleUpdate((function(){return t.mediaSource.duration=e})).catch((function(e){return t.handleError(e,!1)}))},t.setLiveSeekableRange=function(e,t){var n=this;this.scheduleUpdate((function(){return n.mediaSource.setLiveSeekableRange(e,t)})).catch((function(e){return n.handleError(e,!1)}))},t.scheduleUpdate=function(e){var t=this;void 0===e&&(e=q);var n=Object.keys(this.sourceBuffers).map((function(e){return t.sourceBuffers[e]}));return Promise.all(n.map((function(e){return e.block()}))).then(e).then((function(){return n.forEach((function(e){return e.unblock()}))}))},t.isDrmProtected=function(){return this.bufferProperties.some((function(e){return e.isProtected}))},t.isAudioOnly=function(){return 1===this.bufferProperties.length&&"audio_only"===this.bufferProperties[0].group},t.destroy=function(){this.destroySourceBuffers(),this.unsubscribers.forEach((function(e){return e()})),this.unsubscribers=[]},t.handleError=function(e,t){var n=e.code||102,r=102;"NotSupportedError"===e.name&&(r=n=4),this.onError(r,n,e.message,t)},t.destroySourceBuffers=function(){for(var e=this.mediaSource;e.sourceBuffers.length>0;)try{e.removeSourceBuffer(e.sourceBuffers[0])}catch(e){this.handleError(e,!1);break}for(var t=0,n=Object.keys(this.sourceBuffers);t<n.length;t++){var r=n[t];this.sourceBuffers[r].destroy()}this.sourceBuffers=Object.create(null)},L(e,[{key:"duration",get:function(){return this.mediaSource.duration}},{key:"bufferProperties",get:function(){var e=this.sourceBuffers;return Object.keys(e).map((function(t){var n=e[t];return{trackID:Number(t),codec:n.codec,mode:"mse",path:"",isProtected:n.isProtected,group:n.group,srcObj:null}}))}}]),e}(),q=function(){},z=function(e){var t,n;function r(t,n){var r;return(r=e.call(this)||this).sendMessageToClient=t,r.sendMessageToCore=n,r.mseSink=void 0,r.awaitSink=void 0,r.sendRPC=void 0,r.nativeControlsEnabled=!1,r.resolveIdleDeferral=void 0,r.paused=!0,r.sendRPC=t.bind(function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(r),a.MEDIA_SINK_RPC),r.awaitSink=void 0,r}n=e,(t=r).prototype=Object.create(n.prototype),t.prototype.constructor=t,U(t,n);var i=r.prototype;return i.invoke=function(e,t){var n=this.awaitSink,r=this.mseSink;n&&r?["enqueue","addTrack","setTimestampOffset"].includes(e.name)?this.invokeAsync(e):this.invokeSync(e):n?this.invokeAsync(e):r?this.invokeSync(e):this.sendRPC(e,t)},i.configure=function(e){var t=this,n=this.awaitSink,r=this.mseSink;if("mse-worker"===e.mode){if(!r&&!n){console.info("MSE in workers enabled");var i=V.create(this.onMediaSourceEnded.bind(this),this.onMediaSourceError.bind(this)),o=i.ms.handle;this.sendMessageToClient(a.CONFIGURE,x({},e,{srcObj:o}),[o]),this.awaitSink=new b((function(e,n){i.sink.then((function(n){t.handleCreateSuccess(n),e()})).catch((function(e){t.handleCreateError(e),n()}))}))}this.awaitSink||!this.isContentProtectionChanging(e)&&!this.isChangingToFromAudioOnly(e)||this.queueNewSink(),e.isProtected&&this.sendMessageToClient(a.CONFIGURE,e),this.invoke({name:"addTrack",arg:x({},K,e)})}else this.sendMessageToClient(a.CONFIGURE,e)},i.addTrack=function(e){var t=e.trackID,n=e.codec,r=e.group,i=e.isProtected;this.mseSink.addTrack(t,n,r,i)},i.enqueue=function(e){var t=e.trackID,n=e.buffer;this.mseSink.append(t,n)},i.remove=function(e){var t=e.start,n=e.end;this.mseSink.remove(t,n)},i.setTimestampOffset=function(e){var t=e.trackID,n=e.offset;this.mseSink.setTimestampOffset(t,n)},i.onSourceDurationChanged=function(e){var t=this.mseSink,n=t.duration,r=function(e,t,n){var r=e;return e===1/0||e===G?n?r=1/0:n||(r=G):e!==t&&(r=e),r}(e,n,this.nativeControlsEnabled);r!==n&&t.setDuration(r)},i.play=function(){var e=this;this.paused=!1,this.mseSink.scheduleUpdate().then((function(){return e.sendRPC({name:"play",arg:void 0})}))},i.pause=function(){var e=this;this.paused=!0,this.mseSink.scheduleUpdate().then((function(){return e.sendRPC({name:"pause",arg:void 0})}))},i.seekTo=function(e){var t=this;this.mseSink.scheduleUpdate().then((function(){return t.sendRPC({name:"seekTo",arg:e})}))},i.setVolume=function(e){this.sendRPC({name:"setVolume",arg:e})},i.setMuted=function(e){this.sendRPC({name:"setMuted",arg:e})},i.setPlaybackRate=function(e){this.sendRPC({name:"setPlaybackRate",arg:e})},i.endOfStream=function(){var e=this;this.mseSink.scheduleUpdate().then((function(){return e.sendRPC({name:"endOfStream",arg:void 0})}))},i.changeSrcObj=function(e){this.sendRPC({name:"changeSrcObj",arg:e},[e])},i.unblockIfWaitingForIdle=function(){var e;null==(e=this.resolveIdleDeferral)||e.call(this),this.resolveIdleDeferral=void 0},i.reset=function(){this.destroyMSESink(),this.sendMessageToClient(a.RESET)},i.delete=function(){this.destroyMSESink(),this.sendMessageToClient(a.DESTROY)},i.invokeSync=function(e){this[e.name].call(this,e.arg)},i.invokeAsync=function(e){var t=this;this.awaitSink.then((function(){return t.invokeSync(e)})).catch((function(){}))},i.handleCreateSuccess=function(e){this.mseSink=e,this.awaitSink=void 0,this.onSourceDurationChanged(G),this.mseSink.setLiveSeekableRange(0,G)},i.handleCreateError=function(e){this.sendMessageToCore("onClientSinkError",[4,4,e.toString()]),this.awaitSink=void 0},i.queueNewSink=function(){var e=this;this.awaitSink=new b((function(t,n){e.deferUntilIdle().then((function(){var t=V.create(e.onMediaSourceEnded.bind(e),e.onMediaSourceError.bind(e));return e.changeSrcObj(t.ms.handle),t.sink})).then((function(n){e.destroyMSESink(),e.handleCreateSuccess(n),e.play(),t()})).catch((function(t){e.handleCreateError(t),n()}))}))},i.isContentProtectionChanging=function(e){var t,n;return(null!=(t=null==(n=this.mseSink)?void 0:n.isDrmProtected())&&t)!==e.isProtected},i.isChangingToFromAudioOnly=function(e){var t,n;return(null!=(t=null==(n=this.mseSink)?void 0:n.isAudioOnly())&&t)!==("audio_only"===e.group)},i.deferUntilIdle=function(){var e=this,t=this.mseSink;return new b((function(n){t&&!e.paused?e.resolveIdleDeferral=n:n()}))},i.onMediaSourceError=function(e,t,n,r){var i=[e,t,n];n.includes("HTMLMediaElement.error")&&(r=!0,this.destroyMSESink()),r?this.sendMessageToCore("onClientSinkError",i):this.sendMessageToCore("onClientSinkRecoverableError",i)},i.onMediaSourceEnded=function(){this.destroyMSESink(),this.sendMessageToCore("onClientSinkReset",void 0)},i.destroyMSESink=function(){var e=this,t=function(){e.mseSink.destroy(),e.awaitSink=void 0,e.mseSink=void 0,e.paused=!0};this.mseSink?t():this.awaitSink&&this.awaitSink.then((function(){return t()}))},L(r,[{key:"controls",set:function(e){this.nativeControlsEnabled=e,this.mseSink&&this.invoke({name:"onSourceDurationChanged",arg:this.mseSink.duration})}}]),r}(function(){function e(){}var t=e.prototype;return t.addTrack=function(e){},t.bufferDuration=function(){return 0},t.buffered=function(){return{start:0,end:0}},t.getBufferedRanges=function(e){return[]},t.captureGesture=function(){},t.configure=function(e){},t.decodedFrames=function(){return 0},t.delete=function(){},t.droppedFrames=function(){return 0},t.endOfStream=function(){},t.enqueue=function(e){},t.framerate=function(){return 0},t.getCurrentTime=function(){return 0},t.getDisplayHeight=function(){return 0},t.getDisplayWidth=function(){return 0},t.getPlaybackRate=function(){return 0},t.getVolume=function(){return 0},t.invoke=function(e){this[e.name].call(this,e.arg)},t.isMuted=function(){return!1},t.onSourceDurationChanged=function(e){},t.pause=function(){},t.play=function(){},t.reinit=function(){},t.remove=function(e){},t.seekTo=function(e){},t.setMuted=function(e){},t.setPlaybackRate=function(e){},t.setTimestampOffset=function(e){},t.setVolume=function(e){},t.changeSrc=function(e){},t.changeSrcObj=function(e){},t.onSegmentDiscontinuity=function(){},e}()),K={trackID:0,codec:'codecs="magic"',mode:"mse-worker",isProtected:!1,path:"",group:"",srcObj:null},X={powerEfficient:!0,smooth:!0,supported:!0};function J(e){return Q.apply(this,arguments)}function Q(){return(Q=M(I().mark((function e(t){var n,r,i,o,a,s;return I().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(navigator.mediaCapabilities){e.next=3;break}return console.log("MediaCapabilities is not supported"),e.abrupt("return",X);case 3:if(t.codecs){e.next=6;break}return console.log("MediaCapabilities: codec string is empty or undefined:",t.codecs),e.abrupt("return",X);case 6:if(n=t.codecs.split(","),!t.height){e.next=14;break}o=t.framerate,t.framerate||(o=30,console.log("MediaCapabilities: quality framerate not found, defaulting to 30fps")),r={contentType:'video/mp4;codecs="'+n[0]+'"',width:t.width,height:t.height,bitrate:t.bitrate,framerate:o},2===n.length&&(i={contentType:'audio/mp4;codecs="'+n[1]+'"'}),e.next=20;break;case 14:if(1!==n.length){e.next=18;break}i={contentType:'audio/mp4;codecs="'+n[0]+'"'},e.next=20;break;case 18:return console.log("MediaCapabilities: quality looks like it has video, but height is invalid",t),e.abrupt("return",X);case 20:return a={type:"media-source",video:r,audio:i},e.prev=21,e.next=24,navigator.mediaCapabilities.decodingInfo(a);case 24:return s=e.sent,e.abrupt("return",{powerEfficient:s.powerEfficient,smooth:s.smooth,supported:s.supported});case 28:return e.prev=28,e.t0=e.catch(21),console.warn("MediaCapabilities: Failed to get DecodingInfo",e.t0),e.abrupt("return",X);case 32:case"end":return e.stop()}}),e,null,[[21,28]])})))).apply(this,arguments)}var Z=function(e){return e.AVC="avc1",e.HEVC="hvc1",e.AV1="av01",e}({}),ee=function(e){return e.HEVC="b8b7cac6-956d-4819-8d35-9ae57aa5f0ce",e.AV1="d16629fa-a0bf-4a2a-b490-c1f92af05009",e}({});function te(e,t){switch(t){case"control":case"holdback":return[{codecString:Z.HEVC,setting:{disableUse:!0}}];case"forceOn":return[{codecString:Z.HEVC,setting:{skipPlatformSupportChecks:!0}}];case"treatment":return[];default:return e}}function ne(e,t){switch(t){case"control":case"holdback":return[{codecString:Z.AV1,setting:{disableUse:!0}}];case"forceOn":return[{codecString:Z.AV1,setting:{skipPlatformSupportChecks:!0}}];case"treatment":return[];default:return e}}var re=function(){function e(e){void 0===e&&(e={}),this.mainConfig=void 0,this.listeners=void 0,this.experimentHandler=void 0,this.resetMainConfig(),this.listeners=[],this.setupExperiments(),e.logLevel&&(this.mainConfig.logLevel=e.logLevel),e.media&&Object.assign(this.mainConfig.media,e.media)}var t=e.prototype;return t.setupExperiments=function(){var e=this,t=this.handleExperiment((function(){return e.mainConfig.media.codecConfigs}),te,(function(t){var n=e.mainConfig.media.codecConfigs;e.mainConfig.media.codecConfigs=null==n?void 0:n.concat(t)})),n=this.handleExperiment((function(){return e.mainConfig.media.codecConfigs}),ne,(function(t){var n=e.mainConfig.media.codecConfigs;e.mainConfig.media.codecConfigs=null==n?void 0:n.concat(t)}));this.experimentHandler=new Map([[ee.HEVC,t],[ee.AV1,n]])},t.resetMainConfig=function(){this.mainConfig={media:{codecConfigs:[]},logLevel:""}},t.invokeListeners=function(){this.listeners.forEach((function(e){e()}))},t.handleExperiment=function(e,t,n){var r=this;return function(i){var o=e(),a=t(o,i);a!==o&&(n(a),r.invokeListeners())}},t.getConfigSnapshot=function(){return this.mainConfig},t.setExperiment=function(e,t){var n=this.experimentHandler.get(e);return!!n&&(n(t),!0)},t.addConfigUpdateListener=function(e){this.listeners.push(e)},t.delete=function(){this.listeners=[],this.resetMainConfig()},e}(),ie=function(){function e(e,t,n,r){var i=this;this.id=void 0,this.port=void 0,this.module=void 0,this.configManager=void 0,this.player=void 0,this.playerFactory=void 0,this.workerSink=new z(this.postMessage.bind(this),this.onClientMessage.bind(this)),this.startCapture=void 0,this.stopCapture=void 0,this.requestCaptureAnalytics=void 0,this.captureEntireSegmentBytes=void 0,this.id=t,this.port=e,this.module=n,this.configManager=new re,this.playerFactory=function(e){var t=i.configManager.getConfigSnapshot();return new i.module.WebMediaPlayer(e,r,t)},this.player=this.playerFactory(this),this.configManager.addConfigUpdateListener((function(){i.player.updatePlayerConfiguration(i.configManager.getConfigSnapshot())})),this.postMessage(o.INITIALIZED)}var t=e.prototype;return t.recreatePlayer=function(){this.port.postMessage("recreatePlayer"),this.player=this.playerFactory(this)},t.getPointer=function(){return this.player.$$.ptr},t.onClientMessage=function(e,t){var n;this.applyMessageToSink(e,t),this.applyMessageToConfigManager(e,t),"function"==typeof this.player[e]?(n=this.player)[e].apply(n,t):this[e]},t.getDecodingInfo=function(e){this.postMessage(a.GET_DECODE_INFO,e)},t.onExperiments=function(e){this.postMessage(a.GET_EXPERIMENTS,e)},t.onSessionData=function(e){this.postMessage(o.SESSION_DATA,{sessionData:e})},t.onStateChanged=function(e){this.postMessage(a.STATE_CHANGED,e)},t.onSyncTimeChanged=function(e){this.postMessage(a.SYNC_TIME_CHANGED,e)},t.onSegmentDiscontinuity=function(){this.postMessage(o.SEGMENT_DISCONTINUITY)},t.onNetworkUnavailable=function(){this.postMessage(o.NETWORK_UNAVAILABLE)},t.onRebuffering=function(){this.postMessage(o.REBUFFERING)},t.onQualityChanged=function(e){this.postMessage(o.QUALITY_CHANGED,e)},t.onSeekCompleted=function(e){this.postMessage(o.SEEK_COMPLETED,e)},t.onDurationChanged=function(e){this.postMessage(o.DURATION_CHANGED,e),this.workerSink.invoke({name:"onSourceDurationChanged",arg:e})},t.onBufferedRanges=function(e,t){this.postMessage(a.BUFFERED_RANGES,{audio:e,video:t})},t.onJSONMetadata=function(e){var t,n,r=ae(e);"ID3"in r?(t=N.ID3,n=r.ID3):"caption"in r&&(t=N.CAPTION,n=r.caption),t&&this.postMessage(t,n)},t.onMetadata=function(e,t){if(t.buffer){var n=new Uint8Array(t).buffer;this.postMessage(o.METADATA,{type:e,data:n},[n])}else this.postMessage(o.METADATA,{type:e,data:t})},t.onCue=function(e){if("TextCue"===e.type)this.postMessage(o.TEXT_CUE,e);else if("TextMetadataCue"===e.type){var t=e;if(this.postMessage(o.TEXT_METADATA_CUE,e),"segmentmetadata"===t.description&&t.text)try{var n=JSON.parse(t.text);void 0!==n.stream_offset&&this.postMessage(o.SEGMENT_METADATA,{streamOffset:n.stream_offset})}catch(e){}}else"AdCue"===e.type?this.postMessage(o.AD_CUE,e):"StreamSourceCue"===e.type&&this.postMessage(o.STREAM_SOURCE_CUE,e)},t.onError=function(e,t,n,r){this.postMessage(o.ERROR,{type:e,code:t,source:n,message:r})},t.onRecoverableError=function(e,t,n,r){this.postMessage(o.RECOVERABLE_ERROR,{type:e,code:t,source:n,message:r})},t.onAnalyticsEvent=function(e,t){var n=ae(t);this.postMessage(o.ANALYTICS_EVENT,{name:e,properties:n})},t.configure=function(e,t,n,r,i,o){this.workerSink.configure({trackID:e,codec:t,path:n,mode:r,isProtected:i,group:o,srcObj:null})},t.enqueue=function(e,t){var n=new Uint8Array(t).buffer;this.workerSink.invoke({name:"enqueue",arg:{trackID:e,buffer:n}},[n])},t.endOfStream=function(){this.workerSink.invoke({name:"endOfStream",arg:void 0})},t.setTimestampOffset=function(e,t){this.workerSink.invoke({name:"setTimestampOffset",arg:{trackID:e,offset:t}})},t.play=function(){this.workerSink.invoke({name:"play",arg:void 0})},t.pause=function(){this.workerSink.invoke({name:"pause",arg:void 0})},t.reset=function(){this.workerSink.reset()},t.remove=function(e,t){this.workerSink.invoke({name:"remove",arg:{start:e,end:t}})},t.seekTo=function(e){this.workerSink.invoke({name:"seekTo",arg:e})},t.setPlaybackRate=function(e){this.workerSink.invoke({name:"setPlaybackRate",arg:e}),this.postMessage(o.PLAYBACK_RATE_CHANGED,e)},t.setVolume=function(e){this.workerSink.invoke({name:"setVolume",arg:e})},t.addCue=function(e,t,n){this.postMessage(a.ADD_CUE,{id:e,start:t,end:n})},t.getMediaCapabilities=function(e){var t=this;Promise.all(e.map(function(){var e=M(I().mark((function e(t){return I().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.t0=t,e.next=3,J(t);case 3:return e.t1=e.sent,e.abrupt("return",{quality:e.t0,decodingInfo:e.t1});case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())).then((function(e){console.log("MediaCapabilities found",e),t.onClientMessage("onMediaCapabilities",[e])})).catch((function(e){console.warn("Failed to retrieve mediaCapabilites",e),t.onClientMessage("onMediaCapabilities",[])}))},t.onPropertyChanged=function(e,t){this.postMessage(a.PROPERTY_CHANGED,{key:e,value:t})},t.postMessage=function(e,t,n){oe(this.port,this.id,e,t,n)},t.applyMessageToSink=function(e,t){switch(e){case"delete":this.workerSink.delete();break;case"setControls":this.workerSink.controls=t[0];break;case"onClientSinkIdle":this.workerSink.unblockIfWaitingForIdle()}},t.applyMessageToConfigManager=function(e,t){if("setExperiment"===e){var n=t[0],r=n.id,i=n.assignment;this.configManager.setExperiment(r,i)}},e}();function oe(e,t,n,r,i){e.postMessage({id:t,type:n,arg:r},i)}function ae(e){try{return JSON.parse(e)}catch(t){return console.error("Failed JSON parse:",e),{}}}var se,ue=4,ce=2;(se="undefined"==typeof messageHandler?self:messageHandler).onmessage=function(e){var t=new fe(se);se.onmessage=function(e){return t.dispatch(e)},function(e,t){var n,i,o,a={locateFile:function(){return t.wasmBinaryUrl},sendFetchRequest:function(e,t,r,i){return function(e,t,n,r,i){var o=null;"undefined"!=typeof AbortController&&(o=new AbortController,r.signal=o.signal);var a=new O(e,o),s=-1;function u(){-1!==s&&clearTimeout(s),t.delete()}return i>0&&(s=self.setTimeout((function(){return a.abort()}),i)),T(n,r).then((function(e){a.setResponse(e),a.cancelled||t.response(a)})).catch((function(e){a.cancelled||t.error("AbortError"===e.name,e.message)})).then(u,u),function(){return a.cancel()}}(n,e,t,r,i)},onAbort:function(r){if(n){var i,o=null!=(i=Error().stack)?i:"Stack information not available";if(t.showWorkerLogs)return void e.logMessage("error",r+"\n"+JSON.stringify(o));s(r),s(JSON.stringify(o).replace(/\\n/g,"\n"));var a=JSON.stringify({logList:n.getLogListAndClear(),stack:o});e.sendErrorMessage(a,0)}else console.error(r)},logMessage:function(r,i){if(t.showWorkerLogs){var o="log";return r&ue?o="error":r&ce&&(o="warn"),void e.logMessage(o,i)}n._emscripten_log_js(r,i),s(i)},getLogListAndClear:function(){var e=n.logList;return n.logList=[],e},print:function(e){s(e),console.log(e)},printErr:function(e){s(e),console.warn(e)},copyUint8ArrayToEmscriptenHeap:(i=0,o=0,function(e,t){void 0===t&&(t=!1);var r=e.byteLength;if(t){var a=n._malloc(r);return n.HEAPU8.set(e,a),a}return r>o&&(i&&n._free(i),i=n._malloc(r),o=r),n.HEAPU8.set(e,i),i})};r(a).then((function(t){n=t,e.ready(n)}));var s=function(e){n&&n.collectLogs&&n.logList.push(e)}}(t,e.data)};var fe=function(){function e(e){this.activePlayers=void 0,this.port=void 0,this.eventQueue=void 0,this.module=void 0,this.activePlayers=Object.create(null),this.port=e,this.eventQueue=[],this.module=null}var t=e.prototype;return t.dispatch=function(e){var t=this;if(null!==this.module){if(!this.module.skipEvents){var n=e.data,r=n.id,a=n.funcName,s=n.args;try{if("create"===a){var u=s[0];return this.activePlayers[r]=new ie(this.port,r,this.module,u),void(!0===u.testOnly&&(this.module.recreatePlayer=function(){var e;null==(e=t.activePlayers[r])||e.recreatePlayer()},this.module.getPointer=function(){var e,n;return t.module.skipEvents=!1,null!=(e=null==(n=t.activePlayers[r])?void 0:n.getPointer())?e:0},this.module.skipWorkerMessage=function(){t.module.skipEvents=!0}))}if("runTests"===a)return this.module.collectLogs=!0,this.module.logList=[],this.module.cliOptions=s[0],void this.module._runTests();if(!this.activePlayers[r])return;this.activePlayers[r].onClientMessage(a,s),"delete"===a&&(this.activePlayers[r]=null)}catch(e){console.warn(e),this.activePlayers[r]&&oe(this.port,r,o.WORKER_ERROR,{code:i.GENERIC,source:"worker",message:e.message})}}}else this.eventQueue.push(e)},t.ready=function(e){this.module=e,this.eventQueue.forEach(this.dispatch,this),this.eventQueue=[]},t.logMessage=function(e,t){oe(this.port,0,a.LOG_MESSAGE,{level:e,message:t})},t.sendErrorMessage=function(e,t){oe(this.port,t,o.WORKER_ERROR,{code:i.GENERIC,source:"worker",message:e})},e}()}()}();
