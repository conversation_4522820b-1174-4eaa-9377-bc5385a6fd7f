CANVAS_ID = "canvas-container"
CANVAS_WIDTH = 300
CANVAS_HEIGHT = 150
CANVAS_STYLE = (
    "width: 100% !important; " +
    "visibility: visible !important; " +
    "display: block !important; " +
    "opacity: 1 !important; " +
    "z-index: 9999 !important; " +
    "max-width: 100% !important; " +
    "position: absolute !important; " +
    "height: 100% !important; " +
    "top: 0px !important; " +
    "left: 0px !important; " +
    "pointer-events: none !important;"
);

class VideoAnnotator {
    videoElement;
    player;
    canvas;
    annotations

    constructor(videoId, annotations) {
        this.videoElement = document.getElementById(videoId)
        this.player = videojs(videoId)
        this.canvas = this.createCanvas()
        this.annotations = this.createAnnotations(annotations)
    }

    run() {
        this.prepareCanvas()
        this.insertCanvas()
        this.applyWatermark()
    }

    prepareCanvas() {
        this.listenForMutation()
        this.setStyleOnCanvas()
        this.sharpenTextInCanvas()
    }

    insertCanvas(){
        this.videoElement.appendChild(this.canvas)
    }

    applyWatermark() {
        this.player.on("play", this.drawWatermark)
        this.player.on("pause", this.clearWatermark)
        this.player.on("ended", this.clearWatermark)
    }

    createCanvas() {
        const canvas = document.createElement("canvas")
        canvas.setAttribute("id", CANVAS_ID)

        return canvas
    }

    listenForMutation() {
        const observer = new MutationObserver(this.mutationCallback)
        observer.observe(this.videoElement, {
            childList: true,
            subtree: true,
            attributeFilter: ["style"],
        });
    }

    mutationCallback = (mutations, observer) => {
        for (const mutation of mutations) {
            switch (mutation.type) {
                case "childList":
                    this.reCreateCanvasIfRemoved(mutation.removedNodes)
                    break
                case "attributes":
                    this.applyDefaultStyleOnCanvas(mutation.target)
                    break
            }
        }
    };

    reCreateCanvasIfRemoved(removedNodes) {
        removedNodes.forEach((node) => {
            if (this.isCanvasNode(node)) this.run()
        });
    }

    applyDefaultStyleOnCanvas(node) {
        if (this.isCanvasNode(node) && this.isCanvasStyleChanged())
            this.setStyleOnCanvas()
    }

    isCanvasNode = (node) => node.isSameNode(this.canvas)

    isCanvasStyleChanged = () => this.canvas.style.cssText !== CANVAS_STYLE

    setStyleOnCanvas(){
        this.canvas.style.cssText = CANVAS_STYLE
    };

    // Fixed blurry text issue with https://www.geeksforgeeks.org/how-to-sharpen-blurry-text-in-html5-canvas
    sharpenTextInCanvas() {
        const context = this.canvas.getContext("2d")
        window.devicePixelRatio = 5
        context.canvas.width = CANVAS_WIDTH * window.devicePixelRatio
        context.canvas.height = CANVAS_HEIGHT * window.devicePixelRatio
        context.scale(window.devicePixelRatio, window.devicePixelRatio)
    }

    drawWatermark = () => {
        this.drawWatermarkInterval = setInterval(()=>{this.clearContext(); this.watermark()}, 1000)
    }

    clearContext() {
        const context = this.canvas.getContext("2d")
        context.clearRect(0, 0, context.canvas.width, context.canvas.height)
        context.save();
    }

    watermark() {
        this.annotations.forEach((annotation) => {
            this.draw(annotation)
        })
    }

    draw(annotation) {
        if (!annotation.visible) return
        const context = this.canvas.getContext("2d")
        let { text, x, y, size, color, opacity } = annotation
        context.font = `${size}px verdana`
        context.fillStyle = color
        context.globalAlpha = opacity
        context.fillText(text, x, y)
    }

    clearWatermark = () => {
        clearInterval(this.drawWatermarkInterval)
        this.clearContext()
    }

    createAnnotations(annotations) {
        const objects = [];
        annotations.forEach((annotation) => {
            objects.push(new Annotation({...annotation, canvas: this.canvas}))
        })

        return objects
    }
}

class Annotation {
    constructor({ text, x = 20, y = 20, type = "static", color = "ff0000", size = 6, opacity = 0.8, interval = 2000, skip = 0, canvas }) {
        this.text = text
        this.x = x
        this.y = y
        this.color = color
        this.size = size
        this.opacity = opacity
        this.interval = interval
        this.skip = skip
        this.visible = true
        this.canvas = canvas
        if (type === "dynamic") this.initializeAttributeModifiers()
    }

    initializeAttributeModifiers() {
        this.triggerModifiers()
        setInterval(this.triggerModifiers, this.interval + this.skip)
    }

    triggerModifiers = () => {
        this.setRandomCoordinates()
        this.skipWaterMarkingAfter(this.interval)
    }

    setRandomCoordinates = () => {
        const context = this.canvas.getContext("2d");
        const textMetrics = context.measureText(this.text);
        const textWidth = textMetrics.width;
        const textHeight = textMetrics.actualBoundingBoxAscent + textMetrics.actualBoundingBoxDescent;

        this.x = Math.floor(Math.random() * (CANVAS_WIDTH - textWidth));
        this.y = Math.floor(Math.random() * (CANVAS_HEIGHT - textHeight));
    }

    skipWaterMarkingAfter(seconds) {
        setTimeout(this.hideWatermarkText, seconds)
    }

    hideWatermarkText = () => {
        this.visible = false
        setTimeout(this.showWatermarkText, this.skip)
    }

    showWatermarkText = () => {
        this.visible = true
    }
}
