/*! For license information please see amazon-ivs-quality-plugin.min.js.LICENSE.txt */
var registerIVSQualityPlugin;!function(){"use strict";var e={d:function(t,n){for(var a in n)e.o(n,a)&&!e.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:n[a]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}},t={};!function(){e.d(t,{registerIVSQualityPlugin:function(){return r}});var n=function(e){return e.INITIALIZED="PlayerInitialized",e.QUALITY_CHANGED="PlayerQualityChanged",e.DURATION_CHANGED="PlayerDurationChanged",e.VOLUME_CHANGED="PlayerVolumeChanged",e.MUTED_CHANGED="PlayerMutedChanged",e.PLAYBACK_RATE_CHANGED="PlayerPlaybackRateChanged",e.REBUFFERING="PlayerRebuffering",e.AUDIO_BLOCKED="PlayerAudioBlocked",e.PLAYBACK_BLOCKED="PlayerPlaybackBlocked",e.ERROR="PlayerError",e.RECOVERABLE_ERROR="PlayerRecoverableError",e.ANALYTICS_EVENT="PlayerAnalyticsEvent",e.TIME_UPDATE="PlayerTimeUpdate",e.SYNC_TIME_UPDATE="PlayerSyncTimeUpdate",e.BUFFER_UPDATE="PlayerBufferUpdate",e.SEEK_COMPLETED="PlayerSeekCompleted",e.SESSION_DATA="PlayerSessionData",e.STATE_CHANGED="PlayerStateChanged",e.WORKER_ERROR="PlayerWorkerError",e.METADATA="PlayerMetadata",e.TEXT_CUE="PlayerTextCue",e.TEXT_METADATA_CUE="PlayerTextMetadataCue",e.AD_CUE="PlayerAdCue",e.STREAM_SOURCE_CUE="PlayerStreamSourceCue",e.NETWORK_UNAVAILABLE="PlayerNetworkUnavailable",e.SEGMENT_DISCONTINUITY="PlayerSegmentDiscontinuity",e.SEGMENT_METADATA="PlayerSegmentMetadata",e}({}),a=function(e){return e.IDLE="Idle",e.READY="Ready",e.BUFFERING="Buffering",e.PLAYING="Playing",e.ENDED="Ended",e}({}),i="enableIVSQualityPlugin";function r(e){if(void 0===e||"function"!=typeof e.getTech)throw{message:"videojs not available, Amazon IVS Quality Plugin not registered",code:1};if(!e.getPlugin(i)){var t,r=e.getComponent("MenuButton"),l=e.getComponent("MenuItem"),o={privateConstructor:function(e,t,n){n(this,e,t),this.controlText("Quality")},createItems:function(){var e=this.player(),t=e.getIVSPlayer(),a=[],i=new l(e,{selectable:!0,label:"Auto",qualityGroup:"auto"});i.selected(t.isAutoQualityMode()),i.on(["click","tap"],(function(){t.setAutoQualityMode(!0)})),i.on(["click","tap"],this._clickHandler.bind(this,i)),a.push(i);var r=t.getQuality(),o=t.getQualities();return o&&o.length>0&&o.forEach(function(n){var i=new l(e,{selectable:!0,label:n.name,qualityGroup:n.group}),o=function(e){t.setQuality(e)}.bind(null,n);i.on(["click","tap"],o),i.on(["click","tap"],this._clickHandler.bind(this,i)),t.isAutoQualityMode()||i.selected(r.group===n.group),a.push(i)}.bind(this)),t.addEventListener(n.QUALITY_CHANGED,(function(n){var a;if(!t.isAutoQualityMode()){var i=null==(a=e.controlBar)?void 0:a.getChild("QualityMenuButton");null==i||i.items.forEach((function(e){var t=e.options().qualityGroup;n&&t&&e.selected(n.group===t)}))}})),this.qualityItems=a,a},buildCSSClass:function(){return"vjs-icon-hd vjs-icon-placeholder "+r.prototype.buildCSSClass.call(this)},_clickHandler:function(e){this.items.forEach((function(t){t!==e&&t.selected(!1)}))}};"function"==typeof e.extend?(o.constructor=function(e,t){this.privateConstructor(e,t,(function(e,t,n){r.call(e,t,n)}))},t=e.extend(r,o)):((t=function(e,t){this.privateConstructor(e,t,(function(e,t,n){var a="quality-control";n.name=a;var i=new r(t,n);i.getChild(a).addClass("vjs-icon-hd vjs-icon-placeholder"),Object.assign(e,i),e.items=e.createItems(),e.items.forEach((function(t){e.menu.addItem(t)}))}))}).prototype=Object.create(r.prototype),Object.assign(t.prototype,o)),e.registerComponent("QualityMenuButton",t),(e.registerPlugin||e.plugin)(i,(function(){var e=this;e.getIVSPlayer().addEventListener(a.READY,(function(){var t,n,a=null==(t=e.controlBar)?void 0:t.getChild("QualityMenuButton");a&&(a.dispose(),e.controlBar.removeChild(a)),null==(n=e.controlBar)||n.addChild("QualityMenuButton")}))}))}}}(),registerIVSQualityPlugin=t.registerIVSQualityPlugin}();
