from django.contrib import auth, messages
from django.shortcuts import redirect
from django.utils.translation import gettext_lazy as _

from app.models import Organization


class CheckOrganizationStatusMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        if request.user.is_authenticated and not request.user.is_superuser:
            if (
                request.user.current_organization
                and request.user.current_organization.status
                == Organization.Status.BLOCKED
            ):
                auth.logout(request)
                messages.error(
                    request,
                    _("Your account is blocked. <NAME_EMAIL>."),
                )
                return redirect("account_login")

        return response
