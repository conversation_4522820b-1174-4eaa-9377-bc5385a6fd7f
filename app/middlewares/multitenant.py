from django_multitenant.utils import set_current_tenant, unset_current_tenant

EXCLUDE_PATHS_FOR_SETTING_CURRENT_TENANT = [
    "/admin/",
    "/embed/",
    "/drm_license/",
    "/live-chat/",
    "/api/",
    "/log/",
    "/live/internals/",
    "/live/acknowledge-event/",
]


class MultitenantMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        unset_current_tenant()
        if request.user.is_authenticated and not any(
            path in request.path for path in EXCLUDE_PATHS_FOR_SETTING_CURRENT_TENANT
        ):
            set_current_tenant(request.user.current_organization)
        return self.get_response(request)
