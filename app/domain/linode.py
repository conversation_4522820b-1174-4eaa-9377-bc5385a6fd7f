import base64

import sentry_sdk
from django.conf import settings
from django.urls import reverse
from linode_api4 import LinodeClient
from linode_api4.objects import Instance, Region, Type

from app.utils.live_stream import LiveStreamServer

THIRTY_MINUTES = 30 * 60


class LinodeLiveStreamServer:
    def __init__(self, name, asset):
        from app.domain.live_stream import is_transcoding_needed

        self.name = name
        self.asset = asset
        self.organization = asset.organization
        self.client = LinodeClient(settings.TPSTREAMS_LINODE_API_TOKEN)
        self.store_backup_in_aws = is_transcoding_needed(asset.live_stream)
        self.output_path = self.get_output_path()

    def create_server(self):
        try:
            region = Region(self.client, settings.TPSTREAMS_LINODE_REGION)

            ltype = Type(self.client, self.get_server_type())

            instance = self.client.linode.instance_create(
                ltype=ltype,
                region=region,
                image=self.get_image_id(),
                root_pass=settings.TPSTREAMS_LINODE_ROOT_PASSWORD,
                label=self.name,
                tags=[self.name],
                booted=True,
                private_ip=True,
                metadata={
                    "user_data": base64.b64encode(
                        self.get_user_data().encode("utf-8")
                    ).decode("utf-8")
                },
            )

            # Create and attach a volume
            self.client.volume_create(
                label=f"vol-{instance.id}",  # Using instance ID to ensure uniqueness
                size=self.get_volume_size(),
                region=region,
                linode_id=instance.id,
            )

            return self._parse_live_stream_server_response(instance)
        except Exception as e:
            sentry_sdk.capture_exception(e)
            raise

    def get_image_id(self):
        return "private/31839447"

    def get_server_type(self):
        server_type = (
            "g6-standard-4"
            if self.organization.enable_hd_live_streaming
            else "g6-standard-2"
        )

        if self.asset.live_stream.enable_drm:
            server_type = "g6-standard-4"

        if self.organization.uuid in [
            "bn5yhg",
            "9mpasc",
            "n2nbhp",
            "bg5yak",
            "f87hk3",
            "h8g4g4",
            "mct2nn",
        ]:  # Insights
            server_type = "g6-standard-4"

        if self.asset.live_stream.enable_drm and self.organization.uuid in [
            "f87hk3",
            "h8g4g4",
            "mct2nn",
        ]:
            server_type = "g6-standard-8"

        if (
            self.asset.live_stream.enable_drm
            and self.organization.enable_hd_live_streaming
        ):
            server_type = "g6-standard-8"

        return server_type

    def get_volume_size(self):
        volume_size = 60 if self.organization.enable_hd_live_streaming else 40

        if self.asset.live_stream.enable_drm and self.organization.uuid in [
            "f87hk3",
            "h8g4g4",
            "mct2nn",
        ]:
            volume_size = 60

        return volume_size

    def get_output_path(self):
        from app.domain.live_stream import is_transcoding_needed

        organization = self.organization
        output_path = (
            f"s3://{self.asset.organization.bucket_name}/transcoded/{self.asset.uuid}/"
        )

        requires_transcoding = is_transcoding_needed(self.asset.live_stream)
        if requires_transcoding:
            output_path = f"s3://{settings.LIVE_STREAM_S3_BUCKET}/live_streams/{organization.uuid}/{self.asset.uuid}/"

        return output_path

    def get_callback_url(self):
        org_uuid = self.organization.uuid
        callback_url = settings.SITE_URL + reverse(
            "api:live-stream-server-callback",
            kwargs={
                "organization_id": org_uuid,
            },
        )
        return callback_url

    def get_user_data(self):
        """Generate cloud-init user data script for Linode, equivalent to AWS cloud-init"""
        org_uuid = self.organization.uuid
        callback_url = self.get_callback_url()
        output_path = self.output_path
        store_backup_in_aws = self.store_backup_in_aws

        # Determine git commands based on organization
        git_commands = """
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow pull origin main
    """

        if org_uuid == "f87hk3" or org_uuid == "h8g4g4" or org_uuid == "mct2nn":
            git_commands = """
      - mkdir /home/<USER>/livestream/nonfrag
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow fetch
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow checkout vbr
        """

        if org_uuid == "edee9b":
            git_commands = """
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow fetch
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow checkout vbr_testing
        """

        if org_uuid == "jh5tdb":
            git_commands = """
      - mkdir /home/<USER>/livestream/nonfrag
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow fetch
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow checkout vidyakul_low_bandwidth
        """

        if org_uuid == "hd66qr":
            git_commands = """
      - mkdir /home/<USER>/livestream/nonfrag
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow fetch
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow checkout high_bitrate
        """

        if org_uuid == "n2nbhp":
            git_commands = """
      - mkdir /home/<USER>/livestream/nonfrag
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow fetch
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow checkout variable_bitrate
        """

        if org_uuid == "bg5yak":
            git_commands = """
      - mkdir /home/<USER>/livestream/nonfrag
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow fetch
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow checkout Utkarsh-Classes
        """

        if org_uuid == "dcek2m":
            git_commands = """
      - mkdir /home/<USER>/livestream/nonfrag
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow fetch
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow checkout debug-rclone
        """

        # Set up auto-termination if in debug mode
        schedule_termination_command = ""
        if settings.DEBUG:
            schedule_termination_command = (
                f"sleep {settings.DURATION_TO_AUTO_TERMINATE} && sudo shutdown -h now"
            )

        # Set up AWS credentials
        aws_access_key = settings.TPSTREAMS_AWS_ACCESS_KEY_ID
        aws_secret_key = settings.TPSTREAMS_AWS_SECRET_ACCESS_KEY
        access_key = self.organization.storage_access_key_id
        secret_key = self.organization.storage_secret_access_key
        region = self.organization.storage_region
        endpoint = (
            f"https://s3.{region}.amazonaws.com"
            if self.organization.get_storage_vendor_display() == "Aws"
            else f"https://s3.{region}.wasabisys.com"
        )
        provider = (
            "AWS"
            if self.organization.get_storage_vendor_display() == "Aws"
            else "Wasabi"
        )

        # Create the cloud-init script
        return f"""
#cloud-config
write_files:
  - path: /tmp/post_data.sh
    permissions: '0755'
    owner: root
    content: |
      #!/bin/bash
      SERVER_ID=$(sudo cloud-init query ds.meta_data.instance-id)
      IP_ADDRESS=$(hostname -I | awk '{{print $1}}')
      URL={callback_url}

      JSON_DATA=$(cat <<EOF
      {{
        "server_id": "$SERVER_ID",
        "ip_address": "$IP_ADDRESS",
        "status": "created"
      }}
      EOF
      )

      curl -X POST -H "Content-Type: application/json" -d "$JSON_DATA" "$URL"
  - path: /home/<USER>/.s3_config
    permissions: '0755'
    owner: root
    content: |
      [s3]
      type = s3
      provider = {'AWS' if store_backup_in_aws else provider}
      access_key_id = {aws_access_key if store_backup_in_aws else access_key}
      secret_access_key = {aws_secret_key if store_backup_in_aws else secret_key}
      storage_class = STANDARD
      acl = public-read
      {'region = '+ region if not store_backup_in_aws else ''}
      {'endpoint = '+ endpoint if not store_backup_in_aws else ""}
  - path: /home/<USER>/rclone.sh
    permissions: '0755'
    owner: root
    content: |
      #!/bin/bash

      rclone copy /home/<USER>/livestream/ {output_path} --config=/home/<USER>/.s3_config --exclude "/video.mp4" --exclude "*.txt"

runcmd:
  - SERVER_ID=$(sudo cloud-init query ds.meta_data.instance-id)
  - echo "SERVER_ID=${{SERVER_ID}}" >> /etc/environment
  - echo 'ORG_CODE="{org_uuid}"' >> /etc/environment
  - echo 'WASABI_ACCESS_KEY_ID="{self.organization.storage_access_key_id}"' >> /etc/environment
  - echo 'WASABI_BUCKET_REGION="{self.organization.storage_region}"' >> /etc/environment
  - echo 'WASABI_SECRET_ACCESS_KEY="{self.organization.storage_secret_access_key}"' >> /etc/environment
  - echo 'TPSTREAMS_URL="{settings.SITE_URL}"' >> /etc/environment
  - source /etc/environment
  - /tmp/post_data.sh
  - cd /home/<USER>/workspace/liveflow/live-flow/
  {git_commands}
  - wget https://github.com/Karthik-0/gohlslib/releases/download/v31/muxer_linux_arm64
  - sudo mv muxer_linux_arm64 /usr/local/bin/muxer
  - sudo chmod +rx /usr/local/bin/muxer
  - sudo chmod o+x /home/<USER>
  - sed -i 's|^environment=.*|&SERVER_ID="'"${{SERVER_ID}}"'", ORG_CODE="{org_uuid}", TPSTREAMS_URL="{settings.SITE_URL}", WASABI_ACCESS_KEY_ID="{self.organization.storage_access_key_id}", WASABI_BUCKET_REGION="{self.organization.storage_region}", WASABI_SECRET_ACCESS_KEY="{self.organization.storage_secret_access_key}"|' /etc/supervisor/conf.d/celeryd.conf
  - sed -i 's|^environment=.*|&SERVER_ID="'"${{SERVER_ID}}"'", ORG_CODE="{org_uuid}", TPSTREAMS_URL="{settings.SITE_URL}", WASABI_ACCESS_KEY_ID="{self.organization.storage_access_key_id}", WASABI_BUCKET_REGION="{self.organization.storage_region}", WASABI_SECRET_ACCESS_KEY="{self.organization.storage_secret_access_key}"|' /etc/supervisor/conf.d/gunicorn.conf
  - systemctl enable supervisor
  - systemctl start supervisor
  - supervisorctl reread
  - supervisorctl update
  - supervisorctl start all
  - cd /home/<USER>
  - ./rclone.sh
  - {schedule_termination_command}
"""

    def _parse_live_stream_server_response(self, instance):
        from app.models import LiveStream, LiveStreamUsage

        server_id = str(instance.id)
        status = LiveStream.ServerStatus.CREATING
        if instance.status == "running":
            status = LiveStream.ServerStatus.CREATED
        elif instance.status == "booting":
            status = LiveStream.ServerStatus.CREATING

        private_ip = instance.ipv4[0] if instance.ipv4 else None
        public_ip = instance.ipv4[0] if instance.ipv4 else None

        return LiveStreamServer(
            ip_address=public_ip,
            id=server_id,
            cost_per_hour=None,
            status=status,
            provider=LiveStreamUsage.ServerProvider.LINODE,
            private_ip_address=private_ip,
        )


def create_linode_live_stream_server(name, asset):
    server = LinodeLiveStreamServer(name, asset)
    return server.create_server()


def delete_linode_server(server_id):
    client = LinodeClient(settings.TPSTREAMS_LINODE_API_TOKEN)
    try:
        instance = client.load(Instance, server_id)

        volumes = client.volumes()
        for volume in volumes:
            if volume.linode_id == instance.id:
                volume.delete()
                break

        instance.delete()
    except Exception as e:
        sentry_sdk.capture_exception(e)
        sentry_sdk.capture_message(
            f"Exception occurred while deleting Linode server {server_id}: {str(e)}",
            level="error",
        )
