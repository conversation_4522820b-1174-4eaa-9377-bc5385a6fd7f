import time
import uuid

from django.core.files.uploadedfile import InMemoryUploadedFile

from app.domain.cloudfront import invalidate_cache
from app.domain.subtitle import UploadError

from .cloud_storage import get_client


def upload_thumbnail(asset, input_file: type[InMemoryUploadedFile]):
    path = upload_thumbnail_to_cloud_storage(asset, input_file)
    store_thumbnail(asset, path)
    update_cover_thumbnail(asset, path)
    invalidate_cache(asset.organization, [f"/{path}"])


def upload_thumbnail_to_cloud_storage(asset, input_file):
    try:
        s3_client = get_client(asset.organization)
        destination_file_path = generate_thumbnail_upload_path(asset)
        s3_client.put_object(
            Bucket=asset.organization.bucket_name,
            Key=destination_file_path,
            Body=input_file.read(),
            ACL="public-read",
        )
    except Exception as error:
        raise UploadError(str(error))
    return destination_file_path


def generate_thumbnail_upload_path(asset):
    unique_filename = f"thumbnail_{uuid.uuid4().hex}_{int(time.time())}.png"
    path = f"transcoded/{asset.uuid}/thumbnails/{unique_filename}"
    return path


def store_thumbnail(asset, path):
    if not asset.video.thumbnails:
        asset.video.thumbnails = []
    add_thumbnail_to_list(asset, path)
    asset.video.save(update_fields=["thumbnails"])


def add_thumbnail_to_list(asset, thumbnail_path):
    if thumbnail_path not in asset.video.thumbnails:
        asset.video.thumbnails.insert(0, thumbnail_path)


def update_cover_thumbnail(asset, path):
    asset.video.preview_thumbnail_url = path
    asset.video.cover_thumbnail_url = path
    if path in asset.video.thumbnails:
        asset.video.thumbnails.remove(path)
    asset.video.thumbnails.insert(0, path)
    asset.video.save(
        update_fields=["preview_thumbnail_url", "cover_thumbnail_url", "thumbnails"]
    )


def remove_thumbnail_from_video(video, thumbnail_url):
    if video.cover_thumbnail_url == thumbnail_url:
        video.cover_thumbnail_url = None
    if thumbnail_url in video.thumbnails:
        video.thumbnails.remove(thumbnail_url)
    video.save(update_fields=["thumbnails", "cover_thumbnail_url"])
