from app.models.video_trim import OutputType, VideoOutput


def create_video_output(video, output_type, codec, url):
    duration_seconds = int(video.duration.total_seconds()) if video.duration else 0

    video_output, _ = VideoOutput.objects.update_or_create(
        organization=video.organization,
        video=video,
        output_type=output_type,
        codec=codec,
        defaults={
            "duration": duration_seconds,
            "url": url,
            "is_active": True,
        },
    )
    return video_output


def create_video_outputs(video):
    video_outputs = []
    output_url_configs = _build_output_url_paths(video)

    for codec, hls_url, dash_url in output_url_configs:
        if hls_url:
            video_output = create_video_output(video, OutputType.HLS, codec, hls_url)
            video_outputs.append(video_output)

        if dash_url:
            video_output = create_video_output(video, OutputType.DASH, codec, dash_url)
            video_outputs.append(video_output)

    return video_outputs


def _build_output_url_paths(video):
    folder = "transcoded_private" if video.is_aes_encrypted else "transcoded"

    output_url_configs = []

    hls_url_h264 = f"{folder}/{video.asset.uuid}/video.m3u8"
    dash_url_h264 = f"{folder}/{video.asset.uuid}/video.mpd"
    output_url_configs.append(("h264", hls_url_h264, dash_url_h264))

    if (
        hasattr(video, "video_codecs")
        and video.video_codecs
        and video.VideoCodec.H265 in video.video_codecs
    ):
        hls_url_h265 = f"{folder}/{video.asset.uuid}/video_h265.m3u8"
        dash_url_h265 = f"{folder}/{video.asset.uuid}/video_h265.mpd"
        output_url_configs.append(("h265", hls_url_h265, dash_url_h265))

    return output_url_configs
