import boto3
from django.conf import settings
from django.db import transaction
from django_multitenant.utils import set_current_tenant
from knox.models import get_token_model
from knox.settings import knox_settings

from app.api.v1.serializers import AuthTokenSerializer
from app.models import Membership, Organization, User


@transaction.atomic
def create_organization_for_testpress(data):
    user, user_created = User.objects.get_or_create(
        name=data["user_name"], email=data["email"]
    )
    storage_and_cdn_config = extract_storage_and_cdn_config(data)
    organization, org_created = Organization.objects.get_or_create(
        name=data.get("org_name"),
        created_by=user,
        defaults=storage_and_cdn_config,
    )
    if org_created:
        Organization.objects.filter(id=organization.id).update(**storage_and_cdn_config)
        organization.refresh_from_db()
    
    if user_created:
        user.set_password(data["password"])
        user.current_organization_uuid = organization.uuid
        user.save()

    Membership.objects.get_or_create(organization=organization, user=user)
    return organization, org_created


def extract_storage_and_cdn_config(data):
    return {
        "cdn_id": data.get("cdn_id"),
        "cdn_url": data.get("cdn_url"),
        "bucket_name": data.get("bucket_name"),
        "bucket_secret_token": data.get("bucket_secret_token"),
        "cloudfront_key_group_id": data.get("cloudfront_key_group_id"),
        "storage_region": data.get("storage_region"),
        "storage_access_key_id": data.get("storage_access_key_id"),
        "storage_secret_access_key": data.get("storage_secret_access_key"),
        "cdn_access_key_id": data.get("cdn_access_key_id"),
        "cdn_secret_access_key": data.get("cdn_secret_access_key"),
        "cdn_one_year_cache_policy_id": data.get(
            "cdn_one_year_cache_policy_id", settings.CDN_ONE_YEAR_CACHE_POLICY_ID
        ),
        "cdn_expire_in_3_seconds_cache_policy_id": data.get(
            "cdn_expire_in_3_seconds_cache_policy_id",
            settings.CDN_EXPIRE_IN_3_SECONDS_CACHE_POLICY_ID,
        ),
        "cdn_public_key_id": data.get("cdn_public_key_id"),
        "cdn_private_key": data.get("cdn_private_key"),
    }


def generate_auth_token(org, username, password):
    serializer = AuthTokenSerializer(
        data={"username": username, "password": password, "organization_id": org.uuid}
    )
    serializer.is_valid(raise_exception=True)
    user = serializer.validated_data["user"]

    token_prefix = knox_settings.TOKEN_PREFIX
    auth_token, raw_token = get_token_model().objects.create(
        user=user,
        expiry=knox_settings.TOKEN_TTL,
        organization=org,
        prefix=token_prefix,
    )

    return raw_token


def create_organization_for_npo(user_name, org_name, email, password):
    user = User.objects.create(name=user_name, email=email)
    user.set_password(password)
    organization = Organization.objects.create(
        name=org_name, created_by=user, **get_storage_and_cdn_configs()
    )
    Membership.objects.create(organization=organization, user=user)
    user.current_organization_uuid = organization.uuid
    user.save()
    return organization


def get_storage_and_cdn_configs():
    # These keys were obtained from our AWS account (<EMAIL>)
    ONE_YEAR_CACHE_POLICY_ID = "a20ff56b-2c69-4201-bfb1-3942f21b256e"
    EXPIRE_IN_3_SECONDS_CACHE_POLICY_ID = "240df16b-0938-401b-b09e-a5589b831003"
    CDN_PUBLIC_KEY_ID = "K3FQADABDT6LF3"

    return {
        "storage_region": "us-east-1",
        "storage_access_key_id": "********************",
        "storage_secret_access_key": "HBhhqJ7sR+akxYHyB+fYXNGZUpq6tIMBvdvx6RIf",
        "cdn_access_key_id": "********************",
        "cdn_secret_access_key": "HBhhqJ7sR+akxYHyB+fYXNGZUpq6tIMBvdvx6RIf",
        "cdn_one_year_cache_policy_id": ONE_YEAR_CACHE_POLICY_ID,
        "cdn_expire_in_3_seconds_cache_policy_id": EXPIRE_IN_3_SECONDS_CACHE_POLICY_ID,
        "cdn_public_key_id": CDN_PUBLIC_KEY_ID,
        "storage_vendor": Organization.StorageProvider.AWS,
        "cloudfront_key_group_id": "8afe3960-145e-44bc-8c24-f258f7cd4caa",
        "cdn_private_key": settings.NPO_CLOUDFRONT_PRIVATE_KEY,
    }


def get_boto_client(organization):
    endpoint_url = f"https://s3.{organization.storage_region}.wasabisys.com"
    if organization.get_storage_vendor_display() == "Aws":
        endpoint_url = f"https://s3.{organization.storage_region}.amazonaws.com"

    return boto3.client(
        "s3",
        endpoint_url=endpoint_url,
        aws_access_key_id=organization.storage_access_key_id,
        aws_secret_access_key=organization.storage_secret_access_key,
        region_name=organization.storage_region,
    )
