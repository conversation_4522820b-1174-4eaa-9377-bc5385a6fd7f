from dataclasses import dataclass

import requests
from django_multitenant.utils import set_current_tenant

from app.models import Organization
from app.models.videoimport import ImportedFolder, ImportedVideo
from app.tasks.teachable import MigrateTeachableVideoTask


@dataclass
class TeachableVideo:
    id: int
    name: str
    url: str
    duration: int
    thumbnail_url: str
    status: str
    data: dict

    @property
    def is_ready(self):
        return self.status == "READY"

    @property
    def m3u8_url(self):
        return self.url


@dataclass
class TeachableLecture:
    id: int
    name: str
    position: int
    is_published: bool
    videos: list
    data: dict


class TeachableAPI:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://developers.teachable.com/v1"
        self.headers = {"accept": "application/json", "apiKey": api_key}

    def get_courses(self):
        try:
            response = requests.get(f"{self.base_url}/courses", headers=self.headers)
            response.raise_for_status()
            return response.json().get("courses", [])
        except Exception:
            return []

    def get_course(self, course_id):
        try:
            response = requests.get(
                f"{self.base_url}/courses/{course_id}", headers=self.headers
            )
            response.raise_for_status()
            return response.json()["course"], True
        except Exception:
            return None, False

    def get_lectures(self, course_id):
        lectures = []
        try:
            response = requests.get(
                f"{self.base_url}/courses/{course_id}", headers=self.headers
            )
            response.raise_for_status()
            course_data = response.json()["course"]

            for section in course_data["lecture_sections"]:
                for lecture_data in section.get("lectures", []):
                    if lecture := self._retrieve_lecture(course_id, lecture_data):
                        lectures.append(lecture)

            return lectures
        except Exception:
            return []

    def _retrieve_lecture(self, course_id, lecture_data):
        try:
            response = requests.get(
                f"{self.base_url}/courses/{course_id}/lectures/{lecture_data['id']}",
                headers=self.headers,
            )
            response.raise_for_status()
            lecture_details = response.json()["lecture"]

            lecture = TeachableLecture(
                id=lecture_details["id"],
                name=lecture_details.get(
                    "name", f"Lecture {lecture_details['position']}"
                ),
                position=lecture_details["position"],
                is_published=lecture_details["is_published"],
                videos=[],
                data=lecture_details,
            )

            for attachment in lecture_details.get("attachments", []):
                if attachment["kind"] == "video":
                    video = self.get_video(
                        course_id, lecture_details["id"], attachment["id"]
                    )
                    if video and video.is_ready:
                        video.name = lecture.name
                        lecture.videos.append(video)

            return lecture if lecture.videos else None
        except Exception:
            return None

    def get_video(self, course_id, lecture_id, video_id):
        try:
            response = requests.get(
                f"{self.base_url}/courses/{course_id}/lectures/{lecture_id}/videos/{video_id}",
                headers=self.headers,
            )
            response.raise_for_status()
            video_data = response.json()["video"]
            video_asset = video_data.get("video_asset") or {}

            if not video_asset.get("url") or video_data.get("status") != "READY":
                return None

            return TeachableVideo(
                id=video_data["id"],
                name="",
                url=video_asset["url"],
                duration=video_data.get("media_duration", 0),
                thumbnail_url=video_data.get("url_thumbnail", ""),
                status=video_data.get("status", ""),
                data=video_data,
            )
        except Exception:
            return None


class TeachableMigrator:
    def __init__(
        self, api_key, org_id, content_protection_type=None, transcoding_queue_name=None
    ):
        self.api_key = api_key
        self.teachable = TeachableAPI(api_key)
        self.organization = Organization.objects.get(uuid=org_id)
        set_current_tenant(self.organization)
        self.content_protection_type = content_protection_type
        self.transcoding_queue_name = transcoding_queue_name

    def migrate_course(self, course_id=None):
        if course_id:
            print(f"\nFetching Course with ID: {course_id}")
            course_data, success = self.teachable.get_course(course_id)
            if success and course_data:
                print(f"Found Course: {course_data['name']}")
                course_folder = self._create_course_folder(course_data)
                self._migrate_lectures_to_folder(course_data, course_folder)
        else:
            print("\nFetching all courses...")
            courses = self.teachable.get_courses()
            for course in courses:
                print(f"\nProcessing Course: {course['name']} (ID: {course['id']})")
                course_folder = self._create_course_folder(course)
                self._migrate_lectures_to_folder(course, course_folder)

    def _create_course_folder(self, course_data):
        return ImportedFolder.objects.create(
            name=course_data["name"],
            organization=self.organization,
            details=course_data,
        )

    def _migrate_lectures_to_folder(self, course_data, course_folder):
        for lecture in self.teachable.get_lectures(course_data["id"]):
            print(f"\nProcessing Lecture: {lecture.name} (ID: {lecture.id})")
            for video in lecture.videos:
                print(f"Found Video: {video.name} (ID: {video.id})")
                if not self._is_video_already_migrated(
                    video, course_data["id"], lecture.id
                ):
                    print("Video is new, proceeding with migration...")
                    imported_video = self._create_imported_video(
                        course_data, lecture, video, course_folder
                    )
                    self._migrate_teachable_video(imported_video)
                else:
                    print("Video already exists, skipping...")

    def _create_imported_video(self, course_data, lecture, video, folder):
        return ImportedVideo.objects.create(
            folder=folder,
            name=lecture.name,
            uri=video.m3u8_url,
            organization=self.organization,
            details={
                **video.data,
                "thumbnail_url": video.thumbnail_url,
                "duration": video.duration,
                "course_id": course_data["id"],
                "course_name": course_data["name"],
                "lecture_id": lecture.id,
                "video_id": video.id,
                "title": lecture.name,
            },
            source=ImportedVideo.Source.TEACHABLE,
        )

    def _migrate_teachable_video(self, imported_video):
        print(f"Triggering migration task for video: {imported_video.name}")
        MigrateTeachableVideoTask.apply_async(
            kwargs={
                "organization_uuid": self.organization.uuid,
                "access_token": self.api_key,
                "imported_video_id": imported_video.id,
                "content_protection_type": self.content_protection_type,
                "transcoding_queue_name": self.transcoding_queue_name,
            },
            queue="migration_queue",
        )

    def _is_video_already_migrated(self, video, course_id, lecture_id):
        from app.models import ImportedVideo

        return ImportedVideo.objects.filter(
            source=ImportedVideo.Source.TEACHABLE,
            details__video_id=video.id,
            details__course_id=course_id,
            details__lecture_id=lecture_id,
            video__isnull=False,
            video__asset__isnull=False,
        ).exists()
