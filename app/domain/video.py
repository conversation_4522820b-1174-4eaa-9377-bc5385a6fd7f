import logging
from datetime import timedelta

import requests
from django.utils import timezone
from django.utils.timezone import now
from django_multitenant.utils import get_current_tenant, set_current_tenant

from app.domain import cloud_storage, cloudfront
from app.domain.aes import create_aes_encryption_key_if_not_present
from app.domain.cloud_storage import generate_presigned_url
from app.domain.preview_thumbnail import create_preview_thumbnail_track
from app.utils.datetime import convert_iso8601_to_datetime

logger = logging.getLogger(__name__)

ONE_HOUR = 1 * 60 * 60


class LumberjackDataParser:
    def __init__(self, data):
        self.data = data

    def __dict__(self):
        return {
            "transcoding_status": str(self.transcoding_status),
            "duration": str(self.duration),
            "transcoding_start_time": str(self.transcoding_start_time),
            "transcoding_end_time": str(self.transcoding_end_time),
        }

    @property
    def transcoding_status(self):
        from app.models.video import Video

        status = self.data.get("status", "").lower()
        if status == "queued":
            return Video.Status.QUEUED
        elif status == "not started":
            return Video.Status.NOT_STARTED
        elif status == "processing":
            return Video.Status.TRANSCODING
        elif status == "completed":
            return Video.Status.COMPLETED
        return Video.Status.ERROR

    @property
    def duration(self):
        if self.data.get("video_duration"):
            return timedelta(seconds=self.data.get("video_duration"))

    @property
    def transcoding_start_time(self):
        return convert_iso8601_to_datetime(self.data.get("start_time"))

    @property
    def transcoding_end_time(self):
        return convert_iso8601_to_datetime(self.data.get("end_time"))

    @property
    def thumbnail_urls(self):
        if self.data.get("thumbnails"):
            return self.data.get("thumbnails")

    @property
    def cover_thumbnail_url(self):
        if self.data.get("thumbnails"):
            return self.data.get("thumbnails")[0]

    @property
    def preview_thumbnail(self):
        if self.data.get("preview_thumbnail"):
            return self.data.get("preview_thumbnail")

    @property
    def outputs(self):
        from app.models.video import Video

        if self.transcoding_status == Video.Status.COMPLETED:
            return self.data.get("outputs", [])
        return []

    def playlists_info(self, video):
        info = []
        for output in self.outputs:
            if output.get("dash_bytes"):
                info.append(self._get_output_data(output, video, format_type="dash"))

            if output.get("hls_bytes"):
                info.append(self._get_output_data(output, video, format_type="hls"))
        return info

    def _get_output_data(self, output, video, format_type):
        base_file_name, base_file_path = self._get_output_base_file_name_and_path(
            video, output, format_type
        )
        size = output.get(f"{format_type}_bytes")
        width = output.get("width")
        height = output.get("height")
        return {
            "name": base_file_name,
            "size": size,
            "path": base_file_path,
            "width": width,
            "height": height,
        }

    def _get_output_base_file_name_and_path(self, video, output, format_type):
        base_file_name = output.get("name")
        base_file_path = f"transcoded/{video.asset.uuid}/{base_file_name}"

        if format_type == "dash":
            base_file_name = f"{base_file_name}_dash"
            base_file_path = f"{base_file_path}_dash/"
        elif video.enable_drm and format_type == "hls":
            base_file_name = f"{base_file_name}_hls"
            base_file_path = f"{base_file_path}_hls/"

        return (base_file_name, base_file_path)


def get_source_video_download_url(video_input):
    if video_input is None or not video_input.is_video_ready_to_download():
        return ""
    if video_input.organization.uuid == "fyd3gg":
        return cloud_storage.generate_presigned_url(
            video_input.organization,
            video_input.url,
            download_as="video.mp4",
            expires_in=ONE_HOUR,
        )

    if video_input.organization.uuid == "568qxh":
        return generate_presigned_url(
            video_input.organization,
            video_input.url,
            expires_in=ONE_HOUR,
            download_as=video_input.get_download_filename(),
        )

    return cloudfront.generate_presigned_url(
        video_input.organization,
        video_input.url,
        download=True,
        download_as=video_input.get_download_filename(),
        expires_in=ONE_HOUR,
    )


def start_transcoding(video, extra_settings=None, transcoding_queue_name=None):
    from app.domain.transcoder import get_transcoder
    from app.models import Video

    if video.status not in [
        Video.Status.UPLOADING,
        Video.Status.UPLOADED,
        Video.Status.NOT_STARTED,
    ]:
        return

    if not video.transmux_only and len(video.resolutions) == 0:
        return

    if video.is_aes_encrypted:
        create_aes_encryption_key_if_not_present(video.asset)

    try:
        response = get_transcoder(video.asset).start(
            extra_settings=extra_settings, transcoding_queue_name=transcoding_queue_name
        )
        video.status = Video.Status.QUEUED
        video.job_id = response["id"]
        video.transcoding_submission_time = timezone.now()
    except requests.exceptions.HTTPError:
        video.status = Video.Status.ERROR
    video.save(update_fields=["status", "job_id", "transcoding_submission_time"])


def save_transcoding_info_to_asset(request, asset):
    from app.domain.live_stream import terminate_live_server_on_transcoding_completion

    transcoding_info = LumberjackDataParser(request.data)
    asset.video.duration = transcoding_info.duration
    asset.video.transcoding_start_time = transcoding_info.transcoding_start_time
    asset.video.transcoding_end_time = transcoding_info.transcoding_end_time
    asset.video.status = transcoding_info.transcoding_status
    if (
        transcoding_info.preview_thumbnail
        and transcoding_info.preview_thumbnail["is_sprite_image_generated"]
    ):
        save_preview_thumbnail_details(transcoding_info.preview_thumbnail, asset)
    if not asset.video.thumbnails:
        asset.video.thumbnails = transcoding_info.thumbnail_urls
    if not asset.video.preview_thumbnail_url:
        asset.video.preview_thumbnail_url = transcoding_info.cover_thumbnail_url
    if not asset.video.cover_thumbnail_url:
        asset.video.cover_thumbnail_url = transcoding_info.cover_thumbnail_url
    if transcoding_info.outputs:
        store_video_playlist_info(
            asset.video, transcoding_info.playlists_info(asset.video)
        )
    terminate_live_server_on_transcoding_completion(asset)
    asset.video.save(
        update_fields=[
            "status",
            "duration",
            "transcoding_start_time",
            "transcoding_end_time",
            "thumbnails",
            "preview_thumbnail_url",
            "cover_thumbnail_url",
        ]
    )


def save_preview_thumbnail_details(preview_thumbnail, asset):
    create_preview_thumbnail_track(
        asset=asset,
        url=preview_thumbnail["url"],
        columns=preview_thumbnail["columns"],
        rows=preview_thumbnail["rows"],
        interval=preview_thumbnail["interval"],
        width=preview_thumbnail["width"],
        height=preview_thumbnail["height"],
    )


def get_all_asset_data(folder_id=None):
    from app.models import Asset, Video

    if not get_current_tenant():
        raise Exception("To retrieve the data, set up a tenant.")

    if folder_id:
        asset_data = Asset.objects.filter(
            parent__uuid=folder_id,
            video__isnull=False,
            video__status=Video.Status.COMPLETED,
        ).values_list("title", "uuid", "bytes", "video__duration", named=True)
        return asset_data

    asset_data = Asset.objects.filter(
        video__isnull=False, video__status=Video.Status.COMPLETED
    ).values_list("title", "uuid", "bytes", "video__duration", named=True)
    return asset_data


def transcode_video_with_backup(video, transcoding_queue_name=None):
    from app.domain.transcoder import get_transcoder
    from app.models import Video

    if video.status not in [Video.Status.COMPLETED, Video.Status.ERROR]:
        return
    if not video.meta_data:
        video.meta_data = {}
    try:
        response = get_transcoder(video.asset).start(
            transcoding_queue_name=transcoding_queue_name,
            preserve_existing_transcodings=True,
        )
        video.meta_data["transcoding_status"] = Video.Status.QUEUED
        video.meta_data["job_id"] = response["id"]
        video.meta_data["transcoding_submission_time"] = timezone.now().isoformat()
    except requests.exceptions.HTTPError:
        video.meta_data["transcoding_status"] = Video.Status.ERROR
    video.save(update_fields=["meta_data"])


def store_migrated_path_to_asset(asset):
    folder = "transcoded_private" if asset.video.is_aes_encrypted else "transcoded"
    playback_url = f"{folder}/{asset.uuid}/new/video.m3u8"
    dash_url = f"{folder}/{asset.uuid}/new/video.mpd"
    asset.video.playback_url = playback_url
    asset.video.dash_url = dash_url
    asset.video.set_output_url("h264", playback_url, dash_url)
    asset.video.save()


def get_migrated_playback_url(video):
    return f"{video.organization.cdn_url}transcoded/{video.asset.uuid}/new/video.m3u8"


def get_migrated_dash_url(video):
    return f"{video.organization.cdn_url}transcoded/{video.asset.uuid}/new/video.mpd"


def delete_old_transcoded_files(asset):
    from app.tasks import DeleteMigratedVideoFilesTask

    DeleteMigratedVideoFilesTask.apply_async(
        kwargs={
            "asset_id": asset.uuid,  # type: ignore
            "organization_uuid": asset.organization.uuid,  # type: ignore
        },
        queue="migration_queue",
    )


def schedule_asset_storage_calculation_task(asset):
    from app.models import Video
    from app.tasks import UpdateAssetSizeTask

    if asset.video.status == Video.Status.COMPLETED:
        UpdateAssetSizeTask.schedule(
            run_at=now() + timedelta(hours=2),
            kwargs={
                "asset_id": asset.uuid,  # type: ignore
                "organization_uuid": asset.organization.uuid,  # type: ignore
            },
        )


def is_live_video_uploaded(live_stream):
    from app.domain.live_stream import get_live_source_video_url
    from app.utils.browser import is_valid_url

    url = get_live_source_video_url(live_stream)
    return is_valid_url(url)


def store_video_playlist_info(video, playlists_info=None):
    from app.domain.track import create_playlist_track

    if not video or not video.resolutions:
        return
    if not playlists_info:
        playlists_info = get_transcoded_files_info(video)
    for playlist in playlists_info:
        playlist, created = create_playlist_track(
            video.asset,
            name=playlist["name"],
            path=playlist["path"],
            size=playlist["size"],
            width=playlist["width"],
            height=playlist["height"],
        )


def get_transcoded_files_info(video):
    from app.domain.cloud_storage import get_size
    from app.domain.transcoder import VBR_RESOLUTION_MAP

    transcoded_files_info = []

    for playlist in video.resolutions:
        dimension_info = VBR_RESOLUTION_MAP.get(playlist)
        file_paths = get_playlist_paths(video, playlist)
        for file_path in file_paths:
            # get_size may take time to execute. For large files, consider running this in a background task.
            size = get_size(video.asset.organization, file_path)
            file_info = {
                "name": file_path.split("/")[-1],
                "path": file_path,
                "size": size,
                "width": dimension_info.width,
                "height": dimension_info.height,
            }
            transcoded_files_info.append(file_info)

    return transcoded_files_info


def get_playlist_paths(video, playlist):
    from app.models import Video

    transcoded_path = f"transcoded/{video.asset.uuid}"
    playlist_name = video.Resolutions(playlist).label

    if video.content_protection_type == Video.ContentProtectionType.DISABLED:
        return [f"{transcoded_path}/{playlist_name}"]
    elif video.content_protection_type == Video.ContentProtectionType.DRM:
        return [
            f"{transcoded_path}/{playlist_name}_hls",
            f"{transcoded_path}/{playlist_name}_dash",
        ]
    return []


def stop_transcoding(asset):
    from app.domain.transcoder import get_transcoder

    video = getattr(asset, "video", None)
    if video and video.get_status_display() in ["Queued", "Transcoding"]:
        get_transcoder(video).stop()


def create_video_input(video):
    from app.models.video import VideoInput

    VideoInput.objects.create(
        url=f"private/{video.asset.uuid}/video.mp4",
        video=video,
        organization=video.organization,
    )


def create_video_with_outputs(asset, organization, video_data):
    from app.domain.video_output import create_video_outputs
    from app.models.video import Video

    video = Video.objects.create(asset=asset, organization=organization, **video_data)

    video_outputs = create_video_outputs(video)
    video.update_output_urls(video_outputs)
    video.update_primary_playback_urls(video_outputs)

    video.save(update_fields=["playback_url", "dash_url", "output_urls"])

    return video


def check_cdn_status(asset):
    url = f"{asset.organization.cdn_url}transcoded/{asset.uuid}/video.m3u8"
    if hasattr(asset, "video"):
        response = requests.head(url)
        return {"result": response.status_code == 200, "url": url}
    url = (
        f"{asset.live_stream.organization.cdn_url}live/{asset.live_stream.organization.uuid}/"
        f"{asset.live_stream.asset.uuid}/video.m3u8"
    )
    if not asset.live_stream.get_server_status_display() == "Destroyed":
        response = requests.head(url)
        return {"result": response.status_code == 200, "url": url}
    return {"result": False, "url": url}


class VideoCloner:
    def __init__(self, source_org, destination_org):
        self.source_org = source_org
        self.destination_org = destination_org

    def clone(self, asset_uuid, new_parent=None):
        try:
            set_current_tenant(self.source_org)
            source_asset = self._get_source_asset(asset_uuid)
            if not source_asset:
                return None

            set_current_tenant(self.destination_org)
            new_asset = self._create_video_asset(source_asset, new_parent)
            new_video = self._clone_video(source_asset.video, new_asset)
            self._clone_encryption_key(source_asset, new_asset, new_video)

            set_current_tenant(self.source_org)
            return new_asset

        except Exception as e:
            logger.error(f"Failed to clone video asset {asset_uuid}: {str(e)}")
            return None

    def _get_source_asset(self, asset_uuid):
        from app.models.asset import Asset

        try:
            return Asset.objects.get(uuid=asset_uuid, type=Asset.Type.VIDEO)
        except Asset.DoesNotExist:
            logger.error(f"Video asset with UUID {asset_uuid} does not exist")
            return None

    def _create_video_asset(self, source_asset, new_parent):
        from app.models.asset import Asset

        return Asset.objects.create(
            organization=self.destination_org,
            title=source_asset.title,
            type=Asset.Type.VIDEO,
            parent=new_parent,
            bytes=source_asset.bytes,
            disable_domain_restriction=source_asset.disable_domain_restriction,
        )

    def _clone_video(self, source_video, new_asset):
        from app.models.video import Video

        new_video = Video.objects.create(
            organization=self.destination_org,
            asset=new_asset,
            status=source_video.status,
            progress=source_video.progress,
            thumbnails=source_video.thumbnails,
            job_id=source_video.job_id,
            transmux_only=source_video.transmux_only,
            transcoding_submission_time=source_video.transcoding_submission_time,
            transcoding_start_time=source_video.transcoding_start_time,
            transcoding_end_time=source_video.transcoding_end_time,
            meta_data=source_video.meta_data,
            duration=source_video.duration,
            generate_subtitle=source_video.generate_subtitle,
            use_global_player_preferences=source_video.use_global_player_preferences,
            format=source_video.format,
            resolutions=source_video.resolutions,
            video_codec=source_video.video_codec,
            audio_codec=source_video.audio_codec,
            video_codecs=source_video.video_codecs,
            enable_drm=source_video.enable_drm,
            thumbnail_duration=source_video.thumbnail_duration,
            thumbnail_format=source_video.thumbnail_format,
            content_protection_type=source_video.content_protection_type,
        )

        new_video.playback_url = source_video.playback_url
        new_video.dash_url = source_video.dash_url
        new_video.preview_thumbnail_url = source_video.preview_thumbnail_url
        new_video.cover_thumbnail_url = source_video.cover_thumbnail_url
        new_video.output_urls = source_video.output_urls
        new_video.save(
            update_fields=[
                "playback_url",
                "dash_url",
                "preview_thumbnail_url",
                "cover_thumbnail_url",
                "output_urls",
            ]
        )

        self._clone_video_inputs(source_video, new_video)
        self._clone_video_tracks(source_video, new_video)
        return new_video

    def _clone_video_inputs(self, source_video, new_video):
        from app.models.video import VideoInput

        set_current_tenant(self.source_org)
        inputs = list(source_video.inputs.all())

        set_current_tenant(self.destination_org)
        for video_input in inputs:
            VideoInput.objects.create(
                organization=self.destination_org, video=new_video, url=video_input.url
            )

    def _clone_video_tracks(self, source_video, new_video):
        from app.models.track import Track

        set_current_tenant(self.source_org)
        tracks = list(source_video.tracks.all())

        set_current_tenant(self.destination_org)
        for track in tracks:
            try:
                new_track = Track.objects.create(
                    organization=self.destination_org,
                    type=track.type,
                    video=new_video,
                    language=track.language,
                    url=track.url,
                    is_active=track.is_active,
                    name=track.name,
                    width=track.width,
                    height=track.height,
                    bytes=track.bytes,
                    duration=track.duration,
                    subtitle_type=track.subtitle_type,
                    subtitle_data=track.subtitle_data,
                )
                self._clone_preview_thumbnail(track, new_track)
                self._clone_playlists(track, new_track)
            except Exception as e:
                logger.error(f"Failed to clone track {track.id}: {str(e)}")

    def _clone_preview_thumbnail(self, source_track, new_track):
        from app.models.preview_thumbnail import PreviewThumbnail

        try:
            set_current_tenant(self.source_org)
            if (
                hasattr(source_track, "preview_thumbnail")
                and source_track.preview_thumbnail
            ):
                preview_thumbnail = source_track.preview_thumbnail
                set_current_tenant(self.destination_org)
                new_preview_thumbnail = PreviewThumbnail.objects.create(
                    organization=self.destination_org,
                    width=preview_thumbnail.width,
                    height=preview_thumbnail.height,
                    rows=preview_thumbnail.rows,
                    columns=preview_thumbnail.columns,
                    interval=preview_thumbnail.interval,
                    url=preview_thumbnail.url,
                )
                new_track.preview_thumbnail = new_preview_thumbnail
                new_track.save(update_fields=["preview_thumbnail"])
        except Exception as e:
            logger.error(
                f"Failed to clone preview thumbnail for track {source_track.id}: {str(e)}"
            )

    def _clone_playlists(self, source_track, new_track):
        from app.models.playlist import Playlist

        try:
            set_current_tenant(self.source_org)
            playlists = list(source_track.playlists.all())

            set_current_tenant(self.destination_org)
            for playlist in playlists:
                Playlist.objects.create(
                    organization=self.destination_org,
                    name=playlist.name,
                    track=new_track,
                    bytes=playlist.bytes,
                    width=playlist.width,
                    height=playlist.height,
                    path=playlist.path,
                )
        except Exception as e:
            logger.error(
                f"Failed to clone playlists for track {source_track.id}: {str(e)}"
            )

    def _clone_encryption_key(self, source_asset, new_asset, new_video):
        from app.models.drm import EncryptionKey

        try:
            set_current_tenant(self.source_org)
            if hasattr(source_asset, "encryption_key"):
                source_key = source_asset.encryption_key
                set_current_tenant(self.destination_org)

                EncryptionKey.objects.create(
                    organization=self.destination_org,
                    content_id=new_video.uuid.hex,
                    asset=new_asset,
                    aes_encryption_key=source_key.aes_encryption_key,
                    widevine_encryption_keys=source_key.widevine_encryption_keys,
                    fairplay_encryption_key_data=source_key.fairplay_encryption_key_data,
                )
        except Exception as e:
            logger.error(
                f"Failed to clone encryption key for asset {source_asset.uuid}: {str(e)}"
            )
