import tempfile

import sentry_sdk
import webvtt
from django.conf import settings
from django.db.models import Sum
from django.template.loader import render_to_string
from django.utils import timezone

from app.domain.aws import delete_aws_server
from app.models import Track
from app.models.usages import AssetUsage
from app.tasks.send_mail import send_email_task
from app.utils.datetime import (
    convert_iso8601_to_datetime,
    get_month_start_date,
    get_time_difference,
)
from app.utils.wasabi import get_wasabi_config, upload_from_url_to_wasabi

from .cloud_storage import delete_file, get_client, get_size, has_object

EMAIL_NOTIFICATION_EXCLUDED_ORGS = ["a7dseu"]


class SubtitleDataParser:
    def __init__(self, track, subtitle_status, server_id=None, server_details=None):
        self.track = track
        self.server_id = server_id
        self.server_details = server_details
        self.subtitle_status = subtitle_status
        self.current_time = timezone.now().isoformat()

    @property
    def subtitle_data(self):
        data = self.track.subtitle_data or {}
        if self.server_id:
            data["server_id"] = self.server_id
        if self.server_details:
            data.update(self.server_details)
        if self.subtitle_status == "SERVER_DELETED":
            data["subtitle_generation_cost_dollar"] = self.get_subtitle_generation_cost
        data[self.subtitle_status] = self.current_time
        return data

    @property
    def uploaded_path(self):
        return (
            f"transcoded/{self.track.video.asset.uuid}/subtitles/en-autogenerated.vtt"
        )

    @property
    def url(self):
        if self.subtitle_status == "UPLOADED" and has_object(
            self.track.organization, self.uploaded_path
        ):
            return self.uploaded_path

    @property
    def bytes(self):
        if self.url:
            return get_size(self.track.organization, self.uploaded_path)

    @property
    def get_subtitle_generation_cost(self):
        if self.server_running_time_minutes and self.server_cost_per_minute_dollar:
            return round(
                float(self.server_running_time_minutes)
                * float(self.server_cost_per_minute_dollar),
                5,
            )

    @property
    def server_running_time_minutes(self):
        start_time = convert_iso8601_to_datetime(
            self.track.subtitle_data["SERVER_STARTED"]
        )
        end_time = convert_iso8601_to_datetime(self.current_time)
        return get_time_difference(start_time, end_time, unit="minutes")

    @property
    def server_cost_per_minute_dollar(self):
        from app.domain.aws import AWSSubtitleServer

        serever_type_cost = self.track.subtitle_data["server_type_cost_per_hour_dollar"]
        volume_cost = self.track.subtitle_data["volume_cost_per_gb_per_hour_dollar"]
        volume_size = self.track.subtitle_data["volume_size"]
        server_cost_estimate = (
            AWSSubtitleServer.get_server_cost_estimate_per_minute_in_dollar(
                serever_type_cost, volume_cost, volume_size
            )
        )
        return server_cost_estimate


def convert_srt_to_vtt(uploaded_srt_file):
    with tempfile.NamedTemporaryFile(suffix=".srt") as temp_srt_file:
        for chunk in uploaded_srt_file.chunks():
            temp_srt_file.write(chunk)
        temp_srt_file.seek(0)
        vtt_file = tempfile.NamedTemporaryFile(suffix=".vtt")
        webvtt.from_srt(temp_srt_file.name).save(output=vtt_file.name)
        return vtt_file


def upload_subtitle(asset, name, language, input_file):
    input_file = (
        convert_srt_to_vtt(input_file)
        if input_file.name.endswith(".srt")
        else input_file
    )
    path = upload_subtitle_to_cloud_storage(asset, language, input_file)
    size = get_size(asset.organization, path)
    create_subtitle_track(asset, name, language, path, size)


def upload_subtitle_from_url(asset, name, language, input_url):
    try:
        destination_file_path = f"transcoded/{asset.uuid}/{language}.vtt"
        config = get_wasabi_config(asset.organization)
        upload_from_url_to_wasabi(
            input_url,
            f"{asset.organization.bucket_name}/" + destination_file_path,
            config,
            make_public=True,
        )
        size = get_size(asset.organization, destination_file_path)
        create_subtitle_track(asset, name, language, destination_file_path, size)
        return destination_file_path
    except Exception as error:
        raise UploadError(str(error))


def upload_subtitle_to_cloud_storage(asset, language, input_file):
    try:
        s3_client = get_client(asset.organization)
        destination_file_path = f"transcoded/{asset.uuid}/{language}.vtt"
        s3_client.put_object(
            Bucket=asset.organization.bucket_name,
            Key=destination_file_path,
            Body=input_file.read(),
            ACL="public-read",
        )

    except Exception as error:
        raise UploadError(str(error))

    return destination_file_path


class UploadError(Exception):
    pass


def create_subtitle_track(
    asset,
    name,
    language,
    path=None,
    size=None,
    subtitle_type=Track.SubtitleType.UPLOADED,
    subtitle_data=None,
):
    track, created = Track.objects.update_or_create(
        video=asset.video,
        url=path,
        language=language,
        name=name,
        organization=asset.organization,
        type=Track.Type.SUBTITLE,
        bytes=size,
        subtitle_type=subtitle_type,
        subtitle_data=subtitle_data,
    )
    return track, created


def generate_subtitle(asset):
    from app.tasks.subtitle import GenerateSubtitleTask

    GenerateSubtitleTask.apply_async(
        kwargs={
            "asset_id": asset.uuid,
            "organization_uuid": asset.organization.uuid,
        },
        queue="subtitle_generation",
    )


def store_subtitle_generation_details(
    track, subtitle_status, server_id=None, server_details=None
):
    data_parser = SubtitleDataParser(track, subtitle_status, server_id, server_details)
    track.subtitle_data = data_parser.subtitle_data
    if not track.url:
        track.url = data_parser.url
        track.bytes = data_parser.bytes
    track.save(update_fields=["url", "bytes", "subtitle_data"])


def delete_subtitle_server_if_needed(track, subtitle_status):
    if not track.subtitle_data.get("server_id", None):
        return
    if subtitle_status in [
        "UPLOADED",
        "DEPENDENCY_INSTALL_FAILED",
        "AUDIO_EXTRACTION_FAILED",
        "SUBTITLE_GENERATION_FAILED",
        "UPLOAD_FAILED",
        "AUTO_TERMINATED",
    ]:
        delete_aws_server(track.subtitle_data["server_id"], region_name="us-east-1")
        delete_file(track.organization, f"private/{track.video.asset.uuid}/audio.mp3")
        store_subtitle_generation_details(track, subtitle_status="SERVER_DELETED")
        track.video.asset.notify_webhook()
        send_subtitle_generation_status_email(track, subtitle_status)


def send_subtitle_generation_status_email(track, subtitle_status):
    subject = ""
    message_text = ""
    cc_emails = []
    if subtitle_status == "UPLOADED":
        subject = f"Subtitle Generation Successful for: {track.video.asset.uuid}"
        message_text = get_email_message_based_on_subtitle_status(track)

    elif subtitle_status in [
        "DEPENDENCY_INSTALL_FAILED",
        "AUDIO_EXTRACTION_FAILED",
        "SUBTITLE_GENERATION_FAILED",
        "UPLOAD_FAILED",
        "AUTO_TERMINATED",
    ]:
        subject = f"Subtitle Generation Failed for: {track.video.asset.uuid}"
        message_text = get_email_message_based_on_subtitle_status(
            track, is_success=False
        )
        cc_emails = settings.SUPPORT_EMAILS + settings.DEVELOPER_EMAILS

    if message_text:
        to_emails = (
            [f"{track.video.asset.created_by}"]
            if track.organization.uuid not in EMAIL_NOTIFICATION_EXCLUDED_ORGS
            else []
        )
        send_email_task.apply_async(
            kwargs={
                "subject": subject,
                "message_text": message_text,
                "from_email": settings.DEFAULT_FROM_EMAIL,
                "to_emails": to_emails,
                "cc_emails": cc_emails,
            }
        )


def get_email_message_based_on_subtitle_status(track, is_success=True):
    context = {
        "asset": track.video.asset,
        "video_link": f"{settings.SITE_URL}/assets/{track.video.asset.uuid}",
        "support_email": settings.DEFAULT_FROM_EMAIL,
    }
    email_body_text = ""
    if is_success:
        email_body_text = render_to_string(
            "account/email/subtitle_generation_success_message.txt", context
        )
    else:
        email_body_text = render_to_string(
            "account/email/subtitle_generation_failure_message.txt", context
        )
    return email_body_text


def update_subtitles_generation_minutes(organization, date):
    try:
        update_subtitles_generation_minutes_for_day(organization, date)
        update_subtitles_generation_minutes_for_month(organization, date)
    except Exception as error:
        sentry_sdk.capture_exception(error)


def update_subtitles_generation_minutes_for_day(organization, date):
    subtitle_generation_minutes = get_subtitles_generation_minutes_for_day(
        organization, date
    )
    save_subtitles_generation_minutes(organization, date, subtitle_generation_minutes)


def update_subtitles_generation_minutes_for_month(organization, date):
    subtitle_generation_minutes = get_subtitles_generation_minutes_for_month(
        organization, date
    )
    save_subtitles_generation_minutes(
        organization,
        get_month_start_date(date),
        subtitle_generation_minutes,
        time_frame=AssetUsage.TimeFrames.MONTHLY,
    )


def get_subtitles_generation_minutes_for_day(organization, date):
    total_subtitle_generated = Track.objects.filter(
        organization=organization,
        created__date=date,
        subtitle_type=Track.SubtitleType.AUTO_GENERATED,
        url__isnull=False,
        bytes__isnull=False,
    ).aggregate(total_video_duration=Sum("video__duration"))["total_video_duration"]
    total_subtitle_generated_minutes = round(
        (total_subtitle_generated.total_seconds() if total_subtitle_generated else 0)
        / 60
    )
    return total_subtitle_generated_minutes


def get_subtitles_generation_minutes_for_month(organization, date):
    total_subtitle_generated = Track.objects.filter(
        organization=organization,
        created__year=date.year,
        created__month=date.month,
        subtitle_type=Track.SubtitleType.AUTO_GENERATED,
        url__isnull=False,
        bytes__isnull=False,
    ).aggregate(total_video_duration=Sum("video__duration"))["total_video_duration"]
    total_subtitle_generated_minutes = round(
        (total_subtitle_generated.total_seconds() if total_subtitle_generated else 0)
        / 60
    )
    return total_subtitle_generated_minutes


def save_subtitles_generation_minutes(
    organization,
    date,
    subtitle_generation_minutes,
    time_frame=AssetUsage.TimeFrames.DAILY,
):
    AssetUsage.objects.update_or_create(
        date=date,
        organization=organization,
        time_frame=time_frame,
        defaults={
            "subtitle_generation_minutes": subtitle_generation_minutes,
        },
    )
