def get_bucket_policy(organization):
    return {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Sid": "AllowOnlyRequestsHTTPHeaderOrPreSignedURLs",
                "Effect": "Deny",
                "NotPrincipal": {"AWS": "arn:aws:iam::100000104509:root"},
                "Action": ["s3:GetObject", "s3:GetObjectVersion"],
                "Resource": f"arn:aws:s3:::{organization.bucket_name}/*",
                "Condition": {
                    "StringNotLike": {
                        "aws:Referer": organization.bucket_secret_token,
                        "aws:UserAgent": "*Botocore*",
                    },
                },
            }
        ],
    }
