import base64
import binascii
import hashlib
import json
import uuid

import requests
from Cryptodome.Cipher import AES
from Cryptodome.Util.Padding import pad
from django.conf import settings

from app.models import Asset, EncryptionKey
from app.utils.drm import get_content_id_from_widevine_request


def generate_and_store_widevine_encryption_keys(widevine_request_data, organization):
    content_id = get_content_id_from_widevine_request(widevine_request_data)
    try:
        encryption_key = EncryptionKey.objects.get(
            content_id=content_id, widevine_encryption_keys__isnull=False
        )
        content_key_response = encryption_key.widevine_content_key_response
    except EncryptionKey.DoesNotExist:
        content_key_response = generate_widevine_encryption_keys(widevine_request_data)
        encryption_keys = base64.b64decode(
            content_key_response.get("response")
        ).decode()
        asset = get_asset_for_content_id_if_available(content_id)

        EncryptionKey.objects.update_or_create(
            content_id=content_id,
            defaults={
                "content_id": content_id,
                "widevine_encryption_keys": encryption_keys,
                "asset": asset,
                "organization": organization,
            },
        )
    return content_key_response


def generate_widevine_encryption_keys(widevine_request_data):
    decoded_request_data = base64.b64decode(widevine_request_data)
    encoded_signature = get_signature(decoded_request_data)

    data = {
        "request": widevine_request_data,
        "signature": encoded_signature,
        "signer": "testpress",
    }
    response = requests.post(settings.WIDEVINE_CONTENT_KEY_URL, data=json.dumps(data))
    return json.loads(response.content)


def get_asset_for_content_id_if_available(content_id):
    try:
        asset = Asset.objects.get(video__uuid=content_id)
    except Asset.DoesNotExist:
        # If client uses DRM service alone, then asset will not be available
        asset = None
    return asset


def get_signature(json_data):
    hash = hashlib.sha1(json_data).digest()
    cipher = AES.new(
        binascii.unhexlify(settings.WIDEVINE_AES_KEY),
        AES.MODE_CBC,
        binascii.unhexlify(settings.WIDEVINE_IV),
    )
    padded_hash = pad(hash, AES.block_size, style="pkcs7")
    signature = cipher.encrypt(padded_hash)
    return base64.b64encode(signature).decode()


def generate_fairplay_encryption_keys():
    return {
        "iv": uuid.uuid4().hex,
        "key": uuid.uuid4().hex,
        "uri": f"skd://{uuid.uuid4().hex}",
    }


def generate_and_store_fairplay_encryption_keys(content_id, organization):
    try:
        encryption_key = EncryptionKey.objects.get(
            content_id=content_id, fairplay_encryption_key_data__isnull=False
        )
        fairplay_key_data = encryption_key.fairplay_encryption_key_data
    except EncryptionKey.DoesNotExist:
        fairplay_key_data = generate_fairplay_encryption_keys()
        asset = get_asset_for_content_id_if_available(content_id)
        EncryptionKey.objects.update_or_create(
            content_id=content_id,
            defaults={
                "content_id": content_id,
                "fairplay_encryption_key_data": fairplay_key_data,
                "asset": asset,
                "organization": organization,
            },
        )
    return fairplay_key_data
