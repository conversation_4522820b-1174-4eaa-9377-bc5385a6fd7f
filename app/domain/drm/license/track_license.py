import base64
import json
from dataclasses import dataclass

from app.models import As<PERSON>, DRMLicenseLog


@dataclass
class DRMContentData:
    download: bool
    drm_type: str
    content_id: str

    @property
    def license_type(self):
        if self.download:
            return DRMLicenseLog.LicenseType.DOWNLOAD
        return DRMLicenseLog.LicenseType.STREAMING

    @property
    def drm(self):
        if self.drm_type == "fairplay":
            return DRMLicenseLog.DRMType.FAIRPLAY
        return DRMLicenseLog.DRMType.WIDEVINE

    @classmethod
    def create(cls, data):
        data = base64.b64decode(data).decode()
        data = json.loads(data)
        content_data = base64.b64decode(data.get("content_data")).decode()
        content_data = json.loads(content_data)

        return cls(
            content_id=content_data.get("content_id"),  # type: ignore
            download=content_data.get("download", False),  # type: ignore
            drm_type=content_data.get("drm_type", "widevine"),  # type: ignore
        )


def track_license(
    content_data,
    organization,
    user_agent,
    ip_address,
    provider=DRMLicenseLog.DRMProvider.TESTPRESS,
):
    meta_data = None
    asset = Asset.objects.filter(video__uuid=content_data.content_id).first()
    if asset:
        meta_data = {"asset_uuid": asset.uuid}

    DRMLicenseLog.objects.create(
        drm_type=content_data.drm,
        content_id=content_data.content_id,
        ip_address=ip_address,
        license_type=content_data.license_type,
        user_agent=user_agent,
        asset=asset,
        provider=provider,
        meta_data=meta_data,
        device=DRMLicenseLog.Device.PC,
        organization=organization,
    )
