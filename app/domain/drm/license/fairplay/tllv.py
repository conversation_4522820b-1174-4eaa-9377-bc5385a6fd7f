import binascii
import hashlib
import io
import struct
from collections import defaultdict

from Cryptodome.Cipher import AES
from django.conf import settings


class TLLVFactory:
    def __init__(self, payload):
        self.payload = payload

    def create(self):
        tag = self.parse_tag()
        block_length = self.parse_block_length()
        value_length = self.parse_value_length()
        value = self.parse_value(value_length)
        padding = self.parse_padding(value_length, block_length)
        return self.create_tllv(tag, block_length, value_length, value, padding)

    def parse_tag(self):
        return int.from_bytes(self.payload[0:8], byteorder="big")

    def parse_block_length(self):
        return int.from_bytes(self.payload[8:12], byteorder="big")

    def parse_value_length(self):
        return int.from_bytes(self.payload[12:16], byteorder="big")

    def parse_value(self, value_length):
        return self.payload[16 : 16 + value_length]

    def parse_padding(self, value_length, block_length):
        assert 16 + block_length <= len(
            self.payload
        ), "Cannot parse TLLV, not enough bytes in the value+padding"
        return self.payload[16 + value_length : 16 + block_length]

    def create_tllv(self, tag, block_length, value_length, value, padding):
        return TLLV_MAP[tag](tag, block_length, value_length, value, padding)


class TLLV:
    NAME = "TLLV"
    FIELD = "tllv"
    TAG = 0x0

    def __init__(self, tag, block_length, value_length, value, padding):
        self.tag = tag
        self.block_length = block_length
        self.value_length = value_length
        self.value = value
        self.padding = padding
        self.parse()

    def parse(self):
        pass

    @property
    def original_data(self):
        length_bytes = struct.pack(">I", self.block_length)
        payload_length = struct.pack(">I", self.value_length)

        binary_tag = struct.pack(">Q", self.tag)
        memory = io.BytesIO()
        memory.write(binary_tag)
        memory.write(length_bytes)
        memory.write(payload_length)
        memory.write(self.value)
        memory.write(self.padding)
        return memory.getvalue()


class SKR1(TLLV):
    NAME = "[SK..R1]"
    FIELD = "sk_r1"
    TAG = 0x3D1A10B8BFFAC2EC

    def parse(self):
        self.iv = self.value[0:16]
        self.encrypted_payload = self.value[16 : 16 + 96]

    def decrypt(self, dask):
        self.payload = AES.new(dask, AES.MODE_CBC, self.iv).decrypt(
            self.encrypted_payload
        )
        session_key = self.payload[0:16]
        r1 = self.payload[36:80]
        self.integrity = self.payload[80:96]
        return session_key, r1


class SKR1Integrity(TLLV):
    NAME = "[SK..R1] integrity"
    FIELD = "sk_r1_integrity"
    TAG = 0xB349D4809E910687


class ARSeed(TLLV):
    NAME = "Anti-replay seed"
    FIELD = "ar_seed"
    TAG = 0x89C90F12204106B2


class R2(TLLV):
    NAME = "R2"
    FIELD = "r2"
    TAG = 0x71B5595AC1521133

    def get_dask(self):
        PRIME = 813416437
        TOTAL_ROTATIONS = 16
        pad = [0] * 64
        m_block = [0] * 14

        # Padding until a multiple of 56B
        for i in range(self.value_length):
            pad[i] = self.value[i]

        pad[self.value_length] = 0x80

        for i in range(self.value_length + 1, 56):
            pad[i] = 0

        # Create 14 32b values
        for i in range(14):
            m_block[i] = (
                (pad[4 * i] << 24)
                ^ (pad[4 * i + 1] << 16)
                ^ (pad[4 * i + 2] << 8)
                ^ (pad[4 * i + 3])
            )

        m1 = sum(m_block[:7]) & 0xFFFFFFFF
        m2 = sum(m_block[7:]) & 0xFFFFFFFF

        for r in range(TOTAL_ROTATIONS):
            if m1 & 1:
                m1 = m1 >> 1
            else:
                m1 = (((3 * m1) & 0xFFFFFFFF) + 1) % PRIME

            if m2 & 1:
                m2 = m2 >> 1
            else:
                m2 = (((3 * m2) & 0xFFFFFFFF) + 1) % PRIME

        for i in range(4):
            pad[56 + i] = (m1 >> (8 * i)) & 0xFF
            pad[60 + i] = (m2 >> (8 * i)) & 0xFF
        hash_object = hashlib.sha1(bytearray(pad)).digest()
        return AES.new(binascii.unhexlify(settings.FAIRPLAY_ASK), AES.MODE_ECB).encrypt(
            hash_object[:16]
        )


class ReturnRequest(TLLV):
    NAME = "Return Request"
    FIELD = "return_request"
    TAG = 0x19F9D4E5AB7609CB

    def parse(self):
        self.tags = []
        data = self.value
        while data:
            self.tags.append(int.from_bytes(data[0:8], byteorder="big"))
            data = data[8:]


class AssetID(TLLV):
    NAME = "Asset ID"
    FIELD = "asset_id"
    TAG = 0x1BF7F53F5D5D5A1F


class TransactionID(TLLV):
    NAME = "Transaction ID"
    FIELD = "transaction_id"
    TAG = 0x47AA7AD3440577DE


class EncryptedContentKey(TLLV):
    NAME = "Encrypted CK"
    FIELD = "encrypted_ck"
    TAG = 0x58B38165AF0E3D5A


class MediaPlaybackState(TLLV):
    NAME = "Media Playback State"
    FIELD = "media_playback_state"
    TAG = 0xEB8EFDF2B25AB3A0

    def parse(self):
        assert self.value_length == 16
        self.creation_date = int.from_bytes(self.value[0:4], byteorder="big")
        self.playback_state = int.from_bytes(self.value[4:8], byteorder="big")
        self.session_id = int.from_bytes(self.value[8:16], byteorder="big")


class SecurityLevelReport(TLLV):
    NAME = "Security Level Report"
    FIELD = "security_level_report"
    TAG = 0xB18EE16EA50F6C02
    SECURITY_LEVEL = {
        0x32F0004966A5C4F8: "AppleBaseline / Baseline",
        0x4E7FD92421D588B4: "AppleMain / Main",
    }

    def parse(self):
        assert self.value_length == 20

        self.version = int.from_bytes(self.value[0:4], byteorder="big")
        self.reserved = self.value[4:8]
        self.security_level = int.from_bytes(self.value[8:16], byteorder="big")
        self.reserved = self.value[16:20]
        assert self.security_level in self.SECURITY_LEVEL


class R1(TLLV):
    NAME = "R1"
    FIELD = "r1"
    TAG = 0xEA74C4645D5EFEE9


class Unknown(TLLV):
    NAME = "Unknown"
    TAG = 0x0


TLLV_MAP: dict = defaultdict(lambda: Unknown)
for tllv in TLLV.__subclasses__():
    TLLV_MAP[tllv.TAG] = tllv
