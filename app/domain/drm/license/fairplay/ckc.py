import binascii
import enum
import hashlib
import io
import os
import random
import struct

from Cryptodome.Cipher import AES

LEASE_DURATION = 60 * 60 * 5
RENTAL_DURATION = 60 * 60 * 24 * 30


class SPCTags(enum.Enum):
    SK_R1 = 0x3D1A10B8BFFAC2EC
    SK_R1_INTEGRITY = 0xB349D4809E910687
    AR_SEED = 0x89C90F12204106B2
    R2 = 0x71B5595AC1521133
    RETURN_REQUEST = 0x19F9D4E5AB7609CB
    ASSET_ID = 0x1BF7F53F5D5D5A1F
    TRANSACTION_ID = 0x47AA7AD3440577DE
    PROTOCOL_VERSIONS_SUPPORTED = 0x67B8FB79ECCE1A13
    PROTOCOL_VERSION_USED = 0x5D81BCBCC7F61703
    STREAMING_INDICATOR = 0xABB0256A31843974
    MEDIA_PLAYBACK_STATE = 0xEB8EFDF2B25AB3A0
    CAPABILITIES = 0x9C02AF3253C07FB2
    UNIDENTIFIED_TAG = 0x0


class TLLVTags(enum.Enum):
    AR = 0x89C90F12204106B2
    Asset = 0x1BF7F53F5D5D5A1F
    Capabilities = 0x9C02AF3253C07FB2
    DurationCK = 0x47ACF6A418CD091A
    EncryptedCK = 0x58B38165AF0E3D5A
    HDCPEnforcement = 0x2E52F1530D8DDB4A
    MediaPlayback = 0xEB8EFDF2B25AB3A0
    ProtocolSupported = 0x67B8FB79ECCE1A13
    ProtocolUsed = 0x5D81BCBCC7F61703
    R1 = 0xEA74C4645D5EFEE9
    R2 = 0x71B5595AC1521133
    SKR1Integrity = 0xB349D4809E910687
    SKR1 = 0x3D1A10B8BFFAC2EC
    StreamIndicator = 0xABB0256A31843974
    Transaction = 0x47AA7AD3440577DE
    TRR = 0x19F9D4E5AB7609CB
    OfflineKey = 0x6375D9727060218C


class HDCPEnforcement(enum.Enum):
    NONE = 0xEF72894CA7895B78
    TYPE_0 = 0x40791AC78BD5C571
    TYPE_1 = 0x285A0863BBA8E1D3


def generate_tllv_block(tag, payload):
    length = ((len(payload) - (len(payload) % 16)) // 16 + 1) * 16
    padding = bytearray(length - len(payload))
    random_bytes = bytearray(random.getrandbits(8) for _ in range(len(padding)))
    padding[0 : len(random_bytes)] = random_bytes
    length_bytes = struct.pack(">I", length)
    payload_length = struct.pack(">I", len(payload))

    binary_tag = struct.pack(">Q", tag)
    memory = io.BytesIO()
    memory.write(binary_tag)
    memory.write(length_bytes)
    memory.write(payload_length)
    memory.write(payload)
    memory.write(padding)
    return memory.getvalue()


class CKC:
    def __init__(self, spc, key, iv):
        self.spc = spc
        self.key = key
        self.iv = iv
        self.tllv_blocks = self.spc.tllv_blocks
        self.session_key, self.r1 = self.decrypt_sk_r1()
        self.random_iv = os.urandom(16)
        self.license_specs = {}

    def decrypt_sk_r1(self):
        sk_r1 = self.tllv_blocks.get(SPCTags.SK_R1.value)
        r2 = self.tllv_blocks.get(SPCTags.R2.value)
        dask = r2.get_dask()
        return sk_r1.decrypt(dask)

    def generate(self, license_specs={}):
        self.license_specs = license_specs
        ckc_blocks = []
        ckc_blocks.extend([self.add_content_key_block()])
        ckc_blocks.extend(self.add_return_request_blocks())
        ckc_blocks.extend([self.add_duration_block()])
        ckc_blocks.extend([self.add_r1_block()])
        ckc_blocks.extend([self.generate_hdcp_enforcement_block()])

        encrypted_ckc_message = self.encrypt_ckc_message(ckc_blocks)
        return self.create_ckc_payload(encrypted_ckc_message)

    def add_content_key_block(self):
        encrypted_content_key = AES.new(self.session_key, AES.MODE_ECB).encrypt(
            binascii.unhexlify(self.key)
        )
        stream = io.BytesIO()
        stream.write(binascii.unhexlify(self.iv))
        stream.write(encrypted_content_key)
        stream_contents = stream.getvalue()
        return generate_tllv_block(TLLVTags.EncryptedCK.value, stream_contents)

    def add_return_request_blocks(self):
        ckc_blocks = []
        for tag in self.tllv_blocks.get(SPCTags.RETURN_REQUEST.value).tags:
            ckc_blocks.append(self.tllv_blocks.get(tag).original_data)
        return ckc_blocks

    def add_duration_block(self):
        rental_duration = RENTAL_DURATION
        key_type = 0x27B59BDE  # Content key valid for both lease and rental.
        if self.license_specs.get("can_persist"):
            key_type = 0x18F06048  # Content key valid for persistence
            rental_duration = int(
                self.license_specs.get("rental_duration_seconds", RENTAL_DURATION)
            )

        lease_duration = struct.pack(">I", LEASE_DURATION)
        memory = io.BytesIO()
        memory.write(lease_duration)
        memory.write(struct.pack(">I", rental_duration))
        memory.write(struct.pack(">I", key_type))
        memory.write(struct.pack(">I", 0x86D34A3A))
        return generate_tllv_block(TLLVTags.DurationCK.value, memory.getvalue())

    def add_r1_block(self):
        return generate_tllv_block(TLLVTags.R1.value, self.r1)

    def generate_hdcp_enforcement_block(self):
        memory = io.BytesIO()
        memory.write(struct.pack(">Q", HDCPEnforcement.TYPE_0.value))

        return generate_tllv_block(TLLVTags.HDCPEnforcement.value, memory.getvalue())

    def encrypt_ckc_message(self, ckc_blocks):
        memory = io.BytesIO()
        for block in ckc_blocks:
            memory.write(block)
        ckc_message = memory.getvalue()

        ar = self.tllv_blocks.get(SPCTags.AR_SEED.value)
        hash_object = hashlib.sha1(bytearray(self.r1))
        hex_dig = hash_object.digest()[:16]
        key = AES.new(hex_dig, AES.MODE_ECB).encrypt(ar.value)
        encrypted_ckc_message = AES.new(key, AES.MODE_CBC, self.random_iv).encrypt(
            ckc_message
        )
        return encrypted_ckc_message

    def create_ckc_payload(self, encrypted_ckc_message):
        memory = io.BytesIO()
        memory.write(struct.pack(">I", 1))
        memory.write(struct.pack(">I", 0))
        memory.write(self.random_iv)
        memory.write(struct.pack(">I", len(encrypted_ckc_message)))
        memory.write(encrypted_ckc_message)
        return memory.getvalue()
