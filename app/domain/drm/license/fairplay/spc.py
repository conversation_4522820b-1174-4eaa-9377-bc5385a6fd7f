import base64
import binascii

from Cryptodome.Cipher import AES
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding
from django.conf import settings

from app.domain.drm.license.fairplay.tllv import TLLVFactory


class Spc:
    def __init__(self, spc):
        self.binary_data = base64.b64decode(spc)
        self.tllv_blocks = {}

    def parse(self):
        payload = self.decrypt_payload()
        self.parse_tllv_blocks(payload)
        return self

    def decrypt_payload(self):
        return AES.new(self.aes_key, AES.MODE_CBC, self.iv).decrypt(
            self.binary_data[176:]
        )

    @property
    def aes_key(self):
        private_key = serialization.load_pem_private_key(
            settings.FAIRPLAY_PRIVATE_KEY, password=settings.FAIRPLAY_PASSWORD
        )
        return private_key.decrypt(  # type: ignore
            self.binary_data[24:152],
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA1()),
                algorithm=hashes.SHA1(),
                label=None,
            ),
        )

    @property
    def iv(self):
        return self.binary_data[8:24]

    def parse_tllv_blocks(self, payload):
        while payload:
            assert len(payload) >= 16, "Cannot parse TLLV not enough bytes"
            tllv = TLLVFactory(payload).create()
            self.tllv_blocks[tllv.tag] = tllv
            payload = payload[16 + tllv.block_length :]

    @property
    def version(self):
        return int.from_bytes(self.binary_data[:4], byteorder="big")

    @property
    def reserved(self):
        return binascii.hexlify(self.binary_data[4:8])

    @property
    def certificate_hash(self):
        return binascii.hexlify(self.binary_data[152:172])

    @property
    def payload_length(self):
        return int.from_bytes(self.binary_data[172:176], byteorder="big")
