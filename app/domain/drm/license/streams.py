import base64
import binascii
import json

import requests
import sentry_sdk
from django.conf import settings

from app.domain.drm.encryption.streams import get_signature
from app.domain.drm.license.fairplay.ckc import CKC
from app.domain.drm.license.fairplay.spc import Spc


def generate_widevine_license(
    content_id, content_keys, player_payload, license_specs={}
):
    try:
        request = get_license_request(
            content_id, content_keys, license_specs, player_payload
        )
        encoded_request = base64.standard_b64encode(request).decode()
        encoded_signature = get_signature(request)
        data = {
            "request": encoded_request,
            "signature": encoded_signature,
            "signer": "testpress",
        }

        response = requests.post(
            settings.WIDEVINE_LICENSE_KEY_URL,
            data=json.dumps(data),
            headers={"Content-Type": "application/json"},
        )
        response.raise_for_status()
        license_data = response.json().get("license", None)

        if not license_data:
            status_message = response.json().get(
                "status_message", "No message provided"
            )
            with sentry_sdk.push_scope() as scope:
                status = response.json().get("status", "UNKNOWN")
                status_message = response.json().get(
                    "status_message", "No message provided"
                )
                platform_verification_status = response.json().get(
                    "platform_verification_status", "UNKNOWN"
                )
                scope.set_extra("status", status)
                scope.set_extra("status_message", status_message)
                scope.set_extra(
                    "platform_verification_status", platform_verification_status
                )
                scope.set_extra("full_response", response.json())
                sentry_sdk.capture_message(
                    f"License data missing due to : {status_message}"
                )
            return {
                "status": "ERROR",
                "status_message": status_message,
            }
        
        return license_data

    except Exception as e:
        sentry_sdk.capture_exception(e)
        return {
            "status": "ERROR",
            "status_message": "Network error occurred while generating the license.",
        }


def get_license_request(content_id, content_keys, license_specs, player_payload):
    FIFTEEN_DAYS = 60 * 60 * 24 * 15
    data = {
        "payload": player_payload,
        "content_id": base64.b64encode(binascii.unhexlify(content_id)).decode(),
        "provider": "testpress",
        "content_key_specs": content_keys,
        "policy_overrides": {
            "can_play": True,
            "can_persist": license_specs.get("can_persist", False),
            "license_duration_seconds": license_specs.get(
                "license_duration_seconds", FIFTEEN_DAYS
            ),
            "rental_duration_seconds": license_specs.get(
                "rental_duration_seconds", FIFTEEN_DAYS
            ),
        },
        "session_init": {"override_device_revocation": True},
    }
    return json.dumps(data).encode()


def generate_fairplay_license(spc, key, iv, license_specs={}):
    parsed_spc = Spc(spc).parse()
    ckc = CKC(parsed_spc, key, iv).generate(license_specs)
    return ckc
