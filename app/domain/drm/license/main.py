import base64

from django.shortcuts import get_object_or_404
from django.http import JsonResponse

from app.domain.drm.license.streams import (
    generate_fairplay_license,
    generate_widevine_license,
)
from app.models import Encry<PERSON><PERSON><PERSON>


def get_tpstreams_license(content_id, player_payload, drm_type, license_specs={}):
    if drm_type == "fairplay":
        return get_tpstreams_fairplay_license(content_id, player_payload, license_specs)
    return get_tpstreams_widevine_license(content_id, player_payload, license_specs)


def get_tpstreams_fairplay_license(content_id, player_payload, license_specs={}):
    encryption_key = get_object_or_404(
        EncryptionKey, content_id=content_id, fairplay_encryption_key_data__isnull=False
    )
    key = encryption_key.fairplay_encryption_key_data["key"]
    iv = encryption_key.fairplay_encryption_key_data["iv"]
    return generate_fairplay_license(player_payload, key, iv, license_specs)


def get_tpstreams_widevine_license(content_id, player_payload, license_specs={}):
    try:
        encryption_key = get_object_or_404(
            EncryptionKey, content_id=content_id, widevine_encryption_keys__isnull=False
        )

        content_key_specs = get_content_key_specs(encryption_key, license_specs)

        license_data = generate_widevine_license(
            content_id,
            content_key_specs,
            base64.b64encode(player_payload).decode(),
            license_specs,
        )
        if isinstance(license_data, dict) and license_data.get("status") == "ERROR":
            return JsonResponse(license_data, status=400)
        
        if not license_data:
            raise ValueError("No license data returned for content_id: {}".format(content_id))

        return base64.b64decode(license_data)
    
    except Exception as e:
        error_response = {
            "status": "ERROR",
            "status_message": f"Error in generating license: {str(e)}",
        }
        return JsonResponse(error_response, status=400)


def get_content_key_specs(encryption_key, license_specs):
    content_key_specs = license_specs.get("content_key_specs", [])
    track_spec_dict = {spec.get("track_type"): spec for spec in content_key_specs}

    result = []
    for content_key in encryption_key.widevine_content_keys:
        track_type = content_key["track_type"]
        track_spec = track_spec_dict.get(track_type, {})
        result.append({**content_key, **track_spec})

    return result
