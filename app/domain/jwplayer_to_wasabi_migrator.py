from app.models import Organization, Video
from app.tasks.jwplayer import MigrateImportedVideoTask
from app.utils.jwplayer import JwplayerInterface as Jwplayer


class Migrator:
    def __init__(
        self,
        site_id,
        api_key,
        org_id,
        content_protection_type=Video.ContentProtectionType.DISABLED,
    ):
        self.site_id = site_id
        self.api_key = api_key
        self.organization = Organization.objects.get(uuid=org_id)
        self.content_protection_type = content_protection_type
        self.jwp = Jwplayer(site_id=self.site_id, api_key=self.api_key)

    def migrate_all_videos(self):
        site = self.jwp.get_site()
        for video_id in site.videos:
            self.migrate_video(video_id)

    def migrate_video(self, video_id):
        from app.models import ImportedVideo

        video = self.jwp.get_video(video_id)
        if not self.is_video_already_migrated(video):
            imported_video = ImportedVideo.objects.create(
                name=video.name,
                organization=self.organization,
                details=video.data,
                source=ImportedVideo.Source.JWPLAYER,
            )
            MigrateImportedVideoTask.apply_async(
                kwargs={
                    "organization_uuid": self.organization.uuid,
                    "site_id": self.site_id,
                    "api_key": self.api_key,
                    "imported_video_id": imported_video.id,
                    "content_protection_type": self.content_protection_type,
                },
                queue="migration_queue",
            )

    def is_video_already_migrated(self, video):
        from app.models import ImportedVideo

        return ImportedVideo.objects.filter(details__link=video.link).exists()
