from app.domain import cloudfront


def get_s3_config_for_rclone(organization):
    return f"""
    [s3]
    type = s3
    provider = AWS
    access_key_id = {organization.storage_access_key_id}
    secret_access_key = {organization.storage_secret_access_key}
    region = {organization.storage_region}
    endpoint = s3.{organization.storage_region}.amazonaws.com
    storage_class = STANDARD
    no_check_bucket = true
    """.replace(
        "\\n", "\n"
    )


def get_bucket_policy(organization):
    origin_access_id = cloudfront.get_origin_access_id(organization)

    return {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Principal": {
                    "AWS": f"arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity {origin_access_id}"
                },
                "Sid": "AllowOnlyRequestsHTTPHeaderOrPreSignedURLs",
                "Effect": "Allow",
                "Action": ["s3:GetObject", "s3:GetObjectVersion"],
                "Resource": f"arn:aws:s3:::{organization.bucket_name}/*",
            }
        ],
    }
