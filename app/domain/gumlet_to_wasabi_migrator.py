from app.models import Organization, Video
from app.tasks.gumlet import MigrateImportedVideoTask
from app.utils.gumlet import GumletInterface as Gumlet


class Migrator:
    def __init__(
        self,
        api_key,
        org_id,
        playlist_id=None,
        content_protection_type=Video.ContentProtectionType.DISABLED,
    ):
        print("Initializing Migrator...")
        self.api_key = api_key
        self.playlist_id = playlist_id
        self.organization = Organization.objects.get(uuid=org_id)
        self.content_protection_type = content_protection_type
        self.gumlet = Gumlet(api_key=self.api_key)
        print(
            f"Migrator initialized for Organization: {self.organization.name} ({self.organization.uuid})"
        )

    def migrate_all_videos(self):
        assets = []
        assets = self.get_videos()
        for asset in assets:
            self.migrate_video(asset)

    def get_videos(self):
        print("Fetching assets for migration...")
        assets = []

        if self.playlist_id:
            print(f"Fetching assets for specific playlist ID: {self.playlist_id}")
            assets = self.gumlet.get_all_assets(self.playlist_id)
            return assets

        playlists = self.gumlet.get_all_playlists()
        for playlist in playlists:
            print(f"Fetching assets for playlist {playlist.id} - {playlist.name}")
            assets.extend(self.gumlet.get_all_assets(playlist.id))
        print(f"Total assets found: {len(assets)}")
        return assets

    def migrate_video(self, asset):
        from app.models import ImportedVideo

        print(
            f"Checking if video '{asset.title}' (ID: {asset.id}) is already migrated..."
        )
        if not self.is_video_already_migrated(asset):
            print(f"Migrating video: {asset.title} (ID: {asset.id})")
            imported_video = ImportedVideo.objects.create(
                name=asset.title,
                organization=self.organization,
                source=ImportedVideo.Source.GUMLET,
                uri=asset.id,
                details=asset.__dict__,
            )
            print(f"Created ImportedVideo entry: {imported_video.id}")

            MigrateImportedVideoTask.apply_async(
                kwargs={
                    "organization_uuid": self.organization.uuid,
                    "api_key": self.api_key,
                    "imported_video_id": imported_video.id,
                    "content_protection_type": self.content_protection_type,
                },
                queue="migration_queue",
            )
            print(f"Migration task queued for video: {asset.title} (ID: {asset.id})")
        else:
            print(f"Video already migrated: {asset.title} (ID: {asset.id})")

    def is_video_already_migrated(self, asset):
        from app.models import ImportedVideo

        exists = ImportedVideo.objects.filter(uri=asset.id).exists()
        if exists:
            print(f"Video {asset.id} exists in database: {exists}")
        return exists
