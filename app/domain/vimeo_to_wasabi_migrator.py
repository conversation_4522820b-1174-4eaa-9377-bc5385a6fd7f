from django_multitenant.utils import set_current_tenant

from app.models import Organization, Video
from app.models.videoimport import ImportedFolder, ImportedVideo
from app.tasks import MigrateImportedVideoTask
from app.utils.vimeo import Interface as Vimeo


class Implementation:
    def __init__(
        self,
        access_token,
        org_id,
        content_protection_type=Video.ContentProtectionType.DRM,
    ):
        self.access_token = access_token
        self.vimeo = Vimeo(access_token)
        self.organization = Organization.objects.get(uuid=org_id)
        set_current_tenant(self.organization)
        self.content_protection_type = content_protection_type

    def migrate_folder(self, folder, parent=None):
        imported_folder = ImportedFolder.objects.create(
            name=folder.name,
            parent=parent,
            organization=self.organization,
            details=folder.data,
        )
        for uri in folder.video_links:
            self.migrate_video(uri=uri, imported_folder=imported_folder)

        for subfolder_id in folder.subfolder_ids:
            subfolder = self.vimeo.get_folder(folder_id=subfolder_id)
            self.migrate_folder(subfolder, parent=imported_folder)

    def migrate_video(self, uri, imported_folder=None):
        video = self.vimeo.get_video(uri)

        if not video.is_transcoded:
            return

        if self.is_video_already_migrated(video):
            return

        video_name = self.generate_unique_name_for_video(video, imported_folder)
        imported_video = ImportedVideo.objects.create(
            folder=imported_folder,
            name=video_name,
            uri=video.link,
            organization=self.organization,
            details=video.data,
        )
        MigrateImportedVideoTask.apply_async(
            kwargs={
                "organization_uuid": self.organization.uuid,
                "access_token": self.access_token,
                "imported_video_id": imported_video.id,
                "content_protection_type": self.content_protection_type,
            },
            queue="migration_queue",
        )

    def generate_unique_name_for_video(self, video, imported_folder):
        video_name = video.name
        if ImportedVideo.objects.filter(
            name=video_name, folder=imported_folder
        ).exists():
            video_name += f" ({video.id})"
        return video_name

    def is_video_already_migrated(self, video):
        return ImportedVideo.objects.filter(uri=video.link).exists()


class Migrator:
    def __init__(
        self,
        access_token,
        org_id,
        content_protection_type=Video.ContentProtectionType.DRM,
    ):
        self.impl = Implementation(
            access_token, org_id, content_protection_type=content_protection_type
        )

    def migrate_all(self, user_id=None):
        folder = self.impl.vimeo.get_root_folder(user_id=user_id)
        self.impl.migrate_folder(folder)

    def migrate_folder(self, folder_id=None):
        folder = self.impl.vimeo.get_folder(folder_id=folder_id)
        self.impl.migrate_folder(folder)

    def migrate_video(self, uri):
        self.impl.migrate_video(uri)
