import logging

from django.utils import timezone
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.models import Asset

logger = logging.getLogger(__name__)


def soft_delete_folder(asset):
    descendants_queryset = asset.get_descendants(include_self=False).values_list(
        "id", flat=True
    )
    now = timezone.now()

    batch_update_assets(
        descendants_queryset, deleted=now, deleted_by_cascade=True, modified=now
    )

    Asset.objects.filter(uuid=asset.uuid).update(
        deleted=now, deleted_by_cascade=False, modified=now
    )


def restore_folder(asset):
    descendant_queryset = Asset.all_objects.filter(
        tree_id=asset.tree_id,
        lft__gt=asset.lft,
        rght__lt=asset.rght,
        deleted__isnull=False,
    ).values_list("id", flat=True)

    now = timezone.now()
    batch_update_assets(
        descendant_queryset,
        deleted=None,
        deleted_by_cascade=False,
        modified=now,
    )

    Asset.all_objects.filter(uuid=asset.uuid).update(
        deleted=None, deleted_by_cascade=False, modified=now
    )


def batch_update_assets(asset_ids, batch_size=500, **update_fields):
    current_batch = []
    for asset_id in asset_ids.iterator(chunk_size=batch_size):
        current_batch.append(asset_id)
        if len(current_batch) >= batch_size:
            Asset.all_objects.filter(id__in=current_batch).update(**update_fields)
            current_batch = []
    if current_batch:
        Asset.all_objects.filter(id__in=current_batch).update(**update_fields)


def get_or_create_folder(organization, title):
    default_folder, _ = Asset.objects.get_or_create(
        organization=organization,
        type=Asset.Type.FOLDER,
        title=title,
    )
    return default_folder


class AssetCloner:
    def __init__(self, source_org, destination_org):
        self.source_org = source_org
        self.destination_org = destination_org

    def clone(self, asset_uuid):
        try:
            set_current_tenant(self.source_org)
            source_asset = self._get_source_asset(asset_uuid)
            if not source_asset:
                return None

            if source_asset.type == Asset.Type.FOLDER:
                cloner = FolderCloner(self.source_org, self.destination_org)
                return cloner.clone(asset_uuid)
            elif source_asset.type == Asset.Type.VIDEO:
                from app.domain.video import VideoCloner

                cloner = VideoCloner(self.source_org, self.destination_org)
                return cloner.clone(asset_uuid)
            else:
                logger.info(
                    f"Asset type {source_asset.type} is not currently supported for cloning"
                )
                return None

        except Exception as e:
            logger.error(f"Asset cloning failed: {str(e)}")
            return None

    def _get_source_asset(self, asset_uuid):
        try:
            return Asset.objects.get(uuid=asset_uuid)
        except Asset.DoesNotExist:
            logger.error(f"Asset with UUID {asset_uuid} does not exist")
            return None


class FolderCloner:
    def __init__(self, source_org, destination_org):
        self.source_org = source_org
        self.destination_org = destination_org

    def clone(self, folder_uuid):
        try:
            set_current_tenant(self.source_org)
            folder = self._get_folder(folder_uuid)
            if not folder:
                return None

            new_folder = self._clone_folder(folder, None)
            return new_folder

        except Exception as e:
            logger.error(f"Folder cloning failed: {str(e)}")
            unset_current_tenant()
            return None

    def _get_folder(self, folder_uuid):
        try:
            return Asset.objects.get(uuid=folder_uuid, type=Asset.Type.FOLDER)
        except Asset.DoesNotExist:
            logger.error(f"Folder with UUID {folder_uuid} does not exist")
            return None

    def _clone_folder(self, source_folder, new_parent):
        try:
            new_folder = self._create_folder(source_folder, new_parent)
            self._clone_children(source_folder, new_folder)

            set_current_tenant(self.destination_org)
            new_folder.update_children_count()
            unset_current_tenant()
            return new_folder

        except Exception as e:
            logger.error(f"Failed to clone folder {source_folder.uuid}: {str(e)}")
            return None

    def _create_folder(self, source_folder, new_parent):
        set_current_tenant(self.destination_org)
        new_folder = Asset.objects.create(
            organization=self.destination_org,
            title=source_folder.title,
            type=Asset.Type.FOLDER,
            parent=new_parent,
        )
        set_current_tenant(self.source_org)
        return new_folder

    def _clone_children(self, source_folder, new_folder):
        children = Asset.objects.filter(parent=source_folder).order_by("created")
        for child in children:
            if child.type == Asset.Type.FOLDER:
                self._clone_folder(child, new_folder)
            elif child.type == Asset.Type.VIDEO:
                try:
                    self._clone_asset(child.uuid, new_folder)
                except Exception as e:
                    logger.error(f"Failed to clone asset {child.uuid}: {str(e)}")

    def _clone_asset(self, asset_uuid, new_parent=None):
        source_asset = self._get_source_asset(asset_uuid)
        if not source_asset:
            return

        if source_asset.type == Asset.Type.VIDEO:
            from app.domain.video import VideoCloner

            video_cloner = VideoCloner(self.source_org, self.destination_org)
            video_cloner.clone(asset_uuid, new_parent)
        else:
            logger.info(
                f"Asset type {source_asset.type} is not currently supported for cloning: {source_asset.uuid}"
            )

    def _get_source_asset(self, asset_uuid):
        set_current_tenant(self.source_org)
        return Asset.objects.get(uuid=asset_uuid)
