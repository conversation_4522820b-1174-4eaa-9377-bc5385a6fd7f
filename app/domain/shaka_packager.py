import base64
import json

from app.utils.crypto import generate_signature


def parse_packager_request(request):
    wsgi_input = request.META.get("wsgi.input")
    if wsgi_input:
        body = wsgi_input.read()
    else:
        body = b""

    data = body.decode("utf-8")
    json_data = json.loads(data)
    return json_data


def is_packager_request_data_valid(data, organization):
    signature = data.get("signature")
    request_data = data.get("request")
    try:
        expected_signature = generate_signature(
            base64.b64decode(request_data).decode(),
            organization.drm_aes_signing_key,
            organization.drm_aes_signing_iv,
        )
    except TypeError:
        return False
    return expected_signature == signature
