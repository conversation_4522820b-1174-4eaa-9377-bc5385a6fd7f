import json

import boto3
from botocore.config import Config

from app.domain import s3, wasabi


def create_bucket(organization):
    client = get_client(organization)
    client.create_bucket(Bucket=organization.bucket_name)
    policy = get_bucket_policy(organization)
    client.put_bucket_policy(Bucket=organization.bucket_name, Policy=json.dumps(policy))

    if organization.uses_s3_storage:
        configure_bucket_cors(client, organization.bucket_name)
        configure_bucket_public_access(client, organization.bucket_name)
        set_bucket_object_ownership(client, organization.bucket_name)


def get_bucket_policy(organization):
    if organization.uses_s3_storage:
        return s3.get_bucket_policy(organization)
    else:
        return wasabi.get_bucket_policy(organization)


def configure_bucket_cors(client, bucket_name):
    client.put_bucket_cors(
        Bucket=bucket_name,
        CORSConfiguration={
            "CORSRules": [
                {
                    "AllowedHeaders": ["*"],
                    "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
                    "AllowedOrigins": ["*"],
                    "ExposeHeaders": ["ETag"],
                },
            ],
        },
    )


def configure_bucket_public_access(client, bucket_name):
    # Allow public access to objects within the bucket
    client.put_public_access_block(
        Bucket=bucket_name,
        PublicAccessBlockConfiguration={
            "BlockPublicAcls": False,
            "IgnorePublicAcls": False,
            "BlockPublicPolicy": True,
            "RestrictPublicBuckets": True,
        },
    )


def set_bucket_object_ownership(client, bucket_name):
    # Setting ownership to ObjectWriter will allow us to specify ACL during upload
    client.put_bucket_ownership_controls(
        Bucket=bucket_name,
        OwnershipControls={"Rules": [{"ObjectOwnership": "ObjectWriter"}]},
    )


def create_multipart_upload(organization, **parameters):
    return get_client(organization).create_multipart_upload(**parameters)


def get_multipart_parts(organization, key, upload_id, from_part_number=0):
    parts = []
    client = get_client(organization)
    response = client.list_parts(
        Bucket=organization.bucket_name,
        Key=key,
        PartNumberMarker=from_part_number,
        UploadId=upload_id,
    )
    parts.extend(response.get("Parts", []))

    if response.get("IsTruncated"):
        parts.extend(
            get_multipart_parts(
                organization.bucket_name,
                key,
                upload_id,
                response.get("NextPartNumberMarker"),
            )
        )
    return parts


def abort_multipart_upload(organization, key, upload_id):
    return get_client(organization).abort_multipart_upload(
        Bucket=organization.bucket_name,
        Key=key,
        UploadId=upload_id,
    )


def generate_presigned_put_url(organization, key, upload_id, part_number):
    return get_client(organization).generate_presigned_url(
        ClientMethod="upload_part",
        Params={
            "Bucket": organization.bucket_name,
            "Key": key,
            "UploadId": upload_id,
            "PartNumber": int(part_number),
        },
        ExpiresIn=3600,
        HttpMethod="PUT",
    )


def complete_multipart_upload(organization, key, upload_id, parts):
    return get_client(organization).complete_multipart_upload(
        Bucket=organization.bucket_name,
        Key=key,
        MultipartUpload={"Parts": parts},
        UploadId=upload_id,
    )


THREE_DAYS = 3 * 24 * 60 * 60


def generate_presigned_url(organization, key, expires_in=THREE_DAYS, download_as=None):
    params = {"Bucket": organization.bucket_name, "Key": key}

    if download_as:
        params["ResponseContentDisposition"] = f"attachment; filename={download_as}"

    return get_client(organization).generate_presigned_url(
        ClientMethod="get_object",
        Params=params,
        ExpiresIn=expires_in,
    )


def get_client(organization):
    return boto3.client(
        "s3",
        endpoint_url=f"https://{organization.storage_domain}",
        aws_access_key_id=organization.storage_access_key_id,
        aws_secret_access_key=organization.storage_secret_access_key,
        region_name=organization.storage_region,
        config=Config(signature_version="s3v4"),
    )


def get_size(organization, file_path=""):
    size = 0
    client = get_client(organization)
    response = client.list_objects_v2(Bucket=organization.bucket_name, Prefix=file_path)
    size += sum(obj["Size"] for obj in response.get("Contents", []))

    while response.get("IsTruncated"):
        response = client.list_objects_v2(
            Bucket=organization.bucket_name,
            Prefix=file_path,
            ContinuationToken=response["NextContinuationToken"],
        )
        size += sum(obj["Size"] for obj in response.get("Contents", []))

    return size


def delete_folder(organization, folder_path):
    paths = get_all_object_paths(organization, folder_path)
    for path in paths:
        delete_file(organization, path)


def get_all_object_paths(organization, file_path):
    object_paths = []
    client = get_client(organization)
    response = client.list_objects_v2(Bucket=organization.bucket_name, Prefix=file_path)

    object_paths.extend([obj["Key"] for obj in response.get("Contents", [])])

    while response.get("IsTruncated"):
        response = client.list_objects_v2(
            Bucket=organization.bucket_name,
            Prefix=file_path,
            ContinuationToken=response["NextContinuationToken"],
        )

        object_paths.extend([obj["Key"] for obj in response.get("Contents", [])])

    return object_paths


def delete_file(organization, file_path):
    client = get_client(organization)
    try:
        client.delete_object(Bucket=organization.bucket_name, Key=file_path)
    except Exception as error:
        raise DeleteError(str(error))


class DeleteError(Exception):
    pass


def has_object_in_folder(organization, folder_path):
    try:
        client = get_client(organization)
        response = client.list_objects_v2(
            Bucket=organization.bucket_name, Prefix=folder_path
        )
        return "Contents" in response
    except Exception:
        return False


def has_object(organization, object_path):
    try:
        client = get_client(organization)
        response = client.list_objects_v2(
            Bucket=organization.bucket_name, Prefix=object_path
        )
        for obj in response.get("Contents", []):
            if obj["Key"] == object_path:
                return True
        return False
    except Exception:
        return False
