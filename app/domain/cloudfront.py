import datetime
import functools
import time
import urllib
import uuid
from datetime import timed<PERSON><PERSON>, timezone

import boto3
import six
from botocore.exceptions import ClientError
from botocore.signers import CloudFrontSigner
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding

OPTIMIZED_CACHE_POLICY_ID = "658327ea-f89d-4fab-a63d-7e88639e58f6"
CACHING_DISABLED_ID = "4135ea2d-6df8-44a3-9df3-4b5a84be39ad"


def create_cdn(organization):
    client = boto3.client(
        "cloudfront",
        aws_access_key_id=organization.cdn_access_key_id,
        aws_secret_access_key=organization.cdn_secret_access_key,
    )
    try:
        response = client.create_distribution(
            DistributionConfig=get_distribution_config(organization)
        )
    except ClientError as e:
        if e.response["Error"]["Code"] == "TooManyDistributionsAssociatedToKeyGroup":
            organization.refresh_cloudfront_key_group()
            response = client.create_distribution(
                DistributionConfig=get_distribution_config(organization)
            )
        else:
            raise e
    cdn_id = response.get("Distribution").get("Id")
    url = "https://{}/".format(response.get("Distribution").get("DomainName"))
    # During distribution creation if we use custom_cache_policy then we will get Quota Exceeded error.
    # That's why during creation we use default policy and we update it here
    update_custom_cache_policies(organization, cdn_id)
    return cdn_id, url


def get_distribution_config(organization):
    config = {
        "CallerReference": organization.uuid,
        "Comment": str(organization),
        "PriceClass": "PriceClass_All",
        "HttpVersion": "http2",
        "Enabled": True,
        "ViewerCertificate": {
            "CloudFrontDefaultCertificate": True,
        },
    }
    add_origins(config, organization)
    add_behaviors(config, organization)
    add_default_behavior(config, organization)
    return config


def add_origins(config, organization):
    origins: list[dict] = []
    add_static_origin(origins)
    add_live_stream_origin(origins)
    add_new_live_stream_origin(origins)
    add_live_stream_s3_origin(origins)
    add_organization_origin(origins, organization)
    config["Origins"] = {"Quantity": len(origins), "Items": origins}


def add_behaviors(config, organization):
    behaviors: list[dict] = []
    add_static_path_behavior(behaviors, organization)
    add_live_stream_path_behavior(behaviors)
    add_new_live_stream_path_behavior(behaviors)
    add_live_stream_s3_path_behavior(behaviors)
    add_transcoded_path_behaviour(behaviors, organization)
    add_transcoded_aes_video_paths_behavior(behaviors, organization)
    add_root_path_behavior(behaviors, organization)
    config["CacheBehaviors"] = {"Quantity": len(behaviors), "Items": behaviors}


def add_default_behavior(config, organization):
    behavior_config = {
        "CachePolicyId": OPTIMIZED_CACHE_POLICY_ID,
        "TargetOriginId": organization.uuid,
        "ViewerProtocolPolicy": "allow-all",
        "SmoothStreaming": False,
        "Compress": True,
    }
    add_allowed_methods(behavior_config)
    add_trusted_key_group(behavior_config, organization.cloudfront_key_group_id)
    config["DefaultCacheBehavior"] = behavior_config


def add_static_origin(origins):
    config = {
        "Id": "static.tpstreams.com",
        "DomainName": "s3.eu-central-1.wasabisys.com",
        "OriginPath": "/static.tpstreams.com",
    }
    add_custom_headers(config)
    add_custom_origin_config(config)
    origins.append(config)


def add_live_stream_origin(origins):
    config = {
        "Id": "live.tpstreams.com",
        "DomainName": "live.tpstreams.com",
        "OriginPath": "",
        "CustomHeaders": {"Quantity": 0, "Items": []},
        "CustomOriginConfig": {
            "HTTPPort": 80,
            "HTTPSPort": 443,
            "OriginProtocolPolicy": "http-only",
            "OriginSslProtocols": {"Quantity": 1, "Items": ["TLSv1"]},
            "OriginReadTimeout": 5,
            "OriginKeepaliveTimeout": 30,
        },
    }
    origins.append(config)


def add_new_live_stream_origin(origins):
    config = {
        "Id": "live2.tpstreams.com",
        "DomainName": "live2.tpstreams.com",
        "OriginPath": "",
        "CustomHeaders": {"Quantity": 0, "Items": []},
        "CustomOriginConfig": {
            "HTTPPort": 80,
            "HTTPSPort": 443,
            "OriginProtocolPolicy": "http-only",
            "OriginSslProtocols": {"Quantity": 1, "Items": ["TLSv1"]},
            "OriginReadTimeout": 5,
            "OriginKeepaliveTimeout": 30,
        },
    }
    origins.append(config)


def add_live_stream_s3_origin(origins):
    config = {
        "Id": "live-streaming-s3",
        "DomainName": "livestreaming-tpstreams.s3.ap-south-1.amazonaws.com",
        "OriginPath": "",
        "CustomHeaders": {"Quantity": 0, "Items": []},
        "S3OriginConfig": {
            "OriginAccessIdentity": "origin-access-identity/cloudfront/E16WDFTHV3SM2K"
        },
        "ConnectionAttempts": 3,
        "ConnectionTimeout": 10,
        "OriginShield": {"Enabled": False},
        "OriginAccessControlId": "",
    }
    origins.append(config)


def add_organization_origin(origins, organization):
    from app.models import Organization

    if organization.storage_vendor == Organization.StorageProvider.AWS:
        origin_access_id = get_origin_access_id(organization)
        config = {
            "Id": organization.uuid,
            "DomainName": f"{organization.bucket_name}.{organization.storage_domain}",
            "OriginPath": "",
            "S3OriginConfig": {
                "OriginAccessIdentity": f"origin-access-identity/cloudfront/{origin_access_id}"
            },
        }
    else:
        config = {
            "Id": organization.uuid,
            "DomainName": organization.storage_domain,
            "OriginPath": f"/{organization.bucket_name}",
        }
        add_custom_origin_config(config)
    add_custom_headers(config, organization.bucket_secret_token)
    origins.append(config)


def add_root_path_behavior(behaviors, organization):
    config = {
        "PathPattern": "*",
        "CachePolicyId": OPTIMIZED_CACHE_POLICY_ID,
        "TargetOriginId": organization.uuid,
        "Compress": True,
        "SmoothStreaming": False,
        "ViewerProtocolPolicy": "allow-all",
    }
    add_allowed_methods(config)
    add_trusted_key_group(config, organization.cloudfront_key_group_id)
    behaviors.append(config)


def add_transcoded_path_behaviour(behaviors, organization):
    config = {
        "PathPattern": "transcoded/*",
        "CachePolicyId": OPTIMIZED_CACHE_POLICY_ID,
        "TargetOriginId": organization.uuid,
        "Compress": True,
        "SmoothStreaming": False,
        "ViewerProtocolPolicy": "allow-all",
    }
    add_allowed_methods(config)
    behaviors.append(config)


def add_static_path_behavior(behaviors, organization):
    config = {
        "PathPattern": "static/*",
        "CachePolicyId": OPTIMIZED_CACHE_POLICY_ID,
        "TargetOriginId": "static.tpstreams.com",
        "Compress": True,
        "SmoothStreaming": False,
        "ViewerProtocolPolicy": "allow-all",
    }
    add_allowed_methods(config)
    add_trusted_key_group(config)
    behaviors.append(config)


def add_live_stream_path_behavior(behaviors):
    mpd_config = {
        "PathPattern": "live/*/*/*.mpd",
        "CachePolicyId": OPTIMIZED_CACHE_POLICY_ID,
        "TargetOriginId": "live2.tpstreams.com",
        "Compress": True,
        "SmoothStreaming": False,
        "ViewerProtocolPolicy": "allow-all",
        "FieldLevelEncryptionId": "",
        "LambdaFunctionAssociations": {"Quantity": 0},
    }
    add_allowed_methods(mpd_config)
    add_trusted_key_group(mpd_config)
    behaviors.append(mpd_config)

    m3u8_config = {
        "PathPattern": "live/*/*/*.m3u8",
        "CachePolicyId": OPTIMIZED_CACHE_POLICY_ID,
        "TargetOriginId": "live2.tpstreams.com",
        "Compress": True,
        "SmoothStreaming": False,
        "ViewerProtocolPolicy": "allow-all",
        "FieldLevelEncryptionId": "",
        "LambdaFunctionAssociations": {"Quantity": 0},
    }
    add_allowed_methods(m3u8_config)
    add_trusted_key_group(m3u8_config)
    behaviors.append(m3u8_config)

    ts_files_config = {
        "PathPattern": "live/*",
        "CachePolicyId": OPTIMIZED_CACHE_POLICY_ID,
        "TargetOriginId": "live2.tpstreams.com",
        "Compress": True,
        "SmoothStreaming": False,
        "ViewerProtocolPolicy": "allow-all",
        "FieldLevelEncryptionId": "",
        "LambdaFunctionAssociations": {"Quantity": 0},
    }
    add_allowed_methods(ts_files_config)
    add_trusted_key_group(ts_files_config)
    behaviors.append(ts_files_config)


def add_new_live_stream_path_behavior(behaviors):
    mpd_config = {
        "PathPattern": "live2/*/*/*.mpd",
        "CachePolicyId": OPTIMIZED_CACHE_POLICY_ID,
        "TargetOriginId": "live2.tpstreams.com",
        "Compress": True,
        "SmoothStreaming": False,
        "ViewerProtocolPolicy": "allow-all",
        "FieldLevelEncryptionId": "",
        "LambdaFunctionAssociations": {"Quantity": 0},
    }
    add_allowed_methods(mpd_config)
    add_trusted_key_group(mpd_config)
    behaviors.append(mpd_config)

    m3u8_config = {
        "PathPattern": "live2/*/*/*.m3u8",
        "CachePolicyId": OPTIMIZED_CACHE_POLICY_ID,
        "TargetOriginId": "live2.tpstreams.com",
        "Compress": True,
        "SmoothStreaming": False,
        "ViewerProtocolPolicy": "allow-all",
        "FieldLevelEncryptionId": "",
        "LambdaFunctionAssociations": {"Quantity": 0},
    }
    add_allowed_methods(m3u8_config)
    add_trusted_key_group(m3u8_config)
    behaviors.append(m3u8_config)

    ts_files_config = {
        "PathPattern": "live2/*",
        "CachePolicyId": OPTIMIZED_CACHE_POLICY_ID,
        "TargetOriginId": "live2.tpstreams.com",
        "Compress": True,
        "SmoothStreaming": False,
        "ViewerProtocolPolicy": "allow-all",
        "FieldLevelEncryptionId": "",
        "LambdaFunctionAssociations": {"Quantity": 0},
    }
    add_allowed_methods(ts_files_config)
    add_trusted_key_group(ts_files_config)
    behaviors.append(ts_files_config)


def add_live_stream_s3_path_behavior(behaviors):
    mpd_config = {
        "PathPattern": "live_streams/*/*/*.mpd",
        "CachePolicyId": OPTIMIZED_CACHE_POLICY_ID,
        "TargetOriginId": "live-streaming-s3",
        "Compress": True,
        "SmoothStreaming": False,
        "ViewerProtocolPolicy": "allow-all",
        "FieldLevelEncryptionId": "",
        "LambdaFunctionAssociations": {"Quantity": 0},
    }
    add_allowed_methods(mpd_config)
    add_trusted_key_group(mpd_config)
    behaviors.append(mpd_config)

    m3u8_config = {
        "PathPattern": "live_streams/*/*/*.m3u8",
        "CachePolicyId": OPTIMIZED_CACHE_POLICY_ID,
        "TargetOriginId": "live-streaming-s3",
        "Compress": True,
        "SmoothStreaming": False,
        "ViewerProtocolPolicy": "allow-all",
        "FieldLevelEncryptionId": "",
        "LambdaFunctionAssociations": {"Quantity": 0},
    }
    add_allowed_methods(m3u8_config)
    add_trusted_key_group(m3u8_config)
    behaviors.append(m3u8_config)

    ts_files_config = {
        "PathPattern": "live_streams/*",
        "TargetOriginId": "live-streaming-s3",
        "TrustedSigners": {"Enabled": False, "Quantity": 0},
        "ViewerProtocolPolicy": "allow-all",
        "SmoothStreaming": False,
        "Compress": True,
        "LambdaFunctionAssociations": {"Quantity": 0},
        "FunctionAssociations": {"Quantity": 0},
        "FieldLevelEncryptionId": "",
        "CachePolicyId": OPTIMIZED_CACHE_POLICY_ID,
    }
    add_allowed_methods(ts_files_config)
    add_trusted_key_group(ts_files_config)
    behaviors.append(ts_files_config)


def add_transcoded_aes_video_paths_behavior(behaviors, organization):
    resolution_playlist_path_behavior = {
        "PathPattern": "transcoded_private/*/*/*.m3u8",
        "CachePolicyId": OPTIMIZED_CACHE_POLICY_ID,
        "TargetOriginId": organization.uuid,
        "Compress": True,
        "SmoothStreaming": False,
        "ViewerProtocolPolicy": "allow-all",
        "FieldLevelEncryptionId": "",
    }
    add_allowed_methods(resolution_playlist_path_behavior)

    master_playlist_path_behaviour = {
        "PathPattern": "transcoded_private/*/*.m3u8",
        "CachePolicyId": OPTIMIZED_CACHE_POLICY_ID,
        "TargetOriginId": organization.uuid,
        "Compress": True,
        "SmoothStreaming": False,
        "ViewerProtocolPolicy": "allow-all",
        "FieldLevelEncryptionId": "",
    }
    add_allowed_methods(master_playlist_path_behaviour)
    add_trusted_key_group(
        master_playlist_path_behaviour, organization.cloudfront_key_group_id
    )

    folder_behaviour = {
        "PathPattern": "transcoded_private/*",
        "CachePolicyId": OPTIMIZED_CACHE_POLICY_ID,
        "TargetOriginId": organization.uuid,
        "Compress": True,
        "SmoothStreaming": False,
        "ViewerProtocolPolicy": "allow-all",
        "FieldLevelEncryptionId": "",
    }
    add_allowed_methods(folder_behaviour)

    behaviors.append(resolution_playlist_path_behavior)
    behaviors.append(master_playlist_path_behaviour)
    behaviors.append(folder_behaviour)


def add_custom_headers(config, bucket_secret_token=None):
    items: list[dict] = []
    add_cors_headers(items)
    add_origin_headers(items)
    add_vary_headers(items)

    if bucket_secret_token:
        add_referer_header(items, bucket_secret_token)

    config["CustomHeaders"] = {"Quantity": len(items), "Items": items}


def add_custom_origin_config(config):
    config["CustomOriginConfig"] = {
        "HTTPPort": 80,
        "HTTPSPort": 443,
        "OriginProtocolPolicy": "https-only",
        "OriginSslProtocols": {"Quantity": 1, "Items": ["TLSv1"]},
        "OriginReadTimeout": 5,
        "OriginKeepaliveTimeout": 30,
    }


def add_cors_headers(headers):
    config = {"HeaderName": "Access-Control-Allow-Origin", "HeaderValue": "*"}
    headers.append(config)


def add_origin_headers(headers):
    config = {"HeaderName": "Origin", "HeaderValue": "https://app.tpstreams.com"}
    headers.append(config)


def add_vary_headers(headers):
    config = {
        "HeaderName": "Vary",
        "HeaderValue": "Origin, Access-Control-Request-Headers, Access-Control-Request-Method",
    }
    headers.append(config)


def add_referer_header(headers, bucket_secret_token):
    config = {"HeaderName": "Referer", "HeaderValue": bucket_secret_token}
    headers.append(config)


def add_allowed_methods(config):
    methods = ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"]
    config["AllowedMethods"] = {"Quantity": len(methods), "Items": methods}
    add_cached_methods(config["AllowedMethods"])


def add_cached_methods(config):
    methods = ["HEAD", "GET"]
    config["CachedMethods"] = {
        "Quantity": len(methods),
        "Items": methods,
    }


def add_trusted_key_group(config, key_group_id=None):
    if key_group_id:
        config["TrustedKeyGroups"] = {
            "Enabled": True,
            "Quantity": 1,
            "Items": [key_group_id],
        }
    else:
        config["TrustedKeyGroups"] = {"Enabled": False, "Quantity": 0}


def create_key_group(organization):
    client = boto3.client(
        "cloudfront",
        aws_access_key_id=organization.cdn_access_key_id,
        aws_secret_access_key=organization.cdn_secret_access_key,
    )
    response = client.create_key_group(
        KeyGroupConfig={
            "Name": uuid.uuid4().hex,
            "Items": [
                organization.cdn_public_key_id,
            ],
            "Comment": "",
        }
    )
    return response["KeyGroup"]["Id"]


def update_custom_cache_policies(organization, cdn_id):
    client = boto3.client(
        "cloudfront",
        aws_access_key_id=organization.cdn_access_key_id,
        aws_secret_access_key=organization.cdn_secret_access_key,
    )
    response = client.get_distribution_config(Id=cdn_id)
    distribution_config = response.get("DistributionConfig")
    etag = response.get("ETag")
    for cache_behavior in distribution_config["CacheBehaviors"]["Items"]:
        MANIFEST_PATHS = [
            "live/*/*/*.m3u8",
            "live2/*/*/*.m3u8",
            "live_streams/*/*/*.m3u8",
            "live_streams/*/*/*.mpd",
            "live/*/*/*.mpd",
            "live2/*/*/*.mpd",
        ]
        if cache_behavior["PathPattern"] in MANIFEST_PATHS:
            cache_behavior[
                "CachePolicyId"
            ] = organization.cdn_expire_in_3_seconds_cache_policy_id
        elif cache_behavior["CachePolicyId"] == OPTIMIZED_CACHE_POLICY_ID:
            cache_behavior["CachePolicyId"] = organization.cdn_one_year_cache_policy_id
    client.update_distribution(
        Id=cdn_id, DistributionConfig=distribution_config, IfMatch=etag
    )


def get_bandwidth_used(organization, from_date, to_date):
    client = boto3.client(
        "cloudwatch",
        aws_access_key_id=organization.cdn_access_key_id,
        aws_secret_access_key=organization.cdn_secret_access_key,
        region_name="us-east-1",
    )
    response = client.get_metric_data(
        MetricDataQueries=[get_bytes_downloaded_metric_data(organization.cdn_id)],
        StartTime=datetime.datetime.strptime(from_date, "%Y-%m-%d-%H-%M-%S"),
        EndTime=datetime.datetime.strptime(to_date, "%Y-%m-%d-%H-%M-%S"),
        ScanBy="TimestampDescending",
    )
    return sum(response.get("MetricDataResults")[0].get("Values", []))


def get_bytes_downloaded_metric_data(cdn_id):
    return {
        "Id": "media",
        "MetricStat": {
            "Metric": {
                "Namespace": "AWS/CloudFront",
                "MetricName": "BytesDownloaded",
                "Dimensions": [
                    {"Name": "DistributionId", "Value": cdn_id},
                    {"Name": "Region", "Value": "Global"},
                ],
            },
            "Period": 86400,
            "Stat": "Sum",
            "Unit": "None",
        },
        "ReturnData": True,
    }


def get_origin_access_id(organization):
    client = get_cloudfront_client(organization)

    response = client.list_cloud_front_origin_access_identities()
    origin_access_ids = response["CloudFrontOriginAccessIdentityList"]["Items"]
    return origin_access_ids[0]["Id"]


def get_cloudfront_client(organization):
    return boto3.client(
        "cloudfront",
        aws_access_key_id=organization.cdn_access_key_id,
        aws_secret_access_key=organization.cdn_secret_access_key,
    )


THREE_DAYS = 3 * 24 * 60 * 60


def generate_presigned_url(
    organization, path, download=False, download_as=None, expires_in=THREE_DAYS
):
    url = get_cdn_url(organization, path, download, download_as)
    private_key = load_private_key(organization.cdn_private_key)
    expiration = datetime.datetime.now(timezone.utc) + timedelta(seconds=expires_in)
    cloudfront_signer = create_cloudfront_signer(
        organization.cdn_public_key_id, private_key
    )
    signed_url = cloudfront_signer.generate_presigned_url(
        url, date_less_than=expiration
    )
    return signed_url


def get_cdn_url(organization, path, download, download_as):
    url = f"{organization.cdn_url}{path}"
    if download:
        url = get_cdn_url_with_download_params(url, download_as)
    return url


def get_cdn_url_with_download_params(url, download_as):
    url += "?response-content-disposition=attachment"
    filename = urllib.parse.quote_plus("; filename=" + download_as)
    url += filename
    return url


def load_private_key(cdn_private_key):
    private_key = serialization.load_pem_private_key(
        six.ensure_binary(cdn_private_key, encoding="utf-8"),
        password=None,
        backend=default_backend(),
    )
    return private_key


def create_cloudfront_signer(public_key_id, private_key):
    signer = functools.partial(
        private_key.sign, padding=padding.PKCS1v15(), algorithm=hashes.SHA1()
    )
    cloudfront_signer = CloudFrontSigner(public_key_id, signer)
    return cloudfront_signer


def invalidate_cache(organization, paths=None):
    cloudfront_client = get_cloudfront_client(organization)
    if not paths:
        paths = ["/*"]
    response = cloudfront_client.create_invalidation(
        DistributionId=organization.cdn_id,
        InvalidationBatch={
            "Paths": {"Quantity": len(paths), "Items": paths},
            "CallerReference": str(time.time()),
        },
    )
    return response


def configure_distribution_for_testpress_livestream(organization):
    client = boto3.client(
        "cloudfront",
        aws_access_key_id=organization.cdn_access_key_id,
        aws_secret_access_key=organization.cdn_secret_access_key,
    )
    response = client.get_distribution_config(Id=organization.cdn_id)
    distribution_config = response["DistributionConfig"]
    existing_origins = distribution_config["Origins"]["Items"]
    existing_behaviors = distribution_config["CacheBehaviors"]["Items"]
    path_to_remove = "live_streams/*"
    existing_behaviors = [
        b for b in existing_behaviors if b.get("PathPattern") != path_to_remove
    ]

    update_testpress_livestream_distribution_config(
        distribution_config, existing_origins, existing_behaviors
    )

    client.update_distribution(
        Id=organization.cdn_id,
        DistributionConfig=distribution_config,
        IfMatch=response.get("ETag"),
    )
    update_custom_cache_policies(organization, organization.cdn_id)


def update_testpress_livestream_distribution_config(
    distribution_config, existing_origins, existing_behaviors
):
    add_live_stream_origin(existing_origins)
    add_live_stream_s3_origin(existing_origins)
    add_new_live_stream_origin(existing_origins)
    add_live_stream_path_behavior(existing_behaviors)
    add_live_stream_s3_path_behavior(existing_behaviors)
    add_new_live_stream_path_behavior(existing_behaviors)
    existing_behaviors.sort(key=lambda x: x.get("PathPattern") == "*")
    distribution_config["Origins"]["Quantity"] = len(existing_origins)
    distribution_config["Origins"]["Items"] = existing_origins
    distribution_config["CacheBehaviors"]["Quantity"] = len(existing_behaviors)
    distribution_config["CacheBehaviors"]["Items"] = existing_behaviors
