import datetime

from app.models import AssetUsage, LiveStreamUsage
from app.utils.datetime import get_month_end_date


def get_live_stream_usage(organization, from_date, to_date):
    usage_objects = LiveStreamUsage.objects.filter(
        end_time__date__range=(from_date, to_date),
        organization=organization,
    )

    total_seconds = 0

    for usage in usage_objects:
        end_time = usage.end_time or usage.live_stream.end
        if usage.start_time and end_time:
            duration = end_time - usage.start_time
            total_seconds += int(duration.total_seconds())

    return total_seconds


def update_daily_live_stream_usage(organization, date):
    from_date = date

    to_date = datetime.datetime(
        from_date.year, from_date.month, from_date.day, 23, 59, 59
    )
    daily_usage = get_live_stream_usage(
        organization,
        from_date,
        to_date,
    )
    save_live_stream_usage(organization, date, daily_usage)

    return daily_usage


def update_monthly_live_stream_usage(organization, date):
    month_first_date = datetime.date(date.year, date.month, 1)
    month_last_date = get_month_end_date(date)
    to_date = datetime.datetime(
        month_last_date.year, month_last_date.month, month_last_date.day, 23, 59, 59
    )
    monthly_usage = get_live_stream_usage(organization, month_first_date, to_date)
    save_live_stream_usage(
        organization,
        month_first_date,
        monthly_usage,
        time_frame=AssetUsage.TimeFrames.MONTHLY,
    )

    return monthly_usage


def save_live_stream_usage(
    organization,
    date,
    live_stream_usage,
    time_frame=AssetUsage.TimeFrames.DAILY,
):
    AssetUsage.objects.update_or_create(
        date=date,
        organization=organization,
        time_frame=time_frame,
        defaults={
            "live_stream_usage": live_stream_usage,
        },
    )
