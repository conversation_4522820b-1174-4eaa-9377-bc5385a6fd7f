import requests
import sentry_sdk
from django.conf import settings

from app.domain.aws import AWSOpenRestyServer

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Basic {settings.TPSTREAMS_AWS_OPENRESTY_PROXY_API_KEY}",
}


def create_openresty_proxy_server(
    name,
    server_type="r5a.large",
    image_id=settings.TPSTREAMS_AWS_DOCKER_INSTALLED_AMI,
    region_name="ap-south-1",
    security_group_id=settings.TPSTREAMS_AWS_PROXY_SECURITY_GROUP_ID,
    volume_size=50,
    volume_type="gp3",
):
    try:
        server = AWSOpenRestyServer(
            server_type=server_type,
            image_id=image_id,
            region_name=region_name,
            security_group_id=security_group_id,
            volume_size=volume_size,
            volume_type=volume_type,
        )
        return server.create_server(name)
    except Exception as e:
        sentry_sdk.capture_exception(e)
        return


def add_ip_address_to_openresty(ip_address, asset):
    path_rule = f"/live2/{asset.organization.uuid}/{asset.uuid}"
    url = f"{settings.TPSTREAMS_AWS_OPENRESTY_PROXY_URL}/api/add_path"
    payload = {"path_rule": path_rule, "ip_address": ip_address}

    try:
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()
    except Exception as e:
        sentry_sdk.capture_exception(e)
    return response


def remove_ip_address_from_openresty(asset):
    path_rule = f"/live2/{asset.organization.uuid}/{asset.uuid}"
    url = f"{settings.TPSTREAMS_AWS_OPENRESTY_PROXY_URL}/api/remove_path"
    payload = {"path_rule": path_rule}

    try:
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()
    except Exception as e:
        sentry_sdk.capture_exception(e)
    return response


def add_ip_address_to_linode_openresty(ip_address, asset):
    path_rule = f"/live_linode/{asset.organization.uuid}/{asset.uuid}"
    url = f"{settings.TPSTREAMS_LINODE_OPENRESTY_PROXY_URL}/api/add_path"
    payload = {"path_rule": path_rule, "ip_address": ip_address}

    try:
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()
    except Exception as e:
        sentry_sdk.capture_exception(e)
    return response


def remove_ip_address_from_linode_openresty(asset):
    path_rule = f"/live_linode/{asset.organization.uuid}/{asset.uuid}"
    url = f"{settings.TPSTREAMS_LINODE_OPENRESTY_PROXY_URL}/api/remove_path"
    payload = {"path_rule": path_rule}

    try:
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()
    except Exception as e:
        sentry_sdk.capture_exception(e)
    return response


def add_ip_address_to_digitalocean_openresty(ip_address, asset):
    path_rule = f"{asset.organization.uuid}/{asset.uuid}"
    url = f"{settings.TPSTREAMS_DIGITALOCEAN_OPENRESTY_PROXY_URL}/api/add_path"
    payload = {"path_rule": path_rule, "ip_address": ip_address}

    try:
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()
    except Exception as e:
        sentry_sdk.capture_exception(e)
    return response


def remove_ip_address_from_digitalocean_openresty(asset):
    path_rule = f"{asset.organization.uuid}/{asset.uuid}"
    url = f"{settings.TPSTREAMS_DIGITALOCEAN_OPENRESTY_PROXY_URL}/api/remove_path"
    payload = {"path_rule": path_rule}

    try:
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()
    except Exception as e:
        sentry_sdk.capture_exception(e)
    return response
