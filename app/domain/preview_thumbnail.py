def create_preview_thumbnail_track(
    asset,
    rows,
    columns,
    interval,
    height,
    width,
    url,
):
    from app.models import PreviewThumbnail, Track

    preview_thumbnail, created = PreviewThumbnail.objects.update_or_create(
        url=url,
        organization=asset.organization,
        rows=rows,
        columns=columns,
        interval=interval,
        height=height,
        width=width,
    )

    track, created = Track.objects.update_or_create(
        video=asset.video,
        organization=asset.organization,
        type=Track.Type.PREVIEW_THUMBNAIL,
        preview_thumbnail=preview_thumbnail,
    )
    return track, created
