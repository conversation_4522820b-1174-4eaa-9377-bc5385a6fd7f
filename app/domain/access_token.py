import json
from datetime import timedelta

from django.conf import settings
from django.core.cache import cache
from django.utils.timezone import now

from app.models import AccessToken, AccessTokenAnnotation
from app.utils.uuid import is_valid_uuid

SEVEN_DAYS = 5 * 24 * 60 * 60
FALLBACK_TOKEN_UUID = "33e345b1-9f22-4382-8c5c-7e49a75d8027"


def get_or_create_access_token_without_validity(asset):
    return get_or_create_access_token_without_validity_from_db(asset)


def get_or_create_access_token_without_validity_from_db(asset):
    from app.models import AccessToken

    access_tokens = AccessToken.objects.filter(
        asset=asset,
        expires_after_first_usage=False,
        valid_until__isnull=True,
        organization=asset.organization,
    )
    if access_tokens.exists():
        return access_tokens.first()
    return generate_access_token_from_db(asset=asset)


def get_access_token(org_id, asset_uuid, token_uuid=None):
    from app.models import Asset

    asset = Asset.objects.filter(uuid=asset_uuid).first()
    key = cache_key_for_access_token(org_id, asset_uuid, token_uuid)
    if not key and org_id not in ["agg625", "4u66uy"]:
        return generate_fallback_access_token(
            asset.organization.id, asset.id, token_uuid
        )

    data = cache.get(key)
    if data:
        annotations = get_access_token_annotations_from_cache(
            org_id, asset_uuid, token_uuid
        )
        access_token = AccessToken.from_dict(json.loads(data))
        if annotations:
            access_token._annotations = annotations
        return access_token
    if org_id not in ["agg625", "4u66uy"]:
        return generate_fallback_access_token(
            asset.organization.id, asset.id, token_uuid
        )
    return None


def generate_fallback_access_token(org_id, asset_id, token_uuid=None):
    """Generate a fallback access token as a default."""
    return AccessToken(
        uuid=FALLBACK_TOKEN_UUID,
        asset_id=asset_id,
        expires_after_first_usage=False,
        organization_id=org_id,
    )


def get_all_access_tokens_from_cache(org_id, asset_uuid, token_uuid=None):
    from app.models import Asset

    asset = Asset.objects.filter(uuid=asset_uuid).first()
    keys = cache.keys(f"org_id_{org_id}_asset_id_{asset_uuid}_token_*")
    tokens = []
    if keys:
        for key in keys:
            data = cache.get(key)
            if data:
                tokens.append(AccessToken.from_dict(json.loads(data)))
    return (
        tokens
        if tokens
        else [generate_fallback_access_token(asset.organization.id, asset.id)]
    )


def cache_key_for_access_token(org_id, asset_uuid, token_uuid):
    if not token_uuid:
        keys = cache.keys(f"org_id_{org_id}_asset_id_{asset_uuid}_token_*")
        if not keys:
            return None
        if len(keys) > 0:
            return keys[0]
    return f"org_id_{org_id}_asset_id_{asset_uuid}_token_{token_uuid}"


def get_access_token_annotations_from_cache(org_id, asset_uuid, token_uuid):
    key = cache_key_for_access_token_annotations(org_id, asset_uuid, token_uuid)
    data = cache.get(key)
    if data:
        return [AccessTokenAnnotation.from_dict(item) for item in json.loads(data)]
    return []


def cache_key_for_access_token_annotations(org_id, asset_uuid, token_uuid):
    return (
        f"org_id_{org_id}_asset_id_{asset_uuid}_access_token_annotations_{token_uuid}"
    )


def generate_access_token(asset, time_to_live=None, expires_after_first_usage=False):
    access_token = AccessToken(
        asset=asset,
        expires_after_first_usage=expires_after_first_usage,
        organization=asset.organization,
    )
    if time_to_live is not None:
        valid_until = now() + timedelta(seconds=time_to_live)
        access_token.valid_until = valid_until

    store_access_token_in_cache(access_token, time_to_live)
    return access_token


def generate_access_token_from_db(
    asset, time_to_live=None, expires_after_first_usage=False
):
    access_token = AccessToken.objects.create(
        asset=asset,
        expires_after_first_usage=expires_after_first_usage,
        organization=asset.organization,
    )
    access_token.update_validity(time_to_live)
    return access_token


def store_access_token_in_cache(access_token, time_to_live):
    key = cache_key_for_access_token(
        access_token.organization.uuid, access_token.asset.uuid, access_token.uuid
    )
    timeout = None if time_to_live is None else int(time_to_live) + SEVEN_DAYS
    cache.set(key, json.dumps(access_token.to_dict()), timeout)


def validate_access_token(asset, token):
    if not is_valid_uuid(token):
        return False

    access_token = get_access_token(asset.organization.uuid, asset.uuid, token)

    if access_token:
        return access_token.is_active

    # Store access_token from db to cache
    if AccessToken.objects.filter(uuid=token, asset=asset).exists():
        access_token = cache_db_access_token(token)
        return access_token.is_active
    return False


def store_access_token_annotations_in_cache(access_token, annotations, time_to_live):
    organization = access_token.organization
    asset = access_token.asset
    key = cache_key_for_access_token_annotations(
        organization.uuid, asset.uuid, access_token.uuid
    )
    timeout = None if time_to_live is None else int(time_to_live) + SEVEN_DAYS
    cache.set(key, json.dumps([ann.to_dict() for ann in annotations]), timeout)


def expire_single_usage_access_token(asset, token_uuid):
    if asset.organization.uuid not in settings.USE_ACCESS_TOKEN_FROM_CACHE:
        return

    access_token = get_access_token(asset.organization.uuid, asset.uuid, token_uuid)
    if access_token and access_token.expires_after_first_usage:
        update_access_token_expiry(access_token, now())


def update_access_token_expiry(access_token, expiry_time):
    access_token.valid_until = expiry_time
    time_to_live = int((access_token.valid_until - now()).total_seconds())
    store_access_token_in_cache(access_token, time_to_live)


def get_annotations_for_token(asset, token):
    access_token = get_access_token(asset.organization.uuid, asset.uuid, token)
    if access_token and hasattr(access_token, "_annotations"):
        return access_token._annotations
    return []


def cache_db_access_token(token_uuid):
    access_token = AccessToken.objects.filter(uuid=token_uuid).first()

    time_to_live = None
    if access_token.valid_until:
        time_to_live = int((access_token.valid_until - now()).total_seconds())
    store_access_token_in_cache(access_token, time_to_live)
    store_access_token_annotations_in_cache(
        access_token, access_token.annotations.all(), time_to_live
    )
    access_token._annotations = access_token.annotations
    return access_token
