import datetime

from safedelete.models import HARD_DELETE
import sentry_sdk

from app.domain.cloud_storage import delete_folder


def purge_assets_marked_for_deletion(days=30):
    from app.models import Asset

    threshold_date = datetime.datetime.today() - datetime.timedelta(days=days)
    assets_to_delete = Asset.objects.deleted_only().filter(deleted__lte=threshold_date)
    for asset in assets_to_delete:
        delete_asset(asset)


def delete_asset(asset):
    try:
        video = getattr(asset, "video", None)
        if video:
            delete_video(video)
        asset.delete(force_policy=HARD_DELETE)
    except Exception as e:
        sentry_sdk.capture_exception(f"Error in delete_asset for asset ID {asset.id}: {e}")


def delete_video(video):
    from app.models import Video
    try:
        video.status = Video.Status.DELETING
        video.save(update_fields=["status"])
        delete_source_folder(video)
        delete_transcoded_folder(video)
    except Exception as e:
        sentry_sdk.capture_exception(f"Error in delete_video for video ID {video.id}: {e}")


def delete_source_folder(video):
    source_folder_paths = get_deleted_source_folder_paths(video)
    if source_folder_paths:
        for path in source_folder_paths:
            delete_folder(video.asset.organization, path)


def get_deleted_source_folder_paths(video):
    from app.models import VideoInput

    source_folder_paths = []
    source_videos = VideoInput.objects.deleted_only().filter(video__asset=video.asset)
    for video in source_videos:
        source_folder_paths.append(video.url)

    return source_folder_paths


def delete_transcoded_folder(video):
    transcoded_folder_path = "transcoded/" + str(video.asset.uuid)
    delete_folder(video.asset.organization, transcoded_folder_path)
