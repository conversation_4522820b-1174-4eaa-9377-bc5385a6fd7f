import json
import re
from abc import ABC
from dataclasses import dataclass
from urllib.parse import urlparse

import requests
from django.conf import settings
from django.urls import reverse

from app.domain.drm.encryption.streams import (
    generate_and_store_fairplay_encryption_keys,
)
from app.models import TranscodingJob, Video
from app.utils.requests import post


@dataclass(frozen=True)
class Resolution(ABC):
    name: str
    width: int
    height: int
    video_bitrate: int
    max_video_bitrate: int
    bitrate_buffer_size: int
    crf: int
    audio_bitrate: int
    fps: int

    @classmethod
    def get_output_settings(cls, video_codec="h264", audio_codec="aac"):
        video_settings = {
            "codec": "libx265" if video_codec == "h265" else "h264",
            "width": cls.width,
            "height": cls.height,
            "preset": "faster",
        }

        if hasattr(cls, "video_bitrate"):
            video_settings["bitrate"] = cls.video_bitrate

        if hasattr(cls, "bitrate_buffer_size"):
            video_settings["bitrate_buffer_size"] = cls.bitrate_buffer_size

        if hasattr(cls, "max_video_bitrate"):
            video_settings["max_video_bitrate"] = cls.max_video_bitrate

        if hasattr(cls, "crf"):
            video_settings["crf"] = cls.crf

        if hasattr(cls, "fps"):
            video_settings["fps"] = cls.fps

        return {
            "name": cls.name + "_" + video_codec,
            "audio": {
                "codec": audio_codec,
                "bitrate": cls.audio_bitrate,
            },
            "video": video_settings,
        }

    def __str__(self):
        return {
            "name": self.name,
            "width": self.width,
            "height": self.height,
        }


class VBR240p(Resolution):
    name = "240p"
    width = 426
    height = 240
    audio_bitrate = 72000
    max_video_bitrate = 192000
    bitrate_buffer_size = 192000
    crf = 26
    fps = 24


class VBR360p(Resolution):
    name = "360p"
    width = 640
    height = 360
    audio_bitrate = 72000
    max_video_bitrate = 400000
    bitrate_buffer_size = 400000
    crf = 26
    fps = 24


class VBR480p(Resolution):
    name = "480p"
    width = 854
    height = 480
    audio_bitrate = 128000
    max_video_bitrate = 500000
    bitrate_buffer_size = 500000
    crf = 26
    fps = 24


class VBR720p(Resolution):
    name = "720p"
    width = 1280
    height = 720
    audio_bitrate = 128000
    max_video_bitrate = 1000000
    bitrate_buffer_size = 1000000
    crf = 26
    fps = 24


class High720p(Resolution):
    name = "720p"
    width = 1280
    height = 720
    audio_bitrate = 128000
    max_video_bitrate = 2000000
    bitrate_buffer_size = 2000000
    crf = 26
    fps = 24


class TestVBR720p(Resolution):
    name = "720p"
    width = 1280
    height = 720
    audio_bitrate = 128000
    max_video_bitrate = 700000
    bitrate_buffer_size = 700000
    crf = 28
    fps = 24


class VBR1080p(Resolution):
    name = "1080p"
    width = 1920
    height = 1080
    audio_bitrate = 128000
    max_video_bitrate = 2500000
    bitrate_buffer_size = 2500000
    crf = 26
    fps = 24


class BIOMENTORSVBR1080p(Resolution):
    name = "1080p"
    width = 1920
    height = 1080
    audio_bitrate = 128000
    max_video_bitrate = 1750000
    bitrate_buffer_size = 1750000
    crf = 26
    fps = 24


class VBR4k(Resolution):
    name = "4k"
    width = 3840
    height = 2160
    audio_bitrate = 192000
    max_video_bitrate = 4000000
    bitrate_buffer_size = 4000000
    crf = 26
    fps = 24


VBR_RESOLUTION_MAP = {
    Video.Resolutions._240p.value: VBR240p,
    Video.Resolutions._360p.value: VBR360p,
    Video.Resolutions._480p.value: VBR480p,
    Video.Resolutions._720p.value: VBR720p,
    Video.Resolutions._1080p.value: VBR1080p,
    Video.Resolutions._4k.value: VBR4k,
}


TEST_VBR_RESOLUTION_MAP = {
    Video.Resolutions._240p.value: VBR240p,
    Video.Resolutions._360p.value: VBR360p,
    Video.Resolutions._480p.value: VBR480p,
    Video.Resolutions._720p.value: TestVBR720p,
    Video.Resolutions._1080p.value: VBR1080p,
    Video.Resolutions._4k.value: VBR4k,
}


BIO_MENTORS_RESOLUTION_MAP = {
    Video.Resolutions._240p.value: VBR240p,
    Video.Resolutions._360p.value: VBR360p,
    Video.Resolutions._480p.value: VBR480p,
    Video.Resolutions._720p.value: VBR720p,
    Video.Resolutions._1080p.value: BIOMENTORSVBR1080p,
    Video.Resolutions._4k.value: VBR4k,
}


class LumberjackAssetTranscoder:
    def __init__(self, asset):
        self.asset = asset

    def start(
        self,
        extra_settings=None,
        transcoding_queue_name=None,
        preserve_existing_transcodings=False,
    ):
        url = f"{settings.TRANSCODER_URL}/create/"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Token {settings.LUMBERJACK_AUTH_TOKEN}",
        }
        response = post(
            url,
            data=self.settings(
                extra_settings=extra_settings,
                transcoding_queue_name=transcoding_queue_name,
                preserve_existing_transcodings=preserve_existing_transcodings,
            ),
            headers=headers,
        )
        return json.loads(response.content)

    def settings(
        self,
        extra_settings=None,
        transcoding_queue_name=None,
        preserve_existing_transcodings=None,
    ):
        video = self.asset.video
        outputs = self.get_resolution_settings()
        config = self._get_settings_for_outputs(outputs)
        if video.is_drm_encrypted:
            config.update({"drm_encryption": self._get_drm_settings()})
            config["settings"]["format"] = "hls_and_dash"

        if video.is_aes_encrypted:
            config.update(self._get_aes_settings())

        if extra_settings:
            config.update(extra_settings)

        if transcoding_queue_name:
            config["settings"]["task_queue_name"] = transcoding_queue_name

        if preserve_existing_transcodings:
            video_path = (
                f"{self.asset.organization.bucket_url}/transcoded/{self.asset.uuid}/"
            )
            if not video.video_codecs == [Video.VideoCodec.H265]:
                video_path = f"{self.asset.organization.bucket_url}/transcoded/{self.asset.uuid}/new/"

            config["output_url"] = video_path
            config["webhook_url"] = settings.SITE_URL + reverse(
                "api:lumberjack-migrated-video-callback",
                kwargs={
                    "asset_id": self.asset.uuid,
                    "organization_id": self.asset.organization.uuid,
                },
            )

        metadata = self._get_metadata()
        config.update({"meta_data": json.dumps(metadata)})
        return config

    def get_resolution_settings(self):
        video = self.asset.video
        outputs = []
        for resolution in video.resolutions:
            res = VBR_RESOLUTION_MAP[resolution]

            if (
                self.asset.organization.uuid in ["f87hk3", "h8g4g4", "mct2nn", "73sqtk"]
                and resolution == Video.Resolutions._720p.value
            ):
                res = High720p

            if (
                self.asset.organization.uuid == "dcek2m"
                or self.asset.organization.uuid == "hd66qr"
                or self.asset.organization.uuid == "n2nbhp"
                or self.asset.organization.uuid == "6332n7"
            ):
                res = TEST_VBR_RESOLUTION_MAP[resolution]

            if self.asset.organization.uuid == "m9n4m6":
                res = BIO_MENTORS_RESOLUTION_MAP[resolution]
            audio_codec = video.get_audio_codec_display().lower()

            if not video.video_codecs:
                video.video_codecs = [Video.VideoCodec.H264]

            if Video.VideoCodec.H264 in video.video_codecs:
                output_settings = res.get_output_settings(
                    Video.VideoCodec.H264.label, audio_codec
                )
                outputs.append(output_settings)

            if Video.VideoCodec.H265 in video.video_codecs:
                output_settings = res.get_output_settings(
                    Video.VideoCodec.H265.label, audio_codec
                )
                outputs.append(output_settings)
        return outputs

    def _get_drm_settings(self):
        return {
            "fairplay": self.get_fairplay_drm_settings(),
            "widevine": self.get_widevine_drm_settings(),
        }

    def _get_aes_settings(self):
        aes_encryption_key = self.asset.encryption_key.aes_encryption_key.hex
        aes_encryption_key_url = self._get_aes_encryption_key_url()

        return {"encryption_key": aes_encryption_key, "key_url": aes_encryption_key_url}

    def _get_aes_encryption_key_url(self):
        organization_uuid = self.asset.organization.uuid
        aes_encryption_key_api_path = reverse(
            "api:get_aes_key",
            kwargs={"organization_id": organization_uuid, "asset_id": self.asset.uuid},
        )
        return f"{settings.SITE_URL}{aes_encryption_key_api_path}"

    def get_widevine_drm_settings(self):
        return {
            "signer": settings.DRM_SIGNER,
            "content_id": self.asset.video.uuid.hex,
            "output_name": "video.mpd",
            "aes_signing_iv": self.asset.organization.drm_aes_signing_iv,
            "key_server_url": self.get_widevine_content_key_url(),
            "aes_signing_key": self.asset.organization.drm_aes_signing_key,
        }

    def get_widevine_content_key_url(self):
        license_url = settings.SITE_URL + reverse(
            "api:generate-widevine-key",
            kwargs={
                "organization_id": self.asset.organization.uuid,
            },
        )
        return license_url

    def get_fairplay_drm_settings(self):
        video = self.asset.video
        fairplay_key_data = generate_and_store_fairplay_encryption_keys(
            video.uuid.hex, video.organization
        )
        return {
            "key": fairplay_key_data["key"],
            "uri": fairplay_key_data["uri"],
            "iv": fairplay_key_data["iv"],
            "content_id": video.uuid.hex,
            "output_name": "video.m3u8",
        }

    def _get_settings_for_outputs(self, outputs):
        config = {
            "input_url": self.asset.video.inputs.first().get_input_url(),
            "output_url": self.output_url,
            "settings": {
                "outputs": outputs,
                "format": "hls",
                "playlist_type": "vod",
                "segmentLength": 10,
                "segment_only": True if self.asset.video.transmux_only else False,
            },
            "webhook_url": self._get_webhook_url(),
            "storage_parameters": {
                "output": self._get_output_storage_parameters(),
            },
        }
        if self.asset.organization.uuid == "36d99f":
            config["settings"]["segment_length"] = 0

        if self.asset.video.transmux_only:
            config["settings"]["task_queue_name"] = "migrate_m3u8"
            config["inputs"] = [
                {"url": video_input.get_input_url(), "name": ""}
                for video_input in self.asset.video.inputs.all()
            ]
        return config

    @property
    def output_url(self):
        folder = (
            "transcoded_private" if self.asset.video.is_aes_encrypted else "transcoded"
        )
        return f"{self.asset.organization.bucket_url}/{folder}/{self.asset.uuid}/"

    def _get_webhook_url(self):
        return settings.SITE_URL + reverse(
            "api:update-video-status",
            kwargs={
                "asset_id": self.asset.uuid,
                "organization_id": self.asset.organization.uuid,
            },
        )

    def _get_output_storage_parameters(self):
        return {
            "ACCESS_KEY_ID": self.asset.organization.storage_access_key_id,
            "SECRET_KEY": self.asset.organization.storage_secret_access_key,
            "ENDPOINT": f"https://{self.asset.organization.storage_domain}",
        }

    def _get_metadata(self):
        return {
            "org_code": self.asset.organization.uuid,
            "org_name": self.asset.organization.name,
            "asset_id": str(self.asset.uuid),
        }

    def generate_thumbnail(self):
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Token {settings.LUMBERJACK_AUTH_TOKEN}",
        }
        url = f"{settings.TRANSCODER_URL}/generate_thumbnail/"
        response = post(
            url,
            data={
                "job_id": str(self.asset.video.job_id),
                "input_url": self.asset.video.inputs.first().get_input_url(),
            },
            headers=headers,
        )
        response.raise_for_status()

    def stop(self):
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Token {settings.LUMBERJACK_AUTH_TOKEN}",
        }
        url = f"{settings.TRANSCODER_URL}/cancel/"
        response = post(
            url, data={"job_id": str(self.asset.video.job_id)}, headers=headers
        )
        response.raise_for_status()


def get_transcoder(asset):
    return LumberjackAssetTranscoder(asset)


def create_transcoding_job(
    input_url,
    output_path,
    resolutions,
    created_by,
    organization,
):
    transcoding_job = TranscodingJob.objects.create(
        input_url=input_url,
        output_path=output_path,
        resolutions=resolutions,
        created_by=created_by,
        organization=organization,
    )

    try:
        response = LumberjackJobTranscoder(transcoding_job).start()
        transcoding_job.job_id = response["id"]
    except requests.exceptions.HTTPError:
        transcoding_job.status = Video.Status.ERROR
    transcoding_job.save()
    return transcoding_job


class LumberjackJobTranscoder:
    def __init__(self, transcoding_job):
        self.transcoding_job = transcoding_job

    @property
    def headers(self):
        return {
            "Content-Type": "application/json",
            "Authorization": f"Token {settings.LUMBERJACK_AUTH_TOKEN}",
        }

    def start(self):
        url = f"{settings.TRANSCODER_URL}/create/"
        response = post(url, data=self.settings(), headers=self.headers)
        data = json.loads(response.content)
        return data

    def settings(self):
        outputs = self.get_resolution_settings()
        config = self._get_settings_for_outputs(outputs)
        metadata = self._get_metadata()
        config.update({"meta_data": json.dumps(metadata)})
        return config

    def get_resolution_settings(self):
        outputs = []
        for resolution in self.transcoding_job.resolutions:
            res = VBR_RESOLUTION_MAP[resolution]
            if (
                self.transcoding_job.organization.uuid == "dcek2m"
                or self.transcoding_job.organization.uuid == "hd66qr"
                or self.transcoding_job.organization.uuid == "n2nbhp"
            ):
                res = TEST_VBR_RESOLUTION_MAP[resolution]
            outputs.append(res.get_output_settings())
        return outputs

    def _get_settings_for_outputs(self, outputs):
        parsed_output_path = urlparse(self.transcoding_job.output_path)
        config = {
            "input_url": self.transcoding_job.input_url,
            "output_url": f"{parsed_output_path.scheme}://{parsed_output_path.netloc}{parsed_output_path.path}",
            "settings": {
                "outputs": outputs,
                "format": "hls",
                "playlist_type": "vod",
                "segment_length": 10,
            },
            "webhook_url": self._get_webhook_url(),
            "storage_parameters": {
                "output": self._get_output_storage_parameters(),
                "input": self._get_input_storage_parameters(),
            },
        }

        config["settings"]["separate_server"] = True
        return config

    def _get_output_storage_parameters(self):
        matches = re.findall(r"[\?&]([^&]+)=([^&]+)", self.transcoding_job.output_path)
        query_params = {key: value for key, value in matches}
        return {
            "ACCESS_KEY_ID": query_params.get("access_key"),
            "SECRET_KEY": query_params.get("secret_key"),
        }

    def _get_input_storage_parameters(self):
        matches = re.findall(r"[\?&]([^&]+)=([^&]+)", self.transcoding_job.input_url)
        query_params = {key: value for key, value in matches}
        return {
            "ACCESS_KEY_ID": query_params.get("access_key"),
            "SECRET_KEY": query_params.get("secret_key"),
            "REGION": query_params.get("region"),
        }

    def _get_metadata(self):
        return {
            "org_code": self.transcoding_job.organization.uuid,
            "org_name": self.transcoding_job.organization.name,
            "job_uuid": str(self.transcoding_job.uuid),
        }

    def _get_webhook_url(self):
        return settings.SITE_URL + reverse(
            "api:update-transcoding-job-status",
            kwargs={
                "transcoding_job_id": self.transcoding_job.uuid,
                "organization_id": self.transcoding_job.organization.uuid,
            },
        )

    def stop(self):
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Token {settings.LUMBERJACK_AUTH_TOKEN}",
        }
        url = f"{settings.TRANSCODER_URL}/cancel/"
        response = post(
            url, data={"job_id": str(self.transcoding_job.job_id)}, headers=headers
        )
        response.raise_for_status()
