import json

import requests
from django.conf import settings

headers = {
    "apikey": settings.CHAT_ADMIN_KEY,
    "Authorization": f"Bearer {settings.CHAT_ADMIN_KEY}",
    "Content-Type": "application/json",
    "Prefer": "return=representation",
}


def create_chat_room_for_asset(asset):
    data = create_chat_room(asset)
    asset.live_stream.update_chat_room_id(data[0]["id"])


def create_chat_room(asset):
    url = f"{settings.CHAT_URL}rest/v1/chat_room"
    data = {
        "name": asset.title,
        "institute_subdomain": asset.organization.uuid,
        "content_id": asset.live_stream.pk,
    }
    response = requests.post(url, headers=headers, data=json.dumps(data))
    response.raise_for_status()
    data = json.loads(response.content)
    return data


def disable_chat_room(chat_room_id):
    url = f"{settings.CHAT_URL}rest/v1/chat_room?id=eq.{chat_room_id}"
    data = {"is_active": False}
    response = requests.patch(url, headers=headers, data=json.dumps(data))
    data = json.loads(response.content)
    return data


def get_chat_messages(chat_room_id, offset, limit=500):
    url = (
        f"{settings.CHAT_URL}rest/v1/messages?room_id=eq.{chat_room_id}"
        f"&select=timestamp,content,is_deleted,user(id,name,is_blocked)"
        f"&offset={offset}&limit={limit}&order=timestamp.asc"
    )
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    data = json.loads(response.content)
    return data


def update_chat_room_as_exported(chat_room_id):
    url = f"{settings.CHAT_URL}rest/v1/chat_room?id=eq.{chat_room_id}"
    data = {"is_exported": True}
    response = requests.patch(url, headers=headers, data=json.dumps(data))
    data = json.loads(response.content)
    return data
