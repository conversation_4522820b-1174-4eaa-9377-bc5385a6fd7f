from app.models import Playlist, Track


def create_playlist_track(asset, name, path=None, size=None, width=None, height=None):
    track, created = Track.objects.get_or_create(
        video=asset.video,
        organization=asset.organization,
        type=Track.Type.PLAYLIST,
    )
    playlist, created = Playlist.objects.update_or_create(
        track=track,
        organization=asset.organization,
        name=name,
        defaults={
            "path": path,
            "bytes": size,
            "width": width,
            "height": height,
        },
    )
    return playlist, created
