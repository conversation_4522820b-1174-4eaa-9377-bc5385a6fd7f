from urllib.parse import urljoin

import requests
import sentry_sdk
from django.conf import settings
from django.urls import reverse
from django.utils import timezone

from app.domain.asset import get_or_create_folder
from app.models.zoom import (
    ConnectionStatus,
    EventType,
    ImportStatus,
    WebhookStatus,
    ZoomAccount,
    ZoomRecording,
    ZoomWebhookLog,
)

ZOOM_API_TIMEOUT = 20
ZOOM_TOKEN_URL = "https://zoom.us/oauth/token"
ZOOM_REVOKE_URL = "https://zoom.us/oauth/revoke"
ZOOM_USER_INFO_URL = "https://api.zoom.us/v2/users/me"
DEFAULT_RECORDINGS_FOLDER = "Zoom Recordings"
ZOOM_RECORDINGS_API_URL = "https://api.zoom.us/v2/meetings/{}/recordings/?include_fields=download_access_token"


class ZoomOAuthError(Exception):
    pass


class ZoomError(Exception):
    pass


class ZoomImportError(Exception):
    pass


def fetch_zoom_tokens_using_authorization_code(code):
    redirect_uri = urljoin(settings.SITE_URL, reverse("zoom_oauth_callback"))
    data = {
        "grant_type": "authorization_code",
        "code": code,
        "redirect_uri": redirect_uri,
    }
    auth = (settings.ZOOM_CLIENT_ID, settings.ZOOM_CLIENT_SECRET)
    try:
        response = requests.post(
            ZOOM_TOKEN_URL, data=data, auth=auth, timeout=ZOOM_API_TIMEOUT, verify=True
        )
        response.raise_for_status()
        token_data = response.json()
        return {
            "access_token": token_data["access_token"],
            "refresh_token": token_data["refresh_token"],
            "expires_in": token_data["expires_in"],
        }
    except requests.RequestException as e:
        sentry_sdk.capture_exception(e)


def fetch_zoom_user_info(access_token):
    headers = {"Authorization": f"Bearer {access_token}"}
    try:
        response = requests.get(
            ZOOM_USER_INFO_URL,
            headers=headers,
            timeout=ZOOM_API_TIMEOUT,
            verify=True,
        )
        response.raise_for_status()
        user_info = response.json()
        return {
            "id": user_info["id"],
            "email": user_info["email"],
        }
    except requests.RequestException as e:
        sentry_sdk.capture_exception(e)


def store_zoom_account_in_db(user, user_info, tokens):
    expires_at = timezone.now() + timezone.timedelta(seconds=tokens["expires_in"])
    default_folder = get_or_create_folder(
        user.current_organization, DEFAULT_RECORDINGS_FOLDER
    )

    ZoomAccount.objects.update_or_create(
        organization=user.current_organization,
        user=user,
        defaults={
            "zoom_user_id": user_info["id"],
            "email": user_info["email"],
            "access_token": tokens["access_token"],
            "refresh_token": tokens["refresh_token"],
            "expires_at": expires_at,
            "import_destination": default_folder,
            "status": ConnectionStatus.CONNECTED,
            "disconnected_at": None,
        },
    )


def revoke_zoom_account(zoom_account):
    access_token = zoom_account.access_token
    if zoom_account.is_token_expired:
        access_token = refresh_zoom_token(zoom_account)

    revoke_url = ZOOM_REVOKE_URL
    auth = (settings.ZOOM_CLIENT_ID, settings.ZOOM_CLIENT_SECRET)
    response = requests.post(
        revoke_url,
        data={"token": access_token},
        auth=auth,
        timeout=ZOOM_API_TIMEOUT,
        verify=True,
    )

    if response.status_code == 200:
        zoom_account.disconnect()
    else:
        raise ZoomError("Failed to revoke Zoom tokens")


def refresh_zoom_token(zoom_account):
    token_url = ZOOM_TOKEN_URL
    data = {"grant_type": "refresh_token", "refresh_token": zoom_account.refresh_token}

    auth = (settings.ZOOM_CLIENT_ID, settings.ZOOM_CLIENT_SECRET)
    try:
        response = requests.post(
            token_url,
            data=data,
            auth=auth,
            timeout=ZOOM_API_TIMEOUT,
            verify=True,
        )
        response.raise_for_status()
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 400:
            # Zoom says refresh_token is invalid/expired
            sentry_sdk.capture_exception(e)
            zoom_account.mark_refresh_token_expired()
            raise ZoomOAuthError("Zoom session expired. Please reconnect.")
        raise

    tokens = response.json()
    zoom_account.update_tokens(
        access_token=tokens["access_token"],
        refresh_token=tokens["refresh_token"],
        expires_in_seconds=tokens["expires_in"],
    )

    return zoom_account.access_token


def store_zoom_recording_webhook_event(body):
    event_name = body.get("event")
    event_type = get_event_type(event_name)
    host_id = body.get("payload", {}).get("object", {}).get("host_id")
    zoom_account = get_zoom_account_from_host_id(host_id)

    if not zoom_account or not event_type:
        return None

    webhook_log = ZoomWebhookLog.objects.create(
        event_type=event_type,
        payload=body,
        organization=zoom_account.organization,
    )

    if event_type != EventType.RECORDING_COMPLETED:
        webhook_log.update_status(WebhookStatus.SUCCESS)
        return webhook_log

    meeting_uuid = body.get("payload", {}).get("object", {}).get("uuid")
    if is_recording_already_processed(meeting_uuid, zoom_account.organization):
        webhook_log.update_status(WebhookStatus.SKIPPED)
        return webhook_log

    return webhook_log


def get_event_type(event_name):
    event_map = {
        "recording.started": EventType.RECORDING_STARTED,
        "recording.stopped": EventType.RECORDING_STOPPED,
        "recording.paused": EventType.RECORDING_PAUSED,
        "recording.resumed": EventType.RECORDING_RESUMED,
        "recording.completed": EventType.RECORDING_COMPLETED,
    }
    return event_map.get(event_name)


def get_zoom_account_from_host_id(host_id):
    try:
        return ZoomAccount.objects.get(zoom_user_id=host_id)
    except ZoomAccount.DoesNotExist:
        return None


def is_recording_already_processed(meeting_uuid, organization):
    return ZoomRecording.objects.filter(
        meeting_uuid=meeting_uuid,
        organization=organization,
        status=ImportStatus.IMPORTED,
    ).exists()


def extract_recording_data_from_webhook_payload(webhook_log):
    payload_object = webhook_log.payload.get("payload", {}).get("object", {})
    recording_files = payload_object.get("recording_files")

    if not recording_files:
        return None

    attach_download_token_to_recording_files(
        recording_files, webhook_log.payload.get("download_token")
    )
    return payload_object


def fetch_zoom_recording_data_from_api(zoom_account, meeting_uuid):
    if zoom_account.is_token_expired:
        refresh_zoom_token(zoom_account)

    try:
        response = requests.get(
            ZOOM_RECORDINGS_API_URL.format(meeting_uuid),
            headers={"Authorization": f"Bearer {zoom_account.access_token}"},
            timeout=ZOOM_API_TIMEOUT,
        )
        response.raise_for_status()
        recording_data = response.json()

        attach_download_token_to_recording_files(
            recording_data.get("recording_files"),
            recording_data.get("download_access_token"),
        )
        return recording_data

    except requests.exceptions.RequestException as e:
        sentry_sdk.capture_exception(e)
        return None


def attach_download_token_to_recording_files(recording_files, download_token):
    for recording in recording_files:
        recording["download_token"] = download_token


def create_video_for_zoom_recording(zoom_recording, zoom_account):
    from app.domain.video import create_video_with_outputs
    from app.models import Video

    content_protection_type = (
        Video.ContentProtectionType.DRM
        if zoom_account.enable_drm
        else Video.ContentProtectionType.DISABLED
    )
    resolutions = [
        Video.Resolutions._240p,
        Video.Resolutions._360p,
        Video.Resolutions._480p,
        Video.Resolutions._720p,
    ]

    video = create_video_with_outputs(
        asset=zoom_recording.asset,
        organization=zoom_recording.organization,
        video_data={
            "video_codecs": [Video.VideoCodec.H264],
            "content_protection_type": content_protection_type,
            "resolutions": resolutions,
        },
    )
    return video


def create_or_update_zoom_recording(
    recording_data, recording_file, zoom_account, organization
):
    download_url = (
        f"{recording_file.get('download_url')}?access_token={recording_file.get('download_token')}"
        "&download=1"
    )

    zoom_recording = ZoomRecording.objects.filter(
        organization=organization,
        recording_uuid=recording_file.get("id"),
    ).first()

    if zoom_recording:
        zoom_recording.status = ImportStatus.PENDING
        zoom_recording.error_message = None
        zoom_recording.download_url = download_url
        zoom_recording.save(update_fields=["status", "error_message", "download_url"])
        return zoom_recording

    asset = create_asset_for_zoom_recording(recording_data, organization, zoom_account)

    zoom_recording = ZoomRecording.objects.create(
        organization=organization,
        asset=asset,
        meeting_uuid=recording_data.get("uuid"),
        recording_uuid=recording_file.get("id"),
        zoom_user_id=zoom_account.zoom_user_id,
        topic=recording_data.get("topic"),
        start_time=recording_data.get("start_time"),
        duration=recording_file.get("duration"),
        download_url=download_url,
        status=ImportStatus.PENDING,
    )
    return zoom_recording


def create_asset_for_zoom_recording(recording_data, organization, zoom_account):
    from app.models import Asset

    asset = Asset.objects.create(
        organization=organization,
        title=recording_data.get("topic", "Untitled Zoom Recording"),
        type=Asset.Type.VIDEO,
        created_by=zoom_account.user,
        parent=zoom_account.import_destination,
    )
    if asset.parent:
        asset.parent.update_children_count()
    return asset
