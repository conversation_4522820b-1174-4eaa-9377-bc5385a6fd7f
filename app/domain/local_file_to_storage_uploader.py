from app.domain.s3 import get_s3_config_for_rclone
from app.utils.s3 import copy_files_to_s3
from app.utils.wasabi import copy_files_to_wasabi, get_wasabi_config


def upload_local_files_to_storage(local_files, output_path, organization):
    from app.models import Organization

    if organization.storage_vendor == Organization.StorageProvider.AWS:
        upload_files_to_s3(local_files, output_path, organization)
    else:
        upload_files_to_wasabi(local_files, output_path, organization)


def upload_files_to_s3(local_files, output_path, organization):
    config = get_s3_config_for_rclone(organization)
    for local_file_path in local_files:
        copy_files_to_s3(
            local_file_path,
            output_path,
            config,
        )


def upload_files_to_wasabi(local_files, output_path, organization):
    config = get_wasabi_config(organization)
    for local_file_path in local_files:
        copy_files_to_wasabi(
            local_file_path,
            output_path,
            config,
            make_public=True,
        )
