import datetime

import sentry_sdk
from django_multitenant.utils import (
    get_current_tenant,
    set_current_tenant,
    unset_current_tenant,
)

from app.domain.cloudfront import get_bandwidth_used
from app.models import AssetUsage, Organization
from app.utils.datetime import get_month_end_date


def update_bandwidth_usage(date=None):
    if date is None:
        # If no date is provided, fetch yesterday's date because the cron job activates at 2 AM on the next day
        date = datetime.date.today() - datetime.timedelta(days=1)
    # Take a backup of current tenant
    current_tenant = get_current_tenant()

    unset_current_tenant()
    for organization in Organization.objects.all():
        set_current_tenant(organization)
        try:
            update_org_bandwidth_usage(organization, date)
        except Exception as error:
            sentry_sdk.capture_exception(error)

    set_current_tenant(current_tenant)


def update_org_bandwidth_usage(organization, date):
    if organization.status != Organization.Status.BLOCKED:
        update_day_bandwidth_usage(organization, date)
        update_monthly_bandwidth_usage(organization, date)


def update_day_bandwidth_usage(organization, date):
    from_date = date
    to_date = datetime.datetime(
        from_date.year, from_date.month, from_date.day, 23, 59, 59
    )
    bandwidth_used = get_bandwidth_used(
        organization,
        from_date=from_date.strftime("%Y-%m-%d-%H-%M-%S"),
        to_date=to_date.strftime("%Y-%m-%d-%H-%M-%S"),
    )

    save_bandwidth(organization, from_date, bandwidth_used)


def update_monthly_bandwidth_usage(organization, date):
    month_first_date = datetime.date(date.year, date.month, 1)
    bandwidth_used = get_bandwidth_used(
        organization,
        from_date=month_first_date.strftime("%Y-%m-%d-%H-%M-%S"),
        to_date=get_month_end_date(date).strftime("%Y-%m-%d-%H-%M-%S"),
    )

    save_bandwidth(
        organization,
        month_first_date,
        bandwidth_used,
        time_frame=AssetUsage.TimeFrames.MONTHLY,
    )


def save_bandwidth(
    organization, date, bandwidth_used, time_frame=AssetUsage.TimeFrames.DAILY
):
    AssetUsage.objects.update_or_create(
        date=date,
        organization=organization,
        time_frame=time_frame,
        defaults={"bandwidth_used": bandwidth_used},
    )
