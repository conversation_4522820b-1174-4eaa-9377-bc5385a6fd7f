import datetime

import sentry_sdk
from django_multitenant.utils import (
    get_current_tenant,
    set_current_tenant,
    unset_current_tenant,
)

from app.domain.cloud_storage import get_size
from app.models.organization import Organization
from app.models.usages import AssetUsage
from app.utils.datetime import get_month_start_date
from app.utils.wasabi import get_all_bucket_metrics


def get_total_size(asset):
    transcoded_file_size = get_transcoded_file_size(asset)
    source_files_size = get_source_files_size(asset)
    total_size = transcoded_file_size + source_files_size

    return total_size


def get_transcoded_file_size(asset):
    transcoded_file_path = "transcoded/" + str(asset.uuid)

    return get_size(asset.organization, transcoded_file_path)


def get_source_files_size(asset):
    size = 0
    source_file_paths = get_source_file_paths(asset)
    for source_file_path in source_file_paths:
        size += get_size(asset.organization, source_file_path)

    return size


def get_source_file_paths(asset):
    from app.models import VideoInput

    source_file_paths = []
    source_files = VideoInput.objects.filter(video__asset=asset)
    for source_file in source_files:
        source_file_paths.append(source_file.url)

    return source_file_paths


def update_storage_usage(date):
    # Take a backup of current tenant
    current_tenant = get_current_tenant()
    unset_current_tenant()

    for organization in Organization.objects.all():
        set_current_tenant(organization)
        try:
            if organization.status != Organization.Status.BLOCKED:
                update_day_storage_usage(organization, date)
                update_monthly_storage_usage(organization, date)
        except Exception as error:
            sentry_sdk.capture_exception(error)

    set_current_tenant(current_tenant)


def update_day_storage_usage(organization, date):
    active_storage_bytes, deleted_storage_bytes = get_day_storage_usage(
        organization, date
    )
    save_usage(organization, date, active_storage_bytes, deleted_storage_bytes)


def update_monthly_storage_usage(organization, date):
    active_storage_bytes, deleted_storage_bytes = get_monthly_storage_usage(
        organization, date
    )
    save_usage(
        organization,
        get_month_start_date(date),
        active_storage_bytes,
        deleted_storage_bytes,
        time_frame=AssetUsage.TimeFrames.MONTHLY,
    )


def get_day_storage_usage(organization, date):
    from_date = date - datetime.timedelta(days=1)
    to_date = date + datetime.timedelta(days=1)
    active_storage_bytes = 0
    deleted_storage_bytes = 0

    responses = get_all_bucket_metrics(
        from_date=from_date, to_date=to_date, organization=organization
    )
    for response in responses:
        if response["Bucket"] == organization.bucket_name:
            active_storage_bytes = (
                response["PaddedStorageSizeBytes"]
                + response["MetadataStorageSizeBytes"]
                - active_storage_bytes
            )
            deleted_storage_bytes = (
                response["DeletedStorageSizeBytes"] - deleted_storage_bytes
            )
    return (abs(active_storage_bytes), abs(deleted_storage_bytes))


def get_monthly_storage_usage(organization, date):
    to_date = date + datetime.timedelta(days=1)
    active_storage_bytes = 0
    deleted_storage_bytes = 0

    responses = get_all_bucket_metrics(
        from_date=date, to_date=to_date, organization=organization
    )
    for response in responses:
        if response["Bucket"] == organization.bucket_name:
            active_storage_bytes = (
                response["PaddedStorageSizeBytes"]
                + response["MetadataStorageSizeBytes"]
            )
            deleted_storage_bytes = response["DeletedStorageSizeBytes"]
    return (active_storage_bytes, deleted_storage_bytes)


def save_usage(
    organization,
    date,
    active_storage_bytes,
    deleted_storage_bytes,
    time_frame=AssetUsage.TimeFrames.DAILY,
):
    AssetUsage.objects.update_or_create(
        date=date,
        organization=organization,
        time_frame=time_frame,
        defaults={
            "active_storage_bytes": active_storage_bytes,
            "deleted_storage_bytes": deleted_storage_bytes,
        },
    )
