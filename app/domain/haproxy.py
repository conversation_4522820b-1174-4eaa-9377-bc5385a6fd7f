from django.conf import settings

from app.utils.haproxy import (
    add_backend_switching_rule,
    add_ip_address_to_backend,
    add_new_backend_server,
    get_configuration_version_number,
    remove_backend,
    remove_backend_switching_rule,
)

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Basic {settings.PROXY_API_KEY}",
}


def add_ip_address_to_proxy(ip_address, asset):
    backend_name = f"backend_{asset.organization.uuid}_{asset.uuid}"
    path_rule = f"/live/{asset.organization.uuid}/{asset.uuid}"
    version_number = get_configuration_version_number()
    new_version_number = add_new_backend_server(backend_name, version_number)
    new_version_number = add_ip_address_to_backend(
        ip_address, backend_name, new_version_number
    )
    add_backend_switching_rule(backend_name, path_rule, new_version_number)


def remove_assigned_ip_address_from_proxy(asset):
    backend_name = f"backend_{asset.organization.uuid}_{asset.uuid}"
    version_number = get_configuration_version_number()
    remove_backend_switching_rule(version_number, backend_name)
    remove_backend(version_number, backend_name)
