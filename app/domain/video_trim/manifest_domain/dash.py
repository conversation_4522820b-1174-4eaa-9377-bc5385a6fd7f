from app.domain.video_trim.exceptions import ManifestHandlingError
from app.utils.hls_trimmer import generate_trim_id
from app.utils.manifest_storage import (
    ManifestStorageError,
    get_manifest_content,
    upload_manifest_content,
)
from app.utils.mpd import DASHTrimmerError, trim_mpd_content


class TrimContext:
    def __init__(self, video, start_time, end_time):
        self.video = video
        self.start_time = start_time
        self.end_time = end_time
        self.trim_id = generate_trim_id()
        self.base_path = f"transcoded/{video.asset.uuid}"
        self.master_url = f"{video.organization.cdn_url}{self.base_path}/video.mpd"
        self.master_path = f"{self.base_path}/trimmed_{self.trim_id}.mpd"


def trim_dash_manifests(video, start_time, end_time):
    trim_context = TrimContext(video, start_time, end_time)

    try:
        mpd_content = get_manifest_content(
            video.organization, f"{trim_context.base_path}/video.mpd"
        )
        if not mpd_content:
            raise ManifestHandlingError("Could not fetch master MPD")

        trimmed_mpd_content = trim_mpd_content(mpd_content, start_time, end_time)

        upload_manifest_content(
            trim_context.video.organization,
            trimmed_mpd_content,
            trim_context.master_path,
        )

        return _build_response(trim_context.master_path, trim_context.trim_id)

    except (ManifestStorageError, DASHTrimmerError) as e:
        raise ManifestHandlingError(f"Failed to trim DASH manifests: {e}") from e
    except Exception as e:
        raise ManifestHandlingError(
            f"Unexpected error while trimming DASH manifests: {e}"
        ) from e


def _build_response(path, trim_id):
    return {
        "master": path,
        "trim_id": trim_id,
    }
