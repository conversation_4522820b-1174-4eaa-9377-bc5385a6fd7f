import sentry_sdk
from django.db import transaction

from app.models.video_trim import OutputType, TrimStatus, VideoOutput, VideoTrim
from app.utils.video_trim import calculate_trimmed_duration, validate_trim_parameters

from .exceptions import VideoTrimValidationError


def check_existing_trim_job(video):
    return VideoTrim.objects.filter(
        video=video, status__in=[TrimStatus.PENDING, TrimStatus.PROCESSING]
    ).first()


def create_trim_job(video, start_time, end_time, created_by, background_task_id=None):
    existing_job = check_existing_trim_job(video)
    if existing_job:
        raise VideoTrimValidationError(
            f"A trim operation is already in progress for this video "
            f"(Status: {existing_job.get_status_display()})"
        )
    return VideoTrim.objects.create(
        video=video,
        start_time=start_time,
        end_time=end_time,
        created_by=created_by,
        status=TrimStatus.PENDING,
        background_task_id=background_task_id,
        organization=video.organization,
    )


def mark_trim_status(trim_job, status):
    trim_job.status = status
    trim_job.save(update_fields=["status"])


def get_latest_completed_trim(video):
    return (
        VideoTrim.objects.filter(video=video, status=TrimStatus.COMPLETED)
        .order_by("-created")
        .first()
    )


def get_video_outputs(video, output_types):
    return VideoOutput.objects.filter(
        video=video, output_type__in=output_types, is_active=True
    )


def get_original_outputs(video):
    return get_video_outputs(video, [OutputType.HLS, OutputType.DASH])


def has_video_outputs(video):
    return get_original_outputs(video).exists()


def ensure_video_outputs_exist(video):
    # This is specifically for older videos that do not have video outputs.
    # Ensures outputs are created before proceeding with trim or revert logic.
    if not has_video_outputs(video):
        from app.domain.video_output import create_video_outputs

        create_video_outputs(video)


def get_trimmed_outputs(video):
    return get_video_outputs(video, [OutputType.TRIMMED_HLS, OutputType.TRIMMED_DASH])


def delete_trimmed_outputs(video):
    get_trimmed_outputs(video).delete()


def create_trimmed_output(video, output_type, codec, duration, url):
    return VideoOutput.objects.update_or_create(
        video=video,
        output_type=output_type,
        codec=codec,
        organization=video.organization,
        defaults={"duration": duration, "url": url, "is_active": True},
    )[0]


def create_trimmed_outputs_from_dict(video, trimmed_outputs, duration):
    for codec, urls in trimmed_outputs.items():
        if "hls" in urls:
            create_trimmed_output(
                video, OutputType.TRIMMED_HLS, codec, duration, urls["hls"]
            )
        if "dash" in urls:
            create_trimmed_output(
                video, OutputType.TRIMMED_DASH, codec, duration, urls["dash"]
            )


def update_video_output_urls(video):
    trimmed_outputs = get_trimmed_outputs(video)
    if trimmed_outputs.exists():
        video.update_output_urls(trimmed_outputs)
    else:
        ensure_video_outputs_exist(video)
        original_outputs = get_original_outputs(video)
        video.update_output_urls(original_outputs)
    video.save(update_fields=["output_urls"])


def update_video_urls_to_trimmed(video):
    trimmed_outputs = get_trimmed_outputs(video)
    if trimmed_outputs.exists():
        video.update_primary_playback_urls(trimmed_outputs)
        video.save(update_fields=["playback_url", "dash_url"])


def revert_to_original_urls(video, original_outputs):
    if original_outputs.exists():
        video.update_primary_playback_urls(original_outputs)
        video.save(update_fields=["playback_url", "dash_url"])


def create_video_trim_job(
    video, start_time, end_time, created_by, background_task_id=None
):
    validate_trim_parameters(video, start_time, end_time)
    ensure_video_outputs_exist(video)
    return create_trim_job(video, start_time, end_time, created_by, background_task_id)


def mark_trim_completed(video, trim_job, trimmed_outputs):
    try:
        with transaction.atomic():
            duration = calculate_trimmed_duration(trim_job)
            delete_trimmed_outputs(video)
            create_trimmed_outputs_from_dict(video, trimmed_outputs, duration)
            update_video_output_urls(video)
            update_video_urls_to_trimmed(video)
            mark_trim_status(trim_job, TrimStatus.COMPLETED)
    except Exception as e:
        mark_trim_status(trim_job, TrimStatus.FAILED)
        raise e from None


def revert_to_original(video):
    completed_trim = get_latest_completed_trim(video)
    if not completed_trim:
        raise VideoTrimValidationError("No completed trim found for this video")

    with transaction.atomic():
        delete_trimmed_outputs(video)
        update_video_output_urls(video)
        ensure_video_outputs_exist(video)
        original_outputs = get_original_outputs(video)
        revert_to_original_urls(video, original_outputs)
        mark_trim_status(completed_trim, TrimStatus.REVERTED)

    return completed_trim


def start_video_trim_background_task(video, start_time, end_time, created_by):
    trim_job = create_video_trim_job(video, start_time, end_time, created_by)

    from app.tasks.video_trim import VideoTrimTask

    try:
        task_result = VideoTrimTask.apply_async(
            kwargs={
                "trim_job_id": trim_job.id,
                "organization_uuid": str(video.organization.uuid),
            },
            queue="live_stream",
        )
        trim_job.background_task_id = task_result.id
        trim_job.save(update_fields=["background_task_id"])
    except Exception as exc:
        mark_trim_status(trim_job, TrimStatus.FAILED)
        sentry_sdk.capture_exception(exc)
        raise

    return trim_job


def start_video_trim_revert_background_task(video):
    from app.tasks.video_trim import VideoTrimRevertTask

    return VideoTrimRevertTask.apply_async(
        kwargs={
            "video_id": video.id,
            "organization_uuid": str(video.organization.uuid),
        },
        queue="live_stream",
    )
