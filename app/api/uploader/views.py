from knox.auth import TokenAuthentication
from rest_framework import generics
from rest_framework.authentication import SessionAuthentication

from app.api.mixins import OrganizationMixin
from app.api.uploader.serializers import UploaderConfigSerializer


class UploaderConfigView(OrganizationMixin, generics.RetrieveAPIView):
    authentication_classes = [SessionAuthentication, TokenAuthentication]
    serializer_class = UploaderConfigSerializer

    def get_object(self):
        return self.organization
