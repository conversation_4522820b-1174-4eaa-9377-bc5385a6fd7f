from django.urls import reverse
from rest_framework import serializers

from app.models import Organization


class UploaderConfigSerializer(serializers.ModelSerializer):
    content_protection_types = serializers.SerializerMethodField()
    max_file_size = serializers.SerializerMethodField()
    max_video_duration = serializers.SerializerMethodField()
    assets_create_url = serializers.SerializerMethodField()
    multipart_upload_url = serializers.SerializerMethodField()
    start_transcoding_url = serializers.SerializerMethodField()
    update_video_status_url = serializers.SerializerMethodField()
    generate_subtitle_url = serializers.SerializerMethodField()

    class Meta:
        model = Organization
        fields = [
            "name",
            "uuid",
            "enabled_resolutions",
            "content_protection_types",
            "limited_access_enabled",
            "max_file_size",
            "max_video_duration",
            "assets_create_url",
            "multipart_upload_url",
            "start_transcoding_url",
            "update_video_status_url",
            "generate_subtitle_url",
        ]

    def get_content_protection_types(self, obj):
        return ["disabled", "drm", "aes"]

    def get_max_file_size(self, obj):
        if obj.limited_access_enabled:
            return 50000000  # 50 MB in bytes
        return None

    def get_max_video_duration(self, obj):
        if obj.limited_access_enabled:
            return 600  # 10 minutes in seconds
        return None

    def get_assets_create_url(self, obj):
        relative_url = reverse(
            "api:bulk-create-video-assets", kwargs={"organization_id": obj.uuid}
        )
        return self.context.get("request").build_absolute_uri(relative_url)

    def get_multipart_upload_url(self, obj):
        relative_url = f"/api/v1/{obj.uuid}/"
        return self.context.get("request").build_absolute_uri(relative_url)

    def get_start_transcoding_url(self, obj):
        relative_url = reverse(
            "api:start-transcoding",
            kwargs={"organization_id": obj.uuid, "asset_id": "asset_id_placeholder"},
        )
        return self.context.get("request").build_absolute_uri(relative_url)

    def get_update_video_status_url(self, obj):
        relative_url = reverse(
            "api:update-video-status",
            kwargs={"organization_id": obj.uuid, "asset_id": "asset_id_placeholder"},
        )
        return self.context.get("request").build_absolute_uri(relative_url)

    def get_generate_subtitle_url(self, obj):
        relative_url = reverse(
            "api:generate-subtitle",
            kwargs={"organization_id": obj.uuid, "asset_id": "asset_id_placeholder"},
        )
        return self.context.get("request").build_absolute_uri(relative_url)
