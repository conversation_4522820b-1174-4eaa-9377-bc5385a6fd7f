from __future__ import annotations

from django.shortcuts import get_object_or_404
from django_multitenant.utils import set_current_tenant
from rest_framework import permissions

from app.api.v1.permissions import HasOrganizationAccess
from app.models import Organization


class OrganizationMixin:
    permission_classes = [permissions.IsAuthenticated, HasOrganizationAccess]
    organization_kwarg = "organization_id"

    def dispatch(self, request, *args, **kwargs):
        self.organization = get_object_or_404(
            Organization, uuid=kwargs[self.organization_kwarg]
        )
        set_current_tenant(self.organization)
        return super().dispatch(request, *args, **kwargs)  # type: ignore

    def get_serializer_context(self):
        context = super().get_serializer_context()  # type: ignore
        context.update({"organization": self.organization})
        return context


class PermissionPolicyMixin:
    permission_classes_per_method: dict[str, list] = {}

    def check_permissions(self, request):
        try:
            handler = getattr(self, request.method.lower())
        except AttributeError:
            handler = None

        if (
            handler
            and self.permission_classes_per_method
            and self.permission_classes_per_method.get(handler.__name__)
        ):
            self.permission_classes = self.permission_classes_per_method.get(
                handler.__name__
            )

        super().check_permissions(request)  # type: ignore
