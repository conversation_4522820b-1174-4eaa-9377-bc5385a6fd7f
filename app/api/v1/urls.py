from django.urls import include, path
from rest_framework import routers

from app.api.v1.views import (
    AccessTokenViewSet,
    AssetListView,
    AssetUpdateDeleteRetrieveView,
    AssetUsageView,
    BatchSignPartUpload,
    BulkCreateVideoAssetView,
    CompleteMultipartUpload,
    CreateMultipartView,
    DRMLicenseView,
    FolderListCreateView,
    GetEmbedURLView,
    MultipartUploadRetrieveAbortView,
    OrganizationListView,
    SignPartUpload,
    TestpressIntegrationView,
    UpdateVideoAssetStatusView,
    VideoAssetCreateView,
    WebhookViewSet,
    start_transcoding_view,
)
from app.api.v1.views.analytics import (
    GeneratePresignedURLAnalyticsView,
    TrackAnalyticsView,
)
from app.api.v1.views.asset import AssetMoveView
from app.api.v1.views.drm import (
    DRMLicenseViewV2,
    GenerateFairplayEncryptionKeyView,
    GenerateWidevineEncryptionKeyView,
    generate_widevine_encryption_key_view,
)
from app.api.v1.views.live_stream import (
    CreateLiveStreamServerView,
    LiveStreamListCreateView,
    LiveStreamOnPublishCallbackView,
    LiveStreamOnUnPublishCallbackView,
    LiveStreamOnUploadCompleteCallbackView,
    LiveStreamServerStatusCallbackView,
    StopLiveStreamView,
)
from app.api.v1.views.subtitle import (
    GenerateSubtitleView,
    SubtitleServerStatusCallbackView,
    TrackViewSet,
    UploadSubtitleView,
)
from app.api.v1.views.thumbnail import UploadThumbnailView
from app.api.v1.views.transcoding_job import (
    TranscodingJobViewSet,
    UpdateTranscodingJobStatusView,
)
from app.api.v1.views.video import (
    AESEncryptionKeyView,
    LumberjackCallbackForMigratedVideoView,
)
from app.api.v1.views.video_trim import (
    VideoTrimLogsView,
    VideoTrimRevertView,
    VideoTrimStatusView,
    VideoTrimView,
)

router = routers.DefaultRouter()
router.register(
    r"(?P<organization_id>[\w]+)/webhooks", WebhookViewSet, basename="webhook"
)
router.register(
    r"(?P<organization_id>[\w]+)/subtitles", TrackViewSet, basename="subtitles"
)
router.register(
    r"(?P<organization_id>[\w]+)/assets/(?P<asset_uuid>[\w-]+)/subtitles",
    TrackViewSet,
    basename="asset_subtitles",
)

router.register(
    r"(?P<organization_id>[\w]+)/transcoding_jobs",
    TranscodingJobViewSet,
    basename="transcoding_job",
)
router.register(
    r"(?P<organization_id>[\w]+)/assets/(?P<asset_uuid>[\w-]+)/access_tokens",
    AccessTokenViewSet,
    basename="access-token",
)

app_name = "api"
urlpatterns = router.urls + [
    path(
        "<str:organization_id>/",
        include(
            [
                path(
                    "assets_usage/",
                    AssetUsageView.as_view(),
                    name="asset-usage",
                ),
                path(
                    "assets/videos/",
                    VideoAssetCreateView.as_view(),
                    name="create-video-asset",
                ),
                path(
                    "assets/live_streams/",
                    LiveStreamListCreateView.as_view(),
                    name="create-live-stream",
                ),
                path(
                    "assets/<str:asset_id>/start_server/",
                    CreateLiveStreamServerView.as_view(),
                    name="create-live-stream-server",
                ),
                path(
                    "livestream/on_server_callback/",
                    LiveStreamServerStatusCallbackView.as_view(),
                    name="live-stream-server-callback",
                ),
                path(
                    "livestream/on_publish/",
                    LiveStreamOnPublishCallbackView.as_view(),
                    name="live-stream-on-publish",
                ),
                path(
                    "livestream/on_unpublish/",
                    LiveStreamOnUnPublishCallbackView.as_view(),
                    name="live-stream-on-unpublish",
                ),
                path(
                    "livestream/on_upload_complete/",
                    LiveStreamOnUploadCompleteCallbackView.as_view(),
                    name="live-stream-on-upload-complete",
                ),
                path(
                    "assets/<str:asset_id>/stop_live_stream/",
                    StopLiveStreamView.as_view(),
                    name="stop-live-stream",
                ),
                path(
                    "subtitle/<int:subtitle_id>/update_subtitle_status/",
                    SubtitleServerStatusCallbackView.as_view(),
                    name="subtitle-server-callback",
                ),
                path(
                    "assets/bulk_create_videos/",
                    BulkCreateVideoAssetView.as_view(),
                    name="bulk-create-video-assets",
                ),
                path(
                    "assets/folders/",
                    FolderListCreateView.as_view(),
                    name="create-folder",
                ),
                path(
                    "assets/<str:asset_id>/move/",
                    AssetMoveView.as_view(),
                    name="asset_move",
                ),
                path(
                    "assets/<str:uuid>/",
                    AssetUpdateDeleteRetrieveView.as_view(
                        {"put": "update", "delete": "destroy", "get": "retrieve"}
                    ),
                    name="assets",
                ),
                path(
                    "assets/",
                    AssetListView.as_view(),
                    name="asset-list",
                ),
                path(
                    "transcoding_job/<str:transcoding_job_id>/update_status/",
                    UpdateTranscodingJobStatusView.as_view(),
                    name="update-transcoding-job-status",
                ),
                path(
                    "assets/<str:asset_id>/update_video_status/",
                    UpdateVideoAssetStatusView.as_view(),
                    name="update-video-status",
                ),
                path(
                    "assets/<str:asset_id>/lumberjack_migrated_video_callback/",
                    LumberjackCallbackForMigratedVideoView.as_view(),
                    name="lumberjack-migrated-video-callback",
                ),
                path(
                    "assets/<str:asset_id>/start_transcoding/",
                    start_transcoding_view,
                    name="start-transcoding",
                ),
                path(
                    "assets/<str:asset_id>/generate_subtitle/",
                    GenerateSubtitleView.as_view(),
                    name="generate-subtitle",
                ),
                path(
                    "assets/<str:asset_id>/upload_subtitle/",
                    UploadSubtitleView.as_view(),
                    name="upload-subtitle",
                ),
                path(
                    "assets/<str:asset_id>/upload_thumbnail/",
                    UploadThumbnailView.as_view(),
                    name="upload-thumbnail",
                ),
                path(
                    "assets/<str:asset_id>/embed_url/",
                    GetEmbedURLView.as_view(),
                    name="get-embed-url",
                ),
                path(
                    "assets/<str:asset_id>/presigned_urls/",
                    GeneratePresignedURLAnalyticsView.as_view(),
                    name="generate-presigned-urls",
                ),
                path(
                    "assets/<str:asset_id>/track_analytics/",
                    TrackAnalyticsView.as_view(),
                    name="track_analytics",
                ),
                path(
                    "assets/<str:asset_id>/drm_license/",
                    DRMLicenseView.as_view(),
                    name="drm-license",
                ),
                path(
                    "assets/<str:asset_id>/aes_key/",
                    AESEncryptionKeyView.as_view(),
                    name="get_aes_key",
                ),
                path(
                    "assets/<str:asset_uuid>/trim/",
                    VideoTrimView.as_view(),
                    name="video-trim",
                ),
                path(
                    "assets/<str:asset_uuid>/trim/status/",
                    VideoTrimStatusView.as_view(),
                    name="video-trim-status",
                ),
                path(
                    "assets/<str:asset_uuid>/trim/revert/",
                    VideoTrimRevertView.as_view(),
                    name="video-trim-revert",
                ),
                path(
                    "assets/<str:asset_uuid>/trim/logs/",
                    VideoTrimLogsView.as_view(),
                    name="video-trim-logs",
                ),
                path(
                    "s3/multipart",
                    CreateMultipartView.as_view(),
                    name="create-multipart",
                ),
                path(
                    "s3/multipart/<str:upload_id>",
                    MultipartUploadRetrieveAbortView.as_view(),
                    name="multipart-detail",
                ),
                path(
                    "s3/multipart/<str:upload_id>/batch",
                    BatchSignPartUpload.as_view(),
                    name="batch-sign-part-upload",
                ),
                path(
                    "s3/multipart/<str:upload_id>/<int:part_number>",
                    SignPartUpload.as_view(),
                    name="sign-part-upload",
                ),
                path(
                    "s3/multipart/<str:upload_id>/complete",
                    CompleteMultipartUpload.as_view(),
                    name="complete-multipart-upload",
                ),
                path(
                    "widevine_key/",
                    GenerateWidevineEncryptionKeyView.as_view(),
                    name="generate-widevine-key",
                ),
                path(
                    "widevine_key_new/",
                    generate_widevine_encryption_key_view,
                    name="generate-widevine-key-new",
                ),
                path(
                    "fairplay_key/",
                    GenerateFairplayEncryptionKeyView.as_view(),
                    name="generate-fairplay-key",
                ),
                path(
                    "drm_license/",
                    DRMLicenseViewV2.as_view(),
                    name="drm-license-v2",
                ),
            ]
        ),
    ),
    path(
        "testpress/livestream_integration/",
        TestpressIntegrationView.as_view(),
        name="testpress-integration",
    ),
    path("organizations/", OrganizationListView.as_view(), name="projects-list"),
    path("organizations/", OrganizationListView.as_view(), name="projects-list"),
]
