from rest_framework import serializers

from app.models import Webhook


class WebhookSerializer(serializers.ModelSerializer):
    id = serializers.ReadOnlyField(source="uuid")

    class Meta:
        model = Webhook
        fields = ["url", "secret_token", "id"]

    def create(self, validated_data):
        return Webhook.objects.create(
            created_by=self.context.get("request").user,
            organization=self.context.get("organization"),
            **validated_data,
        )
