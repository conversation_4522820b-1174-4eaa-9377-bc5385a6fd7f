from rest_framework import serializers


class TestpressSerializer(serializers.Serializer):
    org_name = serializers.Char<PERSON><PERSON>(max_length=255)
    email = serializers.EmailField()
    user_name = serializers.CharField(max_length=255)
    password = serializers.CharField(write_only=True)
    institute_id = serializers.CharField(max_length=50)
    cdn_id = serializers.CharField(max_length=100)
    cdn_url = serializers.URLField()
    cloudfront_key_group_id = serializers.Char<PERSON>ield(max_length=100)
    bucket_name = serializers.Char<PERSON>ield(max_length=255)
    bucket_secret_token = serializers.Char<PERSON>ield(max_length=255)

    storage_region = serializers.Char<PERSON>ield(max_length=100)
    storage_access_key_id = serializers.Char<PERSON>ield(max_length=255)
    storage_secret_access_key = serializers.Char<PERSON>ield(max_length=255)

    cdn_access_key_id = serializers.Char<PERSON><PERSON>(max_length=255)
    cdn_secret_access_key = serializers.Cha<PERSON><PERSON><PERSON>(max_length=255)
    cdn_one_year_cache_policy_id = serializers.Char<PERSON>ield(
        max_length=100, required=False, allow_null=True
    )
    cdn_expire_in_3_seconds_cache_policy_id = serializers.CharField(
        max_length=100, required=False, allow_null=True
    )
    cdn_public_key_id = serializers.CharField(max_length=100)
    cdn_private_key = serializers.CharField()

    webhook_url = serializers.URLField()
    secret_token = serializers.CharField(max_length=100)

    def validate(self, attrs):
        if not attrs.get("institute_id"):
            raise serializers.ValidationError("Institute ID is required.")
        return attrs
