from django.db import transaction
from django_multitenant.utils import get_current_tenant
from rest_framework import serializers

from app.api.fields import <PERSON><PERSON>ield, ContentProtectionTypeField, FolderField
from app.domain import cloudfront
from app.domain.video import create_video_with_outputs
from app.models import (
    Asset,
    Organization,
    Playlist,
    PreviewThumbnail,
    Track,
    Video,
    VideoInput,
)


class VideoInputSerializer(serializers.ModelSerializer):
    class Meta:
        model = VideoInput
        fields = ["url"]


class PreviewThumbnailerializer(serializers.ModelSerializer):
    url = serializers.SerializerMethodField()

    def get_url(self, obj):
        return f"{obj.organization.cdn_url}{obj.url}"

    class Meta:
        model = PreviewThumbnail

        fields = [
            "url",
            "interval",
            "width",
            "height",
            "rows",
            "columns",
        ]


class PlaylistSerializer(serializers.ModelSerializer):
    class Meta:
        model = Playlist
        fields = [
            "name",
            "bytes",
            "width",
            "height",
        ]


class TrackSerializer(serializers.ModelSerializer):
    type = serializers.ReadOnlyField(source="get_type_display")
    url = serializers.SerializerMethodField()
    subtitle_type = serializers.ReadOnlyField(source="get_subtitle_type_display")
    playlists = PlaylistSerializer(many=True, read_only=True)
    preview_thumbnail = PreviewThumbnailerializer(read_only=True)

    def get_url(self, obj):
        return f"{obj.organization.cdn_url}{obj.url}"

    class Meta:
        model = Track
        read_only_fields = [
            field.name for field in Track._meta.fields if field.name != "is_active"
        ]
        fields = [
            "id",
            "type",
            "name",
            "url",
            "bytes",
            "language",
            "width",
            "height",
            "duration",
            "is_active",
            "playlists",
            "subtitle_type",
            "preview_thumbnail",
        ]


class VideoSerializer(serializers.ModelSerializer):
    inputs = VideoInputSerializer(many=True)
    status = serializers.ReadOnlyField(source="get_status_display")
    playback_url = serializers.SerializerMethodField()
    dash_url = serializers.SerializerMethodField()
    title = serializers.CharField(write_only=True)
    video_codec = ChoiceField(choices=Video.VideoCodec.choices, required=False)
    audio_codec = ChoiceField(choices=Video.AudioCodec.choices, required=False)
    format = ChoiceField(choices=Video.Format.choices, required=False)
    resolutions = serializers.ListField(
        child=ChoiceField(choices=Video.Resolutions.choices),
        required=True,
        allow_empty=False,
    )
    folder = FolderField(write_only=True, required=False, allow_null=True)
    duration = serializers.SerializerMethodField(source="get_duration")
    content_protection_type = ContentProtectionTypeField(required=False)
    enable_drm = serializers.SerializerMethodField()
    preview_thumbnail_url = serializers.SerializerMethodField()
    cover_thumbnail_url = serializers.SerializerMethodField()
    thumbnails = serializers.SerializerMethodField()
    video_codecs = serializers.ListField(
        child=ChoiceField(choices=Video.VideoCodec.choices),
        required=False,
        default=[Video.VideoCodec.H264],
    )
    output_urls = serializers.SerializerMethodField()

    def get_output_urls(self, obj):
        cdn_url = (
            obj.organization.cdn_url
            if obj.organization and obj.organization.cdn_url
            else ""
        )
        output_urls = obj.output_urls or {}

        # Update URLs with the CDN prefix
        return {
            codec: {
                "hls_url": f"{cdn_url}{urls['hls_url']}"
                if urls.get("hls_url")
                else None,
                "dash_url": f"{cdn_url}{urls['dash_url']}"
                if urls.get("dash_url")
                else None,
            }
            for codec, urls in output_urls.items()
        }

    def get_duration(self, obj):
        if obj.duration:
            return int(obj.duration.total_seconds())

    def get_playback_url(self, obj):
        return obj.get_playback_url()

    def get_dash_url(self, obj):
        if obj.content_protection_type == Video.ContentProtectionType.DRM:
            return obj.get_dash_url()

    def get_enable_drm(self, obj):
        return obj.content_protection_type == Video.ContentProtectionType.DRM

    def get_preview_thumbnail_url(self, obj):
        if obj.preview_thumbnail_url:
            return obj.organization.cdn_url + obj.preview_thumbnail_url

    def get_cover_thumbnail_url(self, obj):
        return obj.get_cover_thumbnail_url()

    def get_thumbnails(self, obj):
        return obj.get_thumbnail_urls()

    @transaction.atomic
    def create(self, validated_data):
        inputs = validated_data.pop("inputs")
        folder = validated_data.pop("folder", None)
        asset = self._create_asset(validated_data, folder)
        video = self._create_video(validated_data, asset)
        self._create_video_inputs(inputs, video)
        return video

    def _create_asset(self, validated_data, folder):
        asset_data = {
            "created_by": self.context.get("request").user,
            "organization": self.context.get("organization"),
            "title": validated_data.pop("title", None),
            "parent": folder,
        }
        asset = Asset.objects.create(**asset_data)
        if folder:
            folder.update_children_count()
        return asset

    def _create_video(self, validated_data, asset):
        content_protection_type = self._get_content_protection_type(validated_data)
        video_data = {
            "content_protection_type": content_protection_type,
            **validated_data,
        }
        return create_video_with_outputs(
            asset=asset,
            organization=self.context.get("organization"),
            video_data=video_data,
        )

    def _create_video_inputs(self, inputs_data, video):
        for ip in inputs_data:
            VideoInput.objects.create(
                url=ip["url"],
                video=video,
                organization=self.context.get("organization"),
            )

    def _get_content_protection_type(self, validated_data):
        content_protection_type = validated_data.pop("content_protection_type", None)
        enable_drm = self.context.get("request").data.get("enable_drm", False)

        if content_protection_type is not None:
            return content_protection_type

        return (
            Video.ContentProtectionType.DRM
            if enable_drm
            else Video.ContentProtectionType.DISABLED
        )

    class Meta:
        model = Video
        read_only_fields = [
            "progress",
            "status",
            "dash_url",
            "playback_url",
            "download_url",
            "asset",
            "output_urls",
        ]
        fields = [
            "progress",
            "thumbnails",
            "status",
            "playback_url",
            "dash_url",
            "preview_thumbnail_url",
            "cover_thumbnail_url",
            "format",
            "resolutions",
            "video_codec",
            "audio_codec",
            "enable_drm",
            "inputs",
            "title",
            "folder",
            "transmux_only",
            "duration",
            "content_protection_type",
            "generate_subtitle",
            "video_codecs",
            "output_urls",
        ]

    def validate(self, attrs):
        organization = get_current_tenant()
        if organization.status != Organization.Status.ACTIVE:
            raise serializers.ValidationError(
                "Your account is Suspended, <NAME_EMAIL>."
            )

        if (
            organization.limited_access_enabled
            and Asset.all_objects.filter(
                type=Asset.Type.VIDEO,
                organization=organization,
            ).count()
            >= 2
        ):
            raise serializers.ValidationError(
                "You can't upload more than 2 videos. To update your plan, contact <EMAIL>."
            )
        return attrs


class VideoSerializerWithoutInput(VideoSerializer):
    inputs = VideoInputSerializer(many=True, read_only=True)

    class Meta:
        model = Video
        read_only_fields = VideoSerializer.Meta.read_only_fields + ["inputs"]
        fields = VideoSerializer.Meta.fields


class VideoDetailSerializer(VideoSerializer):
    tracks = TrackSerializer(many=True, read_only=True)

    def get_playback_url(self, obj):
        request = self.context.get("request")
        if obj.is_aes_encrypted:
            expiry = int(request.GET.get("expiry", 120))

            return cloudfront.generate_presigned_url(
                obj.organization, obj.playback_url, expires_in=expiry
            )
        expiry = request.data.get("expiry") if hasattr(request, "data") else None
        if expiry:
            expiry = int(expiry)
            return cloudfront.generate_presigned_url(
                obj.organization, obj.playback_url, expires_in=expiry
            )
        return super().get_playback_url(obj)

    class Meta:
        model = Video
        fields = VideoSerializer.Meta.fields + ["tracks"]
