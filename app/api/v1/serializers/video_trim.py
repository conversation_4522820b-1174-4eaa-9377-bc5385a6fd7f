from rest_framework import serializers

from app.models.video_trim import VideoTrim


class VideoTrimRequestSerializer(serializers.Serializer):
    start_time = serializers.IntegerField(min_value=0, required=False, allow_null=True)
    end_time = serializers.IntegerField(min_value=1, required=False, allow_null=True)

    def __init__(self, *args, **kwargs):
        self.video_duration = kwargs.pop("video_duration", None)
        super().__init__(*args, **kwargs)

    def validate(self, data):
        start_time = data.get("start_time")
        end_time = data.get("end_time")

        if self.video_duration is None:
            raise serializers.ValidationError(
                "Video duration is required for validation."
            )

        if start_time is None and end_time is None:
            raise serializers.ValidationError(
                "At least one of start_time or end_time must be provided"
            )

        if start_time is None:
            start_time = 0
            data["start_time"] = start_time

        if end_time is None:
            end_time = self.video_duration
            data["end_time"] = end_time

        if start_time >= self.video_duration:
            raise serializers.ValidationError(
                f"Start time ({start_time}s) cannot be greater than or equal to video duration ({self.video_duration}s)"
            )

        if end_time > self.video_duration:
            raise serializers.ValidationError(
                f"End time ({end_time}s) cannot be greater than video duration ({self.video_duration}s)"
            )

        if end_time <= start_time:
            raise serializers.ValidationError(
                f"End time ({end_time}s) must be greater than start time ({start_time}s)"
            )

        return data


class VideoTrimStatusSerializer(serializers.ModelSerializer):
    status_display = serializers.CharField(source="get_status_display", read_only=True)

    class Meta:
        model = VideoTrim
        fields = [
            "id",
            "start_time",
            "end_time",
            "status",
            "status_display",
            "background_task_id",
            "created",
            "modified",
        ]
        read_only_fields = fields


class VideoTrimResponseSerializer(serializers.Serializer):
    message = serializers.CharField()
    trim_job_id = serializers.IntegerField()
    status = serializers.CharField()


class VideoTrimLogSerializer(serializers.ModelSerializer):
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    created_by_email = serializers.CharField(source="created_by.email", read_only=True)
    duration = serializers.SerializerMethodField()

    class Meta:
        model = VideoTrim
        fields = [
            "id",
            "start_time",
            "end_time",
            "status",
            "status_display",
            "background_task_id",
            "created_by_email",
            "duration",
            "created",
            "modified",
        ]
        read_only_fields = fields

    def get_duration(self, obj):
        if obj.start_time is not None and obj.end_time is not None:
            return obj.end_time - obj.start_time
        return None
