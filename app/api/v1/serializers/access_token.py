from django.urls import reverse
from rest_framework import serializers

from app.api.fields import <PERSON><PERSON><PERSON>
from app.domain.access_token import (
    generate_access_token,
    store_access_token_annotations_in_cache,
    store_access_token_in_cache,
)
from app.models import AccessToken, AccessTokenAnnotation


class AccessTokenAnnotationSerializer(serializers.ModelSerializer):
    type = ChoiceField(
        choices=AccessTokenAnnotation.WatermarkType.choices, required=False
    )

    class Meta:
        model = AccessTokenAnnotation
        fields = (
            "text",
            "type",
            "color",
            "opacity",
            "size",
            "interval",
            "skip",
            "x",
            "y",
        )


class AccessTokenSerializer(serializers.ModelSerializer):
    time_to_live = serializers.IntegerField(
        required=False, write_only=True, min_value=0
    )
    status = serializers.ReadOnlyField()
    annotations = AccessTokenAnnotationSerializer(
        many=True, required=False, source="_annotations"
    )
    playback_url = serializers.SerializerMethodField()
    code = serializers.ReadOnlyField(source="uuid")

    def get_playback_url(self, obj):
        embed_url = reverse(
            "embed-view",
            kwargs={"uuid": obj.asset.uuid, "organization_id": obj.organization.uuid},
        )
        return (
            f"https://{self.context.get('request').get_host()}"
            f"{embed_url}?access_token={obj.uuid}"
        )

    class Meta:
        model = AccessToken
        fields = (
            "playback_url",
            "time_to_live",
            "expires_after_first_usage",
            "code",
            "status",
            "valid_until",
            "annotations",
        )
        read_only_fields = ("code", "valid_until")

    def create(self, validated_data):
        annotations = validated_data.pop("_annotations", [])
        annotation_objs = [
            AccessTokenAnnotation(**annotation) for annotation in annotations
        ]
        access_token = generate_access_token(
            asset=self.context.get("asset"), **validated_data
        )
        self.store_access_token(
            access_token, annotation_objs, validated_data.get("time_to_live")
        )
        access_token._annotations = annotation_objs
        return access_token

    def update(self, instance, validated_data):
        annotations = validated_data.pop("_annotations", [])
        annotation_objs = [
            AccessTokenAnnotation(**annotation) for annotation in annotations
        ]
        self.store_access_token(
            instance, annotation_objs, validated_data.get("time_to_live")
        )
        instance._annotations = annotation_objs
        return instance

    def store_access_token(self, access_token, annotations, time_to_live):
        store_access_token_in_cache(access_token, time_to_live)
        store_access_token_annotations_in_cache(access_token, annotations, time_to_live)
