from urllib.parse import parse_qs, urlparse

from rest_framework import serializers

from app.api.fields import <PERSON><PERSON>ield
from app.domain.transcoder import create_transcoding_job
from app.models import TranscodingJob
from app.utils.browser import is_valid_content_type, is_valid_url


class TranscodingJobSerializer(serializers.ModelSerializer):
    id = serializers.ReadOnlyField(source="uuid")
    status = serializers.ReadOnlyField(source="get_status_display")
    resolutions = serializers.ListField(
        child=ChoiceField(choices=TranscodingJob.Resolutions.choices)
    )
    type = serializers.SerializerMethodField()
    input_url = serializers.CharField()

    ALLOWED_CONTENT_TYPES = [".mp4", ".avi"]
    REQUIRED_QUERY_PARAMS = ["access_key", "secret_key", "region"]
    SUPPORTED_SCHEMES = ["s3", "wasabi"]
    VALID_URL_SCHEMES = ["http", "https"]

    class Meta:
        model = TranscodingJob
        fields = [
            "id",
            "resolutions",
            "video_duration",
            "status",
            "input_url",
            "output_path",
            "start_time",
            "end_time",
            "type",
            "error_message"
        ]
        read_only_fields = ["video_duration", "status", "start_time", "end_time", "error_message"]

    def get_type(self, obj):
        return "transcoding_job"

    def validate_input_url(self, value):
        if value.startswith(tuple(self.VALID_URL_SCHEMES)):
            self._validate_http_url(value)
        else:
            self._validate_custom_scheme_url(value)
        return value

    def _validate_http_url(self, value):
        if not is_valid_url(value):
            raise serializers.ValidationError("Invalid input URL")

        if not is_valid_content_type(value, self.ALLOWED_CONTENT_TYPES):
            raise serializers.ValidationError(
                "Please provide a valid video URL with a .mp4 or .avi format."
            )

    def validate_output_path(self, value):
        parsed_url = urlparse(value)

        if not parsed_url.path or parsed_url.path[-1] != "/":
            raise serializers.ValidationError("Invalid path format")

        self._validate_custom_scheme_url(value)
        return value

    def _validate_custom_scheme_url(self, value):
        parsed_url = urlparse(value)

        if parsed_url.scheme not in self.SUPPORTED_SCHEMES:
            raise serializers.ValidationError(
                "Output path format not recognized. Please use the following format: s3://bucket/path/"
            )

        self._validate_query_params(parsed_url.query)
        return value

    def _validate_query_params(self, query):
        if not query:
            raise serializers.ValidationError(
                "Query parameters 'access_key', 'secret_key' and 'region' are required"
            )

        params = parse_qs(query)
        if any(param not in params for param in self.REQUIRED_QUERY_PARAMS):
            raise serializers.ValidationError(
                "Query parameters 'access_key', 'secret_key' and 'region' are required"
            )

    def create(self, validated_data):
        return create_transcoding_job(
            validated_data.get("input_url"),
            validated_data.get("output_path"),
            validated_data.get("resolutions"),
            self.context.get("request").user,
            self.context.get("organization"),
        )
