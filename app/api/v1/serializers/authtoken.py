from django.contrib.auth import authenticate
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from app.models import Organization


class AuthTokenSerializer(serializers.Serializer):
    username = serializers.CharField(label=_("Username"), write_only=True)
    password = serializers.CharField(
        label=_("Password"),
        style={"input_type": "password"},
        trim_whitespace=False,
        write_only=True,
    )
    organization_id = serializers.CharField(write_only=True)
    token = serializers.Char<PERSON>ield(label=_("Token"), read_only=True)

    def validate(self, attrs):
        username = attrs.get("username")
        password = attrs.get("password")

        if username and password:
            user = authenticate(
                request=self.context.get("request"),
                username=username,
                password=password,
            )

            if not user:
                msg = _("Unable to log in with provided credentials.")
                raise serializers.ValidationError(msg, code="authorization")
        else:
            msg = _('Must include "username" and "password".')
            raise serializers.ValidationError(msg, code="authorization")

        self.validate_organization(user, attrs.get("organization_id"))
        attrs["user"] = user
        return attrs

    def validate_organization(self, user, organization_id):
        if not Organization.objects.filter(
            Q(members=user) | Q(created_by=user), uuid=organization_id
        ).exists():
            raise serializers.ValidationError(
                _("Either organization id is incorrect or you don't have access to it")
            )
        return
