from rest_framework import serializers

from app.api.fields import Fold<PERSON><PERSON>ield
from app.api.v1.serializers import VideoSerializer
from app.api.v1.serializers.live_stream import (
    LiveStreamAdminSerializer,
    LiveStreamSerializer,
)
from app.api.v1.serializers.video import VideoDetailSerializer
from app.models import Asset


class AssetSerializer(serializers.ModelSerializer):
    type = serializers.ReadOnlyField(source="get_type_display")
    format = serializers.ReadOnlyField(source="get_format_display")
    video = VideoSerializer(read_only=True)
    id = serializers.SerializerMethodField()
    live_stream = LiveStreamSerializer(read_only=True)
    parent_id = serializers.SerializerMethodField()
    parent = FolderField(required=False, allow_null=True)

    def get_id(self, obj):
        return obj.uuid

    def get_parent_id(self, obj):
        if obj.parent:
            return obj.parent.uuid
        return None

    class Meta:
        model = Asset
        read_only_fields = ["bytes"]
        fields = [
            "title",
            "bytes",
            "type",
            "format",
            "video",
            "id",
            "live_stream",
            "parent",
            "parent_id",
        ]


class AssetDetailSerializer(AssetSerializer):
    video = VideoDetailSerializer(read_only=True)
    folder_tree = serializers.SerializerMethodField(source="get_folder_tree")

    def get_folder_tree(self, obj):
        ancestors = obj.get_ancestors()
        if ancestors:
            return " > ".join([ancestor.title for ancestor in ancestors])
        return ""

    class Meta:
        model = Asset
        read_only_fields = AssetSerializer.Meta.read_only_fields
        fields = AssetSerializer.Meta.fields + ["folder_tree"]


class AdminAssetDetailSerializer(AssetSerializer):
    video = VideoDetailSerializer(read_only=True)
    live_stream = LiveStreamAdminSerializer(read_only=True)
    folder_tree = serializers.SerializerMethodField(source="get_folder_tree")
    download_url = serializers.ReadOnlyField(source="get_download_url")
    views_count = serializers.IntegerField(required=False, read_only=True)
    average_watched_time = serializers.IntegerField(required=False, read_only=True)
    total_watch_time = serializers.IntegerField(required=False, read_only=True)
    unique_viewers_count = serializers.IntegerField(required=False, read_only=True)

    def get_folder_tree(self, obj):
        ancestors = obj.get_ancestors()
        if ancestors:
            return " > ".join([ancestor.title for ancestor in ancestors])
        return ""

    def to_representation(self, instance):
        organization = instance.organization
        data = super().to_representation(instance)

        if organization and organization.show_analytics:
            data.update(
                {
                    "views_count": instance.views_count,
                    "average_watched_time": instance.average_watched_time,
                    "total_watch_time": instance.total_watch_time,
                    "unique_viewers_count": instance.unique_viewers_count,
                }
            )
        else:
            data.pop("views_count", None)
            data.pop("average_watched_time", None)
            data.pop("total_watch_time", None)
            data.pop("unique_viewers_count", None)

        return data

    class Meta:
        model = Asset
        read_only_fields = AssetSerializer.Meta.read_only_fields
        fields = AssetSerializer.Meta.fields + [
            "download_url",
            "folder_tree",
            "views_count",
            "average_watched_time",
            "total_watch_time",
            "unique_viewers_count",
        ]


class FolderSerializer(serializers.ModelSerializer):
    title = serializers.CharField(max_length=1024, required=True)
    parent = FolderField(write_only=True, required=False, allow_null=True)

    class Meta:
        model = Asset
        read_only_fields = ["uuid"]
        fields = ["title", "parent", "uuid"]

    def validate(self, attrs):
        if Asset.objects.filter(
            type=Asset.Type.FOLDER,
            title__iexact=attrs.get("title").lower(),
            parent=attrs.get("parent"),
        ).exists():
            raise serializers.ValidationError(
                "Folder name already exists in destination."
            )

        return attrs
