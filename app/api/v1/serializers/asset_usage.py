from rest_framework import serializers

from app.models import AssetUsage


class AssertUsageSerializer(serializers.ModelSerializer):
    total_storage_bytes = serializers.SerializerMethodField()

    class Meta:
        model = AssetUsage
        fields = [
            "id",
            "date",
            "bandwidth_used",
            "subtitle_generation_cost",
            "live_stream_usage",
            "active_storage_bytes",
            "deleted_storage_bytes",
            "total_storage_bytes",
        ]

    def get_total_storage_bytes(self, obj):
        return obj.active_storage_bytes + obj.deleted_storage_bytes
