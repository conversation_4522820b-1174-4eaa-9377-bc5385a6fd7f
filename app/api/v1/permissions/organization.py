from django.db.models import Q
from rest_framework.permissions import BasePermission

from app.models import Organization


class HasOrganizationAccess(BasePermission):
    def has_permission(self, request, view):
        organization_id = view.kwargs.get("organization_id")
        if Organization.objects.filter(
            Q(members=request.user) | Q(created_by=request.user), uuid=organization_id
        ).exists():
            return True
        return False
