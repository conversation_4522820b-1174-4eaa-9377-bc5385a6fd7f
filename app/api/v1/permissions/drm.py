import base64
import json

from rest_framework.permissions import BasePermission

from app.utils.crypto import generate_signature


class IsDRMRequestDataValid(BasePermission):
    def has_permission(self, request, view):
        organization = view.organization
        signature = request.data.get("signature")
        data = request.data.get("request")
        try:
            expected_signature = generate_signature(
                base64.b64decode(data).decode(),
                organization.drm_aes_signing_key,
                organization.drm_aes_signing_iv,
            )
        except TypeError:
            return False
        return signature == expected_signature


class IsDRMLicenseRequestDataValid(BasePermission):
    def has_permission(self, request, view):
        organization = view.organization
        data = base64.b64decode(request.query_params.get("data")).decode()
        data = json.loads(data)

        if type(data) != dict:
            return False

        signature = data.get("signature")  # type: ignore
        content_data = data.get("content_data")  # type: ignore
        expected_signature = generate_signature(
            content_data,
            organization.drm_aes_signing_key,
            organization.drm_aes_signing_iv,
        )
        return signature == expected_signature
