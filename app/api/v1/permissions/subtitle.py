from rest_framework.permissions import BasePermission

from app.models import Track
from app.utils.base64 import url_safe_base64_decode


class IsSubtitleServerRequest(BasePermission):
    def has_permission(self, request, view):
        try:
            server_token = url_safe_base64_decode(
                request.query_params.get("server_token")
            )
            organization_uuid, asset_uuid, track_id = server_token.split("|")
            return Track.objects.filter(
                pk=int(track_id),
                organization__uuid=organization_uuid,
                video__asset__uuid=asset_uuid,
            ).exists()
        except Exception:
            return False
