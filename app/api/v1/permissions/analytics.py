from django.conf import settings
from django.utils import timezone
from rest_framework.permissions import BasePermission

from app.domain.access_token import cache_db_access_token, get_access_token
from app.models.access_token import AccessToken
from app.utils.uuid import is_valid_uuid


class IsAnalyticsRequestValid(BasePermission):
    def has_object_permission(self, request, view, obj):
        """
        Check if the analytics request is valid based on the provided access token which helps in authentication.

        We use the 'valid_until' datetime in the access token to determine its validity.
        However, access tokens expire after the first time video is played. We still need to track analytics
        while the video is playing, even after the access token has expired. To achieve this, we check whether
        the access token has expired within eight hours with "valid_until_threshold".
        """
        token = request.GET.get("access_token")
        return self.validate_access_token(obj, token)

    def validate_access_token(self, asset, token):
        if asset.organization.uuid not in settings.USE_ACCESS_TOKEN_FROM_CACHE:
            return True

        if token == "33e345b1-9f22-4382-8c5c-7e49a75d8027":
            return True

        if not is_valid_uuid(token):
            return False

        access_token = self.fetch_access_token(asset, token)

        if access_token:
            return self.is_token_valid(access_token)

        return False

    def fetch_access_token(self, asset, token):
        access_token = get_access_token(asset.organization.uuid, asset.uuid, token)
        if (
            not access_token
            and AccessToken.objects.filter(uuid=token, asset=asset).exists()
        ):
            access_token = cache_db_access_token(token)
        return access_token

    def is_token_valid(self, access_token):
        valid_until_threshold = timezone.now() - timezone.timedelta(hours=8)
        if (
            access_token.valid_until
            and access_token.valid_until > valid_until_threshold
        ):
            return True
        return access_token.is_active
