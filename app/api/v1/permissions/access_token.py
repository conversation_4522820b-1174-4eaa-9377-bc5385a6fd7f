from django.conf import settings
from django_multitenant.utils import get_current_tenant
from rest_framework.permissions import BasePermission

from app.domain.access_token import validate_access_token


class HasAccessTokenOrCheckAccess(BasePermission):
    def has_object_permission(self, request, view, obj):
        organization = get_current_tenant()
        if organization.uuid not in settings.USE_ACCESS_TOKEN_FROM_CACHE:
            return True

        token = request.GET.get("access_token")
        if token and validate_access_token(asset=obj, token=token):
            return True
        return organization and organization.is_member(request.user)
