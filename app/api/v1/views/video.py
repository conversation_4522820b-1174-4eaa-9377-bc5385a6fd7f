import base64
import binas<PERSON><PERSON>
import json

from django.db import transaction
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from django_multitenant.utils import set_current_tenant
from knox.auth import TokenAuthentication
from requests import HTTPError
from rest_framework import generics, parsers, status, views
from rest_framework.authentication import SessionAuthentication
from rest_framework.decorators import api_view
from rest_framework.response import Response

from app.api.mixins import OrganizationMixin
from app.api.v1.permissions import HasAccessTokenOrCheckAccess
from app.api.v1.serializers import AssetSerializer, VideoSerializer
from app.api.v1.serializers.video import VideoSerializerWithoutInput
from app.domain.access_token import expire_single_usage_access_token
from app.domain.drm.license.main import get_tpstreams_license
from app.domain.drm.license.track_license import DRMContentData, track_license
from app.domain.live_stream import terminate_live_server_on_transcoding_completion
from app.domain.video import (
    LumberjackDataParser,
    create_video_with_outputs,
    save_transcoding_info_to_asset,
    start_transcoding,
    store_migrated_path_to_asset,
)
from app.models import Asset, EncryptionKey, Organization, Video
from app.tasks import UpdateAssetSizeTask
from app.tasks.video import DownloadAndTranscodeVideoTask
from app.utils.browser import get_client_ip
from app.utils.parsers import OctetStreamParser
from app.utils.sentry import log_exception


class VideoAssetCreateView(OrganizationMixin, generics.CreateAPIView):
    serializer_class = VideoSerializer
    authentication_classes = [TokenAuthentication, SessionAuthentication]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        video = self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(
            AssetSerializer(video.asset).data,
            status=status.HTTP_201_CREATED,
            headers=headers,
        )

    @transaction.atomic
    def perform_create(self, serializer):
        video = serializer.save()
        DownloadAndTranscodeVideoTask.apply_async(
            kwargs={
                "video_id": video.id,
                "organization_uuid": self.organization.uuid,  # type: ignore
            }
        )
        return video


class UpdateVideoAssetStatusView(views.APIView, OrganizationMixin):
    def post(self, request, organization_id, asset_id):
        asset = get_object_or_404(Asset, uuid=asset_id)

        save_transcoding_info_to_asset(self.request, asset)
        self.schedule_asset_storage_calculation_task(asset)

        asset.notify_webhook()
        return Response(status=status.HTTP_200_OK)

    def schedule_asset_storage_calculation_task(self, asset):
        if asset.video.status == Video.Status.COMPLETED:
            UpdateAssetSizeTask.apply_async(
                kwargs={
                    "asset_id": asset.uuid,  # type: ignore
                    "organization_uuid": asset.organization.uuid,  # type: ignore
                }
            )


@api_view(["POST"])
def start_transcoding_view(request, organization_id, asset_id):
    organization = get_object_or_404(Organization, uuid=organization_id)
    set_current_tenant(organization)
    asset = get_object_or_404(Asset, uuid=asset_id)
    start_transcoding(asset.video)
    return Response(
        AssetSerializer(asset).data,
        status=status.HTTP_200_OK,
    )


class DRMLicenseView(OrganizationMixin, views.APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    parser_classes = (
        OctetStreamParser,
        parsers.JSONParser,
    )
    permission_classes = [HasAccessTokenOrCheckAccess]

    def dispatch(self, request, *args, **kwargs):
        self.asset = get_object_or_404(Asset, uuid=kwargs.get("asset_id"))

        return super().dispatch(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        if not self.asset.has_drm_encrypted_content:
            return Response(status=status.HTTP_404_NOT_FOUND)

        self.check_object_permissions(self.request, self.asset)
        self.update_single_usage_access_token_expiry()

        try:
            license_data = self.get_license_data()
            if isinstance(license_data, JsonResponse):
                license_data_dict = json.loads(license_data.content)
                if license_data_dict.get("status") == "ERROR":
                    raise ValueError(license_data_dict.get("status_message"))
            self.track_license()
        except HTTPError as exception:
            self.log_failure_on_sentry(exception)

            return HttpResponse(
                content=exception.response.content,
                status=exception.response.status_code,
            )
        except ValueError as exception:
            self.log_failure_on_sentry(exception)

            return HttpResponse(
                content=f"Error in generating license: {str(exception)}",
                status=status.HTTP_400_BAD_REQUEST,
            )

        return HttpResponse(
            license_data,
            status=status.HTTP_200_OK,
            content_type="application/octet-stream",
        )

    @property
    def content_id(self):
        return (
            self.asset.video.uuid.hex
            if hasattr(self.asset, "video")
            else self.asset.live_stream.uuid.hex
        )

    def get_license_data(self):
        drm_type = self.request.query_params.get("drm_type", "widevine")
        player_payload = self.get_player_payload(drm_type)
        license_specs = self.get_license_specs(drm_type)

        return get_tpstreams_license(
            self.content_id, player_payload, drm_type, license_specs
        )

    def get_player_payload(self, drm_type):
        request_data = self.request.data

        if isinstance(request_data, dict):
            player_payload = request_data.get("player_payload", "")

            if drm_type == "widevine":
                # Since JSON doesn't support bytes data, the client will encode the player_payload using base64.
                # So decode it before passing it to the Widevine API.
                return base64.b64decode(player_payload.encode("utf-8"))
            return player_payload or request_data.get("spc")

        return request_data

    def get_license_specs(self, drm_type):
        request_data = self.request.data
        FIFTEEN_DAYS = 60 * 60 * 24 * 15
        rental_duration_seconds = self.request.query_params.get(
            "rental_duration_seconds", FIFTEEN_DAYS
        )
        license_duration_seconds = self.request.query_params.get(
            "license_duration_seconds", FIFTEEN_DAYS
        )
        download = self.request.query_params.get("download", "").lower() == "true"
        if drm_type == "widevine" and isinstance(request_data, dict):
            widevine_data = request_data.get("widevine", {})
            widevine_data["rental_duration_seconds"] = rental_duration_seconds
            widevine_data["license_duration_seconds"] = license_duration_seconds
            widevine_data["can_persist"] = download
            return widevine_data
        return {
            "rental_duration_seconds": rental_duration_seconds,
            "can_persist": download,
            "license_duration_seconds": license_duration_seconds,
        }

    def log_failure_on_sentry(self, exception):
        tags = {"org_code": self.kwargs.get("organization_id")}
        extra = {
            "org_code": self.kwargs.get("organization_id"),
            "access_token": self.request.GET.get("access_token"),
            "Is DRM License exception": True,
        }
        log_exception(exception, tags, extra)

    def update_single_usage_access_token_expiry(self):
        content_length = int(self.request.META.get("CONTENT_LENGTH"))
        token = self.request.GET.get("access_token")
        # Expire only if content_length != 2 because the same API would be called
        # twice. First time it would pass a request object of 2 bytes length
        if token and content_length != 2:
            expire_single_usage_access_token(self.asset, token)

    def track_license(self):
        download = self.request.query_params.get("download", False)
        drm_type = self.request.query_params.get("drm_type", "widevine")
        ip_address = get_client_ip(self.request)
        user_agent = self.request.META.get("HTTP_USER_AGENT", "")[:255]

        content_data = DRMContentData(
            content_id=self.content_id, download=download, drm_type=drm_type
        )
        track_license(content_data, self.organization, user_agent, ip_address)


class BulkCreateVideoAssetView(OrganizationMixin, generics.CreateAPIView):
    serializer_class = VideoSerializerWithoutInput
    authentication_classes = [TokenAuthentication, SessionAuthentication]

    def get_queryset(self):
        return Asset.objects.all().prefetch_related(
            "video", "video__tracks", "video__inputs", "video__organization"
        )

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data, many=True)
        serializer.is_valid(raise_exception=True)
        assets = self.perform_create(serializer)
        return Response(
            AssetSerializer(assets, many=True).data,
            status=status.HTTP_201_CREATED,
        )

    @transaction.atomic
    def perform_create(self, serializer):
        assets = []
        folder = serializer.validated_data[0].get("folder")

        for data in serializer.validated_data:
            assets.append(
                Asset.objects.create(
                    created_by=self.request.user,
                    organization=self.organization,
                    title=data.pop("title", None),
                    parent=data.pop("folder", None),
                )
            )
        if folder:
            folder.update_children_count()

        for index, data in enumerate(serializer.validated_data):
            create_video_with_outputs(
                asset=assets[index],
                organization=self.organization,
                video_data={
                    "status": Video.Status.UPLOADING,
                    **data,
                },
            )
        return assets


class GetEmbedURLView(OrganizationMixin, views.APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]

    def post(self, request, *args, **kwargs):
        asset = get_object_or_404(Asset, uuid=kwargs["asset_id"])
        return Response(
            data={"embed_url": asset.video.get_embed_url()}, status=status.HTTP_200_OK
        )


class AESEncryptionKeyView(OrganizationMixin, views.APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [HasAccessTokenOrCheckAccess]

    def get(self, request, *args, **kwargs):
        asset = get_object_or_404(Asset, uuid=kwargs["asset_id"], type=Asset.Type.VIDEO)
        self.check_object_permissions(request, asset)
        self.update_single_usage_access_token_expiry(asset)

        if not asset.video.is_aes_encrypted:
            return Response(status=status.HTTP_404_NOT_FOUND)

        try:
            aes_key = asset.encryption_key.aes_encryption_key
        except EncryptionKey.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

        return HttpResponse(
            binascii.unhexlify(aes_key.hex), content_type="binary/octet-stream"
        )

    def update_single_usage_access_token_expiry(self, asset):
        token = self.request.GET.get("access_token")

        expire_single_usage_access_token(asset.video, token)


class LumberjackCallbackForMigratedVideoView(views.APIView, OrganizationMixin):
    def post(self, request, organization_id, asset_id):
        asset = get_object_or_404(Asset, uuid=asset_id)
        transcoding_data = LumberjackDataParser(request.data).__dict__()
        if self.is_video_transcoded():
            transcoding_data["has_tpstreams_drm"] = True
            if not asset.video.video_codecs == [Video.VideoCodec.H265]:
                store_migrated_path_to_asset(asset)
            self.save_transcoding_info_to_video(asset.video, transcoding_data)
            terminate_live_server_on_transcoding_completion(asset)
        elif self.is_video_transcoding_failed():
            transcoding_data["has_tpstreams_drm"] = False
            self.save_transcoding_info_to_video(asset.video, transcoding_data)

        return Response(status=status.HTTP_200_OK)

    def save_transcoding_info_to_video(self, video, transcoding_data):
        if video.meta_data:
            video.meta_data.update(transcoding_data)
        else:
            video.meta_data = transcoding_data
        video.save(update_fields=["meta_data"])

    def is_video_transcoded(self):
        status = self.request.data.get("status", "").lower()
        progress = self.request.data.get("progress", 0)
        return status == "completed" and progress == 100

    def is_video_transcoding_failed(self):
        status = self.request.data.get("status", "").lower()
        progress = self.request.data.get("progress", 0)
        return (
            status == "error"
            or (status == "completed" and progress < 100)
            or (status != "completed" and progress == 100)
        )
