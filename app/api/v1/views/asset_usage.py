from django_filters.rest_framework import DjangoFilterBackend
from knox.auth import TokenAuthentication
from rest_framework import generics
from rest_framework.authentication import SessionAuthentication

from app.api.mixins import OrganizationMixin
from app.api.v1.serializers import AssertUsageSerializer
from app.filters.asset import AssetUsageFilter
from app.models import AssetUsage


class AssetUsageView(OrganizationMixin, generics.ListAPIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    serializer_class = AssertUsageSerializer
    filterset_class = AssetUsageFilter
    filter_backends = [DjangoFilterBackend]

    def get_queryset(self):
        return AssetUsage.objects.all()
