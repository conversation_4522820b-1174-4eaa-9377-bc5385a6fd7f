import json
import os
import uuid

from botocore.exceptions import Client<PERSON>rror
from django.core.serializers.json import DjangoJ<PERSON>NEncoder
from django.shortcuts import get_object_or_404
from knox.auth import TokenAuthentication
from rest_framework import generics, status, views
from rest_framework.authentication import SessionAuthentication
from rest_framework.response import Response

from app.api.authentication_classes import CsrfExemptSessionAuthentication
from app.api.mixins import OrganizationMixin
from app.domain.cloud_storage import (
    abort_multipart_upload,
    complete_multipart_upload,
    create_multipart_upload,
    generate_presigned_put_url,
    get_multipart_parts,
)
from app.models import Asset, VideoInput


class CreateMultipartView(OrganizationMixin, views.APIView):
    authentication_classes = (CsrfExemptSessionAuthentication, TokenAuthentication)

    def post(self, request, *args, **kwargs):
        try:
            response = create_multipart_upload(
                self.organization, **self.get_client_parameters()
            )
            if self.is_asset():
                self.add_input_url_to_asset(response.get("Key"))
        except ClientError as e:
            return Response(
                data=json.dumps({"error": e.response}),
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response(
            status=status.HTTP_200_OK,
            content_type="application/json",
            data={"key": response.get("Key"), "uploadId": response.get("UploadId")},
        )

    def get_client_parameters(self):
        options = {
            "ACL": self.get_acl(),
            "Bucket": self.organization.bucket_name,
            "ContentType": self.request.data.get("type"),
            "Key": self.get_upload_path(),
            "Metadata": self.get_sanitized_meta(self.request.data.get("metadata", {})),
        }

        options.update(self.request.data.get("params", {}))
        if self.organization.uses_s3_storage:
            options.update({"StorageClass": "GLACIER_IR"})

        return options

    def get_acl(self):
        # Wasabi supports cloudfront url only for public files
        return "private" if self.organization.uses_s3_storage else "public-read"

    def get_upload_path(self):
        extension = os.path.splitext(self.request.data.get("filename", ""))[1]
        folder = ""
        if self.request.data.get("metadata", {}).get("is_video", "false") == "true":
            folder += "videos/"
        if self.request.data.get("metadata", {}).get("folder", None):
            folder += self.request.data.get("metadata", {}).get("folder", None) + "/"
        return f"private/{folder}{uuid.uuid4().hex}{extension.lower()}"

    def get_sanitized_meta(self, data):
        data = self.remove_unicode_from_metadata(data)
        return data

    def remove_unicode_from_metadata(self, data):
        for key, value in data.items():
            data[key] = value.encode("ascii", "ignore").decode("ascii")
        return data

    def is_asset(self):
        return self.request.data.get("metadata", {}).get("asset_id", None) is not None

    def add_input_url_to_asset(self, input_path):
        asset_id = self.request.data.get("metadata", {}).get("asset_id")
        asset = get_object_or_404(Asset, uuid=asset_id)
        if asset.video.inputs.exists():
            return

        VideoInput.objects.create(
            url=input_path,
            video=asset.video,
            organization=self.organization,
        )


class MultipartUploadRetrieveAbortView(
    OrganizationMixin, generics.RetrieveDestroyAPIView
):
    authentication_classes = (SessionAuthentication, TokenAuthentication)

    def get(self, request, *args, **kwargs):
        try:
            parts = get_multipart_parts(
                self.organization,
                self.request.GET.get("key"),
                self.kwargs.get("upload_id"),
            )
        except ClientError as e:
            return Response(
                data=json.dumps({"error": e.response}),
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response(
            json.dumps(parts, cls=DjangoJSONEncoder),
            content_type="application/json",
            status=status.HTTP_200_OK,
        )

    def delete(self, request, *args, **kwargs):
        try:
            abort_multipart_upload(
                self.organization,
                request.GET.get("key"),
                kwargs.get("upload_id"),
            )
        except ClientError as e:
            return Response(
                data={"error": e.response}, status=status.HTTP_400_BAD_REQUEST
            )
        return Response(status=status.HTTP_200_OK)


class BatchSignPartUpload(OrganizationMixin, views.APIView):
    authentication_classes = (SessionAuthentication, TokenAuthentication)

    def get(self, request, *args, **kwargs):
        presigned_urls = {}
        for part_number in request.GET.get("partNumbers", "").split(","):
            try:
                presigned_urls[int(part_number)] = generate_presigned_put_url(
                    self.organization,
                    self.request.GET.get("key"),
                    self.kwargs.get("upload_id"),
                    int(part_number),
                )
            except ClientError as e:
                return Response(
                    data={"error": e.response}, status=status.HTTP_400_BAD_REQUEST
                )
        return Response(
            data={"presignedUrls": presigned_urls},
            content_type="application/json",
            status=status.HTTP_200_OK,
        )


class SignPartUpload(BatchSignPartUpload):
    def get(self, request, *args, **kwargs):
        try:
            presigned_url = generate_presigned_put_url(
                self.organization,
                self.request.GET.get("key"),
                self.kwargs.get("upload_id"),
                int(kwargs.get("part_number")),
            )
        except ClientError as e:
            return Response(
                data={"error": e.response}, status=status.HTTP_400_BAD_REQUEST
            )
        return Response(
            data={"url": presigned_url},
            content_type="application/json",
            status=status.HTTP_200_OK,
        )


class CompleteMultipartUpload(OrganizationMixin, views.APIView):
    authentication_classes = (CsrfExemptSessionAuthentication, TokenAuthentication)

    def post(self, request, *args, **kwargs):
        try:
            response = complete_multipart_upload(
                self.organization,
                self.request.GET.get("key"),
                self.kwargs.get("upload_id"),
                self.request.data.get("parts", []),
            )
        except ClientError as e:
            return Response(
                data={"error": e.response}, status=status.HTTP_400_BAD_REQUEST
            )
        return Response(
            data={"location": f"{self.organization.cdn_url}{response.get('Key')}"},
            content_type="application/json",
            status=status.HTTP_200_OK,
        )
