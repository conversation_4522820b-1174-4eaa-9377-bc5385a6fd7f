from knox.auth import TokenAuthentication
from rest_framework import viewsets
from rest_framework.authentication import SessionAuthentication

from app.api.mixins import OrganizationMixin
from app.api.v1.serializers import WebhookSerializer
from app.models import Webhook


class WebhookViewSet(OrganizationMixin, viewsets.ModelViewSet):
    lookup_field = "uuid"
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    serializer_class = WebhookSerializer

    def get_queryset(self):
        return Webhook.objects.all()
