from django.db.models import Q
from knox.auth import TokenAuthentication
from rest_framework import generics, permissions
from rest_framework.authentication import SessionAuthentication

from app.api.v1.serializers import OrganizationSerializer
from app.models import Organization


class OrganizationListView(generics.ListAPIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = OrganizationSerializer

    def get_queryset(self):
        return Organization.objects.filter(
            Q(members=self.request.user) | Q(created_by=self.request.user)
        )
