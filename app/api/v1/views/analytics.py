from datetime import datetime

from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.utils.crypto import get_random_string
from rest_framework import views
from rest_framework.throttling import UserRateThrottle

from app.api.authentication_classes import CsrfExemptSessionAuthentication
from app.api.mixins import OrganizationMixin
from app.api.v1.permissions.analytics import IsAnalyticsRequestValid
from app.domain.analytics import AnalyticsData, RequestMetadata, track_analytics
from app.models.asset import Asset, AssetViewerLog
from app.utils.base64 import decode_value, encode_value
from app.utils.timezone import get_timestamp_string
from app.utils.wasabi import generate_presigned_get_url, generate_presigned_put_url

THREE_HOURS = 3 * 60 * 60
ONE_YEAR = 365 * 24 * 60 * 60
ONE_MINUTE = 1 * 60


class GeneratePresignedURLAnalyticsView(OrganizationMixin, views.APIView):
    authentication_classes = [CsrfExemptSessionAuthentication]
    permission_classes = [IsAnalyticsRequestValid]

    def post(self, request, organization_id, asset_id):
        asset = get_object_or_404(Asset, uuid=asset_id)
        self.check_object_permissions(self.request, asset)

        visitor_id = request.GET.get("visitorId")
        session_id = request.GET.get("sessionId")
        key = self.generate_path(asset, session_id, visitor_id)

        presigned_get_url = generate_presigned_get_url(key)
        presigned_put_url = generate_presigned_put_url(key)
        response_data = {
            "presigned_get_url": presigned_get_url,
            "presigned_put_url": presigned_put_url,
        }

        return JsonResponse(response_data)

    def generate_path(self, asset, session_id, visitor_id):
        organization_uuid = asset.organization.uuid
        current_date = datetime.now().strftime("%Y/%m/%d")
        key = f"{organization_uuid}/{current_date}/{asset.uuid}-{session_id}-{visitor_id}.json"
        return key


class AnalyticsThrottle(UserRateThrottle):
    scope = "track_analytics_throttle"

    def allow_request(self, request, view):
        if request.method == "POST":
            return super().allow_request(request, view)
        return True


class TrackAnalyticsView(OrganizationMixin, views.APIView):
    authentication_classes = [CsrfExemptSessionAuthentication]
    throttle_classes = [AnalyticsThrottle]
    permission_classes = [IsAnalyticsRequestValid]

    def get(self, request, organization_id, asset_id):
        asset = get_object_or_404(Asset, uuid=asset_id)
        self.check_object_permissions(self.request, asset)

        duration = self.get_duration_if_exists(asset)
        response_data = {"duration": duration}

        return JsonResponse(response_data)

    def get_duration_if_exists(self, asset):
        visitor_id = decode_value(self.request.GET.get("visitorID"))
        session_id = decode_value(self.request.GET.get("sessionID"))
        asset_viewer_log = AssetViewerLog.objects.filter(
            asset=asset, session_id=session_id, visitor_id=visitor_id
        ).first()
        return asset_viewer_log.duration if asset_viewer_log else 0

    def post(self, request, organization_id, asset_id):
        asset = get_object_or_404(Asset, uuid=asset_id)
        self.check_object_permissions(self.request, asset)

        analytics_data = self.get_or_create_analytics_data()
        track_analytics(request, asset, analytics_data)

        response = self.generate_tracking_response(analytics_data)
        self.set_tracking_cookies(response, analytics_data)

        return response

    def get_or_create_analytics_data(self):
        visitor_id = (
            decode_value(self.request.data.get("visitorID"))
            if self.request.data.get("visitorID")
            else get_random_string(length=32)
        )
        session_id = (
            decode_value(self.request.data.get("sessionID"))
            if self.request.data.get("sessionID")
            else get_random_string(length=40)
        )
        duration = int(self.request.data.get("duration", 0))
        return AnalyticsData(visitor_id, session_id, duration)

    def generate_tracking_response(self, analytics_data):
        response_data = {
            "duration": analytics_data.duration,
        }
        return JsonResponse(response_data)

    def set_tracking_cookies(self, response, analytics_data):
        self.set_cookie_in_response(
            response,
            "visitorID",
            analytics_data.visitor_id,
            ONE_YEAR,
        )

        self.set_cookie_in_response(
            response,
            "sessionID",
            analytics_data.session_id,
            THREE_HOURS,
        )

    def set_cookie_in_response(self, response, name, value, max_age):
        request_metadata = RequestMetadata(self.request)
        encoded_value = encode_value(value)
        response.set_cookie(
            name,
            encoded_value,
            expires=get_timestamp_string(
                max_age, current_time=request_metadata.browser_time
            ),
            secure=True,
            samesite="None",
        )
