from django.conf import settings
from django.shortcuts import get_object_or_404
from knox.auth import TokenAuthentication
from rest_framework import status, viewsets
from rest_framework.authentication import SessionAuthentication
from rest_framework.response import Response

from app.api.mixins import OrganizationMixin
from app.api.v1.serializers import AccessTokenSerializer
from app.domain.access_token import cache_db_access_token, get_access_token
from app.models import AccessToken, Asset


class AccessTokenViewSet(OrganizationMixin, viewsets.ModelViewSet):
    lookup_field = "uuid"
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    serializer_class = AccessTokenSerializer

    def get_queryset(self):
        return AccessToken.objects.none()

    def get_serializer_context(self):
        asset = get_object_or_404(Asset, uuid=self.kwargs["asset_uuid"])
        context = super().get_serializer_context()
        context.update({"asset": asset, "organization": self.organization})
        return context

    def create(self, request, *args, **kwargs):
        if self.organization.uuid in settings.USE_ACCESS_TOKEN_FROM_CACHE:
            return super().create(request, *args, **kwargs)

        asset_id = self.kwargs["asset_uuid"]
        sample_token = "33e345b1-9f22-4382-8c5c-7e49a75d8027"
        url = f"https://app.tpstreams.com/embed/{self.organization.uuid}/{asset_id}/?access_token={sample_token}"
        data = {
            "playback_url": url,
            "expires_after_first_usage": False,
            "code": sample_token,
            "status": "Active",
            "valid_until": None,
            "annotations": [],
        }
        return Response(data=data, status=status.HTTP_201_CREATED)

    def get_object(self):
        asset = get_object_or_404(Asset, uuid=self.kwargs["asset_uuid"])
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
        token_uuid = self.kwargs[lookup_url_kwarg]

        if self.organization.uuid in settings.USE_ACCESS_TOKEN_FROM_CACHE:
            access_token = get_access_token(
                asset.organization.uuid, asset.uuid, token_uuid
            )
            if access_token:
                return access_token

        access_token = get_object_or_404(
            AccessToken,
            uuid=token_uuid,
            organization=self.organization,
            asset=asset,
        )
        cache_db_access_token(token_uuid)
        return access_token
