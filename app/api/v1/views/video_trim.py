from django.shortcuts import get_object_or_404
from knox.auth import TokenAuthentication
from rest_framework import generics, permissions, status, views
from rest_framework.authentication import SessionAuthentication
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response

from app.api.mixins import OrganizationMixin
from app.api.v1.serializers.video_trim import (
    VideoTrimLogSerializer,
    VideoTrimRequestSerializer,
    VideoTrimResponseSerializer,
    VideoTrimStatusSerializer,
)
from app.domain.video_trim.core import (
    check_existing_trim_job,
    get_latest_completed_trim,
    start_video_trim_background_task,
    start_video_trim_revert_background_task,
)
from app.domain.video_trim.exceptions import VideoTrimValidationError
from app.models import Asset
from app.models.video import Video
from app.models.video_trim import VideoTrim


class VideoTrimView(OrganizationMixin, views.APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, organization_id, asset_uuid):
        asset = get_object_or_404(
            Asset, uuid=asset_uuid, type__in=[Asset.Type.VIDEO, Asset.Type.LIVESTREAM]
        )

        self._validate_video_requirements(asset)

        video = asset.video
        video_duration_seconds = int(video.duration.total_seconds())

        serializer = VideoTrimRequestSerializer(
            data=request.data, video_duration=video_duration_seconds
        )
        serializer.is_valid(raise_exception=True)

        return self._start_trim_job(video, serializer.validated_data, request.user)

    def _validate_video_requirements(self, asset):
        if not hasattr(asset, "video"):
            raise ValidationError("Asset does not have an associated video")

        video = asset.video

        if video.status != Video.Status.COMPLETED:
            raise ValidationError("Video must be fully transcoded before trimming")

        if not video.duration:
            raise ValidationError("Video duration is not available")

        if video.duration.total_seconds() <= 0:
            raise ValidationError("Video duration is invalid")

        existing_job = check_existing_trim_job(video)
        if existing_job:
            raise ValidationError(
                f"Another trim operation is currently {existing_job.get_status_display().lower()} for this video. "
                f"Please wait for it to complete before starting a new trim operation."
            )

    def _start_trim_job(self, video, validated_data, user):
        try:
            trim_job = start_video_trim_background_task(
                video=video,
                start_time=validated_data["start_time"],
                end_time=validated_data["end_time"],
                created_by=user,
            )

            response_data = {
                "message": "Video trim job started successfully",
                "trim_job_id": trim_job.id,
                "status": trim_job.get_status_display(),
            }

            return Response(
                VideoTrimResponseSerializer(response_data).data,
                status=status.HTTP_202_ACCEPTED,
            )

        except VideoTrimValidationError as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class VideoTrimStatusView(OrganizationMixin, views.APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, organization_id, asset_uuid):
        asset = get_object_or_404(
            Asset, uuid=asset_uuid, type__in=[Asset.Type.VIDEO, Asset.Type.LIVESTREAM]
        )

        if not hasattr(asset, "video"):
            return Response(
                {"detail": "Asset does not have an associated video"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        latest_trim = (
            VideoTrim.objects.filter(video=asset.video).order_by("-created").first()
        )

        if not latest_trim:
            return Response(
                {"detail": "No trim jobs found for this video"},
                status=status.HTTP_404_NOT_FOUND,
            )

        return Response(
            VideoTrimStatusSerializer(latest_trim).data, status=status.HTTP_200_OK
        )


class VideoTrimRevertView(OrganizationMixin, views.APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, organization_id, asset_uuid):
        asset = get_object_or_404(
            Asset, uuid=asset_uuid, type__in=[Asset.Type.VIDEO, Asset.Type.LIVESTREAM]
        )

        if not hasattr(asset, "video"):
            raise ValidationError("Asset does not have an associated video")

        existing_job = check_existing_trim_job(asset.video)
        if existing_job:
            raise ValidationError(
                f"Another trim operation is currently {existing_job.get_status_display().lower()} for this video. "
                f"Please wait for it to complete before reverting."
            )

        try:
            completed_trim = get_latest_completed_trim(asset.video)
            if not completed_trim:
                raise ValidationError("No completed trim found for this video")

            task_result = start_video_trim_revert_background_task(asset.video)

            return Response(
                {
                    "message": "Video revert job started successfully",
                    "task_id": str(task_result.id),
                },
                status=status.HTTP_202_ACCEPTED,
            )

        except VideoTrimValidationError as e:
            raise ValidationError(str(e))


class VideoTrimLogsView(OrganizationMixin, generics.ListAPIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = VideoTrimLogSerializer

    def get_queryset(self):
        asset_uuid = self.kwargs.get("asset_uuid")
        asset = get_object_or_404(
            Asset, uuid=asset_uuid, type__in=[Asset.Type.VIDEO, Asset.Type.LIVESTREAM]
        )

        if not hasattr(asset, "video"):
            raise ValidationError("Asset does not have an associated video")

        return (
            VideoTrim.objects.select_related("created_by")
            .filter(video=asset.video)
            .order_by("-created")
        )
