from rest_framework import status, views
from rest_framework.response import Response

from app.api.v1.permissions import IsTestpressLiveStreamRequest
from app.api.v1.serializers import TestpressSerializer
from app.domain.cloudfront import configure_distribution_for_testpress_livestream
from app.domain.organization import (
    create_organization_for_testpress,
    generate_auth_token,
)
from app.domain.webhook import register_webhook


class TestpressIntegrationView(views.APIView):
    permission_classes = [IsTestpressLiveStreamRequest]

    def post(self, request):
        serializer = TestpressSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        organization, auth_token = self._integrate_testpress(validated_data)
        return Response(
            {
                "tpstreams_org_id": str(organization.uuid),
                "auth_token": auth_token,
            },
            status=status.HTTP_201_CREATED,
        )

    def _integrate_testpress(self, validated_data):
        organization, org_created = create_organization_for_testpress(validated_data)
        if org_created:
            configure_distribution_for_testpress_livestream(organization)
        register_webhook(
            organization, validated_data["webhook_url"], validated_data["secret_token"]
        )
        auth_token = generate_auth_token(
            organization, validated_data["email"], validated_data["password"]
        )

        return organization, auth_token
