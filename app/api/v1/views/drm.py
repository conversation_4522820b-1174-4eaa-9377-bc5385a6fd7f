import base64
import json

from django.http import HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from rest_framework import parsers, serializers, status
from rest_framework.exceptions import PermissionDenied
from rest_framework.response import Response
from rest_framework.views import APIView

from app.api.authentication_classes import CsrfExemptSessionAuthentication
from app.api.mixins import OrganizationMixin
from app.api.v1.permissions import IsDRMLicenseRequestDataValid, IsDRMRequestDataValid
from app.domain.drm.encryption.streams import (
    generate_and_store_fairplay_encryption_keys,
    generate_and_store_widevine_encryption_keys,
)
from app.domain.drm.license.main import (
    get_tpstreams_fairplay_license,
    get_tpstreams_widevine_license,
)
from app.domain.drm.license.track_license import DRMContentData, track_license
from app.domain.shaka_packager import (
    is_packager_request_data_valid,
    parse_packager_request,
)
from app.utils.browser import get_client_ip
from app.utils.organization import set_tenant
from app.utils.parsers import Oct<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ShakaPackagerParser
from app.utils.uuid import is_valid_uuid


class GenerateWidevineEncryptionKeyView(OrganizationMixin, APIView):
    permission_classes = [IsDRMRequestDataValid]
    authentication_classes = [CsrfExemptSessionAuthentication]
    parser_classes = [ShakaPackagerParser, parsers.JSONParser]

    def post(self, request, *args, **kwargs):
        content_key_response = generate_and_store_widevine_encryption_keys(
            self.request.data.get("request"), self.organization
        )
        return Response(status=status.HTTP_200_OK, data=content_key_response)


@csrf_exempt
def generate_widevine_encryption_key_view(request, organization_id=None):
    organization = set_tenant(organization_id)
    json_data = parse_packager_request(request)

    if not is_packager_request_data_valid(json_data, organization):
        raise PermissionDenied()

    content_key_response = generate_and_store_widevine_encryption_keys(
        json_data.get("request"), organization
    )
    return JsonResponse(content_key_response)


class GenerateFairplayEncryptionKeyView(OrganizationMixin, APIView):
    permission_classes = [IsDRMRequestDataValid]
    authentication_classes = [CsrfExemptSessionAuthentication]

    def post(self, request, *args, **kwargs):
        content_id = self.get_content_id()
        fairplay_encryption_key_data = generate_and_store_fairplay_encryption_keys(
            content_id, self.organization
        )
        return Response(status=status.HTTP_200_OK, data=fairplay_encryption_key_data)

    def get_content_id(self):
        try:
            request_data = base64.b64decode(self.request.data.get("request")).decode()
            data = json.loads(request_data)
        except ValueError:
            raise serializers.ValidationError("Content ID is missing")

        if not data.get("content_id"):
            raise serializers.ValidationError("Content ID is missing")

        if not is_valid_uuid(data["content_id"]):
            raise serializers.ValidationError(
                "Invalid Content ID. Content ID should be a valid uuid"
            )

        return data["content_id"]


class DRMLicenseViewV2(OrganizationMixin, APIView):
    permission_classes = [IsDRMLicenseRequestDataValid]
    authentication_classes = [CsrfExemptSessionAuthentication]
    parser_classes = (
        OctetStreamParser,
        parsers.JSONParser,
    )

    def post(self, request, *args, **kwargs):
        content_data = self.get_content_data()

        if not content_data or not getattr(content_data, "content_id", None):
            return HttpResponse(
                {"error": "Missing or invalid content_id"},
                status=status.HTTP_404_NOT_FOUND,
            )
        
        license_data = self.get_license_data(content_data)
        if isinstance(license_data, dict) and license_data.get("status") == "ERROR":
                return HttpResponse(
                    license_data, 
                    status=status.HTTP_400_BAD_REQUEST,
                )
        
        self.track_license(content_data)
        return HttpResponse(
            license_data,
            status=status.HTTP_200_OK,
            content_type="application/octet-stream",
        )

    def get_content_data(self):
        data = self.request.query_params.get("data")
        if not data:
            raise ValueError("Invalid or missing content data.")
        try:
            return DRMContentData.create(data)
        
        except Exception as e:
            raise ValueError(f"Failed to process content data: {e}")

    def get_license_data(self, content_data):
        request_data = self.request.data
        player_payload = self.get_player_payload(content_data.drm_type)
        license_specs = {"can_persist": content_data.download}

        if content_data.drm_type == "widevine":
            if isinstance(request_data, dict):
                license_specs = request_data.get("widevine", {})

            license_data = get_tpstreams_widevine_license(
                content_data.content_id,
                player_payload,
                license_specs,
            )

        else:
            license_data = get_tpstreams_fairplay_license(
                content_data.content_id, player_payload, license_specs
            )
        return license_data

    def get_player_payload(self, drm_type):
        request_data = self.request.data

        if isinstance(request_data, dict):
            player_payload = request_data.get("player_payload", "")

            if drm_type == "widevine":
                # Since JSON doesn't support bytes data, the client will encode the player_payload using base64.
                # So decode it before passing it to the Widevine API.
                return base64.b64decode(player_payload.encode("utf-8"))
            return player_payload or request_data.get("spc")

        return request_data

    def track_license(self, content_data):
        ip_address = get_client_ip(self.request)
        user_agent = self.request.META.get("HTTP_USER_AGENT", "")[:255]
        track_license(content_data, self.organization, user_agent, ip_address)
