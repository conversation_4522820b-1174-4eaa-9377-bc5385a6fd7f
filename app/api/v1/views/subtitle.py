from django.shortcuts import get_object_or_404
from knox.auth import TokenAuthentication
from rest_framework import generics, status, views, viewsets
from rest_framework.authentication import SessionAuthentication
from rest_framework.response import Response

from app.api.authentication_classes import CsrfExemptSessionAuthentication
from app.api.mixins import OrganizationMixin
from app.api.v1.permissions.subtitle import IsSubtitleServerRequest
from app.api.v1.serializers import AssetSerializer
from app.api.v1.serializers.video import TrackSerializer
from app.domain.subtitle import (
    delete_subtitle_server_if_needed,
    generate_subtitle,
    store_subtitle_generation_details,
    upload_subtitle,
)
from app.models.asset import Asset
from app.models.track import Track


class SubtitleServerStatusCallbackView(OrganizationMixin, generics.CreateAPIView):
    authentication_classes = [CsrfExemptSessionAuthentication]
    permission_classes = [IsSubtitleServerRequest]

    def post(self, request, *args, **kwargs):
        subtitle = get_object_or_404(Track, pk=self.kwargs.get("subtitle_id"))
        subtitle_status = request.data.get("status")
        store_subtitle_generation_details(subtitle, subtitle_status=subtitle_status)
        delete_subtitle_server_if_needed(subtitle, subtitle_status)
        return Response(status=status.HTTP_200_OK)


class GenerateSubtitleView(OrganizationMixin, views.APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]

    def post(self, request, organization_id, asset_id):
        asset = get_object_or_404(
            Asset.objects.prefetch_related("video"), uuid=asset_id
        )
        asset.video.enable_subtitle_generation()
        if asset.video.should_generate_subtitle():
            generate_subtitle(asset)
        response = AssetSerializer(asset).data
        return Response(data=response, status=status.HTTP_201_CREATED)


class UploadSubtitleView(OrganizationMixin, views.APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    MAX_FILE_SIZE = 2 * 1024 * 1024  # 2 MB

    def post(self, request, organization_id, asset_id):
        asset = get_object_or_404(Asset, uuid=asset_id)
        language = request.data.get("language", "en")
        subtitle_name = request.data.get("name", f"{language}_sub")
        subtitle_file = request.FILES.get("subtitle")

        if self.is_subtitle_file_valid(subtitle_file):
            upload_subtitle(asset, subtitle_name, language, subtitle_file)
            return Response(
                {"detail": "Subtitle uploaded successfully"},
                status=status.HTTP_201_CREATED,
            )

        return Response(
            {"detail": "Upload a valid subtitle file in .vtt format with size <= 2MB"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    def is_subtitle_file_valid(self, subtitle_file):
        return (
            subtitle_file
            and subtitle_file.name.endswith(".vtt")
            and subtitle_file.size <= self.MAX_FILE_SIZE
        )


class TrackViewSet(OrganizationMixin, viewsets.ModelViewSet):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    serializer_class = TrackSerializer

    def get_queryset(self):
        return Track.objects.filter(type=Track.Type.SUBTITLE)

    def filter_queryset(self, queryset):
        queryset = super().filter_queryset(queryset)
        asset_uuid = self.kwargs.get("asset_uuid")
        if asset_uuid:
            queryset = queryset.filter(video__asset__uuid=asset_uuid)
        return queryset

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.subtitle_type == Track.SubtitleType.AUTO_GENERATED:
            return Response(
                {"error": "Deletion of auto-generated subtitle not allowed."},
                status=status.HTTP_403_FORBIDDEN,
            )
        return super().destroy(request, *args, **kwargs)
