from datetime import timed<PERSON><PERSON>

from django.shortcuts import get_object_or_404
from knox.auth import TokenAuthentication
from rest_framework import viewsets
from rest_framework.authentication import SessionAuthentication
from rest_framework.response import Response
from rest_framework.views import APIView

from app.api.mixins import OrganizationMixin
from app.api.v1.serializers.transcoding_job import TranscodingJobSerializer
from app.domain.transcoder import <PERSON><PERSON><PERSON><PERSON><PERSON>Transcoder
from app.models import TranscodingJob
from app.utils.datetime import convert_iso8601_to_datetime
from app.utils.organization import set_tenant


class TranscodingJobViewSet(OrganizationMixin, viewsets.ModelViewSet):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    serializer_class = TranscodingJobSerializer
    lookup_field = "uuid"

    def get_queryset(self):
        return TranscodingJob.objects.all()

    def destroy(self, request, *args, **kwargs):
        transcoding_job = self.get_object()
        if transcoding_job.status == TranscodingJob.Status.COMPLETED:
            return Response({"message": "Job is already completed."}, status=204)
        LumberjackJobTranscoder(transcoding_job).stop()
        return Response({"message": "Job cancelled successfully."}, status=204)


class UpdateTranscodingJobStatusView(APIView):
    def post(self, request, organization_id, transcoding_job_id, *args, **kwargs):
        set_tenant(organization_id)
        transcoding_job = get_object_or_404(TranscodingJob, uuid=transcoding_job_id)
        duration = request.data.get("video_duration")

        transcoding_start_time = request.data.get("start_time")
        transcoding_end_time = request.data.get("end_time")

        if duration:
            transcoding_job.video_duration = timedelta(seconds=duration)

        if transcoding_start_time:
            transcoding_job.start_time = convert_iso8601_to_datetime(
                transcoding_start_time
            )

        if transcoding_end_time:
            transcoding_job.end_time = convert_iso8601_to_datetime(transcoding_end_time)
        transcoding_job.status = self.get_status()
        transcoding_job.error_message = request.data.get("error_message")
        transcoding_job.save()
        transcoding_job.notify_webhook()
        return Response(status=200)

    def get_status(self):
        status = self.request.data.get("status", "").lower()
        transcoding_status = TranscodingJob.Status.ERROR
        if status in ["queued", "not started"]:
            transcoding_status = TranscodingJob.Status.QUEUED
        elif status == "processing":
            transcoding_status = TranscodingJob.Status.TRANSCODING
        elif status == "completed":
            transcoding_status = TranscodingJob.Status.COMPLETED
        return transcoding_status
