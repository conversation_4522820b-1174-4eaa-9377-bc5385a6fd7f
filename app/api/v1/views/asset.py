from django.contrib import messages
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from knox.auth import TokenAuthentication
from rest_framework import generics, mixins, permissions, status, viewsets
from rest_framework.authentication import SessionAuthentication
from rest_framework.response import Response

from app.api.mixins import OrganizationMixin, PermissionPolicyMixin
from app.api.v1.permissions import HasAccessTokenOrCheckAccess
from app.api.v1.serializers import AssetSerializer
from app.api.v1.serializers.asset import (
    AdminAssetDetailSerializer,
    AssetDetailSerializer,
    FolderSerializer,
)
from app.domain.haproxy import remove_assigned_ip_address_from_proxy
from app.domain.live_stream import delete_live_stream_server, update_termination_log
from app.domain.openresty import remove_ip_address_from_openresty
from app.filters.asset import AssetFilter
from app.models import Asset
from app.models.live_stream import LiveStream
from app.tasks.purge_asset import soft_delete_asset_task, stop_transcoding_task


class AssetListView(OrganizationMixin, generics.ListAPIView):
    serializer_class = AssetSerializer
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    filterset_class = AssetFilter
    filter_backends = [DjangoFilterBackend]

    def get_queryset(self):
        return Asset.objects.all().prefetch_related(
            "video",
            "video__tracks",
            "video__organization",
            "video__inputs",
            "live_stream",
            "live_stream__organization",
            "parent",
            "organization",
            "video__tracks__organization",
        )


class AssetUpdateDeleteRetrieveView(
    OrganizationMixin,
    PermissionPolicyMixin,
    mixins.RetrieveModelMixin,
    mixins.UpdateModelMixin,
    mixins.DestroyModelMixin,
    viewsets.GenericViewSet,
):
    lookup_field = "uuid"
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [permissions.IsAuthenticated]
    permission_classes_per_method = {"retrieve": [HasAccessTokenOrCheckAccess]}

    def get_serializer_class(self):
        if self.request.user.is_anonymous:
            return AssetDetailSerializer
        return AdminAssetDetailSerializer

    def get_queryset(self):
        return Asset.objects.all().prefetch_related(
            "video", "video__tracks", "video__inputs", "video__tracks__playlists"
        )

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        if hasattr(instance, "live_stream") and instance.live_stream.is_active:
            return Response(
                {
                    "detail": (
                        "Cannot delete asset while live stream is active. "
                        "Please try again after stopping the stream."
                    )
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        self._stop_live_stream_if_needed(instance, request)
        stop_transcoding_task.delay(instance.uuid)
        soft_delete_asset_task.delay(instance.uuid, instance.organization.uuid)
        messages.success(
            self.request,
            "Asset deletion initiated. The asset will be moved to trash shortly.",
        )

        return Response(status=status.HTTP_202_ACCEPTED)

    def _stop_live_stream_if_needed(self, instance, request):
        live_stream = getattr(instance, "live_stream", None)
        if (
            live_stream
            and live_stream.server_id
            and live_stream.server_status
            not in [
                LiveStream.ServerStatus.NOT_CREATED,
                LiveStream.ServerStatus.DESTROYED,
            ]
        ):
            delete_live_stream_server(instance.live_stream)
            update_termination_log(instance.live_stream, request)
            remove_ip_address_from_openresty(instance)
            remove_assigned_ip_address_from_proxy(instance)


class FolderListCreateView(OrganizationMixin, generics.ListCreateAPIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    serializer_class = FolderSerializer
    filterset_class = AssetFilter
    filter_backends = [DjangoFilterBackend]

    def get_queryset(self):
        return Asset.objects.filter(
            organization=self.organization, type=Asset.Type.FOLDER
        )

    def perform_create(self, serializer):
        folder = serializer.save(organization=self.organization, type=Asset.Type.FOLDER)
        if folder.parent:
            folder.parent.update_children_count()


class AssetMoveView(OrganizationMixin, generics.GenericAPIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = AssetSerializer

    def post(self, request, *args, **kwargs):
        asset = get_object_or_404(Asset, uuid=kwargs["asset_id"])
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        parent = serializer.validated_data.get("parent")
        asset.move(parent)
        return Response(
            {"detail": "Asset moved successfully."}, status=status.HTTP_200_OK
        )
