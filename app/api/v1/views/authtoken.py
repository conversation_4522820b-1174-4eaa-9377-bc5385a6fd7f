from django.contrib.auth import login
from knox.models import get_token_model
from knox.views import LoginView as KnoxLoginView
from rest_framework import permissions

from app.api.v1.serializers import AuthTokenSerializer
from app.models import Organization


class LoginView(KnoxLoginView):
    authentication_classes = ()
    permission_classes = (permissions.AllowAny,)

    def create_token(self):
        token_prefix = self.get_token_prefix()
        return get_token_model().objects.create(
            user=self.request.user,
            expiry=self.get_token_ttl(),
            organization=self.organization,
            prefix=token_prefix,
        )

    def post(self, request, format=None):
        serializer = AuthTokenSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.validated_data["user"]
        login(request, user)
        self.organization = Organization.objects.get(
            uuid=serializer.validated_data["organization_id"]
        )
        return super().post(request, format=None)
