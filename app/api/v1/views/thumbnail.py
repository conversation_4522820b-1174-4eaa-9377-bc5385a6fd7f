from django.conf import settings
from django.shortcuts import get_object_or_404
from knox.auth import TokenAuthentication
from rest_framework import status, views
from rest_framework.authentication import SessionAuthentication
from rest_framework.response import Response

from app.api.mixins import OrganizationMixin
from app.domain.thumbnail import upload_thumbnail
from app.models.asset import Asset


class UploadThumbnailView(OrganizationMixin, views.APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]

    def post(self, request, organization_id, asset_id):
        asset = get_object_or_404(Asset, uuid=asset_id)
        thumbail_image = request.FILES.get("thumbnail")

        if self.is_thumbnail_image_valid(thumbail_image):
            upload_thumbnail(asset, thumbail_image)
            return Response(
                {"detail": "Thumbnail uploaded successfully"},
                status=status.HTTP_201_CREATED,
            )

        return Response(
            {
                "detail": "Upload a valid Thumbnail image in .png, .jpeg, .jpg format with size <= 2MB"
            },
            status=status.HTTP_400_BAD_REQUEST,
        )

    def is_thumbnail_image_valid(self, uploaded_image):
        return (
            uploaded_image
            and uploaded_image.name.lower().endswith((".png", ".jpeg", ".jpg"))
            and uploaded_image.size <= settings.MAXIMUM_THUMBNAIL_SIZE_BYTES
        )
