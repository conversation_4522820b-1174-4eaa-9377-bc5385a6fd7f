from rest_framework import serializers

from app.models import Asset, Video


class ChoiceField(serializers.ChoiceField):
    def __init__(self, **kwargs):
        self.choices = kwargs.get("choices")
        self.human_readable = {value: key for key, value in self.choices.items()}
        super().__init__(**kwargs)

    def to_representation(self, obj):
        return self.choices[self.choice_strings_to_values[str(obj)]]

    def to_internal_value(self, data):
        try:
            return self.human_readable[data]
        except KeyError:
            self.fail("invalid_choice", input=data)


class FolderField(serializers.Field):
    def to_representation(self, value):
        from app.api.v1.serializers.asset import FolderSerializer

        if value is None:
            return None
        return FolderSerializer(value).data

    def to_internal_value(self, uuid):
        if not uuid:
            return None

        self._raise_error_if_its_not_valid_folder_uuid(uuid)
        return Asset.objects.get(type=Asset.Type.FOLDER, uuid=uuid)

    def _raise_error_if_its_not_valid_folder_uuid(self, uuid):
        if not Asset.objects.filter(type=Asset.Type.FOLDER, uuid=uuid).exists():
            raise serializers.ValidationError("Invalid folder UUID")


class ContentProtectionTypeField(serializers.CharField):
    content_protection_mapping = {
        Video.ContentProtectionType.DISABLED: "disabled",
        Video.ContentProtectionType.DRM: "drm",
        Video.ContentProtectionType.AES: "aes",
        Video.ContentProtectionType.AES_WITH_SIGNED_URL: "aes_with_signed_url",
    }

    def to_representation(self, value):
        return self.content_protection_mapping.get(value)

    def to_internal_value(self, protection_type):
        protection_type = protection_type.lower()
        if protection_type in self.content_protection_mapping.values():
            reverse_mapping = {
                value: key for key, value in self.content_protection_mapping.items()
            }
            return reverse_mapping.get(protection_type)

        raise serializers.ValidationError("Invalid content protection type")
