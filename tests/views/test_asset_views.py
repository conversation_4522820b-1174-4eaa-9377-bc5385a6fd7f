import datetime
import json
from unittest import mock
from unittest.mock import patch

from django.http import Http404
from django.test import RequestFactory, override_settings
from django.urls import reverse
from django_multitenant.utils import set_current_tenant, unset_current_tenant
from freezegun import freeze_time

from app.api.v1.views import AssetUpdateDeleteRetrieveView
from app.models import Asset
from app.models.live_stream import LiveStream
from app.models.track import Track
from app.views import (
    AssetDebugView,
    AssetDeleteListView,
    AssetDeleteSearchView,
    AssetDetailView,
    AssetListView,
    AssetMoveView,
    AssetRetriggerView,
    AssetSearchView,
    AssetUploadView,
    EmptyTrashView,
    FolderDeleteView,
    VideoPreferencesView,
)
from app.views.assets import download_asset_source_video_view
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin
from tests.mixins.asset import AssetViewerLogMixin, TrackMixin


class TestAssetListView(Asset<PERSON>ix<PERSON>, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)

    def test_should_list_folders_first(self):
        self.create_assets(count=5, type=Asset.Type.VIDEO)
        self.create_assets(count=5, type=Asset.Type.FOLDER)

        request = self.get_request(path="/", user=self.user)
        response = AssetListView.as_view()(request)
        folders = (
            Asset.objects.root_nodes()
            .filter(type=Asset.Type.FOLDER)
            .order_by("-created")
        )
        videos = (
            Asset.objects.root_nodes()
            .filter(type=Asset.Type.VIDEO)
            .order_by("-created")
        )

        self.assertQuerysetEqual(folders, response.context_data["assets"][:5])  # type: ignore
        self.assertQuerysetEqual(videos, response.context_data["assets"][5:])  # type: ignore


class TestAssetMoveView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)

    @patch("app.views.assets.messages.success")
    @patch("app.tasks.rebuild_asset_tree.delay")
    def test_view_should_move_asset_to_destination(self, mock_rebuild, message):
        folder = self.create_folder()
        asset = self.create_asset()

        url = reverse("asset_move") + f"?asset={asset.uuid}&folder={folder.uuid}"
        request = self.get_request(path=url, method="post", user=self.user)
        response = AssetMoveView.as_view()(request)

        asset.refresh_from_db()

        self.assertEqual(asset.parent, folder)
        self.assertEqual(response.status_code, 302)

    @patch("app.views.assets.messages.success")
    @patch("app.tasks.rebuild_asset_tree.delay")
    def test_view_should_move_asset_to_root_directory(self, mock_rebuild, message):
        folder = self.create_folder()
        asset = self.create_asset()
        asset.move(folder)

        url = reverse("asset_move") + f"?asset={asset.uuid}"
        request = self.get_request(path=url, method="post", user=self.user)
        response = AssetMoveView.as_view()(request)

        asset.refresh_from_db()

        self.assertEqual(asset.parent, None)
        self.assertEqual(response.status_code, 302)


class TestAssetSearchView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)

    def test_should_search_asset_by_title_query(self):
        self.create_asset(title="video")
        self.create_assets(count=5)

        request = self.get_request(path="search/?q={}".format("video"), user=self.user)
        response = AssetSearchView.as_view()(request)
        self.assertEqual(
            Asset.objects.get(title="video"), response.context_data["assets"][0]  # type: ignore
        )

    def test_should_search_asset_by_uuid_query(self):
        self.create_assets(count=5, type=Asset.Type.FOLDER)
        folder = (
            Asset.objects.root_nodes()
            .filter(type=Asset.Type.FOLDER)
            .order_by("-created")
            .first()
        )
        request = self.get_request(path=f"search/?q={folder.uuid}", user=self.user)
        response = AssetSearchView.as_view()(request)
        self.assertEqual(folder, response.context_data["assets"][0])  # type: ignore


class TestDownloadSourceVideoView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)
        self.video = self.create_video()
        self.asset = self.video.asset

    @mock.patch("app.domain.video.cloudfront")
    def test_download_source_video_view_with_valid_asset_and_download_url(
        self, mock_generate_presigned_url
    ):
        mock_generate_presigned_url.generate_presigned_url.return_value = (
            "https://example.com/download"
        )
        url = reverse("asset_download", args=[self.asset.uuid])
        request = self.get_request(path=url, method="post", user=self.user)
        response = download_asset_source_video_view(request, asset_uuid=self.asset.uuid)

        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, "https://example.com/download")

    @patch("app.views.assets.messages.warning")
    def test_download_source_video_view_with_invalid_asset(self, message):
        self.asset = self.create_asset(type=Asset.Type.FOLDER)

        url = reverse("asset_download", args=[self.asset.uuid])
        request = self.get_request(path=url, method="post", user=self.user)
        response = download_asset_source_video_view(request, asset_uuid=self.asset.uuid)

        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, reverse("assets"))


class TestAssetDetailView(AssetViewerLogMixin, TrackMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        self.asset = self.create_asset(type=Asset.Type.VIDEO)
        set_current_tenant(self.organization)

    @freeze_time("2023-09-01")
    def test_get_monthly_metrics_json(self):
        # Create a test request with a 'month' parameter
        self.create_asset_viewer_log(
            asset=self.asset,
            created=datetime.datetime(2023, 9, 1, tzinfo=datetime.timezone.utc),
            duration=120,
        )
        url = reverse("asset_detail", args=[self.asset.uuid])
        request = self.get_request(path=url, method="get", user=self.user)
        response = AssetDetailView.as_view()(request, asset_uuid=self.asset.uuid)

        self.assertEqual(response.status_code, 200)

        context = response.context_data

        monthly_metrics_json_str = context["monthly_metrics_json"]
        monthly_metrics_json = json.loads(monthly_metrics_json_str)

        # Assert that the JSON response contains the expected keys and values
        self.assertIn("2023-09-01", monthly_metrics_json)
        self.assertEqual(monthly_metrics_json["2023-09-01"]["total_watch_time"], 120)
        self.assertEqual(monthly_metrics_json["2023-09-01"]["views_count"], 1)

    def test_get_captions_should_return_active_captions_of_asset(self):
        self.active_subtitle = self.create_subtitle()
        url = reverse("asset_detail", args=[self.active_subtitle.video.asset.uuid])
        request = self.get_request(path=url, method="get", user=self.user)
        response = AssetDetailView.as_view()(
            request, asset_uuid=self.active_subtitle.video.asset.uuid
        )
        self.assertEqual(response.status_code, 200)
        context = response.context_data
        active_captions = context["active_captions"]
        expected_captions = Track.objects.filter(is_active=True).first()
        self.assertEqual(active_captions.first(), expected_captions)

    def test_organization_user_can_access_asset_detail_page(self):
        user_from_same_organization = self.create_user(
            current_organization_uuid=self.organization.uuid
        )
        user_from_same_organization.set_password("password")
        user_from_same_organization.save()

        self.client.login(email=user_from_same_organization.email, password="password")

        url = reverse("asset_detail", args=[self.asset.uuid])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, str(self.asset))

    def test_asset_detail_page_raises_404_for_cross_organization_access(self):
        unset_current_tenant()
        other_organization = self.create_organization()
        user_from_other_organization = self.create_user(
            current_organization_uuid=other_organization.uuid
        )
        user_from_other_organization.set_password("password")
        user_from_other_organization.save()

        self.client.login(email=user_from_other_organization.email, password="password")

        url = reverse("asset_detail", args=[self.asset.uuid])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    @override_settings(
        DEBUG=True,
    )
    @patch("app.views.messages.success")
    def test_should_update_video_setting_with_given_data(self, mock_message):
        data = {"autoplay_enabled": True}
        factory = RequestFactory()
        video = self.create_video()
        request = factory.post(
            reverse("edit_video", kwargs={"asset_uuid": video.asset.uuid}), data=data
        )
        request.user = self.user

        response = VideoPreferencesView.as_view()(request, asset_uuid=video.asset.uuid)
        video.refresh_from_db()

        self.assertEqual(response.status_code, 302)
        self.assertEqual(video.autoplay_enabled, True)
        mock_message.assert_called_with(
            request, "Your default video setting has been changed successfully."
        )

    @override_settings(
        DEBUG=True,
    )
    @patch("app.views.messages.success")
    def test_should_chanage_use_global_player_preferences_on_updating_video_setting(
        self, mock_message
    ):
        data = {"autoplay_enabled": True}
        factory = RequestFactory()
        video = self.create_video()
        request = factory.post(
            reverse("edit_video", kwargs={"asset_uuid": video.asset.uuid}), data=data
        )
        request.user = self.user

        response = VideoPreferencesView.as_view()(request, asset_uuid=video.asset.uuid)
        video.refresh_from_db()

        self.assertEqual(response.status_code, 302)
        self.assertEqual(video.use_global_player_preferences, False)
        mock_message.assert_called_with(
            request, "Your default video setting has been changed successfully."
        )


class TestAssetDebugView(AssetViewerLogMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        self.asset = self.create_asset(type=Asset.Type.VIDEO)
        set_current_tenant(self.organization)

    @mock.patch("app.views.assets.AssetDebugView.check_live_server_status")
    @mock.patch("app.views.assets.AssetDebugView.check_haproxy_status")
    @mock.patch("app.views.assets.AssetDebugView.check_wasabi_status")
    @mock.patch("app.views.assets.check_cdn_status")
    @mock.patch("app.views.assets.AssetDebugView.check_license_server")
    def test_video_deubug_page_should_return_only_called(
        self,
        mock_check_license_server,
        mock_check_cdn_status,
        mock_check_wasabi_status,
        mock_check_haproxy_status,
        mock_check_live_server_status,
    ):
        video = self.create_video()
        url = reverse("asset_debug", args=[video.asset.uuid])
        request = self.get_request(path=url, method="get", user=self.user)
        response = AssetDebugView.as_view()(request, asset_uuid=video.asset.uuid)
        self.assertEqual(response.status_code, 200)

    @mock.patch("app.views.assets.AssetDebugView.check_live_server_status")
    @mock.patch("app.views.assets.AssetDebugView.check_haproxy_status")
    @mock.patch("app.views.assets.AssetDebugView.check_wasabi_status")
    @mock.patch("app.views.assets.check_cdn_status")
    @mock.patch("app.views.assets.AssetDebugView.check_license_server")
    def test_asset_debug_view_should_checks_all_server_statuses(
        self,
        mock_check_license_server,
        mock_check_cdn_status,
        mock_check_wasabi_status,
        mock_check_haproxy_status,
        mock_check_live_server_status,
    ):
        video = self.create_video()
        url = reverse("asset_debug", args=[video.asset.uuid])
        request = self.get_request(path=url, method="get", user=self.user)
        response = AssetDebugView.as_view()(request, asset_uuid=video.asset.uuid)
        self.assertEqual(response.status_code, 200)
        mock_check_cdn_status.assert_called()
        mock_check_license_server.assert_called()
        mock_check_wasabi_status.assert_called()
        mock_check_haproxy_status.assert_called()
        mock_check_live_server_status.assert_called()


class TestAssetDeleteListView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)

    @mock.patch("app.tasks.purge_asset.soft_delete_asset_task.delay")
    @mock.patch("app.views.assets.messages.success")
    @mock.patch("app.tasks.purge_asset.stop_transcoding_task.delay")
    def test_success_message_on_delete(
        self, mock_stop_transcoding_task, mock_message, mock_soft_delete_asset_task
    ):
        asset = self.create_asset()

        delete_request = self.get_request(
            "/",
            method="DELETE",
            user=self.user,
        )

        response = AssetUpdateDeleteRetrieveView.as_view({"delete": "destroy"})(
            delete_request,
            organization_id=self.organization.uuid,
            uuid=asset.uuid,
        )

        mock_message.assert_called_with(
            mock.ANY,
            "Asset deletion initiated. The asset will be moved to trash shortly.",
        )

        self.assertEqual(response.status_code, 202)

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @mock.patch("app.views.assets.messages.success")
    @mock.patch("app.tasks.purge_asset.stop_transcoding_task.delay")
    def test_view_should_list_only_deleted_assets(
        self, mock_stop_transcoding_task, mock_message
    ):
        asset = self.create_asset()
        deleted_asset = self.create_asset()
        delete_request = self.get_request(
            "/",
            method="DELETE",
            user=self.organization.created_by,
        )
        AssetUpdateDeleteRetrieveView.as_view({"delete": "destroy"})(
            delete_request,
            organization_id=self.organization.uuid,
            uuid=deleted_asset.uuid,
        )

        request = self.get_request(path="/", user=self.user)
        response = AssetDeleteListView.as_view()(request)

        self.assertTrue(deleted_asset in response.context_data["assets"])  # type: ignore
        self.assertFalse(asset in response.context_data["assets"])  # type: ignore

    @mock.patch("app.views.assets.messages.success")
    @patch("app.api.v1.views.asset.delete_live_stream_server")
    @patch("app.api.v1.views.asset.update_termination_log")
    @patch("app.tasks.purge_asset.stop_transcoding_task.delay")
    def test_destroy_should_stop_live_stream_if_needed_and_delete_asset(
        self,
        mock_stop_transcoding_task,
        mock_termination_log,
        mock_stop_live_stream,
        mock_message,
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.DISCONNECTED, server_id="1234"
        )
        delete_request = self.get_request(
            "/",
            method="DELETE",
            user=self.organization.created_by,
        )
        response = AssetUpdateDeleteRetrieveView.as_view({"delete": "destroy"})(
            delete_request,
            organization_id=self.organization.uuid,
            uuid=live_stream.asset.uuid,
        )

        self.assertEqual(response.status_code, 202)

    @mock.patch("app.views.assets.messages.success")
    @patch("app.api.v1.views.asset.delete_live_stream_server")
    @patch("app.api.v1.views.asset.update_termination_log")
    @patch("app.tasks.purge_asset.stop_transcoding_task.delay")
    def test_destroy_should_not_delete_asset_with_active_live_stream(
        self,
        mock_stop_transcoding_task,
        mock_termination_log,
        mock_stop_live_stream,
        mock_message,
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING, server_id="1234"
        )
        delete_request = self.get_request(
            "/",
            method="DELETE",
            user=self.organization.created_by,
        )
        response = AssetUpdateDeleteRetrieveView.as_view({"delete": "destroy"})(
            delete_request,
            organization_id=self.organization.uuid,
            uuid=live_stream.asset.uuid,
        )

        self.assertEqual(response.status_code, 400)

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @mock.patch("app.views.assets.messages.success")
    @mock.patch("app.tasks.purge_asset.stop_transcoding_task.delay")
    def test_post_method_should_restore_the__video(
        self, mock_stop_transcoding_task, mock_message
    ):
        deleted_asset = self.create_asset()
        delete_request = self.get_request(
            "/",
            method="DELETE",
            user=self.organization.created_by,
        )
        AssetUpdateDeleteRetrieveView.as_view({"delete": "destroy"})(
            delete_request,
            organization_id=self.organization.uuid,
            uuid=deleted_asset.uuid,
        )
        deleted_asset.refresh_from_db()

        request = self.get_request(
            path=f"/?asset={deleted_asset.uuid}", method="post", user=self.user
        )
        request.POST = {"action": "undelete", "asset": deleted_asset.uuid}

        AssetDeleteListView.as_view()(request)
        deleted_asset.refresh_from_db()

        self.assertEqual(deleted_asset.deleted, None)

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @mock.patch("app.views.assets.messages.success")
    @mock.patch("app.domain.purge_asset.delete_video")
    @mock.patch("app.tasks.purge_asset.stop_transcoding_task.delay")
    def test_post_method_should_hard_delete_the_video(
        self, mock_stop_transcoding_task, mock_delete_video, mock_message
    ):
        video = self.create_video()
        deleted_asset = video.asset

        delete_request = self.get_request(
            "/",
            method="DELETE",
            user=self.organization.created_by,
        )
        AssetUpdateDeleteRetrieveView.as_view({"delete": "destroy"})(
            delete_request,
            organization_id=self.organization.uuid,
            uuid=deleted_asset.uuid,
        )
        deleted_asset.refresh_from_db()

        request = self.get_request(
            path=f"/?asset={deleted_asset.uuid}", method="post", user=self.user
        )
        request.POST = {"action": "undelete", "asset": deleted_asset.uuid}

        AssetDeleteListView.as_view()(request)

        mock_delete_video.asset_called()

    @mock.patch("app.views.assets.messages.success")
    @mock.patch("app.tasks.purge_asset.stop_transcoding_task.delay")
    def test_view_should_call_stop_transcoding_task(
        self, mock_stop_transcoding_task, mock_message
    ):
        deleted_asset = self.create_asset()
        delete_request = self.get_request(
            "/",
            method="DELETE",
            user=self.organization.created_by,
        )
        AssetUpdateDeleteRetrieveView.as_view({"delete": "destroy"})(
            delete_request,
            organization_id=self.organization.uuid,
            uuid=deleted_asset.uuid,
        )

        mock_stop_transcoding_task.assert_called()


class TestEmptyTrashView(OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)
        user_from_same_organization = self.create_user(
            current_organization_uuid=self.organization.uuid
        )
        user_from_same_organization.set_password("password")
        user_from_same_organization.save()

        self.client.login(email=user_from_same_organization.email, password="password")

    @mock.patch("app.tasks.purge_asset.empty_trash_task.delay")
    @mock.patch("app.views.assets.messages.success")
    def test_post_triggers_empty_trash_task_and_sends_success_message(
        self, mock_message, mock_empty_trash_task
    ):
        request = self.get_request(path="/", method="post", user=self.user)
        response = EmptyTrashView.as_view()(request)
        mock_empty_trash_task.assert_called_once()
        mock_message.assert_called_once_with(
            request, "Trash empty process has been triggered successfully."
        )

        self.assertEqual(302, response.status_code)
        self.assertEqual(reverse("deleted_assets"), response.url)

    def test_post_redirects_unauthenticated_user_to_login(self):
        self.client.logout()
        response = self.client.post(reverse("empty_trash"))
        login_url = f"/accounts/login/?next={reverse('empty_trash')}"
        self.assertRedirects(response, login_url)


class TestAssetRetriggerView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        self.asset = self.create_asset()
        self.video = self.create_video()
        set_current_tenant(self.organization)
        self.videoStatus = self.video.Status

    @patch("app.views.assets.start_transcoding")
    @patch("app.views.assets.messages.success")
    def test_trigger_retranscode_for_not_started_video(
        self, mock_message, mock_start_transcoding
    ):
        video = self.create_video(asset=self.asset, status=self.videoStatus.NOT_STARTED)
        data = {"action": "retranscode"}
        request = self.get_request(path="", method="post", user=self.user)
        request.POST = data
        AssetRetriggerView.as_view()(request, asset_uuid=self.asset.uuid)
        mock_start_transcoding.assert_called_once_with(video)

    @patch("app.views.assets.generate_subtitle")
    @patch("app.views.assets.messages.success")
    @patch("app.models.video.Video.should_generate_subtitle")
    def test_trigger_subtitle_regeneration_for_error_video(
        self,
        mock_should_generate_subtitle,
        mock_message,
        mock_generate_subtitle,
    ):
        mock_should_generate_subtitle.return_value = True
        self.create_video(asset=self.asset, status=self.videoStatus.ERROR)
        data = {"action": "regenerate_subtitles"}
        request = self.get_request(path="", method="post", user=self.user)
        request.POST = data
        AssetRetriggerView.as_view()(request, asset_uuid=self.asset.uuid)
        mock_generate_subtitle.assert_called_once_with(self.asset)

    @patch("app.views.assets.transcode_video_with_backup")
    @patch("app.views.assets.messages.success")
    def test_trigger_transcode_with_backup_for_completed_video(
        self, mock_message, mock_transcode_video_with_backup
    ):
        video = self.create_video(asset=self.asset, status=self.videoStatus.COMPLETED)
        data = {"action": "transcode_video_with_backup"}
        request = self.get_request(path="", method="post", user=self.user)
        request.POST = data
        AssetRetriggerView.as_view()(request, asset_uuid=self.asset.uuid)
        mock_transcode_video_with_backup.assert_called_once_with(video)

    @patch("app.views.assets.start_transcoding")
    @patch("app.views.assets.messages.error")
    def test_should_not_trigger_for_uploaded_video(
        self, mock_message, mock_start_transcoding
    ):
        asset = self.create_asset()
        self.create_video(asset=asset, status=self.videoStatus.UPLOADED)
        data = {"action": "retranscode"}
        request = self.get_request(path="", method="post", user=self.user)
        request.POST = data

        AssetRetriggerView.as_view()(request, asset_uuid=asset.uuid)
        mock_message.assert_called_once_with(
            request, "Video Processing already in progress."
        )

    @patch("app.views.assets.generate_subtitle")
    @patch("app.views.assets.messages.success")
    @patch("app.models.video.Video.should_generate_subtitle")
    def test_should_not_regenerate_existing_Autogenerated_subtitles(
        self, mock_should_generate_subtitle, mock_message, mock_generate_subtitle
    ):
        mock_should_generate_subtitle.return_value = False
        self.create_video(asset=self.asset, status=self.videoStatus.ERROR)
        data = {"action": "regenerate_subtitles"}
        request = self.get_request(path="", method="post", user=self.user)
        request.POST = data
        AssetRetriggerView.as_view()(request, asset_uuid=self.asset.uuid)
        mock_generate_subtitle.assert_not_called()


class TestAssetUploadView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization(
            enabled_resolutions=["4k", "720p", "240p"]
        )
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)

    def test_should_include_only_enabled_resolutions_in_context(self):
        request = self.get_request(path="", method="get", user=self.user)
        response = AssetUploadView.as_view()(request)
        self.assertEqual(
            response.context_data["enabled_resolutions"], ["4k", "720p", "240p"]
        )


class TestAssetDeleteSearchView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @patch("app.views.assets.messages.success")
    @patch("app.tasks.purge_asset.stop_transcoding_task.delay")
    def test_should_search_deleted_asset_by_title_query(
        self, mock_stop_transcoding_task, mock_message
    ):
        asset = self.create_asset(title="deleted_video")

        delete_request = self.get_request(
            "/",
            method="DELETE",
            user=self.user,
        )
        AssetUpdateDeleteRetrieveView.as_view({"delete": "destroy"})(
            delete_request,
            organization_id=self.organization.uuid,
            uuid=asset.uuid,
        )

        request = self.get_request(
            path="deleted_search/?q={}".format("deleted_video"), user=self.user
        )
        response = AssetDeleteSearchView.as_view()(request)
        self.assertEqual(
            Asset.objects.deleted_only().get(title="deleted_video"),
            response.context_data["assets"][0],
        )

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @patch("app.views.assets.messages.success")
    @patch("app.tasks.purge_asset.stop_transcoding_task.delay")
    def test_should_search_deleted_asset_by_uuid_query(
        self, mock_stop_transcoding_task, mock_message
    ):
        asset = self.create_assets(count=1)[0]

        delete_request = self.get_request(
            "/",
            method="DELETE",
            user=self.user,
        )
        AssetUpdateDeleteRetrieveView.as_view({"delete": "destroy"})(
            delete_request,
            organization_id=self.organization.uuid,
            uuid=asset.uuid,
        )

        request = self.get_request(
            path=f"deleted_search/?q={asset.uuid}", user=self.user
        )
        response = AssetDeleteSearchView.as_view()(request)
        self.assertEqual(asset, response.context_data["assets"][0])  # type: ignore


class TestFolderDeleteView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)
        self.folder = self.create_folder(title="Test Folder")

    @property
    def path(self):
        return reverse("delete_folder", kwargs={"uuid": self.folder.uuid})

    @patch("app.views.assets.messages.success")
    @patch("app.views.assets.soft_delete_asset_task.delay")
    def test_should_trigger_folder_deletion(self, mock_delete_task, mock_message):
        request = self.get_request(
            path=self.path,
            method="post",
            user=self.user,
        )
        response = FolderDeleteView.as_view()(request, uuid=self.folder.uuid)

        mock_delete_task.assert_called_once_with(
            self.folder.uuid, self.organization.uuid
        )
        self.assertEqual(302, response.status_code)

    @patch("app.views.assets.messages.success")
    @patch("app.views.assets.soft_delete_asset_task.delay")
    def test_should_redirect_to_parent_folder_if_exists(
        self, mock_delete_task, mock_message
    ):
        parent_folder = self.create_folder(title="Parent Folder")
        child_folder = self.create_folder(title="Child Folder", parent=parent_folder)

        request = self.get_request(
            path=reverse("delete_folder", kwargs={"uuid": child_folder.uuid}),
            method="post",
            user=self.user,
        )
        response = FolderDeleteView.as_view()(request, uuid=child_folder.uuid)

        mock_delete_task.assert_called_once_with(
            child_folder.uuid, self.organization.uuid
        )
        self.assertEqual(302, response.status_code)
        self.assertEqual(f"/?folder={parent_folder.uuid}", response.url)

    @patch("app.views.assets.messages.success")
    @patch("app.views.assets.soft_delete_asset_task.delay")
    def test_should_redirect_to_root_if_no_parent(self, mock_delete_task, mock_message):
        request = self.get_request(
            path=self.path,
            method="post",
            user=self.user,
        )
        response = FolderDeleteView.as_view()(request, uuid=self.folder.uuid)

        mock_delete_task.assert_called_once_with(
            self.folder.uuid, self.organization.uuid
        )
        self.assertEqual(302, response.status_code)
        self.assertEqual(reverse("assets"), response.url)

    @patch("app.views.assets.soft_delete_asset_task.delay")
    def test_should_return_404_for_invalid_uuid(self, mock_delete_task):
        request = self.get_request(
            path=reverse("delete_folder", kwargs={"uuid": "invalid-uuid"}),
            method="post",
            user=self.user,
        )
        with self.assertRaises(Http404):
            FolderDeleteView.as_view()(request, uuid="invalid-uuid")
        mock_delete_task.assert_not_called()

    @patch("app.views.assets.messages.success")
    @patch("app.views.assets.soft_delete_asset_task.delay")
    def test_should_not_allow_deleting_non_folder_asset(
        self, mock_delete_task, mock_message
    ):
        video = self.create_asset(type=Asset.Type.VIDEO)
        request = self.get_request(
            path=reverse("delete_folder", kwargs={"uuid": video.uuid}),
            method="post",
            user=self.user,
        )
        with self.assertRaises(Http404):
            FolderDeleteView.as_view()(request, uuid=video.uuid)
        mock_delete_task.assert_not_called()
