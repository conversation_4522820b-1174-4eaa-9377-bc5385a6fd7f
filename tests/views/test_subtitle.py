from unittest.mock import patch

from django.core.files.base import ContentFile
from django.urls import reverse
from django_multitenant.utils import set_current_tenant

from app.models.track import Track
from app.views import SubtitleUploadView
from app.views.export import SubtitlesGenerationUsageExportView
from app.views.subtitle import SubtitleDeleteView, SubtitleUpdateView
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin
from tests.mixins.asset import TrackMixin


class TestSubtitleUploadView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)

    @patch("app.views.assets.messages.success")
    @patch("app.views.subtitle.upload_subtitle", return_value="/")
    def test_upload_subtitle_method_should_be_called_while_uploading_subtitle(
        self, mock_upload_captions, mock_message
    ):
        video = self.create_video()
        url = reverse("upload_subtitle", kwargs={"asset_uuid": video.asset.uuid})
        request = self.get_request(path=url, method="post", user=self.user)
        request.FILES["file"] = ContentFile(b"file_content", name="mock_file.vtt")
        SubtitleUploadView.as_view()(request, asset_uuid=video.asset.uuid)

        mock_upload_captions.assert_called()

    @patch("app.views.assets.messages.success")
    @patch("app.views.subtitle.upload_subtitle", return_value="/")
    def test_srt_file_upload(self, mock_upload_captions, mock_message):
        video = self.create_video()
        url = reverse("upload_subtitle", kwargs={"asset_uuid": video.asset.uuid})
        srt_content = """1
        00:00:05,759 --> 00:00:07,580
        foreign"""

        request = self.get_request(path=url, method="post", user=self.user)
        request.FILES["file"] = ContentFile(srt_content.encode(), name="mock_file.srt")

        SubtitleUploadView.as_view()(request, asset_uuid=video.asset.uuid)

        response = SubtitleUploadView.as_view()(request, asset_uuid=video.asset.uuid)

        self.assertEqual(response.status_code, 302)
        mock_upload_captions.assert_called()


class TestSubtitleDeleteView(TrackMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)

    @patch("app.views.assets.messages.success")
    @patch("app.views.subtitle.delete_file", return_value="/")
    def test_subtitle_delete_view_should_delete_subtitle(
        self, mock_upload_captions, mock_message
    ):
        self.subtitle = self.create_subtitle()
        url = reverse(
            "delete_subtitle",
            kwargs={
                "asset_uuid": self.subtitle.video.asset.uuid,
                "subtitle_id": self.subtitle.id,
            },
        )
        request = self.get_request(path=url, method="post", user=self.user)
        SubtitleDeleteView.as_view()(
            request,
            asset_uuid=self.subtitle.video.asset.uuid,
            subtitle_id=self.subtitle.id,
        )
        self.assertEqual(None, Track.objects.first())


class TestSubtitleUpdateView(TrackMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)

    @patch("app.views.assets.messages.success")
    @patch("app.views.subtitle.upload_subtitle", return_value="/")
    def test_should_change_status_of_subtitle(self, mock_upload_captions, mock_message):
        self.subtitle = self.create_subtitle(is_active=True)
        url = reverse(
            "edit_subtitle",
            kwargs={
                "asset_uuid": self.subtitle.video.asset.uuid,
                "subtitle_id": self.subtitle.id,
            },
        )
        request = self.get_request(path=url, method="post", user=self.user)
        SubtitleUpdateView.as_view()(
            request,
            asset_uuid=self.subtitle.video.asset.uuid,
            subtitle_id=self.subtitle.id,
        )
        self.assertEqual(False, Track.objects.first().is_active)


class TestSubtitlesGenerationUsageExportView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)

    def test_export_subtitles_generation_usage__should_return_200_for_valid_request(
        self,
    ):
        month_param = "2024-03-15"
        url = reverse("export-subtitles_generation_usage")
        request = self.get_request(path=url, method="GET", user=self.user)
        response = SubtitlesGenerationUsageExportView.as_view()(
            request, {"month": month_param}
        )

        self.assertEqual(response.status_code, 200)

    def test_export_subtitles_generation_usage_should_have_expected_headers(self):
        url = reverse("export-subtitles_generation_usage")
        request = self.get_request(path=url, method="GET", user=self.user)
        response = SubtitlesGenerationUsageExportView.as_view()(request)
        expected_header = b"Asset Id,Asset title,Generated date & time,Subtitles Generated(in minutes)\r\n"
        self.assertIn(expected_header, response._container)

    def test_export_subtitles_generation_usage_should_csv_file_as_default(self):
        url = reverse("export-subtitles_generation_usage")
        request = self.get_request(path=url, method="GET", user=self.user)
        response = SubtitlesGenerationUsageExportView.as_view()(request)
        self.assertIn("text/csv", response["Content-Type"])
