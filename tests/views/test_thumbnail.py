from copy import deepcopy
from unittest.mock import patch

from django.core.files.base import ContentFile
from django.urls import reverse
from django_multitenant.utils import set_current_tenant

from app.views.thumbnail import ThumbnailUpdateView
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestThumbnailUpdateView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        self.video = self.create_video()
        set_current_tenant(self.organization)

    @patch("app.views.thumbnail.messages.success")
    @patch("app.views.thumbnail.upload_thumbnail", return_value="/")
    def test_upload_thumbnail_method_should_be_called_while_uploading_thumbnail(
        self, mock_upload_thumbnail, mock_message
    ):
        video = self.create_video()
        url = reverse("update_thumbnail", kwargs={"asset_uuid": video.asset.uuid})
        request = self.get_request(path=url, method="post", user=self.user)
        request.FILES["file"] = ContentFile(b"file_content", name="mock_file.jpeg")
        response = ThumbnailUpdateView.as_view()(request, asset_uuid=video.asset.uuid)

        mock_upload_thumbnail.assert_called()
        self.assertEqual(response.status_code, 302)

    @patch("app.views.thumbnail.messages")
    @patch("app.views.thumbnail.upload_thumbnail", return_value="/")
    def test_should_display_error_message_for_invalid_thumbnail_format(
        self, mock_upload_thumbnail, mock_message
    ):
        video = self.create_video()
        url = reverse("update_thumbnail", kwargs={"asset_uuid": video.asset.uuid})
        request = self.get_request(path=url, method="post", user=self.user, follow=True)
        request.FILES["file"] = ContentFile(b"file_content", name="mock_file.txt")
        response = ThumbnailUpdateView.as_view()(request, asset_uuid=video.asset.uuid)

        self.assertEqual(response.status_code, 302)
        mock_upload_thumbnail.assert_not_called()

    @patch("app.views.thumbnail.messages")
    @patch("app.views.thumbnail.upload_thumbnail", return_value="/")
    def test_should_display_error_message_for_thumbnail_size_limit_exceeded(
        self, mock_upload_thumbnail, mock_message
    ):
        video = self.create_video()
        url = reverse("update_thumbnail", kwargs={"asset_uuid": video.asset.uuid})
        request = self.get_request(path=url, method="post", user=self.user)
        large_file = ContentFile(b"file_content" * 1024 * 1024, name="large_file.jpg")
        request.FILES["file"] = large_file
        response = ThumbnailUpdateView.as_view()(request, asset_uuid=video.asset.uuid)

        self.assertEqual(response.status_code, 302)
        mock_upload_thumbnail.assert_not_called()

    @patch("app.views.thumbnail.messages")
    @patch("app.views.thumbnail.update_cover_thumbnail", return_value="/")
    def test_update_selected_thumbnail_as_cover_thumbnail(
        self, mock_update_cover_thumbnail, mock_message
    ):
        video = self.create_video()
        url = reverse("update_thumbnail", kwargs={"asset_uuid": video.asset.uuid})
        request = self.get_request(path=url, method="post", user=self.user)
        mutable_post = deepcopy(request.POST)
        mutable_post["path"] = "selected_thumbnail_path"
        request.POST = mutable_post
        response = ThumbnailUpdateView.as_view()(request, asset_uuid=video.asset.uuid)

        self.assertEqual(response.status_code, 302)
        mock_update_cover_thumbnail.assert_called()

    @patch("app.views.thumbnail.delete_file")
    @patch("app.views.thumbnail.messages.success")
    def test_should_delete_thumbnail_successfully(
        self, mock_message_success, mock_delete_file
    ):
        video = self.create_video(thumbnails=["existing_thumbnail_url"])

        asset = video.asset
        url = reverse("update_thumbnail", kwargs={"asset_uuid": asset.uuid})
        request = self.get_request(path=url, method="post", user=self.user)
        mutable_post = deepcopy(request.POST)
        mutable_post["delete_thumbnail"] = "1"
        mutable_post["thumbnail_url"] = "existing_thumbnail_url"

        request.POST = mutable_post
        response = ThumbnailUpdateView.as_view()(request, asset_uuid=asset.uuid)
        video.refresh_from_db()

        self.assertEqual(response.status_code, 302)
        self.assertNotIn("existing_thumbnail_url", video.thumbnails)
        mock_delete_file.assert_called_once_with(
            asset.organization, "existing_thumbnail_url"
        )
        mock_message_success.assert_called_once_with(
            request, "Thumbnail deleted successfully"
        )
