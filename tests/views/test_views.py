import uuid
from unittest import mock

from django.http import Http404
from django.test import override_settings
from django_multitenant.utils import set_current_tenant

from app.domain.video import get_migrated_dash_url, get_migrated_playback_url
from app.models import EmbedPreset, Organization, Video
from app.models.track import Track
from app.views.embed import AssetUploadEmbedView, EmbedView
from tests import TestCase
from tests.mixins import OrganizationMixin
from tests.mixins.asset import TrackMixin


class TestEmbedView(TrackMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization(cdn_url="http://127.0.0.1/")
        set_current_tenant(self.organization)

    # def test_view_should_return_200_for_authenticated_user(self):
    #     video = self.create_video()
    #     access_token = self.create_access_token(asset=video.asset)
    #     request = self.get_request(
    #         f"/?access_token={access_token.uuid}", user=self.organization.created_by
    #     )
    #     response = EmbedView.as_view()(
    #         request, organization_id=self.organization.uuid, uuid=video.asset.uuid
    #     )

    #     self.assertEqual(200, response.status_code)
    @mock.patch("app.views.embed.check_cdn_status")
    def test_get_player_preference_should_return_preset_preference_for_video(
        self,
        mock_check_cdn_status,
    ):
        preset = EmbedPreset.objects.create(
            title="test2", organization=self.organization
        )
        video = self.create_video(embed_preset=preset)
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}", user=self.organization.created_by
        )
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=video.asset.uuid
        )
        self.assertEqual(response.context_data["player_preference"], video.embed_preset)

    @mock.patch("app.views.embed.check_cdn_status")
    def test_get_player_preference_should_return_organization_preferences_by_default_for_video(
        self, mock_check_cdn_status
    ):
        video = self.create_video(use_global_player_preferences=True)
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}", user=self.organization.created_by
        )
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=video.asset.uuid
        )
        self.assertEqual(response.context_data["player_preference"], self.organization)

    @mock.patch("app.views.embed.check_cdn_status")
    def test_get_player_preference_should_return_Video_preferences(
        self, mock_check_cdn_status
    ):
        video = self.create_video(use_global_player_preferences=False)
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}", user=self.organization.created_by
        )
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=video.asset.uuid
        )
        self.assertEqual(response.context_data["player_preference"], video)

    @mock.patch("app.views.embed.check_cdn_status")
    def test_get_player_preference_should_return_preset_preference_for_live(
        self, mock_check_cdn_status
    ):
        preset = EmbedPreset.objects.create(
            title="test2", organization=self.organization
        )
        live = self.create_livestream(embed_preset=preset)
        access_token = self.create_access_token(asset=live.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}", user=self.organization.created_by
        )
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=live.asset.uuid
        )
        self.assertEqual(response.context_data["player_preference"], live.embed_preset)

    @mock.patch("app.views.embed.check_cdn_status")
    def test_get_player_preference_should_return_organization_preferences_by_default_for_live(
        self, mock_check_cdn_status
    ):
        live = self.create_livestream(use_global_player_preferences=True)
        access_token = self.create_access_token(asset=live.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}", user=self.organization.created_by
        )
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=live.asset.uuid
        )
        self.assertEqual(response.context_data["player_preference"], self.organization)

    @mock.patch("app.views.embed.check_cdn_status")
    def test_get_player_preference_should_return_live_preferences(
        self, mock_check_cdn_status
    ):
        live = self.create_livestream(use_global_player_preferences=False)
        access_token = self.create_access_token(asset=live.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}", user=self.organization.created_by
        )
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=live.asset.uuid
        )
        self.assertEqual(response.context_data["player_preference"], live)

    # def test_view_should_return_404_for_unauthenticated_user(self):
    #     video = self.create_video()
    #     request = self.get_request("/")

    #     with self.assertRaises(Http404):
    #         EmbedView.as_view()(
    #             request, organization_id=self.organization.uuid, uuid=video.asset.uuid
    #         )

    # def test_view_should_return_200_for_access_token(self):
    #     video = self.create_video()
    #     access_token = self.create_access_token(asset=video.asset)
    #     request = self.get_request(f"/?access_token={access_token.uuid}", method="GET")
    #     response = EmbedView.as_view()(
    #         request, organization_id=self.organization.uuid, uuid=video.asset.uuid
    #     )

    #     self.assertEqual(200, response.status_code)

    @mock.patch("app.views.embed.check_cdn_status")
    def test_view_should_return_404_for_incorrect_access_token(
        self, mock_check_cdn_status
    ):
        request = self.get_request(f"/?access_token={uuid.uuid4()}", method="GET")
        with self.assertRaises(Http404):
            EmbedView.as_view()(
                request, uuid=uuid.uuid4(), organization_id=self.organization.uuid
            )

    # def test_access_token_with_single_use_should_expired_after_usage(self):
    #     video = self.create_video()
    #     access_token = self.create_access_token(
    #         asset=video.asset, expires_after_first_usage=True
    #     )
    #     request = self.get_request(f"/?access_token={access_token.uuid}", method="GET")
    #     response = EmbedView.as_view()(
    #         request, organization_id=self.organization.uuid, uuid=video.asset.uuid
    #     )

    #     access_token.refresh_from_db()
    #     self.assertEqual(200, response.status_code)
    #     self.assertEqual("Expired", access_token.status)

    # def test_unauthorized_access_with_invalid_token_raises_404(self):
    #     video = self.create_video()
    #     invalid_access_token = self.create_invalid_access_token(asset=video.asset)
    #     user = self.create_user()
    #     unset_current_tenant()
    #     self.create_membership(organization=self.create_organization(), user=user)
    #     request = self.get_request(
    #         f"/?access_token={invalid_access_token.uuid}", method="GET", user=user
    #     )

    #     with self.assertRaises(Http404):
    #         EmbedView.as_view()(
    #             request, organization_id=self.organization.uuid, uuid=video.asset.uuid
    #         )

    # def test_view_should_have_annotations_in_context_data(self):
    #     video = self.create_video()
    #     access_token = self.create_access_token(asset=video.asset)
    #     self.create_access_token_annotation(access_token=access_token, text="Username")
    #     request = self.get_request(f"/?access_token={access_token.uuid}", method="GET")
    #     response = EmbedView.as_view()(
    #         request, organization_id=self.organization.uuid, uuid=video.asset.uuid
    #     )

    #     annotations_json = json.dumps(
    #         [
    #             {
    #                 "text": "Username",
    #                 "type": "static",
    #                 "color": "#ff0000",
    #                 "opacity": "0.50",
    #                 "size": 15,
    #                 "interval": 2000,
    #                 "skip": 0,
    #                 "x": 16,
    #                 "y": 16,
    #             }
    #         ]
    #     )
    #     self.assertEqual(
    #         annotations_json, response.context_data["annotations"]  # type:ignore
    #     )

    @mock.patch("app.views.embed.check_cdn_status")
    def test_access_token_should_not_get_expired_for_drm_video(
        self, mock_check_cdn_status
    ):
        video = self.create_video(
            content_protection_type=Video.ContentProtectionType.DRM
        )
        access_token = self.create_access_token(
            asset=video.asset, expires_after_first_usage=True
        )
        request = self.get_request(f"/?access_token={access_token.uuid}", method="GET")
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=video.asset.uuid
        )

        access_token.refresh_from_db()

        self.assertEqual(200, response.status_code)
        self.assertEqual("Active", access_token.status)

    @mock.patch("app.views.embed.check_cdn_status")
    def test_hls_url_and_dash_url_should_be_populated_for_video(
        self, mock_check_cdn_status
    ):
        video = self.create_video(playback_url="video.m3u8", dash_url="video.mpd")
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}", user=self.organization.created_by
        )
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=video.asset.uuid
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(
            f"http://127.0.0.1/transcoded/{video.asset.uuid}/video.m3u8",
            response.context_data["hls_url"],  # type:ignore
        )
        self.assertEqual(
            f"http://127.0.0.1/transcoded/{video.asset.uuid}/video.mpd",
            response.context_data["dash_url"],  # type:ignore
        )

    @mock.patch("app.views.embed.EmbedView.is_live_stream_streaming_started")
    @mock.patch("app.views.embed.check_cdn_status")
    def test_hls_url_should_be_populated_for_live_stream(
        self, mock_check_video_available, mock_check_cdn_status
    ):
        video = self.create_video()
        live = self.create_livestream(asset=video.asset, hls_url_path="video.m3u8")
        access_token = self.create_access_token(asset=live.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}", user=self.organization.created_by
        )
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=live.asset.uuid
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(
            "http://127.0.0.1/video.m3u8",
            response.context_data["hls_url"],  # type:ignore
        )
        self.assertEqual(False, response.context_data["has_drm"])  # type:ignore

    @mock.patch("app.views.embed.EmbedView.is_live_stream_streaming_started")
    @mock.patch("app.views.embed.check_cdn_status")
    def test_dash_url_should_be_populated_for_live_stream(
        self, mock_check_video_available, mock_check_cdn_status
    ):
        video = self.create_video()
        live_stream = self.create_livestream(
            asset=video.asset, hls_url_path="video.m3u8", enable_drm=True
        )
        access_token = self.create_access_token(asset=live_stream.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}", user=self.organization.created_by
        )
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=live_stream.asset.uuid
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(
            "http://127.0.0.1/video.mpd",
            response.context_data["dash_url"],  # type:ignore
        )
        self.assertEqual(True, response.context_data["has_drm"])  # type:ignore

    @mock.patch("app.views.embed.check_cdn_status")
    def test_video_details_should_be_populated_if_both_video_and_livestream_present(
        self, mock_check_cdn_status
    ):
        video = self.create_video(
            status=Video.Status.COMPLETED,
            playback_url="video/video.m3u8",
            content_protection_type=Video.ContentProtectionType.DRM,
        )
        self.create_livestream(hls_url_path="video.m3u8")
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}", user=self.organization.created_by
        )
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=video.asset.uuid
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(
            f"http://127.0.0.1/transcoded/{video.asset.uuid}/video.m3u8",
            response.context_data["hls_url"],  # type:ignore
        )
        self.assertEqual(True, response.context_data["has_drm"])  # type:ignore

    @mock.patch("app.views.embed.check_cdn_status")
    def test_should_play_embed_in_any_domain_if_allowed_domains_is_empty(
        self, mock_check_cdn_status
    ):
        video = self.create_video()
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}", user=self.organization.created_by
        )
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=video.asset.uuid
        )

        self.assertEqual(200, response.status_code)

    @mock.patch("app.views.embed.check_cdn_status")
    def test_embed_should_play_only_on_allowed_domains(self, mock_check_cdn_status):
        Organization.objects.filter(id=self.organization.id).update(
            allowed_domains_for_embedding=["www.w3schools.com"]
        )

        video = self.create_video()
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}",
            user=self.organization.created_by,
            HTTP_REFERER="https://www.w3schools.com",
        )
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=video.asset.uuid
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(video.asset, response.context_data["asset"])  # type: ignore

    @mock.patch("app.views.embed.check_cdn_status")
    def test_should_not_play_embed_on_domain_that_not_in_allowed_list(
        self, mock_check_cdn_status
    ):
        Organization.objects.filter(id=self.organization.id).update(
            allowed_domains_for_embedding=["www.w3schools.com"]
        )

        video = self.create_video()
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}",
            user=self.organization.created_by,
            HTTP_REFERER="https://www.programiz.com",
        )
        with self.assertRaises(Http404):
            EmbedView.as_view()(
                request, organization_id=self.organization.uuid, uuid=video.asset.uuid
            )

    @mock.patch("app.views.embed.check_cdn_status")
    def test_embed_should_play_video_on_disable_domain_restriction(
        self, mock_check_cdn_status
    ):
        Organization.objects.filter(id=self.organization.id).update(
            allowed_domains_for_embedding=["www.w3schools.com"]
        )

        video = self.create_video()
        video.asset.disable_domain_restriction = True
        video.asset.save()

        access_token = self.create_access_token(asset=video.asset)

        request = self.get_request(
            f"/?access_token={access_token.uuid}",
            user=self.organization.created_by,
            HTTP_REFERER="https://www.someotherdomain.com",
        )

        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=video.asset.uuid
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(video.asset, response.context_data["asset"])  # type: ignore

    @override_settings(
        SITE_URL="https://parent_site.com",
    )
    @mock.patch("app.views.embed.check_cdn_status")
    def test_embed_should_play_when_played_via_site_url(self, mock_check_cdn_status):
        Organization.objects.filter(id=self.organization.id).update(
            allowed_domains_for_embedding=["www.w3schools.com"]
        )

        video = self.create_video()
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}",
            user=self.organization.created_by,
            HTTP_REFERER="https://parent_site.com",
        )
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=video.asset.uuid
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(video.asset, response.context_data["asset"])  # type: ignore

    @mock.patch("app.views.embed.check_cdn_status")
    def test_view_should_have_subtitles_in_context_data(self, mock_check_cdn_status):
        subtitle = self.create_subtitle()
        video = subtitle.video
        access_token = self.create_access_token(asset=video.asset)
        self.create_access_token_annotation(access_token=access_token, text="Username")
        request = self.get_request(f"/?access_token={access_token.uuid}", method="GET")
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=video.asset.uuid
        )

        self.assertEqual(
            response.context_data["subtitles"],
            [
                [
                    subtitle,
                    f"http://127.0.0.1/transcoded/assets/{video.asset.uuid}/{subtitle.language}.vtt",
                ]
            ],
        )

    @mock.patch("app.views.embed.check_cdn_status")
    def test_should_return_migrated_urls(self, mock_check_cdn_status):
        meta_data = {"has_tpstreams_drm": True}
        video = self.create_video(meta_data=meta_data)
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}&tpstreams_drm=true",
            user=self.organization.created_by,
        )
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=video.asset.uuid
        )
        playback_url = response.context_data["hls_url"]
        dash_url = response.context_data["dash_url"]
        expected_playback_url = get_migrated_playback_url(video)
        expected_dash_url = get_migrated_dash_url(video)
        self.assertEqual(playback_url, expected_playback_url)
        self.assertEqual(dash_url, expected_dash_url)

    @mock.patch("app.views.embed.check_cdn_status")
    def test_should_return_original_url_with_no_tpstreams_drm_in_query_param(
        self, mock_check_cdn_status
    ):
        video = self.create_video()
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}", user=self.organization.created_by
        )
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=video.asset.uuid
        )
        playback_url = response.context_data["hls_url"]
        dash_url = response.context_data["dash_url"]
        self.assertEqual(playback_url, video.get_playback_url())
        self.assertEqual(dash_url, video.get_dash_url())

    @mock.patch("app.views.embed.check_cdn_status")
    def test_should_return_original_url_if_video_has_no_meta_data(
        self, mock_check_cdn_status
    ):
        video = self.create_video()
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}",
            user=self.organization.created_by,
        )
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=video.asset.uuid
        )
        playback_url = response.context_data["hls_url"]
        dash_url = response.context_data["dash_url"]
        self.assertEqual(playback_url, video.get_playback_url())
        self.assertEqual(dash_url, video.get_dash_url())

    @mock.patch("app.views.embed.check_cdn_status")
    def test_should_return_original_url_if_video_metadata_has_no_tpstreams_drm(
        self, mock_check_cdn_status
    ):
        meta_data = {"status": Video.Status.COMPLETED}
        video = self.create_video(meta_data=meta_data)
        video = self.create_video()
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}",
            user=self.organization.created_by,
        )
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=video.asset.uuid
        )
        playback_url = response.context_data["hls_url"]
        dash_url = response.context_data["dash_url"]
        self.assertEqual(playback_url, video.get_playback_url())
        self.assertEqual(dash_url, video.get_dash_url())

    @mock.patch("app.views.embed.check_cdn_status")
    def test_should_display_subtitle_in_video_only_after_complete_generation(
        self, mock_check_cdn_status
    ):
        subtitle_not_fully_generated = self.create_subtitle(
            type=Track.Type.SUBTITLE,
            subtitle_type=Track.SubtitleType.AUTO_GENERATED,
            url=None,
        )
        video = subtitle_not_fully_generated.video
        access_token = self.create_access_token(asset=video.asset)
        self.create_access_token_annotation(access_token=access_token, text="Username")
        request = self.get_request(f"/?access_token={access_token.uuid}", method="GET")
        response = EmbedView.as_view()(
            request, organization_id=self.organization.uuid, uuid=video.asset.uuid
        )
        if not response.is_rendered:
            response.render()
        self.assertEqual([], response.context_data["subtitles"])


class TestAssetUploadEmbedView(TrackMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization(cdn_url="http://127.0.0.1/")
        set_current_tenant(self.organization)

    def test_should_allow_embed_in_any_domain_if_allowed_domains_is_empty(self):
        request = self.get_request("/", user=self.organization.created_by)
        response = AssetUploadEmbedView.as_view()(
            request,
            organization_id=self.organization.uuid,
        )

        self.assertEqual(200, response.status_code)

    def test_embed_should_embed_on_allowed_domains(self):
        Organization.objects.filter(id=self.organization.id).update(
            allowed_domains_for_embedding=["www.w3schools.com"]
        )

        request = self.get_request(
            "/",
            user=self.organization.created_by,
            HTTP_REFERER="https://www.w3schools.com",
        )
        response = AssetUploadEmbedView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(200, response.status_code)

    def test_should_not_embed_on_domain_that_not_in_allowed_list(self):
        Organization.objects.filter(id=self.organization.id).update(
            allowed_domains_for_embedding=["www.w3schools.com"]
        )

        request = self.get_request(
            "/",
            user=self.organization.created_by,
            HTTP_REFERER="https://www.programiz.com",
        )

        with self.assertRaises(Http404):
            AssetUploadEmbedView.as_view()(
                request, organization_id=self.organization.uuid
            )
