from datetime import datetime, <PERSON><PERSON><PERSON>
from unittest import mock
from unittest.mock import patch
from urllib.parse import urlencode

from django.test import RequestFactory
from django.urls import reverse
from django.utils import timezone
from django.utils.timezone import now
from django_multitenant.utils import set_current_tenant, unset_current_tenant
from freezegun import freeze_time

from app.models import LiveStream
from app.models.asset import Asset
from app.views import StopLiveStreamView
from app.views.export import LiveStreamUsageExportView
from app.views.live_streams import (
    LiveStreamListView,
    LiveStreamMoveView,
    LiveStreamUpdateView,
    ScheduledLiveStreamListView,
    ScheduleLiveStreamView,
    create_live_stream_server_view,
)
from tests import TestCase
from tests.data.user_test_data import USER_DETAILS
from tests.mixins import AssetMixin, OrganizationMixin


class TestStopLiveStreamView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)

    @mock.patch("app.views.live_streams.messages.success")
    @mock.patch("app.views.live_streams.stop_live_stream")
    def test_termination_cause_should_be_user_initiated_if_the_user_stops_live_from_ui(
        self, mock_message, mock_stop_live
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_ip="127.0.0.1",
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )

        url = reverse("stop_live_stream", kwargs={"uuid": live_stream.asset.uuid})
        request = self.get_request(
            path=url,
            method="post",
            user=self.user,
        )
        StopLiveStreamView.as_view()(request, uuid=live_stream.asset.uuid)
        live_stream.refresh_from_db()

        self.assertEqual(live_stream.get_termination_cause_display(), "User Initiated")

    @mock.patch("app.utils.browser.get_user_details", return_value=USER_DETAILS)
    @mock.patch("app.views.live_streams.messages.success")
    @mock.patch("app.views.live_streams.stop_live_stream")
    def test_get_user_details_is_called_when_live_stream_is_stopped(
        self,
        mock_message,
        mock_stop_live,
        mock_user_details,
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_ip="127.0.0.1",
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )

        url = reverse("stop_live_stream", kwargs={"uuid": live_stream.asset.uuid})
        request = self.get_request(
            path=url,
            method="post",
            user=self.user,
        )
        StopLiveStreamView.as_view()(request, uuid=live_stream.asset.uuid)
        live_stream.refresh_from_db()

        mock_user_details.assert_called()
        self.assertEqual(live_stream.user_details, USER_DETAILS)


class TestLiveStreamScheduleView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @mock.patch("app.views.live_streams.create_chat_room_task")
    @mock.patch("app.views.live_streams.create_remote_live_stream_server")
    @mock.patch("app.views.live_streams.store_live_stream_server_details")
    def test_schedule_live_stream_should_schedule_live_stream_successfully(
        self, mock_create_server, mock_store_details, mock_chat_room_task
    ):
        factory = RequestFactory()
        url = reverse("schedule_live_stream")
        form_data = {
            "title": "Test Stream",
            "start": now() + timedelta(minutes=60),
            "latency": 0,
        }
        request = factory.post(url, data=form_data)
        request.user = self.organization.created_by
        ScheduleLiveStreamView.as_view()(request)

        self.assertTrue(LiveStream.objects.exists())
        self.assertEqual(None, LiveStream.objects.first().server_id)
        self.assertFalse(mock_store_details.called)
        self.assertFalse(mock_create_server.called)


class TestCreatLiveStreamServerView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)
        self.asset = self.create_asset(
            title="Test Asset",
            type=Asset.Type.LIVESTREAM,
        )

    @mock.patch("app.views.live_streams.create_remote_live_stream_server")
    @mock.patch("app.views.live_streams.store_live_stream_server_details")
    def test_create_live_stream_server_view_should_create_server_if_not_created(
        self, mock_create_server, mock_store_details
    ):
        self.create_livestream(
            asset=self.asset, server_status=LiveStream.ServerStatus.NOT_CREATED
        )

        url = reverse("create_live_stream_server", kwargs={"uuid": self.asset.uuid})
        request = self.get_request(
            path=url,
            method="post",
            user=self.user,
        )

        response = create_live_stream_server_view(request, uuid=self.asset.uuid)

        expected_url = reverse("live_stream_settings", kwargs={"uuid": self.asset.uuid})
        self.assertEqual(response.url, expected_url)
        self.assertTrue(mock_store_details.called)
        self.assertTrue(mock_create_server.called)

    @mock.patch("app.views.live_streams.create_remote_live_stream_server")
    @mock.patch("app.views.live_streams.store_live_stream_server_details")
    def test_create_live_stream_server_view_should_not_create_server_if_created(
        self, mock_create_server, mock_store_details
    ):
        self.create_livestream(
            asset=self.asset, server_status=LiveStream.ServerStatus.CREATED
        )

        url = reverse("create_live_stream_server", kwargs={"uuid": self.asset.uuid})
        request = self.get_request(
            path=url,
            method="post",
            user=self.user,
        )

        response = create_live_stream_server_view(request, uuid=self.asset.uuid)

        expected_url = reverse("live_stream_settings", kwargs={"uuid": self.asset.uuid})
        self.assertEqual(response.url, expected_url)
        self.assertFalse(mock_store_details.called)
        self.assertFalse(mock_create_server.called)


class TestLiveStreamListView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)

    def test_live_stream_list_view(self):
        live_stream_1 = self.create_livestream(
            status=LiveStream.Status.NOT_STARTED,
            server_status=LiveStream.ServerStatus.NOT_CREATED,
        )
        live_stream_2 = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_status=LiveStream.ServerStatus.CREATED,
        )
        live_stream_3 = self.create_livestream(
            status=LiveStream.Status.COMPLETED,
            server_status=LiveStream.ServerStatus.DESTROYED,
        )
        live_stream_4 = self.create_livestream(
            status=LiveStream.Status.NOT_STARTED,
            server_status=LiveStream.ServerStatus.CREATING,
        )

        url = reverse("live_streams")
        request = self.get_request(
            path=url,
            method="get",
            user=self.user,
        )
        response = LiveStreamListView.as_view()(request)
        self.assertEqual(response.status_code, 200)
        self.assertIn(live_stream_2.asset, response.context_data["object_list"])
        self.assertIn(live_stream_4.asset, response.context_data["object_list"])
        self.assertNotIn(live_stream_1.asset, response.context_data["object_list"])
        self.assertNotIn(live_stream_3.asset, response.context_data["object_list"])


class TestScheduleLiveStreamListView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)

    @freeze_time("2023-03-07")
    def test_schedule_live_stream_list_view(self):
        live_stream1 = self.create_livestream(
            server_status=LiveStream.ServerStatus.NOT_CREATED,
            start=timezone.now() - timezone.timedelta(days=1),
        )
        live_stream3 = self.create_livestream(
            server_status=LiveStream.ServerStatus.NOT_CREATED,
            start=timezone.now() + timezone.timedelta(days=1),
        )
        live_stream4 = self.create_livestream(
            server_status=LiveStream.ServerStatus.NOT_CREATED, start=timezone.now()
        )

        url = reverse("scheduled_live_streams")
        request = self.get_request(
            path=url,
            method="get",
            user=self.user,
        )

        response = ScheduledLiveStreamListView.as_view()(request)

        self.assertEqual(response.status_code, 200)

        queryset = response.context_data["object_list"]

        self.assertIn(live_stream3, [item.live_stream for item in queryset])
        self.assertNotIn(live_stream1, [item.live_stream for item in queryset])
        self.assertIn(live_stream4, [item.live_stream for item in queryset])
        self.assertEqual(list(queryset.query.order_by), ["live_stream__start"])


class TestLiveStreamDetailView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        self.asset = self.create_asset()
        self.live_stream = self.create_livestream(
            asset=self.asset,
            server_status=LiveStream.ServerStatus.NOT_CREATED,
            start=timezone.now(),
        )

    def test_organization_user_can_access_live_detail_page(self):
        user_from_same_organization = self.create_user(
            current_organization_uuid=self.organization.uuid
        )
        user_from_same_organization.set_password("password")
        user_from_same_organization.save()

        self.client.login(email=user_from_same_organization.email, password="password")

        url = reverse("live_stream_settings", args=[self.asset.uuid])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, str(self.asset))

    def test_video_detail_page_raises_404_for_cross_organization_access(self):
        unset_current_tenant()
        other_organization = self.create_organization()
        user_from_other_organization = self.create_user(
            current_organization_uuid=other_organization.uuid
        )
        user_from_other_organization.set_password("password")
        user_from_other_organization.save()

        self.client.login(email=user_from_other_organization.email, password="password")

        url = reverse("live_stream_settings", args=[self.asset.uuid])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)


class TestLiveStreamUpdateView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.asset = self.create_asset()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)
        self.live_stream = self.create_livestream(
            status=LiveStream.Status.NOT_STARTED,
            asset=self.asset,
            server_status=LiveStream.ServerStatus.NOT_CREATED,
            start="2022-03-14 06:10:00+05:30",
        )

    @freeze_time("2023-03-07")
    @patch("app.views.messages.success")
    def test_live_stream_update_view(self, mock_message):
        form_data = urlencode({"title": "test", "start": "2094-03-24 06:10:00"})
        request = self.get_request(
            user=self.user,
            path=reverse("live_stream_update", kwargs={"uuid": self.asset.uuid}),
            method="post",
            data=form_data,
            content_type="application/x-www-form-urlencoded",
        )

        LiveStreamUpdateView.as_view()(request, uuid=self.live_stream.asset.uuid)

        expected_time = datetime.strptime(
            "2094-03-24 06:10:00", "%Y-%m-%d %H:%M:%S"
        ).astimezone(timezone.utc)
        self.live_stream.refresh_from_db()
        self.assertEqual(self.live_stream.start, expected_time)

    @patch("app.views.messages.success")
    def test_start_should_not_be_updated_if_it_is_null(self, mock_message):
        self.live_stream.start = None
        self.live_stream.save()
        form_data = urlencode({"title": "test"})

        request = self.get_request(
            user=self.user,
            path=reverse("live_stream_update", kwargs={"uuid": self.asset.uuid}),
            method="post",
            data=form_data,
            content_type="application/x-www-form-urlencoded",
        )
        LiveStreamUpdateView.as_view()(request, uuid=self.live_stream.asset.uuid)
        self.live_stream.refresh_from_db()
        self.asset.refresh_from_db()

        self.assertIsNone(self.live_stream.start)
        self.assertEqual(self.asset.title, "test")

    @patch("app.views.messages.success")
    def test_update_live_stream_with_drm_options(self, mock_message):
        data = urlencode(
            {
                "title": "Updated Title",
                "update_enable_drm": True,
                "enable_drm_for_livestream": False,
            }
        )

        request = self.get_request(
            user=self.user,
            path=reverse("live_stream_update", kwargs={"uuid": self.asset.uuid}),
            method="post",
            data=data,
            content_type="application/x-www-form-urlencoded",
        )
        LiveStreamUpdateView.as_view()(request, uuid=self.live_stream.asset.uuid)

        self.live_stream.refresh_from_db()
        self.asset.refresh_from_db()

        self.assertEqual(self.asset.title, "Updated Title")
        self.assertEqual(self.live_stream.enable_drm, False)
        self.assertEqual(self.live_stream.enable_drm_for_recording, False)


class TestLiveStreamMoveView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)

    @patch("app.views.live_streams.messages.success")
    @patch("app.tasks.rebuild_asset_tree.delay")
    def test_view_should_move_live_stream_to_destination(self, mock_rebuild, message):
        folder = self.create_folder()
        asset = self.create_asset()
        live_stream = self.create_livestream(
            asset=asset,
        )

        url = (
            reverse("live_stream_move")
            + f"?asset={live_stream.asset.uuid}&folder={folder.uuid}"
        )
        request = self.get_request(path=url, method="post", user=self.user)
        response = LiveStreamMoveView.as_view()(request)

        asset.refresh_from_db()
        live_stream.refresh_from_db()

        self.assertEqual(asset.parent, folder)
        self.assertEqual(response.status_code, 302)
        self.assertEqual(live_stream.has_moved, True)

    @patch("app.views.live_streams.messages.success")
    @patch("app.tasks.rebuild_asset_tree.delay")
    def test_view_should_move_asset_to_root_directory(self, mock_rebuild, message):
        folder = self.create_folder()
        asset = self.create_asset()
        live_stream = self.create_livestream(
            asset=asset,
        )
        asset.move(folder)

        url = reverse("live_stream_move") + f"?asset={live_stream.asset.uuid}"
        request = self.get_request(path=url, method="post", user=self.user)
        response = LiveStreamMoveView.as_view()(request)

        asset.refresh_from_db()
        live_stream.refresh_from_db()

        self.assertEqual(asset.parent, None)
        self.assertEqual(response.status_code, 302)
        self.assertEqual(live_stream.has_moved, True)


class TestLiveStreamUsageExportView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)

    def test_live_stream_usage_export__should_return_200_for_valid_request(self):
        month_param = "2024-02-01"
        url = reverse("export-live-stream-usage")
        request = self.get_request(path=url, method="GET", user=self.user)
        response = LiveStreamUsageExportView.as_view()(request, {"month": month_param})

        self.assertEqual(response.status_code, 200)

    def test_live_stream_usage_export_should_have_expected_headers(self):
        url = reverse("export-live-stream-usage")
        request = self.get_request(path=url, method="GET", user=self.user)
        response = LiveStreamUsageExportView.as_view()(request)

        expected_header = b"live_stream__asset__uuid,live_stream__asset__title,Start Time,End Time,Duration in Mins\r\n"
        self.assertIn(expected_header, response._container)

    def test_live_stream_usage_export_should_csv_file_as_default(self):
        url = reverse("export-live-stream-usage")
        request = self.get_request(path=url, method="GET", user=self.user)
        response = LiveStreamUsageExportView.as_view()(request)
        self.assertIn("text/csv", response["Content-Type"])
