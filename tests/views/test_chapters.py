import json
from datetime import timed<PERSON><PERSON>
from unittest.mock import patch

from django.urls import reverse
from django_multitenant.utils import set_current_tenant

from app.models import VideoChapter
from app.views import ChapterView
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class ChapterViewTestCase(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)
        self.video = self.create_video()
        self.asset = self.video.asset
        self.video_chapter = VideoChapter.objects.create(
            start_time="00:01:00",
            label="Chapter 1",
            video=self.video,
            organization=self.asset.organization,
        )
        self.url = reverse("chapters", kwargs={"asset_uuid": self.asset.uuid})

    @patch("app.views.chapters.messages.success")
    def test_should_update_existing_chapter(self, mock_message):
        chapter_data = json.dumps(
            [
                {
                    "id": self.video_chapter.id,
                    "starttime": "00:01:30",
                    "title": "Updated Chapter 1",
                }
            ]
        )
        delete_data = json.dumps([])
        request = self.get_request(
            path=self.url,
            method="post",
            user=self.user,
            data={"chapter_data": chapter_data, "deleteChapter_data": delete_data},
        )

        request.POST = {"chapter_data": chapter_data, "deleteChapter_data": delete_data}
        ChapterView.as_view()(request, asset_uuid=self.asset.uuid)

        updated_chapter = VideoChapter.objects.get(id=self.video_chapter.id)
        self.assertEqual(updated_chapter.start_time, timedelta(minutes=1, seconds=30))
        self.assertEqual(updated_chapter.label, "Updated Chapter 1")

    @patch("app.views.chapters.messages.success")
    def test_should_create_new_chapter(self, mock_message):
        chapter_data = json.dumps([{"starttime": "00:02:00", "title": "New Chapter"}])
        delete_data = json.dumps([])

        request = self.get_request(
            path=self.url,
            method="post",
            user=self.user,
            data={"chapter_data": chapter_data, "deleteChapter_data": delete_data},
        )

        request.POST = {"chapter_data": chapter_data, "deleteChapter_data": delete_data}
        response = ChapterView.as_view()(request, asset_uuid=self.asset.uuid)

        new_chapter = VideoChapter.objects.filter(label="New Chapter").first()
        self.assertIsNotNone(new_chapter)
        self.assertEqual(new_chapter.start_time, timedelta(minutes=2))
        self.assertEqual(new_chapter.video, self.asset.video)
        self.assertEqual(new_chapter.organization, self.asset.organization)

        mock_message.assert_called_with(
            request, "Chapters have been updated successfully."
        )
        self.assertEqual(response.status_code, 302)

    @patch("app.views.chapters.messages.success")
    def test_should_delete_chapter(self, mock_message):
        chapter_data = json.dumps([])
        delete_data = json.dumps([self.video_chapter.id])

        request = self.get_request(
            path=self.url,
            method="post",
            user=self.user,
            data={"chapter_data": chapter_data, "deleteChapter_data": delete_data},
        )

        request.POST = {"chapter_data": chapter_data, "deleteChapter_data": delete_data}
        response = ChapterView.as_view()(request, asset_uuid=self.asset.uuid)

        with self.assertRaises(VideoChapter.DoesNotExist):
            VideoChapter.objects.get(id=self.video_chapter.id)

        mock_message.assert_called_with(
            request, "Chapters have been updated successfully."
        )
        self.assertEqual(response.status_code, 302)
