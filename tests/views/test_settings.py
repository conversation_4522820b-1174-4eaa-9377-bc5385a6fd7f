from unittest.mock import patch

from django.contrib.auth.hashers import check_password
from django.test import RequestFactory, override_settings
from django.urls import reverse
from django_multitenant.utils import set_current_tenant

from app.views.settings import OrganizationVideoPreferencesView, VideoSettingsView
from tests import TestCase
from tests.mixins import OrganizationMixin


class TestVideoEmbeddingSettingsView(OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)

    @override_settings(
        DEBUG=True,
    )
    @patch("app.views.assets.messages.success")
    def test_should_update_allowed_domains_with_given_domains(self, message):
        factory = RequestFactory()
        url = reverse("video_settings")
        data = {"allowed_domains_for_embedding": "stackoverflow.com, demo.testpress.in"}

        request = factory.post(url, data=data)
        request.user = self.user
        response = VideoSettingsView.as_view()(request)
        self.organization.refresh_from_db()

        self.assertEqual(302, response.status_code)
        self.assertEqual(
            ["stackoverflow.com", "demo.testpress.in"],
            self.organization.allowed_domains_for_embedding,
        )

    @override_settings(
        DEBUG=True,
    )
    @patch("app.views.messages.success")
    def test_should_update_video_preferences_with_given_data(self, message):
        factory = RequestFactory()
        url = reverse("player_settings")
        data = {"autoplay_enabled": True}

        request = factory.post(url, data=data)
        request.user = self.user
        response = OrganizationVideoPreferencesView.as_view()(request)
        self.organization.refresh_from_db()

        self.assertEqual(302, response.status_code)
        self.assertEqual(True, self.organization.autoplay_enabled)
        message.assert_called_with(
            request, "Your default video setting has been changed successfully."
        )


class TestCustomPasswordChangeView(OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        self.user.set_password("password")
        self.user.save()
        set_current_tenant(self.organization)

    def test_successful_password_update(self):
        form_data = {"old_password": "password", "new_password1": "new password"}
        url = reverse("password_settings")
        self.client.login(username=self.user.email, password="password")
        response = self.client.post(url, data=form_data)

        self.assertEqual(302, response.status_code)
        self.user.refresh_from_db()
        self.assertTrue(check_password("new password", self.user.password))

    def test_password_change_failure_with_incorrect_old_password(self):
        form_data = {"old_password": "wrong_password", "new_password1": "new password"}
        url = reverse("password_settings")
        self.client.login(username=self.user.email, password="password")
        response = self.client.post(url, data=form_data)

        self.assertEqual(200, response.status_code)
        self.user.refresh_from_db()
        self.assertTrue(check_password("password", self.user.password))
