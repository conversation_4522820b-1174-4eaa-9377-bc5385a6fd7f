from unittest import mock

from django.test import Client, TestCase
from django.urls import reverse
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.models import Organization
from tests.mixins import OrganizationMixin


class TestLoginView(TestCase, OrganizationMixin):
    def setUp(self):
        self.client = Client()
        self.organization = self.create_organization(status=Organization.Status.BLOCKED)
        self.user = self.organization.created_by
        self.create_membership(user=self.user, organization=self.organization)
        self.user.set_password("password")
        self.user.current_organization_uuid = self.organization.uuid
        self.user.save()
        set_current_tenant(self.organization)

    @mock.patch("app.middlewares.organization.auth.logout")
    def test_login_view_should_call_auth_logout_for_blocked_organization(
        self, mock_logout
    ):
        self.client.login(email=self.user.email, password="password")
        self.client.get(reverse("assets"))

        mock_logout.assert_called()

    @mock.patch("app.middlewares.organization.auth.logout")
    def test_login_view_should_not_call_auth_logout_for_active_organization(
        self, mock_logout
    ):
        unset_current_tenant()
        organization = self.create_organization(status=Organization.Status.ACTIVE)
        user = organization.created_by
        self.create_membership(user=user, organization=organization)
        user.set_password("password")
        user.current_organization_uuid = organization.uuid
        user.save()

        self.client.login(email=user.email, password="password")
        self.client.get(reverse("assets"))

        mock_logout.assert_not_called()
