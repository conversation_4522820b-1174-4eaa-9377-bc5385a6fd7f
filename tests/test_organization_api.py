from django_multitenant.utils import unset_current_tenant
from rest_framework import permissions

from app.api.v1.views import OrganizationListView
from tests import TestCase
from tests.mixins import OrganizationMixin


class TestOrganizationListView(OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        unset_current_tenant()

    def test_api_should_allow_only_authenticated_user(self):
        self.assertTrue(
            permissions.IsAuthenticated in OrganizationListView.permission_classes
        )

    def test_api_should_return_organization_list(self):
        organization = self.create_organization()
        request = self.get_request("/", user=organization.created_by)
        response = OrganizationListView.as_view()(request)

        self.assertEqual(200, response.status_code)
        self.assertEqual(str(organization.uuid), response.data["results"][0]["uuid"])
        self.assertEqual(organization.name, response.data["results"][0]["name"])

    def test_api_should_return_organizations_that_user_part_of(self):
        user = self.create_user()
        self.create_membership(user=user)
        request = self.get_request("/", user=user)
        response = OrganizationListView.as_view()(request)

        self.assertEqual(200, response.status_code)
        self.assertEqual(1, len(response.data["results"]))
