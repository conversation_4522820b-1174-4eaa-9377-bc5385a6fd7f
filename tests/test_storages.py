import datetime
from unittest.mock import patch

from freezegun import freeze_time

from app.domain.storages import (
    update_day_storage_usage,
    update_monthly_storage_usage,
    update_storage_usage,
)
from app.models import AssetUsage
from tests import TestCase
from tests.data.storage_usage_test_data import (
    DAY_STORAGE_USAGE_RESPONSE,
    MONTHLY_STORAGE_USAGE_RESPONSE,
)
from tests.mixins import OrganizationMixin, StorageMixin


class TestStorageUsage(OrganizationMixin, StorageMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()

    @freeze_time("2023-03-07")
    @patch(
        "app.domain.storages.get_all_bucket_metrics",
        return_value=MONTHLY_STORAGE_USAGE_RESPONSE,
    )
    def test_get_all_bucket_metrics_called_when_updating_storage_usage(
        self, mock_get_storage_used
    ):
        update_storage_usage(datetime.date(2022, 5, 15))

        mock_get_storage_used.assert_called()

    @freeze_time("2023-03-07")
    @patch(
        "app.domain.storages.get_all_bucket_metrics",
        return_value=MONTHLY_STORAGE_USAGE_RESPONSE,
    )
    def test_update_monthly_storage_usage_should_calculate_storage_usage_for_given_date(
        self, mock_get_storage_used
    ):
        update_monthly_storage_usage(self.organization, datetime.date(2022, 5, 15))
        monthly_usage = AssetUsage.objects.get(
            date=datetime.date(2022, 5, 1), time_frame=AssetUsage.TimeFrames.MONTHLY
        )
        excepted_active_storage_bytes = (
            MONTHLY_STORAGE_USAGE_RESPONSE[0]["PaddedStorageSizeBytes"]
            + MONTHLY_STORAGE_USAGE_RESPONSE[0]["MetadataStorageSizeBytes"]  # type: ignore
        )
        excepted_deleted_storage_bytes = MONTHLY_STORAGE_USAGE_RESPONSE[0][
            "DeletedStorageSizeBytes"
        ]

        self.assertEqual(
            monthly_usage.active_storage_bytes, excepted_active_storage_bytes
        )
        self.assertEqual(
            monthly_usage.deleted_storage_bytes, excepted_deleted_storage_bytes
        )

    @freeze_time("2023-03-07")
    @patch(
        "app.domain.storages.get_all_bucket_metrics",
        return_value=DAY_STORAGE_USAGE_RESPONSE,
    )
    def test_update_day_storage_usage_should_calculate_storage_usage_for_given_date(
        self, mock_get_storage_used
    ):
        update_day_storage_usage(self.organization, datetime.date(2022, 5, 15))
        day_usage = AssetUsage.objects.get(
            date=datetime.date(2022, 5, 15), time_frame=AssetUsage.TimeFrames.DAILY
        )
        excepted_active_storage_bytes = abs(
            (
                DAY_STORAGE_USAGE_RESPONSE[1]["PaddedStorageSizeBytes"]
                + DAY_STORAGE_USAGE_RESPONSE[1]["MetadataStorageSizeBytes"]  # type: ignore
            )
            - (
                DAY_STORAGE_USAGE_RESPONSE[0]["PaddedStorageSizeBytes"]
                + DAY_STORAGE_USAGE_RESPONSE[0]["MetadataStorageSizeBytes"]  # type: ignore
            )
        )
        excepted_deleted_storage_bytes = (
            DAY_STORAGE_USAGE_RESPONSE[1]["DeletedStorageSizeBytes"]
            - DAY_STORAGE_USAGE_RESPONSE[0]["DeletedStorageSizeBytes"]  # type: ignore
        )

        self.assertEqual(day_usage.active_storage_bytes, excepted_active_storage_bytes)
        self.assertEqual(
            day_usage.deleted_storage_bytes, excepted_deleted_storage_bytes
        )
