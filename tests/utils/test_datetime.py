import datetime

from freezegun import freeze_time

from app.utils.datetime import (
    get_current_time_in_milliseconds,
    get_month_end_date,
    get_month_start_date,
)
from tests import TestCase


@freeze_time("2023-03-07")
class TestDateTime(TestCase):
    def test_get_month_start_date_should_return_current_month_start_date(self):
        self.assertEqual(get_month_start_date(), datetime.date(2023, 3, 1))

    def test_get_month_start_date_should_return_given_month_start_date(self):
        previous_month = datetime.date(2023, 2, 23)
        self.assertEqual(
            get_month_start_date(previous_month), datetime.date(2023, 2, 1)
        )

    def test_get_month_end_date_should_return_current_month_end_date(self):
        self.assertEqual(
            get_month_end_date(), datetime.datetime(2023, 3, 31, 23, 59, 59)
        )

    def test_get_month_end_date_should_return_given_month_end_date(self):
        previous_month = datetime.date(2023, 2, 23)
        self.assertEqual(
            get_month_end_date(previous_month),
            datetime.datetime(2023, 2, 28, 23, 59, 59),
        )

    def test_get_current_time_in_milliseconds_should_return_current_date_inmilliseconds(
        self,
    ):
        date_in_milliseconds = get_current_time_in_milliseconds()
        self.assertEqual(
            date_in_milliseconds,
            1678147200000,
        )
