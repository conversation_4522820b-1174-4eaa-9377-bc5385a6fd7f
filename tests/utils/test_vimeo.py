from unittest import mock

import requests
import responses
from django.test import TestCase

from app.utils.vimeo import Interface, VideoSubtitle
from tests.domain.data.vimeo_video_subtitles import VIMEO_VIDEO_SUBTITLE_RESPONSE


class TestVideoSubtitle(TestCase):
    @responses.activate
    def test_should_return_subtitles(self):
        responses.add(
            responses.GET,
            "https://api.vimeo.com/videos/919056305/texttracks",
            json=VIMEO_VIDEO_SUBTITLE_RESPONSE,
            status=200,
        )

        response = requests.get("https://api.vimeo.com/videos/919056305/texttracks")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), VIMEO_VIDEO_SUBTITLE_RESPONSE)

    @responses.activate
    def test_get_video_subtitles(self):
        interface = Interface("cd4c886915358b4eb6")
        responses.add(
            responses.GET,
            "https://api.vimeo.com/videos/919056305/texttracks",
            json=VIMEO_VIDEO_SUBTITLE_RESPONSE,
        )
        video_uri = "https://api.vimeo.com/videos/919056305/"
        subtitles = interface.get_video_subtitles(video_uri)

        self.assertEqual(len(subtitles), 1)
        self.assertIsInstance(subtitles[0], VideoSubtitle)
        self.assertEqual(subtitles[0].name, "auto_generated_captions")
        self.assertEqual(subtitles[0].active, True)
        self.assertEqual(subtitles[0].language, "te")

    @mock.patch("app.utils.vimeo.APIInterface._get")
    def test_get_video_subtitles_empty_response(self, mock_get):
        mock_get.side_effect = requests.exceptions.HTTPError(
            response=mock.Mock(status_code=404)
        )
        interface = Interface("cd4c886915358b4eb6")

        video_uri = "https://api.vimeo.com/videos/919056305/"
        subtitles = interface.get_video_subtitles(video_uri)
        self.assertEqual(len(subtitles), 0)

    @mock.patch("app.utils.vimeo.APIInterface._get")
    def test_should_throw_401_for_unauthorized_response(self, mock_get):
        mock_get.side_effect = requests.exceptions.HTTPError(
            response=mock.Mock(status_code=401)
        )

        interface = Interface("cd4c886915358b4eb6")

        video_uri = "https://api.vimeo.com/videos/900695755/"
        with self.assertRaises(requests.exceptions.HTTPError) as cm:
            interface.get_video_subtitles(video_uri)

        self.assertEqual(cm.exception.response.status_code, 401)

    def test_get_language_code_with_valid_language(self):
        self.assertEqual(VideoSubtitle.get_language_code("en-au-x-autogen"), "en-au")
        self.assertEqual(VideoSubtitle.get_language_code("te"), "te")

    def test_get_language_code_with_invalid_language(self):
        self.assertEqual(VideoSubtitle.get_language_code("fr"), "fr")
        self.assertEqual(VideoSubtitle.get_language_code("it-x-other"), "en")

    def test_get_language_code_with_no_match(self):
        self.assertEqual(VideoSubtitle.get_language_code("unknown-language"), "en")
