from unittest import mock

from django.test import SimpleTestCase

from app.domain.aws import delete_aws_server


class TestAWSServer(SimpleTestCase):
    @mock.patch("boto3.client")
    def test_terminate_instances_api_should_be_called_for_delete_server(
        self, mock_boto3
    ):
        delete_aws_server("1234")

        mock_boto3().terminate_instances.assert_called_with(
            InstanceIds=["1234"], DryRun=False
        )
