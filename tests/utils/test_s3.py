import subprocess
from unittest import mock

from app.utils.s3 import delete_files, get_s3_config
from tests import TestCase
from tests.mixins.organization import OrganizationMixin


class TestS3Utils(OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()

    @mock.patch("subprocess.run")
    @mock.patch("tempfile.NamedTemporaryFile")
    def test_delete_files_should_run_rclone_command(
        self, mock_tempfile, mock_subprocess
    ):
        mock_cfg_file = mock_tempfile.return_value.__enter__.return_value
        mock_cfg_file.write.return_value = None
        mock_cfg_file.flush.return_value = None
        config = get_s3_config(self.organization)
        delete_files("bucket/path/1", config)
        mock_subprocess.assert_called_once_with(
            [
                "rclone",
                "delete",
                "--progress",
                "--transfers=1000",
                "s3://bucket/path/1",
                "--config",
                mock_cfg_file.name,
            ],
            capture_output=True,
            check=True,
        )

    @mock.patch("subprocess.run")
    @mock.patch("tempfile.NamedTemporaryFile")
    def test_exception_should_be_raised_if_error_occurs_during_deletion(
        self, mock_tempfile, mock_subprocess
    ):
        mock_subprocess.side_effect = subprocess.SubprocessError("Command failed")

        with self.assertRaises(Exception):
            delete_files("bucket/path/1")
