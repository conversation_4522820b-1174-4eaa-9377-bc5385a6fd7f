from unittest import mock

import responses
from django.test import SimpleTestCase

from app.utils.jwplayer import JwplayerInterface, Site, Video, VideoResolution
from tests.data.jwplayer import (
    LIST_MEDIA_RESPONSE,
    RENDITIONS_RESPONSE,
    THUMBNAIL_RESPONSE,
)


class TestJwplayer(SimpleTestCase):
    @responses.activate
    def test_get_site_should_return_site_details(self):
        responses.add(
            responses.GET,
            "https://api.jwplayer.com/v2/sites/sample_site_id/is_drm_enabled/",
            json={"is_enabled": True},
        )
        responses.add(
            responses.GET,
            "https://api.jwplayer.com/v2/sites/sample_site_id/media/?page_length=10000&q=status:ready",
            json=LIST_MEDIA_RESPONSE,
        )

        Jwplayer = JwplayerInterface("sample_site_id", "sample_api_key")
        excepted_result = Site(
            id="sample_site_id",
            drm_enabled=True,
            videos=["1234abcd"],
            total=1,
            data={
                "media": [
                    {
                        "created": "2021-07-21T14:33:18+00:00",
                        "duration": 14.744999885559082,
                        "error_message": "null",
                        "external_id": "null",
                        "hosting_type": "hosted",
                        "id": "1234abcd",
                        "last_modified": "2021-08-29T19:47:18+00:00",
                        "media_type": "video",
                        "metadata": {
                            "author": "Levi",
                            "category": "null",
                            "custom_params": {},
                            "description": "A surfer pulls up in a camper van with a board",
                            "language": "null",
                            "permalink": "",
                            "publish_end_date": "null",
                            "publish_start_date": "2021-07-21T14:33:00+00:00",
                            "tags": [],
                            "title": "Daybreak",
                        },
                        "mime_type": "null",
                        "relationships": {
                            "protection_rule": {
                                "id": "zyxw0987",
                                "type": "protection_rule",
                            }
                        },
                        "schema": "null",
                        "source_url": "null",
                        "status": "ready",
                        "trim_in_point": "null",
                        "trim_out_point": "null",
                        "type": "media",
                    }
                ],
                "page": 1,
                "page_length": 10,
                "total": 1,
            },
        )

        self.assertEqual(excepted_result, Jwplayer.get_site())

    @responses.activate
    @mock.patch(
        "app.utils.jwplayer.JwplayerInterface.get_video_title", return_value="Daybreak"
    )
    @mock.patch(
        "app.utils.jwplayer.JwplayerInterface.get_video_duration",
        return_value=14.744999885559082,
    )
    def test_get_video_should_return_video_details(
        self, mock_get_duration, mock_get_title
    ):
        responses.add(
            responses.GET,
            "https://api.jwplayer.com/v2/sites/sample_site_id/media/12345/media_renditions/",
            json=RENDITIONS_RESPONSE,
        )
        responses.add(
            responses.GET,
            "https://api.jwplayer.com/v2/sites/sample_site_id/thumbnails/?q=media_id:12345",
            json=THUMBNAIL_RESPONSE,
        )

        Jwplayer = JwplayerInterface("sample_site_id", "sample_api_key")

        excepted_result = Video(
            id="12345",
            name="Daybreak",
            link="https://cdn.jwplayer.com/manifests/12345.m3u8",
            resolutions=[
                VideoResolution(
                    url="https://content.jwplatform.com/videos/123456-E76sdX8m.mp4",
                    resolution="144p",
                    width=320,
                    height=144,
                    data={
                        "type": "video",
                        "width": 320,
                        "height": 144,
                        "url": "https://content.jwplatform.com/videos/123456-E76sdX8m.mp4",
                        "resolution": "144p",
                    },
                ),
                VideoResolution(
                    url="https://content.jwplatform.com/videos/123456-mTzrOOqf.mp4",
                    resolution="216p",
                    width=480,
                    height=216,
                    data={
                        "type": "video",
                        "width": 480,
                        "height": 216,
                        "url": "https://content.jwplatform.com/videos/123456-mTzrOOqf.mp4",
                        "resolution": "216p",
                    },
                ),
                VideoResolution(
                    url="https://content.jwplatform.com/videos/123456-gxrSIaEp.mp4",
                    resolution="288p",
                    width=640,
                    height=288,
                    data={
                        "type": "video",
                        "width": 640,
                        "height": 288,
                        "url": "https://content.jwplatform.com/videos/123456-gxrSIaEp.mp4",
                        "resolution": "288p",
                    },
                ),
            ],
            thumbnail="https://cdn.jwplayer.com/v2/media/asdf1234/thumbnails/1234567",
            duration=14.744999885559082,
            data={
                "files": [
                    {
                        "type": "video",
                        "width": 320,
                        "height": 144,
                        "url": "https://content.jwplatform.com/videos/123456-E76sdX8m.mp4",
                        "resolution": "144p",
                    },
                    {
                        "type": "video",
                        "width": 480,
                        "height": 216,
                        "url": "https://content.jwplatform.com/videos/123456-mTzrOOqf.mp4",
                        "resolution": "216p",
                    },
                    {
                        "type": "video",
                        "width": 640,
                        "height": 288,
                        "url": "https://content.jwplatform.com/videos/123456-gxrSIaEp.mp4",
                        "resolution": "288p",
                    },
                ],
                "link": "https://cdn.jwplayer.com/manifests/12345.m3u8",
                "id": "12345",
                "duration": 14.744999885559082,
                "title": "Daybreak",
                "thumbnail": "https://cdn.jwplayer.com/v2/media/asdf1234/thumbnails/1234567",
            },
        )

        self.assertEqual(
            excepted_result,
            Jwplayer.get_video(
                video_id="12345",
            ),
        )
