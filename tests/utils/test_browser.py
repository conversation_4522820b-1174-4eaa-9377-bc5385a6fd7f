from unittest import mock

from django.test import SimpleTestCase

from app.utils.browser import get_user_country_code, is_valid_content_type, is_valid_url


class TestValidContentType(SimpleTestCase):
    def test_is_valid_content_type_direct_mp4(self):
        url = "https://example.com/valid-video.mp4"
        allowed_content_types = [".mp4", ".avi"]
        self.assertTrue(is_valid_content_type(url, allowed_content_types))

    def test_is_valid_content_type_indirect_mp4(self):
        url = "https://example.com/video.mp4?key=value&signature=abc123"
        allowed_content_types = [".mp4", ".avi"]
        self.assertTrue(is_valid_content_type(url, allowed_content_types))

    def test_is_valid_content_type_direct_incorrect_extension(self):
        url = "https://example.com/invalid-video.mov"
        allowed_content_types = [".mp4", ".avi"]
        self.assertFalse(is_valid_content_type(url, allowed_content_types))

    def test_is_valid_content_type_indirect_incorrect_extension(self):
        url = "https://example.com/video.mov?signature=abcdef123456"
        allowed_content_types = [".mp4", ".avi"]
        self.assertFalse(is_valid_content_type(url, allowed_content_types))


class TestValidUrl(SimpleTestCase):
    @mock.patch("requests.get")
    def test_valid_url(self, mock_get):
        mock_response = mock.Mock()
        mock_response.status_code = 206
        mock_get.return_value = mock_response

        url = "http://example.com/valid"
        result = is_valid_url(url)
        self.assertTrue(result)

    def test_invalid_url(self):
        url = "https://example.com/valid-video.mp4"
        self.assertEqual(False, is_valid_url(url))

    def test_non_existent_url(self):
        url = "http://sfskndfksinvalid.com/valid"
        result = is_valid_url(url)
        self.assertFalse(result)
        
    @mock.patch("app.utils.browser.get_client_ip", return_value="***************")
    def test_get_country_code_should_return_valid_country_code(self, mock_request):
        actual_result = get_user_country_code("")
        excepted_result = "CA"
        self.assertEqual(excepted_result, actual_result)

    @mock.patch("app.utils.browser.get_client_ip", return_value="0.0.0.0")
    def test_get_country_code_should_return_india_code_by_default(self, mock_request):
        actual_result = get_user_country_code("")
        excepted_result = "IN"
        self.assertEqual(excepted_result, actual_result)
