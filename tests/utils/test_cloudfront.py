from django.test import SimpleTestCase

from app.domain.cloudfront import add_live_stream_origin, add_live_stream_path_behavior


class TestCloudfront(SimpleTestCase):
    def test_add_live_stream_origin_should_add_origin_for_live_stream(self):
        origins: list = []
        add_live_stream_origin(origins)

        expected_data = [
            {
                "Id": "live.tpstreams.com",
                "DomainName": "live.tpstreams.com",
                "OriginPath": "",
                "CustomHeaders": {"Quantity": 0, "Items": []},
                "CustomOriginConfig": {
                    "HTTPPort": 80,
                    "HTTPSPort": 443,
                    "OriginProtocolPolicy": "http-only",
                    "OriginSslProtocols": {"Quantity": 1, "Items": ["TLSv1"]},
                    "OriginReadTimeout": 5,
                    "OriginKeepaliveTimeout": 30,
                },
            }
        ]
        self.assertEqual(expected_data, origins)

    def test_add_live_stream_path_behavior_should_be_added(
        self,
    ):
        behaviours: list = []
        add_live_stream_path_behavior(behaviours)

        expected_data = [
            {
                "PathPattern": "live/*/*/*.mpd", 
                "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6",
                "TargetOriginId": "live2.tpstreams.com", 
                "Compress": True, 
                "SmoothStreaming": False,
                "ViewerProtocolPolicy": "allow-all", 
                "FieldLevelEncryptionId": "",
                "LambdaFunctionAssociations": {"Quantity": 0},
                "AllowedMethods": {
                    "Quantity": 7, 
                    "Items": [
                        "HEAD", 
                        "DELETE", 
                        "POST", 
                        "GET", 
                        "OPTIONS", 
                        "PUT", 
                        "PATCH"
                    ],
                    "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}
                },
                "TrustedKeyGroups": {"Enabled": False, "Quantity": 0}
            },
            {
                "PathPattern": "live/*/*/*.m3u8",
                "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6",
                "TargetOriginId": "live2.tpstreams.com",
                "Compress": True,
                "SmoothStreaming": False,
                "ViewerProtocolPolicy": "allow-all",
                "FieldLevelEncryptionId": "",
                "LambdaFunctionAssociations": {"Quantity": 0},
                "AllowedMethods": {
                    "Quantity": 7,
                    "Items": [
                        "HEAD",
                        "DELETE",
                        "POST",
                        "GET",
                        "OPTIONS",
                        "PUT",
                        "PATCH",
                    ],
                    "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]},
                },
                "TrustedKeyGroups": {"Enabled": False, "Quantity": 0},
            },
            {
                "PathPattern": "live/*",
                "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6",
                "TargetOriginId": "live2.tpstreams.com",
                "Compress": True,
                "SmoothStreaming": False,
                "ViewerProtocolPolicy": "allow-all",
                "FieldLevelEncryptionId": "",
                "LambdaFunctionAssociations": {"Quantity": 0},
                "AllowedMethods": {
                    "Quantity": 7,
                    "Items": [
                        "HEAD",
                        "DELETE",
                        "POST",
                        "GET",
                        "OPTIONS",
                        "PUT",
                        "PATCH",
                    ],
                    "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]},
                },
                "TrustedKeyGroups": {"Enabled": False, "Quantity": 0},
            },
        ]
        self.assertEqual(expected_data, behaviours)
