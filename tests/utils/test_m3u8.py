from django.test import SimpleTestCase

from app.utils.m3u8 import (
    convert_playlist_urls_to_relative_paths,
    has_https_playlist,
    remove_audio_playlist,
    remove_variant_playlists,
)
from tests.data.m3u8 import (
    M3U8_RESPONSE,
    MODIFIED_M3U8,
    TPS_M3U8_CONTENT,
    TPS_M3U8_WITH_REMOVED_RESOLUTIONS,
)


class TestM3u8(SimpleTestCase):
    def test_has_https_playlist_should_return_true(self):
        result = has_https_playlist(M3U8_RESPONSE)
        self.assertTrue(result)

    def test_modified_m3u8_should_return_modified_m3u8_content(self):
        m3u8_with_converted_playlist_path = convert_playlist_urls_to_relative_paths(
            M3U8_RESPONSE
        )
        m3u8_with_removed_audio_playlist = remove_audio_playlist(
            m3u8_with_converted_playlist_path
        )
        self.assertEqual(MODIFIED_M3U8, m3u8_with_removed_audio_playlist)

    def test_remove_variant_playlists_should_remove_the_resolution_mentioned(self):
        m3u8_with_removed_variant_playlists = remove_variant_playlists(
            TPS_M3U8_CONTENT, [1080, 1440, 2160]
        )
        self.assertEqual(
            TPS_M3U8_WITH_REMOVED_RESOLUTIONS, m3u8_with_removed_variant_playlists
        )
