from datetime import timedelta
from unittest.mock import patch

from django.test import TestCase
from django_multitenant.utils import set_current_tenant

from app.management.commands import video_duration
from app.models import Video
from tests.mixins import AssetMixin, OrganizationMixin


class UpdateVideoDurationTestCase(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization(
            name="Test Organization", bucket_name="test-bucket"
        )
        set_current_tenant(self.organization)
        self.asset = self.create_asset(uuid="test-asset-uuid")
        self.video = self.create_video(asset=self.asset, status=Video.Status.COMPLETED)

    def test_update_video_duration(self):
        with patch("app.models.video.Video.get_playback_url") as mock_playback_url:
            mock_playback_url.return_value = (
                "https://dlbdnoa93s0gw.cloudfront.net/transcoded/7UsdZAQK5Df/video.m3u8"
            )

            self.command = video_duration.Command()
            self.command.organization = self.organization
            self.command.update_video_duration([self.video])

            self.video.refresh_from_db()

            expected_duration = timedelta(seconds=596)

            self.assertEqual(self.video.duration, expected_duration)

    def test_get_videos_to_process_force_update_true(self):
        self.command = video_duration.Command()

        videos = self.command.get_videos_to_process(
            all_assets=True, asset_id=None, force_update=True
        )

        self.assertEqual(
            videos.count(), Video.objects.filter(status=Video.Status.COMPLETED).count()
        )

    def test_get_videos_to_process_all_assets_force_update_true(self):
        self.command = video_duration.Command()

        videos = self.command.get_videos_to_process(True, None, True)

        self.assertEqual(
            videos.count(), Video.objects.filter(status=Video.Status.COMPLETED).count()
        )

    def test_get_videos_to_process_all_assets_force_update_false(self):
        self.command = video_duration.Command()

        videos = self.command.get_videos_to_process(True, None, False)

        expected_videos = Video.objects.filter(
            duration=None, status=Video.Status.COMPLETED
        )
        self.assertQuerysetEqual(videos, expected_videos, transform=lambda x: x)

    def test_get_videos_to_process_asset_id_force_update_true(self):
        self.command = video_duration.Command()

        videos = self.command.get_videos_to_process(False, self.asset.uuid, True)

        self.assertEqual(
            len(videos), Video.objects.filter(status=Video.Status.COMPLETED).count()
        )

    def test_get_videos_to_process_asset_id_force_update_false(self):
        self.command = video_duration.Command()
        self.video.duration = timedelta(minutes=5)
        self.video.save()

        videos = self.command.get_videos_to_process(False, self.asset.uuid, False)

        self.assertEqual(videos, None)

    def test_get_videos_to_process_asset_id_with_duration_force_update_true(self):
        self.video.duration = timedelta(minutes=5)
        self.video.save()

        self.command = video_duration.Command()

        videos = self.command.get_videos_to_process(False, self.asset.uuid, True)

        self.assertListEqual(
            list(videos), [Video.objects.filter(asset=self.asset).first()]
        )

    def test_get_videos_to_process_asset_id_with_duration_force_update_false(self):
        self.video.duration = timedelta(minutes=5)
        self.video.save()

        self.command = video_duration.Command()

        videos = self.command.get_videos_to_process(False, self.asset.uuid, False)

        self.assertEqual(videos, None)
