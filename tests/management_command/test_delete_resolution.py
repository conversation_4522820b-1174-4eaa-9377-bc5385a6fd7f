from unittest.mock import patch

from django.core.management import call_command
from django.test import TestCase
from django_multitenant.utils import set_current_tenant

from app.models import Video
from app.utils.wasabi import get_wasabi_config
from tests.data.m3u8 import TPS_M3U8_CONTENT
from tests.mixins import AssetMixin, OrganizationMixin


class DeleteResolutionsCommandTestCase(TestCase, OrganizationMixin, AssetMixin):
    def setUp(self):
        self.organization = self.create_organization(
            name="Test Organization", bucket_name="test-bucket"
        )
        set_current_tenant(self.organization)

    @patch(
        "app.management.commands.delete_resolutions.get_m3u8_content",
        return_value=TPS_M3U8_CONTENT,
    )
    @patch(
        "app.management.commands.delete_resolutions.Command.create_modified_m3u8_file"
    )
    @patch("app.management.commands.delete_resolutions.delete_moved_file_from_local")
    @patch("app.management.commands.delete_resolutions.move_files_to_wasabi")
    @patch("app.management.commands.delete_resolutions.invalidate_cache")
    @patch("app.management.commands.delete_resolutions.delete_folder")
    def test_command_should_call_invalidate_cache(
        self,
        mock_delete_folder,
        mock_invalidate_cache,
        mock_move_files_to_wasabi,
        mock_delete_moved_file_from_local,
        mock_modified_m3u8,
        mock_get_m3u8_content,
    ):
        self.create_video(status=Video.Status.COMPLETED)
        call_command(
            "delete_resolutions",
            "--org-code",
            self.organization.uuid,
            "--resolutions",
            "1080",
            "1440",
            "2160",
        )
        mock_invalidate_cache.assert_called()

    @patch(
        "app.management.commands.delete_resolutions.get_m3u8_content",
        return_value=TPS_M3U8_CONTENT,
    )
    @patch(
        "app.management.commands.delete_resolutions.Command.create_modified_m3u8_file",
        return_value="/path",
    )
    @patch("app.management.commands.delete_resolutions.delete_moved_file_from_local")
    @patch("app.management.commands.delete_resolutions.move_files_to_wasabi")
    @patch("app.management.commands.delete_resolutions.invalidate_cache")
    @patch("app.management.commands.delete_resolutions.delete_folder")
    def test_command_should_call_only_the_passed_asset_id(
        self,
        mock_delete_folder,
        mock_invalidate_cache,
        mock_move_files_to_wasabi,
        mock_delete_moved_file_from_local,
        mock_modified_m3u8,
        mock_get_m3u8_content,
    ):
        video = self.create_video(status=Video.Status.COMPLETED)
        video2 = self.create_video(status=Video.Status.COMPLETED)
        call_command(
            "delete_resolutions",
            "--org-code",
            self.organization.uuid,
            "--asset-id",
            video.asset.uuid,
            "--resolutions",
            "1080",
            "1440",
            "2160",
        )
        config = get_wasabi_config(self.organization)
        mock_move_files_to_wasabi.assert_called_once_with(
            input_file="/path",
            output_path=f"{self.organization.bucket_name}/transcoded/{video.asset.uuid}",
            config=config,
            make_public=True,
        )

    @patch(
        "app.management.commands.delete_resolutions.get_m3u8_content",
        return_value=TPS_M3U8_CONTENT,
    )
    @patch(
        "app.management.commands.delete_resolutions.Command.create_modified_m3u8_file",
        return_value="/path",
    )
    @patch("app.management.commands.delete_resolutions.delete_moved_file_from_local")
    @patch("app.management.commands.delete_resolutions.move_files_to_wasabi")
    @patch("app.management.commands.delete_resolutions.invalidate_cache")
    @patch("app.management.commands.delete_resolutions.delete_folder")
    def test_command_should_call_all_the_assets(
        self,
        mock_delete_folder,
        mock_invalidate_cache,
        mock_move_files_to_wasabi,
        mock_delete_moved_file_from_local,
        mock_modified_m3u8,
        mock_get_m3u8_content,
    ):
        video = self.create_video(status=Video.Status.COMPLETED)
        video2 = self.create_video(status=Video.Status.COMPLETED)
        call_command(
            "delete_resolutions",
            "--org-code",
            self.organization.uuid,
            "--resolutions",
            "1080",
            "1440",
            "2160",
        )

        assert mock_move_files_to_wasabi.call_count == 2
