import os
import tempfile
from unittest.mock import patch

from django.test import TestCase

from app.management.commands import generate_asset_size_report
from app.models import Video
from tests.mixins import AssetMixin, OrganizationMixin


class GenerateAssetSizeReportTestCase(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization(
            name="Test Organization", bucket_name="test-bucket"
        )
        self.asset = self.create_asset(uuid="test-asset-uuid", title="Test Asset")
        self.video = self.create_video(asset=self.asset, status=Video.Status.COMPLETED)
        self.temp_file_name = tempfile.mktemp(suffix=".xlsx")

    def tearDown(self):
        if os.path.exists(self.temp_file_name):
            os.unlink(self.temp_file_name)

    @patch("app.management.commands.generate_asset_size_report.get_source_files_size")
    @patch(
        "app.management.commands.generate_asset_size_report.get_transcoded_file_size"
    )
    def test_generate_report(self, mock_get_transcoded_size, mock_get_source_size):
        mock_get_source_size.return_value = 1024 * 1024
        mock_get_transcoded_size.return_value = 2 * 1024 * 1024

        self.command = generate_asset_size_report.Command()
        self.command.handle(
            org_ids=[str(self.organization.uuid)], output=self.temp_file_name
        )

        self.assertTrue(os.path.exists(self.temp_file_name))
        mock_get_source_size.assert_called_once_with(self.asset)
        mock_get_transcoded_size.assert_called_once_with(self.asset)

    @patch("app.management.commands.generate_asset_size_report.get_source_files_size")
    @patch(
        "app.management.commands.generate_asset_size_report.get_transcoded_file_size"
    )
    def test_generate_report_nonexistent_org(
        self, mock_get_transcoded_size, mock_get_source_size
    ):
        mock_get_source_size.return_value = 1024 * 1024
        mock_get_transcoded_size.return_value = 2 * 1024 * 1024

        self.command = generate_asset_size_report.Command()
        self.command.handle(org_ids=["nonexistent-uuid"], output=self.temp_file_name)

        self.assertFalse(os.path.exists(self.temp_file_name))
