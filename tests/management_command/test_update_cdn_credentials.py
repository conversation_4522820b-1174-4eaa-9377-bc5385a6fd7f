from django.core.management import call_command
from django.test import TestCase

from app.models.organization import Organization
from tests.mixins import OrganizationMixin


class UpdateCDNCredentialsCommandTestCase(OrganizationMixin, TestCase):
    def setUp(self):
        self.old_cdn_access_key_id = "OLDKEY123"
        self.new_cdn_access_key_id = "NEWKEY456"
        self.new_cdn_secret_access_key = "NEWSECRET789"
        self.org1 = self.create_organization(
            cdn_access_key_id=self.old_cdn_access_key_id,
            cdn_secret_access_key="SECRET1",
        )
        self.org2 = self.create_organization(
            cdn_access_key_id=self.old_cdn_access_key_id,
            cdn_secret_access_key="SECRET2",
        )
        self.org3 = self.create_organization(
            cdn_access_key_id="OTHERKEY", cdn_secret_access_key="SECRET3"
        )

    def test_should_update_only_organizations_with_matching_cdn_access_key_id(self):
        out = self._call_command()
        self.org1.refresh_from_db()
        self.org2.refresh_from_db()
        self.org3.refresh_from_db()
        self.assertEqual(self.org1.cdn_access_key_id, self.new_cdn_access_key_id)
        self.assertEqual(self.org2.cdn_access_key_id, self.new_cdn_access_key_id)
        self.assertEqual(
            self.org1.cdn_secret_access_key, self.new_cdn_secret_access_key
        )
        self.assertEqual(
            self.org2.cdn_secret_access_key, self.new_cdn_secret_access_key
        )
        self.assertEqual(self.org3.cdn_access_key_id, "OTHERKEY")
        self.assertIn(
            "Successfully updated CDN credentials for 2 organization(s).", out
        )

    def test_should_display_zero_organizations_when_no_organizations_match(self):
        Organization.objects.filter(id__in=[self.org1.id, self.org2.id]).update(
            cdn_access_key_id="DIFFERENTKEY"
        )
        out = self._call_command()
        self.assertIn(
            "Successfully updated CDN credentials for 0 organization(s).", out
        )

    def test_should_handle_partial_update_correctly(self):
        Organization.objects.filter(id=self.org2.id).update(
            cdn_access_key_id="DIFFERENTKEY"
        )
        out = self._call_command()
        self.org1.refresh_from_db()
        self.org2.refresh_from_db()
        self.assertEqual(self.org1.cdn_access_key_id, self.new_cdn_access_key_id)
        self.assertNotEqual(self.org2.cdn_access_key_id, self.new_cdn_access_key_id)
        self.assertIn(
            "Successfully updated CDN credentials for 1 organization(s).", out
        )

    def _call_command(self):
        from io import StringIO

        out = StringIO()
        call_command(
            "update_cdn_credentials",
            old_cdn_access_key_id=self.old_cdn_access_key_id,
            new_cdn_access_key_id=self.new_cdn_access_key_id,
            new_cdn_secret_access_key=self.new_cdn_secret_access_key,
            stdout=out,
        )
        return out.getvalue()
