from unittest.mock import patch

from django.core.management import call_command
from django.test import TestCase
from django_multitenant.utils import set_current_tenant

from app.models import Video
from tests.mixins import AssetMixin, OrganizationMixin


class TranscodeVideoWithDRMCommandTestCase(TestCase, OrganizationMixin, AssetMixin):
    def setUp(self):
        self.organization = self.create_organization(
            name="Test Organization", bucket_name="test-bucket"
        )
        set_current_tenant(self.organization)

        self.completed_video_no_drm = self.create_video(
            status=Video.Status.COMPLETED, enable_drm=False
        )
        self.completed_video_with_drm = self.create_video(
            status=Video.Status.COMPLETED, enable_drm=True
        )
        self.transcoding_video = self.create_video(
            status=Video.Status.TRANSCODING, enable_drm=False
        )

    @patch(
        "app.management.commands.transcode_video_with_drm.transcode_video_with_backup"
    )
    def test_command_should_transcode_specific_asset(self, mock_transcode):
        call_command(
            "transcode_video_with_drm",
            "--org-uuid",
            self.organization.uuid,
            "--asset-ids",
            self.completed_video_no_drm.asset.uuid,
        )

        self.assertEqual(mock_transcode.call_count, 1)
        self.completed_video_no_drm.refresh_from_db()
        self.assertTrue(self.completed_video_no_drm.enable_drm)
        self.assertEqual(
            self.completed_video_no_drm.content_protection_type,
            Video.ContentProtectionType.DRM,
        )

    @patch(
        "app.management.commands.transcode_video_with_drm.transcode_video_with_backup"
    )
    def test_command_should_transcode_all_assets(self, mock_transcode):
        self.video2 = self.create_video(status=Video.Status.COMPLETED, enable_drm=False)

        call_command(
            "transcode_video_with_drm",
            "--org-uuid",
            self.organization.uuid,
            "--all-assets",
        )

        self.assertEqual(mock_transcode.call_count, 2)
        self.completed_video_no_drm.refresh_from_db()
        self.video2.refresh_from_db()
        self.assertTrue(self.completed_video_no_drm.enable_drm)
        self.assertEqual(
            self.completed_video_no_drm.content_protection_type,
            Video.ContentProtectionType.DRM,
        )
        self.assertTrue(self.video2.enable_drm)
        self.assertEqual(
            self.video2.content_protection_type, Video.ContentProtectionType.DRM
        )

    @patch(
        "app.management.commands.transcode_video_with_drm.transcode_video_with_backup"
    )
    def test_command_should_skip_videos_with_drm(self, mock_transcode):
        call_command(
            "transcode_video_with_drm",
            "--org-uuid",
            self.organization.uuid,
            "--all-assets",
        )

        self.assertEqual(mock_transcode.call_count, 1)
        self.completed_video_with_drm.refresh_from_db()
        self.assertTrue(self.completed_video_with_drm.enable_drm)

    @patch(
        "app.management.commands.transcode_video_with_drm.transcode_video_with_backup"
    )
    def test_command_should_skip_incomplete_videos(self, mock_transcode):
        call_command(
            "transcode_video_with_drm",
            "--org-uuid",
            self.organization.uuid,
            "--all-assets",
        )

        self.assertEqual(mock_transcode.call_count, 1)
        self.transcoding_video.refresh_from_db()
        self.assertFalse(self.transcoding_video.enable_drm)
        self.completed_video_no_drm.refresh_from_db()
        self.assertTrue(self.completed_video_no_drm.enable_drm)
