from django.test import TestCase
from django.core.management import call_command
from unittest import mock
from app.models import Video, Track, Playlist
from tests.mixins import AssetMixin, OrganizationMixin
from django_multitenant.utils import set_current_tenant

class UpdatePlaylistTestCase(AssetMixin, OrganizationMixin, TestCase):
    
    def setUp(self):
        self.organization = self.create_organization(name="Test Organization", bucket_name="test-bucket")
        set_current_tenant(self.organization)
        self.asset = self.create_asset(uuid="test-asset-uuid")
        self.video = self.create_video(asset=self.asset, status= Video.Status.COMPLETED)
    
    @mock.patch("app.models.video.Video.get_playback_url", return_value="https://d28qihy7z761lk.cloudfront.net/transcoded/Y9nezd6SqAm/video.m3u8")
    def test_command_should_update_playlist_from_m3u8(self, mock_playback_url):
        
        call_command("update_playlist_height_width", org_code=self.organization.uuid)
        
        track = Track.objects.filter(video=self.video).first()  
        playlists = Playlist.objects.filter(track=track)

        self.assertEqual(playlists.count(), 4)
        self.assertIn(playlists.first(), track.playlists.all())
