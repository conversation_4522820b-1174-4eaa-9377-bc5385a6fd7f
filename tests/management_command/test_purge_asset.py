from unittest.mock import patch

from django.core.management import call_command
from django.test import TestCase

from tests.mixins import OrganizationMixin


class PurgeAssetsCommandTestCase(TestCase, OrganizationMixin):
    def setUp(self):
        self.organization = self.create_organization(
            name="Test Organization", bucket_name="test-bucket"
        )

    @patch("app.tasks.purge_assets_task.delay")
    def test_command_should_call_purge_assets_task(self, mock_purge_assets_task):
        call_command(
            "purge_assets", "--org-code", self.organization.uuid, "--days", str(30)
        )
        mock_purge_assets_task.assert_called_once_with(self.organization.uuid, 30)
