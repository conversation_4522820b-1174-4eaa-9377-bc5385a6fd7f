from unittest import mock

from django.core.management import call_command
from django.test import TestCase
from django_multitenant.utils import set_current_tenant

from app.models import Track, Video
from tests.mixins import OrganizationMixin, TrackMixin


class GenerateSubtitleTestCase(TrackMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization(
            name="Test Organization", bucket_name="test-bucket"
        )
        set_current_tenant(self.organization)

    @mock.patch("app.tasks.subtitle.GenerateSubtitleTask.apply_async")
    def test_command_should_generate_subtitle_for_specific_asset(
        self, mock_apply_async
    ):
        asset = self.create_asset(uuid="test-asset-uuid")
        video = self.create_video(asset=asset, status=Video.Status.COMPLETED)
        call_command(
            "generate_subtitle", org_code=self.organization.uuid, asset_id=asset.uuid
        )
        mock_apply_async.assert_called_once_with(
            kwargs={
                "asset_id": asset.uuid,
                "organization_uuid": self.organization.uuid,
            },
            queue="subtitle_generation",
        )
        video.refresh_from_db()
        self.assertTrue(video.generate_subtitle)

    @mock.patch("app.tasks.subtitle.GenerateSubtitleTask.apply_async")
    def test_command_should_generate_subtitle_for_all_assets(self, mock_apply_async):
        assets = [self.create_asset(uuid=f"test-asset-uuid{i}") for i in range(1, 4)]

        for asset in assets:
            self.create_video(asset=asset, status=Video.Status.COMPLETED)

        call_command("generate_subtitle", org_code=self.organization.uuid)

        expected_calls = [
            mock.call(
                kwargs={
                    "asset_id": asset.uuid,
                    "organization_uuid": self.organization.uuid,
                },
                queue="subtitle_generation",
            )
            for asset in assets
        ]

        mock_apply_async.assert_has_calls(expected_calls, any_order=True)
        self.assertEqual(mock_apply_async.call_count, len(assets))

        for asset in assets:
            asset.video.refresh_from_db()
            self.assertTrue(asset.video.generate_subtitle)

    @mock.patch("app.tasks.subtitle.GenerateSubtitleTask.apply_async")
    def test_command_should_not_generate_subtitle_for_all_asset_if_already_generated(
        self, mock_apply_async
    ):
        asset = self.create_asset(uuid="test-asset-uuid")
        video = self.create_video(asset=asset, status=Video.Status.COMPLETED)
        self.create_subtitle(
            video=video, subtitle_type=Track.SubtitleType.AUTO_GENERATED
        )
        call_command(
            "generate_subtitle", org_code=self.organization.uuid, asset_id=asset.uuid
        )
        mock_apply_async.assert_not_called()
