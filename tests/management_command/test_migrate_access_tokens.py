import json
import uuid
from unittest import mock

from django.core.cache import cache
from django.core.management import call_command
from django.test import TestCase

from app.models import AccessToken, Video
from tests.mixins import AssetMixin, OrganizationMixin


class MigrateAccessTokensTestCase(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        super().setUp()
        self.organization = self.create_organization(
            name="Test Organization", bucket_name="test-bucket"
        )
        self.asset = self.create_asset(
            uuid="test-asset-uuid", organization=self.organization
        )
        self.asset.video = self.create_video(
            asset=self.asset, status=Video.Status.COMPLETED
        )
        self._setup_test_token()

    def tearDown(self):
        cache.delete(self.cache_key)
        super().tearDown()

    def _setup_test_token(self):
        self.token_data = {
            "uuid": str(uuid.uuid4()),
            "valid_until": None,
            "asset_id": self.asset.uuid,
            "expires_after_first_usage": False,
            "organization_id": str(self.organization.id),
        }
        self.cache_key = (
            f"org_id_{self.organization.uuid}_asset_id_{self.asset.uuid}_token_test"
        )
        cache.set(self.cache_key, json.dumps(self.token_data))

    def test_should_migrate_token_when_expires_after_first_usage_is_false(self):
        call_command("migrate_access_tokens", org_id=self.organization.uuid)
        token = AccessToken.objects.filter(asset=self.asset).first()
        self.assertIsNotNone(token)
        self.assertEqual(str(token.uuid), self.token_data["uuid"])
        self.assertFalse(token.expires_after_first_usage)

    def test_should_not_migrate_token_when_expires_after_first_usage_is_true(self):
        self.token_data["expires_after_first_usage"] = True
        cache.set(self.cache_key, json.dumps(self.token_data))
        call_command("migrate_access_tokens", org_id=self.organization.uuid)
        self.assertEqual(AccessToken.objects.count(), 0)

    def test_should_not_migrate_token_with_valid_until(self):
        self.token_data["valid_until"] = "2024-12-31T23:59:59Z"
        cache.set(self.cache_key, json.dumps(self.token_data))
        call_command("migrate_access_tokens", org_id=self.organization.uuid)
        self.assertEqual(AccessToken.objects.count(), 0)

    def test_should_skip_invalid_token_data(self):
        cache.set(self.cache_key, "not valid json")
        call_command("migrate_access_tokens", org_id=self.organization.uuid)
        self.assertEqual(AccessToken.objects.count(), 0)

    def test_should_handle_assets_with_no_tokens(self):
        self.create_asset(uuid="another-asset-uuid")
        call_command("migrate_access_tokens", org_id=self.organization.uuid)
        self.assertEqual(AccessToken.objects.count(), 1)

    @mock.patch("app.models.AccessToken.save")
    def test_should_handle_save_error(self, mock_save):
        mock_save.side_effect = Exception("Simulated failure")
        call_command("migrate_access_tokens", org_id=self.organization.uuid)
        self.assertEqual(AccessToken.objects.count(), 0)

    def test_should_process_all_organizations_when_no_org_id_provided(self):
        call_command("migrate_access_tokens")
        self.assertEqual(AccessToken.objects.count(), 1)
        self.assertTrue(
            AccessToken.objects.filter(uuid=self.token_data["uuid"]).exists()
        )

    def test_should_properly_handle_tenant_switching(self):
        call_command("migrate_access_tokens")
        self.assertEqual(AccessToken.objects.count(), 1)
        self.assertTrue(
            AccessToken.objects.filter(
                uuid=self.token_data["uuid"], organization_id=self.organization.id
            ).exists()
        )

    def test_should_handle_duplicate_tokens(self):
        call_command("migrate_access_tokens", org_id=self.organization.uuid)
        initial_count = AccessToken.objects.count()
        call_command("migrate_access_tokens", org_id=self.organization.uuid)
        self.assertEqual(AccessToken.objects.count(), initial_count)

    def test_should_handle_multiple_tokens_for_same_asset(self):
        token_data2 = {
            "uuid": str(uuid.uuid4()),
            "valid_until": None,
            "asset_id": self.asset.uuid,
            "expires_after_first_usage": False,
            "organization_id": str(self.organization.id),
        }
        cache_key2 = (
            f"org_id_{self.organization.uuid}_asset_id_{self.asset.uuid}_token_test2"
        )
        cache.set(cache_key2, json.dumps(token_data2))
        call_command("migrate_access_tokens", org_id=self.organization.uuid)
        self.assertEqual(AccessToken.objects.count(), 2)
        self.assertTrue(
            AccessToken.objects.filter(uuid=self.token_data["uuid"]).exists()
        )
        self.assertTrue(AccessToken.objects.filter(uuid=token_data2["uuid"]).exists())

    def test_should_skip_asset_with_incomplete_video(self):
        incomplete_asset = self.create_asset(
            uuid="incomplete-asset", organization=self.organization
        )
        incomplete_asset.video = self.create_video(
            asset=incomplete_asset, status=Video.Status.TRANSCODING
        )
        incomplete_token_data = {
            "uuid": str(uuid.uuid4()),
            "valid_until": None,
            "asset_id": incomplete_asset.uuid,
            "expires_after_first_usage": False,
            "organization_id": str(self.organization.id),
        }
        incomplete_cache_key = f"org_id_{self.organization.uuid}_asset_id_{incomplete_asset.uuid}_token_test"
        cache.set(incomplete_cache_key, json.dumps(incomplete_token_data))
        call_command("migrate_access_tokens", org_id=self.organization.uuid)
        self.assertEqual(AccessToken.objects.count(), 1)
        self.assertTrue(
            AccessToken.objects.filter(uuid=self.token_data["uuid"]).exists()
        )

    def test_should_skip_token_with_missing_uuid(self):
        invalid_token_data = {
            "valid_until": None,
            "asset_id": self.asset.uuid,
            "expires_after_first_usage": False,
            "organization_id": str(self.organization.id),
        }
        cache.set(self.cache_key, json.dumps(invalid_token_data))
        call_command("migrate_access_tokens", org_id=self.organization.uuid)
        self.assertEqual(AccessToken.objects.count(), 0)
