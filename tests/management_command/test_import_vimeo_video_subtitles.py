from unittest.mock import patch

from django.core.management import call_command
from django.test import TestCase
from django_multitenant.utils import set_current_tenant

from app.models.videoimport import ImportedVideo
from app.utils.vimeo import VideoSubtitle
from tests.domain.data.vimeo_video_detail import VIMEO_VIDEO_DETAIL_RESPONSE
from tests.mixins import AssetMixin, OrganizationMixin


class TestImportVimeoVideoSubtitles(TestCase, OrganizationMixin, AssetMixin):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.video = self.create_video()
        self.asset = self.video.asset
        self.imported_video = self.create_import_video(self.video)

    def create_import_video(self, video):
        return ImportedVideo.objects.create(
            folder=None,
            name=VIMEO_VIDEO_DETAIL_RESPONSE["name"],
            uri=VIMEO_VIDEO_DETAIL_RESPONSE["link"],
            organization=self.organization,
            details=VIMEO_VIDEO_DETAIL_RESPONSE,
            video=video,
        )

    @patch(
        "app.management.commands.import_vimeo_video_subtitles.Vimeo.get_video_subtitles"
    )
    @patch(
        "app.management.commands.import_vimeo_video_subtitles.upload_subtitle_from_url"
    )
    def test_command_imports_subtitles_for_specific_asset(
        self, mock_upload, mock_vimeo_get_video_subtitles
    ):
        data = {
            "id": 2,
            "active": True,
            "language": "en",
            "link": "subtitle_link",
            "name": "subtitle1",
        }
        mock_vimeo_get_video_subtitles.return_value = [VideoSubtitle.create(data)]
        call_command(
            "import_vimeo_video_subtitles",
            "--org-code",
            self.organization.uuid,
            "--vimeo-access-token=140091460943200",
            "--asset-id",
            self.asset.uuid,
        )

        mock_upload.assert_called_once_with(
            self.asset, "subtitle1", "en", "subtitle_link"
        )

    @patch(
        "app.management.commands.import_vimeo_video_subtitles.Vimeo.get_video_subtitles"
    )
    @patch(
        "app.management.commands.import_vimeo_video_subtitles.upload_subtitle_from_url"
    )
    def test_command_imports_subtitles_for_all_assets(
        self, mock_upload, mock_vimeo_get_video_subtitles
    ):
        self.second_video = self.create_video()
        self.second_imported_video = self.create_import_video(self.second_video)
        data = {
            "id": 2,
            "active": True,
            "language": "en",
            "link": "subtitle_link",
            "name": "subtitle1",
        }
        mock_vimeo_get_video_subtitles.return_value = [VideoSubtitle.create(data)]

        call_command(
            "import_vimeo_video_subtitles",
            "--org-code",
            self.organization.uuid,
            "--vimeo-access-token=140091460943200",
        )
        self.assertEqual(mock_upload.call_count, 2)
