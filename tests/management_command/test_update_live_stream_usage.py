from unittest.mock import patch

from django.core.management import call_command
from django.test import TestCase

from tests.mixins import OrganizationMixin


class TestLiveStreamUsageCommand(TestCase, OrganizationMixin):
    def setUp(self):
        self.organization = self.create_organization(
            name="Test Organization", bucket_name="test-bucket"
        )

    @patch("app.tasks.live_stream.UpdateLiveStreamUsageTask.apply_async")
    def test_command_should_call_update_live_usage_task(self, mock_live_usage_task):
        call_command("update_live_stream_usage")
        mock_live_usage_task.assert_called_once()
