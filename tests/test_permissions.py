import datetime
import uuid

from django.utils import timezone
from django_multitenant.utils import set_current_tenant

from app.api.v1.permissions import HasAccessTokenOrCheckAccess, HasOrganizationAccess
from app.api.v1.permissions.analytics import IsAnalyticsRequestValid
from tests import TestCase
from tests.mixins import OrganizationMixin
from tests.mixins.asset import AssetMixin


class MockView:
    def __init__(self, organization_id):
        self.kwargs = {"organization_id": organization_id}


class TestHasOrganizationAccessPermission(OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.permission = HasOrganizationAccess()

    def test_should_allow_user_who_created_organization(self):
        request = self.get_request("/", user=self.organization.created_by, method="GET")
        has_permission = self.permission.has_permission(
            request, MockView(self.organization.uuid)
        )

        self.assertTrue(has_permission)

    def test_should_allow_user_who_is_member_of_organization(self):
        new_user = self.create_user()
        self.create_membership(user=new_user)
        request = self.get_request("/", user=new_user, method="GET")
        has_permission = self.permission.has_permission(
            request, MockView(self.organization.uuid)
        )

        self.assertTrue(has_permission)

    def test_should_not_allow_user_who_is_not_member_of_organization(self):
        new_user = self.create_user()
        request = self.get_request("/", user=new_user, method="GET")
        has_permission = self.permission.has_permission(
            request, MockView(self.organization.uuid)
        )

        self.assertFalse(has_permission)

    def test_should_not_allow_for_incorrect_organization_id(self):
        request = self.get_request("/", user=self.organization.created_by, method="GET")
        has_permission = self.permission.has_permission(request, MockView(uuid.uuid4()))

        self.assertFalse(has_permission)


class TestHasAccessTokenOrCheckAccess(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.permission = HasAccessTokenOrCheckAccess()
        set_current_tenant(self.organization)

    def test_should_allow_request_with_valid_access_token(self):
        video = self.create_video()
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(f"/?access_token={access_token.uuid}", method="GET")
        has_permission = self.permission.has_object_permission(
            request, MockView(self.organization.uuid), video.asset
        )

        self.assertTrue(has_permission)

    # def test_should_not_allow_request_with_incorrect_access_token(self):
    #     video = self.create_video()
    #     request = self.get_request(f"/?access_token={uuid.uuid4()}", method="GET")
    #     has_permission = self.permission.has_object_permission(
    #         request, MockView(self.organization.uuid), video.asset
    #     )

    #     self.assertFalse(has_permission)

    def test_should_allow_request_for_logged_in_user(self):
        video = self.create_video()
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(
            f"/?access_token={access_token.uuid}",
            user=self.organization.created_by,
            method="GET",
        )
        has_permission = self.permission.has_object_permission(
            request, MockView(self.organization.uuid), video.asset
        )

        self.assertTrue(has_permission)


class TestIsAnalyticsRequestValid(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.permission = IsAnalyticsRequestValid()

    def test_should_allow_request_with_valid_access(self):
        video = self.create_video()
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(f"/?access_token={access_token.uuid}", method="POST")
        has_permission = self.permission.has_object_permission(
            request, MockView(self.organization.uuid), video.asset
        )

        self.assertTrue(has_permission)

    def test_should_allow_request_with_access_token_expired_within_eight_hours(self):
        video = self.create_video()
        access_token = self.create_access_token(asset=video.asset)
        expiration_time = timezone.now() - datetime.timedelta(hours=7, minutes=59)
        access_token.valid_until = expiration_time
        access_token.save()
        request = self.get_request(f"/?access_token={access_token.uuid}", method="POST")
        has_permission = self.permission.has_object_permission(
            request, MockView(self.organization.uuid), video.asset
        )

        self.assertTrue(has_permission)

    # def test_should_not_allow_request_with_incorrect_access_token(self):
    #     video = self.create_video()
    #     request = self.get_request(f"/?access_token={uuid.uuid4()}", method="POST")
    #     has_permission = self.permission.has_object_permission(
    #         request, MockView(self.organization.uuid), video.asset
    #     )

    #     self.assertFalse(has_permission)
