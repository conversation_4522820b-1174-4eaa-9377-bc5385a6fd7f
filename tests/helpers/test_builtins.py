from django.http import QueryDict
from django.template import Context
from django.urls import reverse

from app.helpers.builtins import get_updated_query_string
from tests import TestCase


class TestGetUpdatedQueryString(TestCase):
    def test_returns_updated_query_string(self):
        context = Context(
            {
                "request": self.get_request(
                    path=reverse("assets") + "?folder=test&page=2"
                )
            }
        )
        updated_query_string = get_updated_query_string(context, "page", 2)
        updated_query_params = QueryDict(updated_query_string)

        self.assertEqual(updated_query_params["page"], "2")

    def test_should_add_query_param_if_its_not_already_exists(self):
        context = Context(
            {"request": self.get_request(path=reverse("assets") + "?folder=test")}
        )
        updated_query_string = get_updated_query_string(context, "page", 10)
        updated_query_params = QueryDict(updated_query_string)

        self.assertEqual(updated_query_params["page"], "10")
