from datetime import timedelta

from app.helpers.time import humanize_time
from tests import TestCase


class TestTimeTemplateTags(TestCase):
    def test_humanize_time_with_hours(self):
        duration = timedelta(hours=2, minutes=30, seconds=45)
        result = humanize_time(duration)
        self.assertEqual(result, "2 hours 30 minutes 45 seconds")

    def test_humanize_time_with_minutes(self):
        duration = timedelta(minutes=45, seconds=20)
        result = humanize_time(duration)
        self.assertEqual(result, "45 minutes 20 seconds")

    def test_humanize_time_with_seconds(self):
        duration = timedelta(seconds=15)
        result = humanize_time(duration)
        self.assertEqual(result, "15 seconds")

    def test_humanize_time_with_zero_duration(self):
        duration = timedelta()
        result = humanize_time(duration)
        self.assertEqual(result, "")

    def test_humanize_time_with_singular_units(self):
        duration_hour = timedelta(hours=1)
        duration_minute = timedelta(minutes=1)
        duration_second = timedelta(seconds=1)

        result_hour = humanize_time(duration_hour)
        result_minute = humanize_time(duration_minute)
        result_second = humanize_time(duration_second)

        self.assertEqual(result_hour, "1 hour")
        self.assertEqual(result_minute, "1 minute")
        self.assertEqual(result_second, "1 second")

    def test_humanize_time_with_int(self):
        seconds = 3665  # 1 hour, 1 minute, and 5 seconds
        result = humanize_time(seconds)
        self.assertEqual(result, "1 hour 1 minute 5 seconds")

    def test_humanize_time_with_timedelta(self):
        duration = timedelta(hours=2, minutes=30, seconds=15)
        result = humanize_time(duration)
        self.assertEqual(result, "2 hours 30 minutes 15 seconds")
