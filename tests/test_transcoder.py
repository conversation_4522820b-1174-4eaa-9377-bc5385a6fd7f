import json

import responses
from django.conf import settings
from django.test import override_settings

from app.domain.transcoder import (
    LumberjackJobTranscoder,
    create_transcoding_job,
    get_transcoder,
)
from app.models import TranscodingJob, Video
from tests import TestCase
from tests.mixins import OrganizationMixin
from tests.mixins.asset import AssetMixin
from tests.mixins.transcoding_job import TranscodingJobMixin


class TestTranscoder(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()

    def test_generate_settings_should_return_settings(self):
        video = self.create_video(resolutions=[Video.Resolutions._240p])
        transcoder = get_transcoder(video.asset)
        transcoder.webhook_url = (
            f"localhost/api/v1/assets/{video.asset.uuid}/update_video_status/"
        )
        actual_settings = transcoder.settings()

        expected = {
            "input_url": video.inputs.first().url,
            "output_url": f"{video.organization.bucket_url}/transcoded/{video.asset.uuid}/",
            "settings": {
                "outputs": [
                    {
                        "name": "240p_h264",
                        "audio": {"codec": "aac", "bitrate": 72000},
                        "video": {
                            "codec": "h264",
                            "width": 426,
                            "height": 240,
                            "preset": "faster",
                            "bitrate_buffer_size": 192000,
                            "max_video_bitrate": 192000,
                            "crf": 26,
                            "fps": 24,
                        },
                    }
                ],
                "format": "hls",
                "playlist_type": "vod",
                "segmentLength": 10,
                "segment_only": False,
            },
            "webhook_url": f"{settings.SITE_URL}/api/v1/{self.organization.uuid}/"
            f"assets/{video.asset.uuid}/update_video_status/",
            "storage_parameters": {
                "output": {
                    "ACCESS_KEY_ID": None,
                    "SECRET_KEY": None,
                    "ENDPOINT": "https://s3.None.wasabisys.com",
                }
            },
            "meta_data": json.dumps(
                {
                    "org_code": self.organization.uuid,
                    "org_name": self.organization.name,
                    "asset_id": video.asset.uuid,
                }
            ),
        }
        self.assertEqual(expected, actual_settings)

    def test_settings_should_have_extra_settings_passed_as_argument(self):
        video = self.create_video(
            resolutions=[Video.Resolutions._240p], transmux_only=True
        )
        transcoder = get_transcoder(video.asset)
        transcoder.webhook_url = (
            f"localhost/api/v1/assets/{video.asset.uuid}/update_video_status/"
        )

        expected_inputs = [
            {"url": "https://custom-url-1.com", "name": ""},
            {"url": "https://custom-url-2.com", "name": ""},
        ]
        settings = transcoder.settings(extra_settings={"inputs": expected_inputs})

        self.assertEqual(expected_inputs, settings["inputs"])

    def test_settings_should_use_different_output_url_if_preserve_transcodings_is_true(
        self,
    ):
        video = self.create_video(resolutions=[Video.Resolutions._240p])
        transcoder = get_transcoder(video.asset)
        transcoder.webhook_url = (
            f"localhost/api/v1/assets/{video.asset.uuid}/update_video_status/"
        )

        settings = transcoder.settings(preserve_existing_transcodings=True)

        self.assertEqual(
            settings["output_url"],
            f"wasabi://{self.organization.bucket_name}/transcoded/{video.asset.uuid}/new/",
        )


@override_settings(
    TRANSCODER_URL="http://transcoder.com",
)
class TestLumberjackJobTranscoder(
    TranscodingJobMixin, OrganizationMixin, AssetMixin, TestCase
):
    def setUp(self) -> None:
        self.organization = self.create_organization()

    def test_generate_settings_should_return_settings(self):
        transcoding_job = self.create_transcoding_job(
            input_url="s3://bucket/input.mp4?access_key=123&secret_key=xyz",
            output_path="s3://bucket/path/?access_key=abc&secret_key=cde",
            resolutions=[TranscodingJob.Resolutions._240p],
        )
        transcoder = LumberjackJobTranscoder(transcoding_job)
        actual_settings = transcoder.settings()

        expected = {
            "input_url": transcoding_job.input_url,
            "output_url": "s3://bucket/path/",
            "settings": {
                "outputs": [
                    {
                        "name": "240p_h264",
                        "audio": {"codec": "aac", "bitrate": 72000},
                        "video": {
                            "codec": "h264",
                            "width": 426,
                            "height": 240,
                            "preset": "faster",
                            "bitrate_buffer_size": 192000,
                            "max_video_bitrate": 192000,
                            "crf": 26,
                            "fps": 24,
                        },
                    }
                ],
                "format": "hls",
                "playlist_type": "vod",
                "segment_length": 10,
                "separate_server": True,
            },
            "webhook_url": f"{settings.SITE_URL}/api/v1/{self.organization.uuid}/transcoding_job"
            f"/{transcoding_job.uuid}/update_status/",
            "storage_parameters": {
                "output": {
                    "ACCESS_KEY_ID": "abc",
                    "SECRET_KEY": "cde",
                },
                "input": {"ACCESS_KEY_ID": "123", "SECRET_KEY": "xyz", "REGION": None},
            },
            "meta_data": json.dumps(
                {
                    "org_code": self.organization.uuid,
                    "org_name": self.organization.name,
                    "job_uuid": str(transcoding_job.uuid),
                }
            ),
        }
        self.assertEqual(expected, actual_settings)

    def test_generate_storage_parameters_should_return_storage_parameters(self):
        transcoding_job = self.create_transcoding_job(
            output_path="s3://bucket/path/?access_key=abc&secret_key=cde+123",
            resolutions=[TranscodingJob.Resolutions._240p],
        )
        transcoder = LumberjackJobTranscoder(transcoding_job)
        actual_settings = transcoder._get_output_storage_parameters()

        expected = {"ACCESS_KEY_ID": "abc", "SECRET_KEY": "cde+123"}

        self.assertEqual(expected, actual_settings)

    @responses.activate
    def test_create_transcoding_job_should_create_and_trigger_transcoding(self):
        responses.add(
            responses.POST,
            "http://transcoder.com/create/",
            json={"id": "bb35f9c1-322b-4fc4-940f-c8a771093615"},
        )
        transcoding_job = create_transcoding_job(
            "https://google.com",
            "s3://abc/folder/",
            [TranscodingJob.Resolutions._240p],
            None,
            self.organization,
        )

        transcoding_job.refresh_from_db()
        self.assertEqual(transcoding_job.input_url, "https://google.com")
        self.assertEqual(transcoding_job.output_path, "s3://abc/folder/")
        self.assertEqual(
            transcoding_job.resolutions, [TranscodingJob.Resolutions._240p]
        )
        self.assertEqual(
            str(transcoding_job.job_id), "bb35f9c1-322b-4fc4-940f-c8a771093615"
        )

    @responses.activate
    def test_stop_transcoding_job_should_call_cancel_api(self):
        responses.add(
            responses.POST,
            "http://transcoder.com/cancel/",
            json={"id": "bb35f9c1-322b-4fc4-940f-c8a771093615"},
        )
        transcoding_job = self.create_transcoding_job()
        LumberjackJobTranscoder(transcoding_job).stop()
