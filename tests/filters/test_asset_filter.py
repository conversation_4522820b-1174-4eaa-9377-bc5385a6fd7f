from datetime import date

from django.test import TestCase
from django_multitenant.utils import set_current_tenant

from app.filters.asset import Asset<PERSON>ilt<PERSON>, AssetUsageFilter
from app.models.asset import Asset
from app.models.video import Video
from tests.mixins import AssetMixin, BandwidthMixin, OrganizationMixin


class TestBandwidthUsageFilter(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_should_filter_asset_by_title_query(self):
        self.create_asset(title="video")
        self.create_assets(count=5)
        queryset = Asset.objects.all()
        filter = AssetFilter(data={"q": "video"}, queryset=queryset)
        queryset = filter.qs

        self.assertEqual(1, queryset.count())
        self.assertEqual(Asset.objects.get(title="video"), queryset.first())

    def test_should_filter_asset_by_uuid_query(self):
        self.create_assets(count=5)
        asset = Asset.objects.first()
        queryset = Asset.objects.all()
        filter = AssetFilter(data={"q": asset.uuid}, queryset=queryset)
        queryset = filter.qs

        self.assertEqual(1, queryset.count())
        self.assertEqual(Asset.objects.get(uuid=asset.uuid), queryset.first())

    def test_should_filter_assets_by_rank(self):
        self.create_assets(count=3)
        assets = Asset.objects.all().order_by("id")
        folder_1 = assets[0]
        folder_1.title = "video capture"
        folder_1.save()
        folder_2 = assets[1]
        folder_2.title = "vid"
        folder_2.save()
        folder_3 = assets[2]
        folder_3.title = "video"
        folder_3.save()
        queryset = Asset.objects.all()
        filter = AssetFilter(data={"q": "vid"}, queryset=queryset)
        queryset = filter.qs
        self.assertEqual(queryset.count(), 3)
        self.assertEqual(queryset.first(), folder_2)
        self.assertEqual(queryset.last(), folder_1)

    def test_should_filter_assets_by_created(self):
        self.create_assets(count=3)
        assets = Asset.objects.all().order_by("id")
        folder_1 = assets[0]
        folder_1.title = "video capture"
        folder_1.save()
        folder_2 = assets[1]
        folder_2.title = "video content"
        folder_2.save()
        folder_3 = assets[2]
        folder_3.title = "video folder"
        folder_3.save()
        queryset = Asset.objects.all()
        filter = AssetFilter(data={"q": "vid"}, queryset=queryset)
        queryset = filter.qs
        assets = assets.order_by("-created")
        self.assertEqual(3, queryset.count())
        self.assertEqual(folder_3, assets[0])
        self.assertEqual(folder_1, assets[2])

    def test_should_return_empty_queryset_when_no_match(self):
        self.create_assets(count=3)
        queryset = Asset.objects.all()
        filter = AssetFilter(data={"q": "foo"}, queryset=queryset)
        queryset = filter.qs
        self.assertEqual(queryset.count(), 0)

    def test_should_filter_assets_by_state(self):
        self.create_video(status=Video.Status.COMPLETED)
        self.create_video(status=Video.Status.UPLOADING)
        self.create_video(status=Video.Status.ERROR)
        self.create_video(status=Video.Status.COMPLETED)

        queryset = Asset.objects.all()
        filter = AssetFilter(
            data={"state": [Video.Status.COMPLETED, Video.Status.UPLOADING]},
            queryset=queryset,
        )
        queryset = filter.qs

        self.assertEqual(queryset.count(), 3)
        self.assertTrue(
            all(
                asset.video.status in [Video.Status.COMPLETED, Video.Status.UPLOADING]
                for asset in queryset
            )
        )

    def test_should_filter_by_parent_uuid(self):
        self.create_assets(2)
        folder_1 = self.create_folder()
        folder_1_child_1 = self.create_folder(parent=folder_1)
        self.create_folder()

        filter_ = AssetFilter(
            data={"parent": folder_1.uuid}, queryset=Asset.objects.all()
        )

        self.assertEqual(1, filter_.qs.count())
        self.assertTrue(folder_1_child_1 in filter_.qs)


class TestAssetUsageFilter(OrganizationMixin, BandwidthMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

        self.create_daily_bandwidth_usage(date=date(2024, 3, 15))
        self.create_daily_bandwidth_usage(date=date(2024, 3, 25))
        self.create_daily_bandwidth_usage(date=date(2024, 3, 5))
        self.create_daily_bandwidth_usage(date=date(2023, 2, 25))

        self.create_monthly_bandwidth_usage(date=date(2023, 1, 1))
        self.create_monthly_bandwidth_usage(date=date(2023, 2, 1))

    def test_asset_usage_filter_by_month(self):
        filter_data = {"month": 3}

        asset_filter = AssetUsageFilter(data=filter_data)

        self.assertTrue(asset_filter.is_valid())
        self.assertEqual(len(asset_filter.qs), 3)

    def test_asset_usage_filter_by_year(self):
        filter_data = {"year": 2024}

        asset_filter = AssetUsageFilter(data=filter_data)

        self.assertTrue(asset_filter.is_valid())
        self.assertEqual(len(asset_filter.qs), 3)

    def test_asset_usage_filter_by_timeframe(self):
        filter_data = {"time_frame": "monthly"}

        asset_filter = AssetUsageFilter(data=filter_data)

        self.assertTrue(asset_filter.is_valid())
        self.assertEqual(len(asset_filter.qs), 2)

    def test_asset_usage_filter_by_date(self):
        filter_data = {"date": date(2024, 3, 25)}

        asset_filter = AssetUsageFilter(data=filter_data)

        self.assertTrue(asset_filter.is_valid())
        self.assertEqual(len(asset_filter.qs), 1)

    def test_filter_with_no_value(self):
        filter = AssetUsageFilter()
        queryset = filter.qs

        self.assertEqual(queryset.count(), 6)
