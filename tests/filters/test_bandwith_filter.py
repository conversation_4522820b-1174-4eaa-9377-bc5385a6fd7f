from datetime import date

from django.test import TestCase
from django_multitenant.utils import set_current_tenant
from freezegun import freeze_time

from app.filters import AssetUsageFilter
from tests.mixins import BandwidthMixin, OrganizationMixin


@freeze_time("2023-08-01T11:38:00.229221+00:00")
class TestAssetUsageFilter(OrganizationMixin, BandwidthMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_bandwidth_filter_month_returns_usage_only_for_filtered_month(self):
        self.create_daily_bandwidth_usage(date=date(2023, 7, 15))
        self.create_daily_bandwidth_usage(date=date(2023, 7, 20))
        last_month_bandwidth = self.create_daily_bandwidth_usage(date=date(2023, 1, 1))

        filter = AssetUsageFilter(data={"month": (date(2023, 7, 1))})

        self.assertEqual(filter.qs.count(), 2)
        self.assertNotIn(last_month_bandwidth, filter.qs)

    def test_bandwidth_filter_ordering_shows_latest_usage_first(self):
        old_bandwidth = self.create_daily_bandwidth_usage(date=date(2023, 3, 15))
        latest_bandwidth = self.create_daily_bandwidth_usage(date=date(2023, 3, 20))
        filter = AssetUsageFilter(
            data={"ordering": "-date", "month": (date(2023, 3, 1))}
        )

        self.assertEqual(filter.qs.first(), latest_bandwidth)
        self.assertEqual(filter.qs.last(), old_bandwidth)

    def test_bandwidth_filter_ordering_shows_oldest_usage_first(self):
        old_bandwidth = self.create_daily_bandwidth_usage(date=date(2023, 3, 15))
        latest_bandwidth = self.create_daily_bandwidth_usage(date=date(2023, 3, 20))
        filter = AssetUsageFilter(
            data={"ordering": "date", "month": (date(2023, 3, 1))}
        )

        self.assertEqual(filter.qs.first(), old_bandwidth)
        self.assertEqual(filter.qs.last(), latest_bandwidth)
