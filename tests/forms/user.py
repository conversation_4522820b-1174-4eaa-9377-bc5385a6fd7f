import pytz
from django.test import TestCase

from app.forms.user import UserForm


class TestUserForm(TestCase):
    def test_form_timezone_choices(self):
        form = UserForm()

        expected_choices = [(t, t) for t in pytz.common_timezones]
        actual_choices = form.fields["timezone"].choices
        self.assertEqual(actual_choices, expected_choices)

    def test_form_valid_data(self):
        form = UserForm(data={"timezone": "America/New_York"})

        self.assertTrue(form.is_valid())

    def test_form_invalid_data(self):
        form = UserForm(data={})

        self.assertFalse(form.is_valid())
        self.assertIn("timezone", form.errors)
