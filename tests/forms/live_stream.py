from datetime import timedelta

from django.utils.timezone import now

from app.forms.live_streams import LiveStreamScheduleForm
from tests import TestCase


class TestLiveStreamScheduleForm(TestCase):
    def test_start_datetime_validation_past(self):
        past_start_datetime = now() - timedelta(minutes=2)
        form_data = {
            "title": "Test Stream",
            "start": past_start_datetime,
        }
        form = LiveStreamScheduleForm(data=form_data)

        self.assertFalse(form.is_valid())

        self.assertEqual(
            form.errors["start"], ["Start time must be greater than current time"]
        )

    def test_start_datetime_validation_future(self):
        future_start_datetime = now() + timedelta(minutes=1)
        form_data = {
            "title": "Test Stream",
            "start": future_start_datetime,
        }
        form = LiveStreamScheduleForm(data=form_data)

        self.assertTrue(form.is_valid())

    def test_start_datetime_validation_empty(self):
        form_data = {
            "title": "Test Stream",
            "start": "",
        }
        form = LiveStreamScheduleForm(data=form_data)

        self.assertFalse(form.is_valid())
