from django.core.exceptions import ValidationError

from app.forms.video_embedding import VideoEmbeddingSettingsForm, validate_domain_names
from tests import TestCase


class TestVideoEmbeddingSettingsForm(TestCase):
    def form_data(self):
        return {"allowed_domains_for_embedding": ["stackoverflow.com"]}

    def test_clean_allowed_domains_for_embedding_valid_domains(self):
        form_data = self.form_data()
        form = VideoEmbeddingSettingsForm(data=form_data)
        self.assertTrue(form.is_valid())
        self.assertEqual(
            form.cleaned_data["allowed_domains_for_embedding"],
            form_data["allowed_domains_for_embedding"],
        )

    def test_should_raise_error_on_invalid_domain_name(self):
        form_data = self.form_data()
        form_data.update({"allowed_domains_for_embedding": ["hello world//"]})
        form = VideoEmbeddingSettingsForm(data=form_data)

        self.assertFalse(form.is_valid())
        self.assertEqual(
            "hello world// is not a valid host name",
            form.errors["allowed_domains_for_embedding"][0],
        )


class TestDomainValidation(TestCase):
    def test_valid_domain_names(self):
        valid_domain_names = [
            "example.com",
            "subdomain.example.com",
            "my-domain.net",
            "123domain.com",
        ]
        try:
            validate_domain_names(valid_domain_names)
        except ValidationError:
            self.fail("Unexpected ValidationError raised for valid domain names")

    def test_invalid_domain_names(self):
        invalid_domain_names = [
            "httpscatking.testpress.in" "example!.com",
            "subdomain.example-.com",
            "too-long-domain-name-abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyz.example.com",
            "missing-tld",
        ]
        for domain_name in invalid_domain_names:
            with self.assertRaises(ValidationError):
                validate_domain_names(domain_name)
