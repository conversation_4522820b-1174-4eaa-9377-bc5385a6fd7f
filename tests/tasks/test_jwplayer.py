from unittest import mock

from django_multitenant.utils import set_current_tenant

from app.domain.jwplayer_to_wasabi_migrator import Migrator
from app.models import ImportedVideo
from app.utils.jwplayer import Video
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestImplementation(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @mock.patch(
        "app.domain.jwplayer_to_wasabi_migrator.Jwplayer.get_video",
        return_value=Video(
            id=1,
            name="sample_video",
            link="fake_link",
            duration=12,
            thumbnail="sample.jpg",
            resolutions=[],
            data={},
        ),
    )
    @mock.patch(
        "app.domain.jwplayer_to_wasabi_migrator.MigrateImportedVideoTask.apply_async"
    )
    def test_migrate_video_should_create_import_video_object(
        self, mock_apply_async, mock_JwplayerInterface
    ):
        jwp_migrator = Migrator(
            site_id="fake_site_id",
            api_key="fake_api_key",
            org_id=self.organization.uuid,
        )
        jwp_migrator.migrate_video(video_id="fake_video_id")
        imported_video = ImportedVideo.objects.last()

        self.assertEqual(imported_video.name, "sample_video")

    @mock.patch(
        "app.domain.jwplayer_to_wasabi_migrator.Jwplayer.get_video",
        return_value=Video(
            id=1,
            name="fake_video",
            link="fake_link",
            duration=12,
            thumbnail="sample.jpg",
            resolutions=[],
            data={},
        ),
    )
    @mock.patch(
        "app.domain.jwplayer_to_wasabi_migrator.MigrateImportedVideoTask.apply_async"
    )
    def test_migrate_video_should_call_get_video_details(
        self, mock_apply_async, mock_get_video_details
    ):
        jwp_migrator = Migrator(
            site_id="fake_site_id",
            api_key="fake_api_key",
            org_id=self.organization.uuid,
        )
        jwp_migrator.migrate_video(video_id="fake_video_id")

        mock_get_video_details.assert_called_once_with("fake_video_id")

    @mock.patch(
        "app.domain.jwplayer_to_wasabi_migrator.Jwplayer.get_video",
        return_value=Video(
            id=1,
            name="fake_video",
            link="fake_link",
            duration=12,
            thumbnail="sample.jpg",
            resolutions=[],
            data={},
        ),
    )
    @mock.patch(
        "app.domain.jwplayer_to_wasabi_migrator.MigrateImportedVideoTask.apply_async"
    )
    def test_migrate_video_should_call_migration_task(
        self, mock_apply_async, mock_JwplayerInterface
    ):
        jwp_migrator = Migrator(
            site_id="fake_site_id",
            api_key="fake_api_key",
            org_id=self.organization.uuid,
        )
        jwp_migrator.migrate_video(video_id="fake_video_id")

        mock_apply_async.assert_called_once()
