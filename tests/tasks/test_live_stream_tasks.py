from unittest import mock

from django.conf import settings
from django.utils import timezone
from django_multitenant.utils import set_current_tenant

from app.api.v1.views.live_stream import LiveStreamOnPublishCallbackView
from app.models import LiveStream, ScheduledTaskReference
from app.models.live_stream import LiveStreamEvent
from app.tasks.live_stream import (
    CheckLiveServerDeletionTask,
    StopDisconnectedLiveStreamTask,
)
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin, ScheduledTaskReferenceMixin


class TestStopDisconnectedLiveStreamTask(
    OrganizationMixin, AssetMixin, ScheduledTaskReferenceMixin, TestCase
):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @mock.patch("app.domain.live_stream.stop_live_stream")
    def test_stop_live_should_not_be_called_if_livestream_is_still_streaming(
        self, mock_stop_live_stream
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        StopDisconnectedLiveStreamTask(
            live_stream_id=live_stream.id, organization_uuid=self.organization.uuid
        )

        self.assertFalse(mock_stop_live_stream.called)

    @mock.patch("app.domain.live_stream.stop_live_stream")
    def test_stop_live_should_be_called_if_livestream_is_disconnected(
        self, mock_stop_live_stream
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.DISCONNECTED,
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        StopDisconnectedLiveStreamTask(
            live_stream_id=live_stream.id, organization_uuid=self.organization.uuid
        )

        mock_stop_live_stream.assert_called()

    @mock.patch("app.domain.live_stream.stop_live_stream")
    def test_stop_live_should_be_called_if_livestream_is_not_started(
        self, mock_stop_live_stream
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.NOT_STARTED,
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        StopDisconnectedLiveStreamTask(
            live_stream_id=live_stream.id, organization_uuid=self.organization.uuid
        )

        mock_stop_live_stream.assert_called()

    @mock.patch("app.domain.live_stream.stop_live_stream")
    def test_termination_cause_should_be_scheduled_termination_if_task_stops_streaming(
        self, mock_stop_live_stream
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.DISCONNECTED,
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        StopDisconnectedLiveStreamTask(
            live_stream_id=live_stream.id, organization_uuid=self.organization.uuid
        )
        live_stream.refresh_from_db()

        self.assertEqual(
            live_stream.get_termination_cause_display(), "Scheduled Termination"
        )

    @mock.patch("app.domain.live_stream.stop_live_stream")
    def test_stop_livestream_disconnected_task_should_update_events(
        self, mock_stop_live_stream
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.NOT_STARTED,
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        StopDisconnectedLiveStreamTask(
            live_stream_id=live_stream.id, organization_uuid=self.organization.uuid
        )
        events = LiveStreamEvent.objects.filter(
            live_stream=live_stream,
            organization=self.organization,
        )

        event_messages = [
            event.data["message"]
            for event in events
            if event.data and "message" in event.data
        ]

        self.assertIn("StopDisconnectedLiveStreamTask completed", event_messages)

    @mock.patch("app.domain.live_stream.stop_live_stream")
    def test_scheduled_task_should_be_revoked_when_live_stream_starts(
        self, mock_stop_live_stream
    ):
        task = self.create_scheduled_task_reference()
        live_stream = self.create_livestream(
            status=LiveStream.Status.NOT_STARTED,
            server_id="1234",
            server_termination_task_id=task.task_id,
        )
        request = self.get_request(
            "/",
            method="POST",
            data={
                "server_id": "1234",
                "ip_address": "*********",
                "stream_key": live_stream.stream_key,
            },
        )
        LiveStreamOnPublishCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        deleted_scheduled_task = ScheduledTaskReference.objects.deleted_only().first()
        self.assertEqual(
            deleted_scheduled_task.task_id, live_stream.server_termination_task_id
        )


class TestCheckLiveServerDeletionTask(
    OrganizationMixin, AssetMixin, ScheduledTaskReferenceMixin, TestCase
):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.live_stream = self.create_livestream()
        set_current_tenant(self.organization)

    @mock.patch("app.tasks.send_mail.send_email_task.apply_async")
    def test_email_alert_should_be_sent_if_live_stream_not_destroyed(
        self, mock_send_email
    ):
        self.live_stream.server_status = LiveStream.ServerStatus.DELETING
        self.live_stream.start = timezone.now()
        self.live_stream.end = None
        self.live_stream.save()

        CheckLiveServerDeletionTask.run(
            live_stream_id=self.live_stream.id,
            organization_uuid=self.organization.uuid,
        )

        mock_send_email.assert_called_once_with(
            kwargs={
                "subject": mock.ANY,
                "message_text": mock.ANY,
                "message_html": mock.ANY,
                "from_email": settings.DEFAULT_FROM_EMAIL,
                "to_emails": settings.DEVELOPER_EMAILS,
            }
        )

    @mock.patch("app.tasks.send_mail.send_email_task.apply_async")
    def test_email_should_not_be_sent_if_server_is_destroyed(
        self, mock_send_email_task
    ):
        self.live_stream.server_status = LiveStream.ServerStatus.DESTROYED
        self.live_stream.start = timezone.now()
        self.live_stream.save()

        CheckLiveServerDeletionTask.run(
            live_stream_id=self.live_stream.id,
            organization_uuid=self.organization.uuid,
        )

        mock_send_email_task.assert_not_called()
