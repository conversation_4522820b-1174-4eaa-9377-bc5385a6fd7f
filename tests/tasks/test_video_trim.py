import uuid
from datetime import timedel<PERSON>
from unittest.mock import MagicMock, patch

from django_multitenant.utils import set_current_tenant

from app.domain.video_trim.core import start_video_trim_background_task
from app.models.video_trim import OutputType, TrimStatus, VideoOutput, VideoTrim
from app.tasks.video_trim import VideoTrimRevertTask, VideoTrimTask
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestVideoTrimTask(TestCase, OrganizationMixin, AssetMixin):
    def setUp(self):
        self.organization = self.create_organization()
        self.organization.cdn_url = "https://cdn.example.com/"
        self.organization.bucket_name = "test-bucket"
        set_current_tenant(self.organization)
        self.video = self.create_video()
        self.user = self.create_user()

    def create_task(self):
        task = type(VideoTrimTask)()
        task._set_organization(self.organization.uuid)
        return task

    def test_video_trim_task_initialization(self):
        task = self.create_task()
        self.assertIsNotNone(task)

    @patch("app.domain.video_trim.manifest_domain.hls.trim_hls_manifests")
    @patch("app.domain.video_trim.core.mark_trim_completed")
    @patch("app.domain.video_trim.core.mark_trim_status")
    def test_video_trim_task_success_hls(
        self, mock_mark_status, mock_mark_completed, mock_trim_hls
    ):
        trim_job = VideoTrim.objects.create(
            video=self.video,
            start_time=300,
            end_time=1800,
            created_by=self.user,
            status=TrimStatus.PENDING,
            organization=self.organization,
        )

        VideoOutput.objects.create(
            video=self.video,
            output_type=OutputType.HLS,
            codec="h264",
            duration=3600,
            url="https://example.com/video.m3u8",
            organization=self.organization,
        )

        mock_trim_hls.return_value = {
            "master": "https://cdn.example.com/trimmed.m3u8",
            "trim_id": "abc123",
        }

        task = self.create_task()
        task.do_run(
            trim_job_id=trim_job.id, organization_uuid=str(self.organization.uuid)
        )

        mock_mark_status.assert_called_with(trim_job, TrimStatus.PROCESSING)
        mock_trim_hls.assert_called_once_with(self.video, 300, 1800)
        mock_mark_completed.assert_called_once()

    @patch("app.domain.video_trim.manifest_domain.dash.trim_dash_manifests")
    @patch("app.domain.video_trim.core.mark_trim_completed")
    @patch("app.domain.video_trim.core.mark_trim_status")
    def test_video_trim_task_success_dash(
        self, mock_mark_status, mock_mark_completed, mock_trim_dash
    ):
        self.video.content_protection_type = self.video.ContentProtectionType.DRM
        self.video.save()

        trim_job = VideoTrim.objects.create(
            video=self.video,
            start_time=300,
            end_time=1800,
            created_by=self.user,
            status=TrimStatus.PENDING,
            organization=self.organization,
        )

        VideoOutput.objects.create(
            video=self.video,
            output_type=OutputType.DASH,
            codec="h264",
            duration=3600,
            url="https://example.com/video.mpd",
            organization=self.organization,
        )

        mock_trim_dash.return_value = {
            "master": "https://cdn.example.com/trimmed.mpd",
            "trim_id": "abc123",
        }

        task = self.create_task()
        task.do_run(
            trim_job_id=trim_job.id, organization_uuid=str(self.organization.uuid)
        )

        mock_mark_status.assert_called_with(trim_job, TrimStatus.PROCESSING)
        mock_trim_dash.assert_called_once_with(self.video, 300, 1800)
        mock_mark_completed.assert_called_once()

    @patch("app.domain.video_trim.manifest_domain.hls.trim_hls_manifests")
    @patch("app.tasks.video_trim.sentry_sdk")
    def test_video_trim_task_failure(self, mock_sentry, mock_trim_hls):
        trim_job = VideoTrim.objects.create(
            video=self.video,
            start_time=300,
            end_time=1800,
            created_by=self.user,
            status=TrimStatus.PENDING,
            organization=self.organization,
        )

        mock_trim_hls.side_effect = Exception("Test error")

        task = self.create_task()
        task.do_run(
            trim_job_id=trim_job.id, organization_uuid=str(self.organization.uuid)
        )

        mock_sentry.capture_exception.assert_called_once()

    def test_get_available_codecs(self):
        VideoOutput.objects.create(
            video=self.video,
            output_type=OutputType.HLS,
            codec="h264",
            duration=3600,
            url="https://example.com/h264.m3u8",
            organization=self.organization,
        )
        VideoOutput.objects.create(
            video=self.video,
            output_type=OutputType.DASH,
            codec="h265",
            duration=3600,
            url="https://example.com/h265.mpd",
            organization=self.organization,
        )

        task = self.create_task()
        codecs = task.get_available_codecs(self.video)

        self.assertIn("h264", codecs)
        self.assertIn("h265", codecs)

    def test_get_available_codecs_default(self):
        task = self.create_task()
        codecs = task.get_available_codecs(self.video)

        self.assertEqual(codecs, ["h264"])


class TestVideoTrimRevertTask(TestCase, OrganizationMixin, AssetMixin):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.video = self.create_video()
        self.user = self.create_user()

    def create_revert_task(self):
        task = type(VideoTrimRevertTask)()
        task._set_organization(self.organization.uuid)
        return task

    @patch("app.domain.video_trim.core.revert_to_original")
    def test_video_trim_revert_task_success(self, mock_revert):
        task = self.create_revert_task()
        task.do_run(
            video_id=self.video.id, organization_uuid=str(self.organization.uuid)
        )

        mock_revert.assert_called_once_with(self.video)

    @patch("app.tasks.video_trim.sentry_sdk")
    def test_video_trim_revert_task_failure(self, mock_sentry):
        task = self.create_revert_task()
        task.do_run(video_id=99999, organization_uuid=str(self.organization.uuid))

        mock_sentry.capture_exception.assert_called_once()


class TestVideoTrimBackgroundTaskIntegration(TestCase, OrganizationMixin, AssetMixin):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.video = self.create_video()
        self.video.duration = timedelta(seconds=3600)
        self.video.save()
        self.user = self.create_user()

    @patch("app.tasks.video_trim.VideoTrimTask")
    def test_start_video_trim_background_task(self, mock_task_class):
        mock_task = MagicMock()
        mock_task.apply_async.return_value.id = str(uuid.uuid4())
        mock_task_class.apply_async = mock_task.apply_async

        trim_job = start_video_trim_background_task(self.video, 300, 1800, self.user)

        self.assertEqual(trim_job.video, self.video)
        self.assertEqual(trim_job.start_time, 300)
        self.assertEqual(trim_job.end_time, 1800)
        self.assertEqual(trim_job.created_by, self.user)
        self.assertIsNotNone(trim_job.background_task_id)
        mock_task.apply_async.assert_called_once_with(
            kwargs={
                "trim_job_id": trim_job.id,
                "organization_uuid": str(self.video.organization.uuid),
            }
        )
