from unittest import mock

from django_multitenant.utils import set_current_tenant

from app.tasks.subtitle import (
    GenerateSubtitleTask,
    UpdateSubtitlesGenerationMinutesTask,
)
from tests import TestCase
from tests.mixins import OrganizationMixin
from tests.mixins.asset import TrackMixin


class TestGenerateSubtitleTask(TrackMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @mock.patch("app.domain.subtitle.store_subtitle_generation_details")
    @mock.patch("app.tasks.subtitle.AWSSubtitleServer.create_server")
    @mock.patch(
        "app.tasks.subtitle.GenerateSubtitleTask.download_video_and_extract_audio"
    )
    @mock.patch("app.tasks.subtitle.GenerateSubtitleTask.upload_audio_to_storage")
    @mock.patch("app.tasks.subtitle.GenerateSubtitleTask.generate_subtitle_data")
    @mock.patch("app.domain.subtitle.create_subtitle_track")
    def test_task_should_create_track_and_start_server(
        self,
        mock_create_subtitle_track,
        mock_generate_subtitle_data,
        mock_upload_audio_to_storage,
        mock_download_video_and_extract_audio,
        mock_create_create_subtitle_server,
        mock_store_subtitle_details,
    ):
        track = self.create_subtitle(subtitle_data={"duration": 12})
        mock_create_subtitle_track.return_value = (track, True)
        GenerateSubtitleTask(
            asset_id=track.video.asset.uuid, organization_uuid=self.organization.uuid
        )

        mock_create_subtitle_track.assert_called()
        mock_create_create_subtitle_server.assert_called()
        mock_store_subtitle_details.assert_called()


class TestUpdateSubtitleGenerationMinutesTask(OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @mock.patch("app.domain.subtitle.update_subtitles_generation_minutes")
    def test_update_storage_usage_task_runs_update_storage_usage(
        self, mock_update_subtitle_generated_seconds
    ):
        UpdateSubtitlesGenerationMinutesTask(organization_uuid=self.organization.uuid)
        mock_update_subtitle_generated_seconds.assert_called_once()
