from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.models import Asset
from app.tasks.purge_asset import restore_asset_task, soft_delete_asset_task
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestFolderDeletionAndRestoreTask(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def tearDown(self):
        unset_current_tenant()
        super().tearDown()

    def test_delete_empty_folder(self):
        folder = self.create_asset(type=Asset.Type.FOLDER, title="Empty Folder")

        soft_delete_asset_task(folder.uuid, self.organization.uuid)

        folder.refresh_from_db()
        self.assertIsNotNone(folder.deleted)
        self.assertFalse(folder.deleted_by_cascade)

    def test_should_delete_folder_and_its_descendants(self):
        root_folder = self.create_asset(type=Asset.Type.FOLDER, title="Root Folder")
        sub_folder = self.create_asset(
            type=Asset.Type.FOLDER, title="Sub Folder", parent=root_folder
        )
        sub_sub_folder = self.create_asset(
            type=Asset.Type.FOLDER, title="Sub Sub Folder", parent=sub_folder
        )

        child1 = self.create_asset(
            type=Asset.Type.VIDEO, title="Video in Root", parent=root_folder
        )
        child2 = self.create_asset(
            type=Asset.Type.VIDEO, title="Video in Sub", parent=sub_folder
        )
        child3 = self.create_asset(
            type=Asset.Type.VIDEO, title="Video in Sub Sub", parent=sub_sub_folder
        )

        soft_delete_asset_task(root_folder.uuid, self.organization.uuid)

        assets = [root_folder, sub_folder, sub_sub_folder, child1, child2, child3]
        for asset in assets:
            asset.refresh_from_db()

        self.assertIsNotNone(root_folder.deleted)
        self.assertFalse(root_folder.deleted_by_cascade)

        descendants = [sub_folder, sub_sub_folder, child1, child2, child3]
        for asset in descendants:
            self.assertIsNotNone(asset.deleted)
            self.assertTrue(asset.deleted_by_cascade)
        self.assertFalse(Asset.objects.filter(uuid=root_folder.uuid).exists())
        self.assertFalse(Asset.objects.filter(uuid=sub_folder.uuid).exists())
        self.assertTrue(Asset.deleted_objects.filter(uuid=root_folder.uuid).exists())
        self.assertTrue(Asset.deleted_objects.filter(uuid=sub_folder.uuid).exists())

    def test_should_restore_folder_and_its_descendants(self):
        root_folder = self.create_asset(type=Asset.Type.FOLDER, title="Root Folder")
        sub_folder = self.create_asset(
            type=Asset.Type.FOLDER, title="Sub Folder", parent=root_folder
        )
        sub_sub_folder = self.create_asset(
            type=Asset.Type.FOLDER, title="Sub Sub Folder", parent=sub_folder
        )
        child1 = self.create_asset(
            type=Asset.Type.VIDEO, title="Video in Root", parent=root_folder
        )
        child2 = self.create_asset(
            type=Asset.Type.VIDEO, title="Video in Sub", parent=sub_folder
        )
        child3 = self.create_asset(
            type=Asset.Type.VIDEO, title="Video in Sub Sub", parent=sub_sub_folder
        )

        soft_delete_asset_task(root_folder.uuid, self.organization.uuid)
        restore_asset_task(root_folder.uuid, self.organization.uuid)

        assets = [root_folder, sub_folder, sub_sub_folder, child1, child2, child3]
        for asset in assets:
            asset.refresh_from_db()

        for asset in assets:
            self.assertIsNone(asset.deleted)
            self.assertFalse(asset.deleted_by_cascade)

    def test_restore_respects_mptt_tree_boundaries(self):
        folder1 = self.create_asset(type=Asset.Type.FOLDER, title="Folder 1")
        child1 = self.create_asset(
            type=Asset.Type.VIDEO, title="Video in Folder 1", parent=folder1
        )

        folder2 = self.create_asset(type=Asset.Type.FOLDER, title="Folder 2")
        child2 = self.create_asset(
            type=Asset.Type.VIDEO, title="Video in Folder 2", parent=folder2
        )

        soft_delete_asset_task(folder1.uuid, self.organization.uuid)
        soft_delete_asset_task(folder2.uuid, self.organization.uuid)
        restore_asset_task(folder1.uuid, self.organization.uuid)

        folder1.refresh_from_db()
        child1.refresh_from_db()
        folder2.refresh_from_db()
        child2.refresh_from_db()

        self.assertIsNone(folder1.deleted)
        self.assertIsNone(child1.deleted)

        self.assertIsNotNone(folder2.deleted)
        self.assertIsNotNone(child2.deleted)

    def test_delete_and_restore_folder_query_count(self):
        root_folder = self.create_asset(
            type=Asset.Type.FOLDER, title="Large Root Folder"
        )

        for i in range(30):
            subfolder = self.create_asset(
                type=Asset.Type.FOLDER, title=f"Subfolder {i+1}", parent=root_folder
            )

            for j in range(10):
                self.create_asset(
                    type=Asset.Type.VIDEO,
                    title=f"Video {j+1} in Subfolder {i+1}",
                    parent=subfolder,
                )

        with self.assertNumQueries(7):
            soft_delete_asset_task(root_folder.uuid, self.organization.uuid)

        with self.assertNumQueries(7):
            restore_asset_task(root_folder.uuid, self.organization.uuid)

    def test_delete_and_restore_asset_query_count(self):
        asset = self.create_asset(type=Asset.Type.VIDEO, title="Asset")

        with self.assertNumQueries(5):
            soft_delete_asset_task(asset.uuid, self.organization.uuid)

        with self.assertNumQueries(5):
            restore_asset_task(asset.uuid, self.organization.uuid)
