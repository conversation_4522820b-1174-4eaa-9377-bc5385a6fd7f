from unittest import mock

from django.urls import resolve, reverse
from django_multitenant.utils import set_current_tenant

from tests import TestCase
from tests.mixins import OrganizationMixin
from tests.mixins.asset import AssetMixin


class TestUpdateAssetSizeTask(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @mock.patch("app.api.v1.views.video.UpdateAssetSizeTask")
    def test_update_asset_size_task_called_when_video_status_becomes_completed(
        self, mocked_task
    ):
        video = self.create_video()
        request = self.get_request(
            "/",
            method="POST",
            data={"status": "completed"},
        )
        view = resolve(
            reverse(
                "api:update-video-status",
                kwargs={
                    "asset_id": video.asset.uuid,
                    "organization_id": self.organization.uuid,
                },
            )
        )
        view.func(request, self.organization.uuid, video.asset.uuid)

        mocked_task.apply_async.assert_called_with(
            kwargs={
                "asset_id": video.asset.uuid,
                "organization_uuid": self.organization.uuid,
            }
        )
