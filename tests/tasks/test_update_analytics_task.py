from unittest import mock

from django_multitenant.utils import set_current_tenant

from app.tasks.analytics import StoreVideoAnalyticsTask
from tests import TestCase
from tests.mixins import OrganizationMixin


class TestStoreVideoAnalyticsTask(OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @mock.patch("app.tasks.analytics.store_video_analytics_to_db")
    def test_update_store_analytics_task_runs_store_video_analytics_to_db(
        self, mock_store_analytics
    ):
        test_date = "2023-12-07"

        task = StoreVideoAnalyticsTask
        task.run(
            date=test_date, organization_uuid=self.organization.uuid
        )  # type: ignore
        mock_store_analytics.assert_called_once()
