import datetime
from unittest import mock

from freezegun import freeze_time

from app.tasks.live_stream import UpdateLiveStreamUsageTask
from tests import TestCase
from tests.mixins import OrganizationMixin


class TestUpdateLiveStreamUsageTask(OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()

    @mock.patch("app.tasks.live_stream.update_daily_live_stream_usage")
    @mock.patch("app.tasks.live_stream.update_monthly_live_stream_usage")
    def test_update_live_usage_task_should_update_day_and_monthly_usage(
        self,
        mock_update_day_usage,
        mock_update_monthly_usage,
    ):
        task = UpdateLiveStreamUsageTask
        task.run()  # type: ignore
        mock_update_day_usage.assert_called()
        mock_update_monthly_usage.assert_called()

    @freeze_time("2023-03-07")
    @mock.patch("app.tasks.live_stream.update_daily_live_stream_usage")
    @mock.patch("app.tasks.live_stream.update_monthly_live_stream_usage")
    def test_update_live_usage_task_should_update_yesterday_usage(
        self,
        mock_update_day_usage,
        mock_update_monthly_usage,
    ):
        task = UpdateLiveStreamUsageTask
        task.run()  # type: ignore
        yesterday_date = datetime.date.today() - datetime.timedelta(days=1)
        mock_update_day_usage.assert_called_with(self.organization, yesterday_date)
        mock_update_monthly_usage.assert_called_with(self.organization, yesterday_date)
