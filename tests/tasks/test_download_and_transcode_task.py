import uuid
from unittest.mock import patch

import responses
from django.conf import settings
from django_multitenant.utils import set_current_tenant

from app.models.video import Video
from app.tasks import DownloadAndTranscodeVideoTask
from app.utils.wasabi import FileCopyError, get_wasabi_config
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestDownloadAndTranscodeVideoTask(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.video = self.create_video(
            resolutions=[Video.Resolutions._240p, Video.Resolutions._720p]
        )
        self.video.inputs.update(url="http://example.com/video.mp4")

    @responses.activate
    @patch("app.tasks.video.upload_from_url_to_wasabi")
    @patch("app.models.video.generate_presigned_url")
    def test_transcoding_started_with_downloaded_video(
        self, mocked_generate_presigned_url, mock_copy_files_to_wasabi
    ):
        mocked_generate_presigned_url.return_value = (
            "https://example.com/video.mp4?sig=1234&expire=23495"
        )
        job_id = str(uuid.uuid4())
        responses.add(
            responses.POST,
            f"{settings.TRANSCODER_URL}/create/",
            status=200,
            json={"id": job_id},
        )
        DownloadAndTranscodeVideoTask(
            video_id=self.video.id, organization_uuid=self.organization.uuid
        )
        config = get_wasabi_config(self.organization)
        mock_copy_files_to_wasabi.assert_called_with(
            "http://example.com/video.mp4",
            f"/{self.organization.bucket_name}/private/{self.video.asset.uuid}.mp4",
            config=config,
            make_public=True,
        )
        self.video.refresh_from_db()
        self.assertEqual(job_id, str(self.video.job_id))

    @patch("app.tasks.video.upload_from_url_to_wasabi")
    def test_video_status_changes_to_error_on_download_failure(
        self, mock_copy_files_to_wasabi
    ):
        mock_copy_files_to_wasabi.side_effect = FileCopyError()
        DownloadAndTranscodeVideoTask(
            video_id=self.video.id, organization_uuid=self.organization.uuid
        )

        self.video.refresh_from_db()
        self.assertEqual(
            Video.Status.INPUT_READ_ERROR,
            self.video.status,
        )

    @patch("app.tasks.video.upload_from_url_to_wasabi")
    def test_video_status_changes_to_uploaded_on_download_success(
        self, mock_copy_files_to_wasabi
    ):
        video = self.create_video()
        video.inputs.update(url="http://example.com/video.mp4")
        DownloadAndTranscodeVideoTask(
            video_id=video.id, organization_uuid=video.organization.uuid
        )

        video.refresh_from_db()
        self.assertEqual(
            Video.Status.UPLOADED,
            video.status,
        )

    @patch("app.models.video.generate_presigned_url")
    @patch("app.tasks.video.start_transcoding")
    @patch("app.tasks.video.upload_from_url_to_wasabi")
    @patch("app.domain.subtitle.generate_subtitle")
    @patch("app.models.video.Video.should_generate_subtitle")
    def test_should_generate_subtitle_for_video(
        self,
        mock_should_generate_subtitle,
        mock_generate_subtitle,
        mock_upload_to_wasabi,
        mock_transcoding,
        mock_presigned_url,
    ):
        video = self.create_video()
        video.inputs.update(url="http://example.com/video.mp4")
        mock_should_generate_subtitle.return_value = True
        DownloadAndTranscodeVideoTask(
            video_id=video.id, organization_uuid=video.organization.uuid
        )

        video.refresh_from_db()
        mock_generate_subtitle.assert_called()
