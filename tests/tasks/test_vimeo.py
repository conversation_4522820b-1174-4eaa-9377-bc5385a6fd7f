from unittest import mock

import responses
from django_multitenant.utils import set_current_tenant

from app.domain.vimeo_to_wasabi_migrator import Migrator
from app.models import Video, VideoInput
from app.models.videoimport import ImportedResolution, ImportedVideo
from app.tasks.vimeo import MigrateImportedVideoTask
from app.utils.vimeo import Interface as Vimeo
from app.utils.wasabi import get_wasabi_config
from tests import TestCase
from tests.domain.data.vimeo_video_detail import VIMEO_VIDEO_DETAIL_RESPONSE
from tests.domain.data.vimeo_video_subtitles import VIMEO_VIDEO_SUBTITLE_RESPONSE
from tests.mixins import AssetMixin, OrganizationMixin


class TestMigrateImportedVideoTask(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def create_import_video(self):
        return ImportedVideo.objects.create(
            folder=None,
            name=VIMEO_VIDEO_DETAIL_RESPONSE["name"],
            uri=VIMEO_VIDEO_DETAIL_RESPONSE["link"],
            organization=self.organization,
            details=VIMEO_VIDEO_DETAIL_RESPONSE,
        )

    @responses.activate
    @mock.patch("app.utils.migrator_interface.get_video_bitrate", return_value=123456)
    @mock.patch("app.tasks.vimeo.start_transcoding")
    @mock.patch("app.utils.migrator_interface.upload_from_url_to_wasabi")
    @mock.patch("app.tasks.vimeo.MigrateImportedVideoTask._upload_subtitles")
    def test_should_copy_only_the_highest_resolution_to_wasabi(
        self,
        mocked_upload_subtitles,
        mocked_copy_files_to_wasabi,
        mocked_start_transcoding,
        mocked_get_video_bitrate,
    ):
        Migrator("abcd", self.organization.uuid)
        responses.add(
            responses.GET,
            Vimeo.VIDEO_DETAIL_URL.format("797262223"),
            status=200,
            json=VIMEO_VIDEO_DETAIL_RESPONSE,
        )
        imported_video = self.create_import_video()
        MigrateImportedVideoTask(
            **{
                "organization_uuid": self.organization.uuid,
                "access_token": "abcd",
                "imported_video_id": imported_video.id,
            }
        )

        imported_resolution = imported_video.resolutions.first()
        highest_resolution_data = VIMEO_VIDEO_DETAIL_RESPONSE["download"][0]  # type: ignore

        self.assertEqual(
            1,
            ImportedResolution.objects.filter(video__uri=imported_video.uri).count(),
        )
        self.assertEqual(
            highest_resolution_data["rendition"],
            ImportedResolution.objects.get(video__uri=imported_video.uri).resolution,
        )
        config = get_wasabi_config(self.organization)
        mocked_copy_files_to_wasabi.assert_called_once_with(
            highest_resolution_data["link"],
            f"{self.organization.bucket_name}/{imported_resolution.output_path}",
            config,
            make_public=True,
        )

    @responses.activate
    @mock.patch("app.utils.migrator_interface.get_video_bitrate", return_value=123456)
    @mock.patch("app.tasks.vimeo.start_transcoding")
    @mock.patch("app.utils.migrator_interface.upload_from_url_to_wasabi")
    @mock.patch("app.tasks.vimeo.MigrateImportedVideoTask._upload_subtitles")
    def test_should_create_video_imput_for_all_highest_resolution_of_vimeo_video(
        self,
        mocked_upload_subtitles,
        mocked_copy_files_to_wasabi,
        mocked_start_transcoding,
        mocked_get_video_bitrate,
    ):
        Migrator("abcd", self.organization.uuid)
        responses.add(
            responses.GET,
            Vimeo.VIDEO_DETAIL_URL.format("797262223"),
            status=200,
            json=VIMEO_VIDEO_DETAIL_RESPONSE,
        )
        imported_video = self.create_import_video()
        MigrateImportedVideoTask(
            **{
                "organization_uuid": self.organization.uuid,
                "access_token": "abcd",
                "imported_video_id": imported_video.id,
            }
        )

        highest_resolution = imported_video.resolutions.first()
        self.assertEqual(
            1,
            VideoInput.objects.filter(
                video__asset__title=imported_video.name,
                organization=self.organization,
            ).exists(),
        )
        self.assertTrue(
            VideoInput.objects.filter(
                url=highest_resolution.output_path,
                video__asset__title=imported_video.name,
                organization=self.organization,
            ).exists()
        )

    @responses.activate
    @mock.patch("app.utils.migrator_interface.get_video_bitrate", return_value=123456)
    @mock.patch("app.tasks.vimeo.start_transcoding")
    @mock.patch("app.utils.migrator_interface.upload_from_url_to_wasabi")
    @mock.patch("app.tasks.vimeo.MigrateImportedVideoTask._upload_subtitles")
    def test_should_start_video_transcoding_with_inpu_urls_of_all_resolutions(
        self,
        mocked_upload_subtitles,
        mocked_copy_files_to_wasabi,
        mocked_start_transcoding,
        mocked_get_video_bitrate,
    ):
        Migrator("abcd", self.organization.uuid)
        responses.add(
            responses.GET,
            Vimeo.VIDEO_DETAIL_URL.format("797262223"),
            status=200,
            json=VIMEO_VIDEO_DETAIL_RESPONSE,
        )
        imported_video = self.create_import_video()
        MigrateImportedVideoTask(
            **{
                "organization_uuid": self.organization.uuid,
                "access_token": "abcd",
                "imported_video_id": imported_video.id,
            }
        )
        video = Video.objects.get(asset__title="Neonatal jaundice")
        mocked_start_transcoding.assert_called_once_with(
            video,
            extra_settings={
                "inputs": [
                    {
                        "url": "https://player.vimeo.com/progressive_redirect/playback/797262223/rendition/240p/file.mp4?loc=external&oauth2_token_id=1732524733&signature=5aaa35afd4ad60cf2e44f2f4f0af3b95a8ef190e530f658a8f95579bb58b528b",
                        "name": "240p",
                        "bandwidth": 123456,
                    },
                    {
                        "url": "https://player.vimeo.com/progressive_redirect/playback/797262223/rendition/720p/file.mp4?loc=external&oauth2_token_id=1732524733&signature=0a56ac7bdb5629f6505e71b7761f1c03bb504314476eaa04e82b11a97c116372",
                        "name": "720p",
                        "bandwidth": 123456,
                    },
                    {
                        "url": "https://player.vimeo.com/progressive_redirect/playback/797262223/rendition/360p/file.mp4?loc=external&oauth2_token_id=1732524733&signature=f1dcaa7d7cc9df3c036976b0beb93118c2c0840a9d4955c5ab50833efad5de04",
                        "name": "360p",
                        "bandwidth": 123456,
                    },
                    {
                        "url": "https://player.vimeo.com/progressive_redirect/playback/797262223/rendition/540p/file.mp4?loc=external&oauth2_token_id=1732524733&signature=3ea497f57f30a409c2ff28313b17dee3d206077ccf1ad98aa9fe5db53c6d8e90",
                        "name": "540p",
                        "bandwidth": 123456,
                    },
                ]
            },
        )

    @responses.activate
    @mock.patch("app.utils.migrator_interface.get_video_bitrate", return_value=123456)
    @mock.patch("app.utils.migrator_interface.upload_from_url_to_wasabi")
    @mock.patch("app.tasks.vimeo.start_transcoding")
    @mock.patch(
        "app.tasks.vimeo.MigrateImportedVideoTask.copy_highest_resolution_to_wasabi"
    )
    @mock.patch("app.domain.subtitle.upload_subtitle_from_url")
    def test_should_create_video_subtitle_for_vimeo_video(
        self,
        mocked_upload_subtitles_from_url,
        mocked_highest_resolution_to_wsabi,
        mocked_start_transcoding,
        mocked_copy_files_to_wasabi,
        mocked_get_video_bitrate,
    ):
        Migrator("abcd", self.organization.uuid)
        responses.add(
            responses.GET,
            Vimeo.VIDEO_DETAIL_URL.format("797262223"),
            status=200,
            json=VIMEO_VIDEO_DETAIL_RESPONSE,
        )
        responses.add(
            responses.GET,
            Vimeo.VIDEO_SUBTITLE_URL.format("797262223"),
            json=VIMEO_VIDEO_SUBTITLE_RESPONSE,
        )
        imported_video = self.create_import_video()

        MigrateImportedVideoTask(
            organization_uuid=self.organization.uuid,
            access_token="abcd",
            imported_video_id=imported_video.id,
        )
        video = Video.objects.get(asset__title="Neonatal jaundice")

        mocked_upload_subtitles_from_url.assert_called_once_with(
            video.asset,
            "auto_generated_captions",
            "te",
            "https://captions.cloud.vimeo.com/captions/138889303?expires=1709729473&sig=b23e59a28a389f186813d84d8d117a428b982259&download=auto_generated_captions.vtt",
        )
