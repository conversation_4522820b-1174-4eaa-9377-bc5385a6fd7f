from unittest import mock

from app.tasks.storage_usage import UpdateStorageUsageTask
from tests import TestCase
from tests.mixins import OrganizationMixin


class TestUpdateStorageUsageTask(OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()

    @mock.patch("app.tasks.storage_usage.update_storage_usage")
    def test_update_storage_usage_task_runs_update_storage_usage(
        self, mock_update_storage_usage
    ):
        task = UpdateStorageUsageTask
        task.run()  # type: ignore
        mock_update_storage_usage.assert_called_once()
