from unittest import mock

from django_multitenant.utils import set_current_tenant

from app.models import Video
from app.models.videoimport import ImportedVideo
from app.tasks.teachable import MigrateTeachableVideoTask
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestMigrateTeachableVideoTask(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.video_data = {
            "video_id": "123",
            "name": "Test Video",
            "url": "https://example.com/video.m3u8",
            "duration": 120,
            "thumbnail_url": "https://example.com/thumb.jpg",
            "status": "READY",
            "data": {},
        }
        self.imported_video = self.create_import_video()

    def create_import_video(self):
        return ImportedVideo.objects.create(
            folder=None,
            name=self.video_data["name"],
            uri=self.video_data["url"],
            organization=self.organization,
            details=self.video_data,
            source=ImportedVideo.Source.TEACHABLE,
        )

    def create_task(self):
        task = type(MigrateTeachableVideoTask)()
        task._set_organization(self.organization.uuid)
        task.imported_video = self.imported_video
        task.video_data = self.imported_video.details
        task.content_protection_type = Video.ContentProtectionType.DISABLED
        task.transcoding_queue_name = None
        return task

    def setup_mocks(self, mocked_popen, mocked_run):
        mock_process = mock.MagicMock()
        mock_process.poll.return_value = 0
        mock_process.returncode = 0
        mock_process.communicate.return_value = (
            "",
            "",
        )  # Return empty stdout and stderr
        mocked_popen.return_value = mock_process
        mocked_run.return_value = mock.MagicMock(returncode=0)

    @mock.patch("app.tasks.teachable.subprocess.Popen")
    @mock.patch("app.tasks.teachable.copy_files_to_wasabi")
    @mock.patch("app.tasks.teachable.os.path.exists")
    @mock.patch("app.tasks.teachable.os.remove")
    @mock.patch("app.tasks.teachable.os.makedirs")
    @mock.patch("app.tasks.teachable.subprocess.run")
    @mock.patch("app.tasks.teachable.start_transcoding")
    def test_should_create_video_asset_with_correct_path(
        self,
        mocked_start_transcoding,
        mocked_run,
        mocked_makedirs,
        mocked_remove,
        mocked_exists,
        mocked_copy_files_to_wasabi,
        mocked_popen,
    ):
        self.setup_mocks(mocked_popen, mocked_run)
        task = self.create_task()
        task._migrate_video()

        mocked_copy_files_to_wasabi.assert_called_once_with(
            mock.ANY,
            f"{self.organization.bucket_name}/video-import-data/{self.imported_video.id}/",
            mock.ANY,
            make_public=False,
        )

    @mock.patch("app.tasks.teachable.subprocess.Popen")
    @mock.patch("app.tasks.teachable.start_transcoding")
    @mock.patch("app.tasks.teachable.os.path.exists")
    @mock.patch("app.tasks.teachable.os.remove")
    @mock.patch("app.tasks.teachable.os.makedirs")
    @mock.patch("app.tasks.teachable.subprocess.run")
    def test_should_start_transcoding_with_correct_input(
        self,
        mocked_run,
        mocked_makedirs,
        mocked_remove,
        mocked_exists,
        mocked_start_transcoding,
        mocked_popen,
    ):
        self.setup_mocks(mocked_popen, mocked_run)
        task = self.create_task()
        task._migrate_video()

        mocked_start_transcoding.assert_called_once_with(
            mock.ANY,
            extra_settings={
                "inputs": [
                    {
                        "url": f"{self.organization.bucket_name}/video-import-data/{self.imported_video.id}/",
                        "name": self.imported_video.name,
                        "bandwidth": 0,
                    }
                ]
            },
            transcoding_queue_name=None,
        )

    @mock.patch("app.tasks.teachable.requests.get")
    @mock.patch("app.tasks.teachable.copy_files_to_wasabi")
    @mock.patch("app.tasks.teachable.os.path.exists")
    @mock.patch("app.tasks.teachable.os.makedirs")
    @mock.patch("app.tasks.teachable.os.remove")
    @mock.patch("app.tasks.teachable.open", new_callable=mock.mock_open)
    def test_should_migrate_thumbnail_successfully(
        self,
        mocked_open,
        mocked_remove,
        mocked_makedirs,
        mocked_exists,
        mocked_copy_files_to_wasabi,
        mocked_get,
    ):
        # Mock the thumbnail download
        mock_response = mock.MagicMock()
        mock_response.content = b"fake_thumbnail_data"
        mock_response.raise_for_status.return_value = None
        mocked_get.return_value = mock_response

        # Mock file operations
        mocked_exists.return_value = True
        mocked_copy_files_to_wasabi.return_value = True

        task = self.create_task()
        task._migrate_thumbnail()

        # Verify thumbnail was downloaded and uploaded
        mocked_get.assert_called_once_with(
            self.video_data["thumbnail_url"],
            headers={
                "User-Agent": "Mozilla/5.0",
                "Referer": "https://play.hotmart.com/",
            },
            verify=True,
        )
        mocked_copy_files_to_wasabi.assert_called_once_with(
            mock.ANY,
            f"{self.organization.bucket_name}/video-import-data/{self.imported_video.id}/thumbnail.jpg",
            mock.ANY,
            make_public=False,
        )
        self.assertEqual(
            task.video_data["thumbnail_path"],
            f"video-import-data/{self.imported_video.id}/thumbnail.jpg",
        )

    @mock.patch("app.tasks.teachable.subprocess.Popen")
    @mock.patch("app.tasks.teachable.os.path.exists")
    @mock.patch("app.tasks.teachable.os.remove")
    @mock.patch("app.tasks.teachable.os.makedirs")
    def test_should_handle_video_conversion_error(
        self,
        mocked_makedirs,
        mocked_remove,
        mocked_exists,
        mocked_popen,
    ):
        # Mock a failed process
        mock_process = mock.MagicMock()
        mock_process.poll.return_value = 1
        mock_process.returncode = 1
        mock_process.communicate.return_value = ("", "FFmpeg error: Invalid data")
        mocked_popen.return_value = mock_process

        task = self.create_task()
        with self.assertRaises(Exception) as context:
            task._extract_video_from_hls()

        self.assertIn("FFmpeg conversion failed", str(context.exception))
        mocked_remove.assert_called_once()  # Verify cleanup was attempted
