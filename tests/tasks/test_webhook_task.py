import responses
from django_multitenant.utils import set_current_tenant

from app.tasks import send_data_to_webhook_task
from tests import TestCase
from tests.mixins.asset import AssetMixin
from tests.mixins.organization import OrganizationMixin


class TestWebhookTask(TestCase, AssetMixin, OrganizationMixin):
    @responses.activate
    def test_token_should_passed_to_webhook(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.asset = self.create_asset()
        responses.add(responses.POST, "https://example.com", status=200)
        send_data_to_webhook_task(
            "https://example.com",
            self.organization.uuid,
            asset_uuid=self.asset.uuid,
            token="abcd",
        )

        self.assertEqual(responses.calls[0].request.headers["x-streams-token"], "abcd")
