from unittest import mock

from django_multitenant.utils import set_current_tenant

from app.tasks.live_chat import CreateChatRoomTask
from tests import TestCase
from tests.mixins import Asset<PERSON>ix<PERSON>, OrganizationMixin


class TestCreateChatRoomTaskTask(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @mock.patch("app.tasks.live_chat.create_chat_room_for_asset")
    def test_create_chat_room_for_asset_should_be_called_if_CreateChatRoomTask_triggered(
        self, mock_create_chat_room_for_asset
    ):
        live_stream = self.create_livestream()
        CreateChatRoomTask(
            asset_id=live_stream.asset.uuid, organization_uuid=self.organization.uuid
        )

        mock_create_chat_room_for_asset.assert_called()
