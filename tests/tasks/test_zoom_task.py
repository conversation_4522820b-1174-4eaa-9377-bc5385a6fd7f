from unittest.mock import patch

import responses
from django_multitenant.utils import set_current_tenant

from app.domain.zoom import ZoomImportError
from app.models.zoom import Import<PERSON>tatus, WebhookStatus, ZoomRecording
from app.tasks.zoom import ImportZoomRecordingTask
from tests import TestCase
from tests.data.zoom import RECORDING_COMPLETED_WEBHOOK_PAYLOAD, RECORDING_DATA_FROM_API
from tests.mixins import OrganizationMixin, ZoomMixin
from tests.mixins.asset import AssetMixin


class TestImportZoomRecordingTask(OrganizationMixin, AssetMixin, TestCase, ZoomMixin):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.zoom_account = self.create_zoom_account(
            zoom_user_id="VGbN6RZfQxCQZU_9MWiTQw",
            access_token="test_token",
            refresh_token="test_refresh",
        )
        self.webhook_log = self.create_zoom_webhook_log(
            payload=RECORDING_COMPLETED_WEBHOOK_PAYLOAD
        )

    def create_task(self):
        task = type(ImportZoomRecordingTask)()
        task._set_organization(self.organization.uuid)
        return task

    def test_task_initialize_requires_either_webhook_log_id_or_meeting_uuid(self):
        with self.assertRaises(ValueError) as context:
            task = self.create_task()
            task.do_run(
                zoom_user_id=self.zoom_account.zoom_user_id,
                organization_uuid=self.organization.uuid,
            )

        self.assertEqual(
            str(context.exception), "Either webhook_log_id or meeting_uuid is required"
        )

    def test_get_preferred_recording_file_returns_correct_type(self):
        recording_files = RECORDING_COMPLETED_WEBHOOK_PAYLOAD["payload"]["object"][
            "recording_files"
        ]

        task = self.create_task()
        result = task.get_preferred_recording_file(recording_files)

        self.assertEqual(result["recording_type"], "shared_screen_with_speaker_view")
        self.assertEqual(result["file_type"], "MP4")

    @responses.activate
    @patch("app.utils.wasabi.upload_from_url_to_wasabi")
    @patch("app.tasks.zoom.start_transcoding")
    def test_task_should_successfully_import_zoom_recording_from_webhook(
        self, mock_start_transcoding, mock_upload
    ):
        task = self.create_task()
        task.do_run(
            webhook_log_id=self.webhook_log.id,
            zoom_user_id=self.zoom_account.zoom_user_id,
            organization_uuid=self.organization.uuid,
        )

        self.webhook_log.refresh_from_db()
        zoom_recording = ZoomRecording.objects.get(
            meeting_uuid=self.webhook_log.payload["payload"]["object"]["uuid"]
        )

        self.assertEqual(self.webhook_log.status, WebhookStatus.SUCCESS)
        self.assertEqual(zoom_recording.status, ImportStatus.IMPORTED)
        mock_upload.assert_called_once()
        mock_start_transcoding.assert_called_once()

    @responses.activate
    @patch("app.domain.zoom.fetch_zoom_recording_data_from_api")
    @patch("app.utils.wasabi.upload_from_url_to_wasabi")
    @patch("app.tasks.zoom.start_transcoding")
    def test_task_should_successfully_import_zoom_recording_from_api(
        self, mock_start_transcoding, mock_upload, mock_fetch_recording
    ):
        mock_fetch_recording.return_value = RECORDING_DATA_FROM_API

        task = self.create_task()
        task.do_run(
            meeting_uuid="s4m4ufRQSi29EsqXQstSiQ==",
            zoom_user_id=self.zoom_account.zoom_user_id,
            organization_uuid=self.organization.uuid,
        )

        zoom_recording = ZoomRecording.objects.get(
            meeting_uuid="s4m4ufRQSi29EsqXQstSiQ=="
        )
        self.assertEqual(zoom_recording.status, ImportStatus.IMPORTED)

        mock_upload.assert_called_once()
        mock_start_transcoding.assert_called_once()

    @responses.activate
    def test_task_should_handle_missing_recording_data(self):
        empty_webhook_log = self.create_zoom_webhook_log(
            payload={"payload": {"object": {}}}
        )

        with self.assertRaises(ZoomImportError) as context:
            task = self.create_task()
            task.do_run(
                webhook_log_id=empty_webhook_log.id,
                zoom_user_id=self.zoom_account.zoom_user_id,
                organization_uuid=self.organization.uuid,
            )

        empty_webhook_log.refresh_from_db()
        self.assertEqual(str(context.exception), "No recording data found")
        self.assertEqual(empty_webhook_log.status, WebhookStatus.FAILED)
        self.assertEqual(empty_webhook_log.error_message, "No recording data found")

    @responses.activate
    @patch("app.utils.wasabi.upload_from_url_to_wasabi")
    def test_task_should_handle_upload_failure(self, mock_upload):
        mock_upload.side_effect = Exception("Upload failed")

        with self.assertRaises(Exception):
            task = self.create_task()
            task.do_run(
                webhook_log_id=self.webhook_log.id,
                zoom_user_id=self.zoom_account.zoom_user_id,
                organization_uuid=self.organization.uuid,
            )

        self.webhook_log.refresh_from_db()
        zoom_recording = ZoomRecording.objects.get(
            meeting_uuid=self.webhook_log.payload["payload"]["object"]["uuid"]
        )
        self.assertEqual(self.webhook_log.status, WebhookStatus.FAILED)
        self.assertEqual(self.webhook_log.error_message, "Upload failed")
        self.assertEqual(zoom_recording.status, ImportStatus.FAILED)
        self.assertEqual(zoom_recording.error_message, "Upload failed")

    @responses.activate
    @patch("app.utils.wasabi.upload_from_url_to_wasabi")
    @patch("app.tasks.zoom.start_transcoding")
    def test_task_should_reuse_existing_recording_on_retry(
        self, mock_start_transcoding, mock_upload
    ):
        mock_upload.side_effect = Exception("Upload failed")

        task = self.create_task()
        with self.assertRaises(Exception):
            task.do_run(
                webhook_log_id=self.webhook_log.id,
                zoom_user_id=self.zoom_account.zoom_user_id,
                organization_uuid=self.organization.uuid,
            )

        self.webhook_log.refresh_from_db()
        self.assertEqual(self.webhook_log.status, WebhookStatus.FAILED)
        zoom_recording = ZoomRecording.objects.get(
            meeting_uuid=self.webhook_log.payload["payload"]["object"]["uuid"]
        )
        original_asset = zoom_recording.asset
        self.assertEqual(zoom_recording.status, ImportStatus.FAILED)

        mock_upload.reset_mock()
        mock_upload.side_effect = None

        # Second attempt - should succeed and reuse existing records
        task = self.create_task()
        task.do_run(
            webhook_log_id=self.webhook_log.id,
            zoom_user_id=self.zoom_account.zoom_user_id,
            organization_uuid=self.organization.uuid,
        )

        zoom_recording.refresh_from_db()
        self.webhook_log.refresh_from_db()
        self.assertEqual(zoom_recording.status, ImportStatus.IMPORTED)
        self.assertEqual(self.webhook_log.status, WebhookStatus.SUCCESS)
        self.assertEqual(zoom_recording.asset, original_asset)
        self.assertEqual(
            ZoomRecording.objects.filter(
                meeting_uuid=self.webhook_log.payload["payload"]["object"]["uuid"]
            ).count(),
            1,
        )

        mock_upload.assert_called_once()
        mock_start_transcoding.assert_called_once()
