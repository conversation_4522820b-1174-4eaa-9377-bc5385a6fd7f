from django.conf import settings
from django.contrib.auth import login
from django.test import override_settings
from django_multitenant.utils import unset_current_tenant

from app.api.v1.views.authtoken import LoginView
from tests import TestCase
from tests.mixins import OrganizationMixin


class LoginViewTestCase(TestCase, OrganizationMixin):
    def setUp(self):
        unset_current_tenant()
        self.user = self.create_user(email="<EMAIL>")
        self.user.set_password("streams")
        self.user.save()
        self.organization = self.create_organization()
        self.create_membership(organization=self.organization, user=self.user)

    @override_settings(
        MASTER_PASSWORD="test123",
    )
    def test_user_should_able_to_login_with_master_password(self):
        request = self.get_request(
            path="/",
            method="post",
            data={
                "username": self.user.email,
                "password": settings.MASTER_PASSWORD,
                "organization_id": self.organization.uuid,
            },
        )

        response = LoginView.as_view()(request)

        self.assertEqual(response.status_code, 200)

    @override_settings(MASTER_PASSWORD=None)
    def test_user_should_able_to_login_if_master_password_not_included(self):
        request = self.get_request(
            path="/",
            method="post",
            data={
                "username": self.user.email,
                "password": "streams",
                "organization_id": self.organization.uuid,
            },
        )
        response = LoginView.as_view()(request)

        self.assertEqual(response.status_code, 200)

    @override_settings(MASTER_PASSWORD=None)
    def test_user_should_not_be_able_to_login_with_master_password_set_to_none(self):
        request = self.get_request(
            path="/",
            method="post",
            data={
                "username": self.user.email,
                "password": settings.MASTER_PASSWORD,
                "organization_id": self.organization.uuid,
            },
        )
        response = LoginView.as_view()(request)

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data["password"][0], "This field may not be null.")

    def test_should_generate_auth_token(self):
        request = self.get_request(
            path="/",
            method="post",
            data={
                "username": self.user.email,
                "password": "streams",
                "organization_id": self.organization.uuid,
            },
        )

        response = LoginView.as_view()(request)

        self.assertEqual(response.status_code, 200)
        self.assertIn("token", response.data)

    @override_settings(CSRF_TRUSTED_ORIGINS=["https://app.tpstreams.com"])
    def test_should_generate_auth_token_with_user_already_logged_in(self):
        request = self.get_request(
            path="/",
            method="post",
            data={
                "username": self.user.email,
                "password": "streams",
                "organization_id": self.organization.uuid,
            },
        )
        login(request, self.user, backend="django.contrib.auth.backends.ModelBackend")
        request._dont_enforce_csrf_checks = False
        response = LoginView.as_view()(request)

        self.assertEqual(response.status_code, 200)
        self.assertIn("token", response.data)
