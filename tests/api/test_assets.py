from unittest.mock import patch

from django.contrib.auth.models import AnonymousUser
from django_multitenant.utils import set_current_tenant

from app.api.v1.views import FolderListCreateView
from app.models import Asset
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestFolderListCreateView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_should_create_folder(self):
        request = self.get_request(
            path="/",
            method="post",
            user=self.organization.created_by,
            data={"title": "Folder 1"},
        )
        response = FolderListCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(201, response.status_code)
        self.assertTrue(
            Asset.objects.filter(type=Asset.Type.FOLDER, title="Folder 1").exists()
        )

    def test_should_list_folders(self):
        folder1 = self.create_folder(title="Folder 1")
        folder2 = self.create_folder(title="Folder 2")
        request = self.get_request(
            path="/",
            method="get",
            user=self.organization.created_by,
        )
        response = FolderListCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )
        self.assertEqual(200, response.status_code)
        response_titles = [folder["title"] for folder in response.data["results"]]
        self.assertIn(folder1.title, response_titles)
        self.assertIn(folder2.title, response_titles)

    def test_should_create_folder_as_child(self):
        parent = self.create_folder()
        request = self.get_request(
            path="/",
            method="post",
            user=self.organization.created_by,
            data={"title": "Folder 2", "parent": parent.uuid},
        )
        response = FolderListCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(201, response.status_code)
        self.assertTrue(
            Asset.objects.filter(
                type=Asset.Type.FOLDER, title="Folder 2", parent=parent
            ).exists()
        )

    def test_should_raise_error_if_folder_already_exists_in_destination(self):
        folder = self.create_folder(title="duplicate")
        request = self.get_request(
            path="/",
            method="post",
            user=self.organization.created_by,
            data={"title": folder.title, "parent": ""},
        )
        response = FolderListCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(400, response.status_code)
        self.assertEqual(
            "Folder name already exists in destination.",
            response.data["non_field_errors"][0],
        )

    def test_should_not_allow_anonymous_user(self):
        request = self.get_request(
            path="/",
            method="post",
            user=AnonymousUser(),
            data={
                "title": "test",
            },
        )
        response = FolderListCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(401, response.status_code)

    def test_should_filter_folders_by_title(self):
        folder_1 = self.create_folder(title="Folder 1")
        self.create_folder()

        request = self.get_request(
            path=f"/?q={folder_1.title}",
            user=self.organization.created_by,
        )
        response = FolderListCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(1, response.data["count"])
        self.assertEqual(folder_1.uuid, response.data["results"][0]["uuid"])


class TestAssetModelMethods(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_delete_method_should_soft_delete_asset(self):
        asset = self.create_asset()
        asset.delete()

        self.assertFalse(Asset.objects.filter(id=asset.id).exists())

    def test_save_method_should_update_asset_with_new_values(self):
        asset = self.create_asset()
        asset.title = "New title"
        asset.save()

        self.assertEqual(Asset.objects.get(id=asset.id).title, "New title")

    def test_all_method_should_only_list_non_deleted_rows(self):
        asset = self.create_asset()
        asset.delete()

        self.assertFalse(Asset.objects.all().filter(uuid=asset.uuid).exists())

    @patch("app.tasks.rebuild_asset_tree.delay")
    def test_move_method_should_only_accept_folder_as_destination(self, mock_rebuild):
        asset = self.create_asset()
        destination = self.create_asset()
        asset.move(destination)

        self.assertEqual(destination.children.count(), 0)
        self.assertNotEqual(asset.parent, destination)

    @patch("app.tasks.rebuild_asset_tree.delay")
    def test_move_method_should_move_asset_to_destination(self, mock_rebuild):
        asset = self.create_asset()
        destination = self.create_folder()
        asset.move(destination)

        self.assertEqual(destination.children.count(), 1)
        self.assertEqual(asset.parent, destination)

    @patch("app.tasks.rebuild_asset_tree.delay")
    def test_move_method_should_move_asset_to_root(self, mock_rebuild):
        asset = self.create_asset()
        destination = self.create_folder()
        asset.move(destination)
        asset.move(None)

        self.assertEqual(destination.children.count(), 0)
        self.assertEqual(asset.parent, None)
