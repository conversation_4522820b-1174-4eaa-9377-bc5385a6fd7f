from unittest import mock
from unittest.mock import MagicMock

from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from django_multitenant.utils import set_current_tenant

from app.api.v1.views.subtitle import TrackViewSet
from app.models import Track
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin, TrackMixin


class SubtitleUploadAPIViewTestCase(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.asset = self.create_asset()
        self.organization.created_by.current_organization_uuid = self.organization.uuid
        self.organization.created_by.save()

    @mock.patch("app.api.v1.views.subtitle.upload_subtitle")
    def test_should_upload_subtitle_for_valid_file(self, mocked_upload_subtitle):
        file_content = b"Subtitle file content"
        file = SimpleUploadedFile("subtitle.vtt", file_content, content_type="text/vtt")

        mock_request = MagicMock()
        data = {"language": "en", "name": "English_sub", "subtitle": file}
        mock_request.data = data
        data = {
            "language": "en",
            "name": "English_sub",
        }

        url = reverse(
            "api:upload-subtitle",
            kwargs={
                "organization_id": self.organization.uuid,
                "asset_id": self.asset.uuid,
            },
        )

        self.client.force_login(self.organization.created_by)
        response = self.client.post(url, mock_request.data, format="multipart")

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["detail"], "Subtitle uploaded successfully")

    @mock.patch("app.api.v1.views.subtitle.upload_subtitle")
    def test_should_throw_400_for_no_file(self, mocked_upload_subtitle):
        mock_request = MagicMock()
        mock_request.data = {"name": "test"}

        url = reverse(
            "api:upload-subtitle",
            kwargs={
                "organization_id": self.organization.uuid,
                "asset_id": self.asset.uuid,
            },
        )

        self.client.force_login(self.organization.created_by)
        response = self.client.post(url, mock_request.data, format="multipart")

        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.data["detail"],
            "Upload a valid subtitle file in .vtt format with size <= 2MB",
        )
        mocked_upload_subtitle.assert_not_called()

    @mock.patch("app.api.v1.views.subtitle.upload_subtitle")
    def test_should_throw_400_for_non_vtt_file(self, mocked_upload_subtitle):
        file_content = b"Subtitle file content"
        file = SimpleUploadedFile("subtitle.txt", file_content, content_type="text/vtt")

        mock_request = MagicMock()
        mock_request.data = {"subtitle": file}

        url = reverse(
            "api:upload-subtitle",
            kwargs={
                "organization_id": self.organization.uuid,
                "asset_id": self.asset.uuid,
            },
        )

        self.client.force_login(self.organization.created_by)
        response = self.client.post(url, mock_request.data, format="multipart")

        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.data["detail"],
            "Upload a valid subtitle file in .vtt format with size <= 2MB",
        )

    @mock.patch("app.api.v1.views.subtitle.upload_subtitle")
    def test_should_throw_400_for_large_file(self, mocked_upload_subtitle):
        file_content = b"Subtitle file content" * 500000
        file = SimpleUploadedFile(
            "large_subtitle.vtt", file_content, content_type="text/vtt"
        )

        mock_request = MagicMock()
        mock_request.data = {"language": "en", "name": "Large_sub", "subtitle": file}

        url = reverse(
            "api:upload-subtitle",
            kwargs={
                "organization_id": self.organization.uuid,
                "asset_id": self.asset.uuid,
            },
        )

        self.client.force_login(self.organization.created_by)
        response = self.client.post(url, mock_request.data, format="multipart")

        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.data["detail"],
            "Upload a valid subtitle file in .vtt format with size <= 2MB",
        )
        mocked_upload_subtitle.assert_not_called()


class TestTrackViewSet(TrackMixin, AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)
        self.asset = self.create_asset()

    def test_update_subtitle_sets_is_active_to_false(self):
        self.subtitle = self.create_subtitle(is_active=True)
        request = self.get_request(
            "/",
            method="PUT",
            user=self.organization.created_by,
            data={"is_active": False},
        )

        TrackViewSet.as_view({"put": "update"})(
            request,
            organization_id=self.organization.uuid,
            pk=self.subtitle.id,
        )

        self.assertEqual(False, Track.objects.first().is_active)

    def test_should_update_only_is_active(self):
        self.subtitle = self.create_subtitle(is_active=True)
        request = self.get_request(
            "/",
            method="PUT",
            user=self.organization.created_by,
            data={
                "is_active": False,
                "name": "test",
            },
        )

        TrackViewSet.as_view({"put": "update"})(
            request,
            organization_id=self.organization.uuid,
            pk=self.subtitle.id,
        )

        self.assertEqual(False, Track.objects.first().is_active)
        self.assertEqual(Track.objects.first().name, self.subtitle.name)

    def test_get_subtitle_list_returns_all_subtitles(self):
        self.create_subtitle(is_active=True)
        self.create_subtitle(is_active=True)
        self.create_subtitle(is_active=True)
        self.create_subtitle(is_active=True)
        self.create_subtitle(is_active=True)

        request = self.get_request("/", method="get", user=self.organization.created_by)

        response = TrackViewSet.as_view({"get": "list"})(
            request,
            organization_id=self.organization.uuid,
        )

        self.assertEqual(5, response.data["count"])

    def test_delete_subtitle_deletes_subtitle(self):
        self.subtitle = self.create_subtitle(is_active=True)
        request = self.get_request(
            "/",
            method="delete",
            user=self.organization.created_by,
        )

        TrackViewSet.as_view({"delete": "destroy"})(
            request,
            organization_id=self.organization.uuid,
            pk=self.subtitle.id,
        )

        self.assertFalse(Track.objects.filter(pk=self.subtitle.id).exists())

    def test_shouldnot_delete_auto_generated_subtitles(self):
        self.subtitle = self.create_subtitle(is_active=True, subtitle_type=0)
        request = self.get_request(
            "/",
            method="delete",
            user=self.organization.created_by,
        )

        response = TrackViewSet.as_view({"delete": "destroy"})(
            request,
            organization_id=self.organization.uuid,
            pk=self.subtitle.id,
        )

        self.assertTrue(Track.objects.filter(pk=self.subtitle.id).exists())
        self.assertEqual(response.status_code, 403)

    def test_get_subtitle_list_returns_asset_subtitles(self):
        video = self.create_video()
        self.create_subtitle(video=video)
        self.create_subtitle(video=video)
        self.create_subtitle(video=video)
        self.subtitle = self.create_subtitle()

        request = self.get_request("/", method="get", user=self.organization.created_by)

        response = TrackViewSet.as_view({"get": "list"})(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=video.asset.uuid,
        )
        self.assertEqual(response.data["count"], 3)


class TestGenerateSubtitleView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.video = self.create_video()
        self.asset = self.video.asset

    @mock.patch("app.tasks.subtitle.GenerateSubtitleTask.apply_async")
    @mock.patch("app.api.v1.views.subtitle.generate_subtitle")
    @mock.patch("app.models.video.Video.should_generate_subtitle", return_value=True)
    def test_should_generate_subtitle_for_valid_video(
        self, mock_should_generate, mock_generate_subtitle, mock_apply_async
    ):
        url = reverse(
            "api:generate-subtitle",
            kwargs={
                "organization_id": self.organization.uuid,
                "asset_id": self.asset.uuid,
            },
        )
        self.client.force_login(self.organization.created_by)
        response = self.client.post(url)
        self.assertEqual(response.status_code, 201)
        mock_generate_subtitle.assert_called_once_with(self.asset)

    def test_should_return_404_for_invalid_asset_id(self):
        invalid_asset_id = "invalid-uuid"
        url = reverse(
            "api:generate-subtitle",
            kwargs={
                "organization_id": self.organization.uuid,
                "asset_id": invalid_asset_id,
            },
        )
        self.client.force_login(self.organization.created_by)
        response = self.client.post(url)
        self.assertEqual(response.status_code, 404)
