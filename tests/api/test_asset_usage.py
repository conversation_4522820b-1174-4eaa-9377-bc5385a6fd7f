from datetime import date

from django_multitenant.utils import set_current_tenant

from app.api.v1.views import AssetUsageView
from tests import TestCase
from tests.mixins import BandwidthMixin, OrganizationMixin


class TestAssetUsageView(BandwidthMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

        self.create_daily_bandwidth_usage(date=date(2024, 3, 15))
        self.create_daily_bandwidth_usage(date=date(2024, 3, 25))
        self.create_daily_bandwidth_usage(date=date(2024, 3, 5))
        self.create_daily_bandwidth_usage(date=date(2024, 2, 25))

        self.create_monthly_bandwidth_usage(date=date(2023, 1, 1))
        self.create_monthly_bandwidth_usage(date=date(2023, 2, 1))

    def test_should_get_assert_usage_by_time_frame(self):
        request = self.get_request(
            path="/?time_frame=daily",
            method="get",
            user=self.organization.created_by,
        )
        response = AssetUsageView.as_view()(
            request, organization_id=self.organization.uuid
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 4)

    def test_should_return_all_assert_usages_without_filter(self):
        request = self.get_request(
            path="/",
            method="get",
            user=self.organization.created_by,
        )
        response = AssetUsageView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 6)

    def test_should_filter_assert_usages_by_end_date_and_timeframe(self):
        request = self.get_request(
            path="/?end=2024-03-20&time_frame=daily",
            method="get",
            user=self.organization.created_by,
        )
        response = AssetUsageView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 3)

    def test_should_filter_assert_usages_by_start_date(self):
        request = self.get_request(
            path="/?start=2024-03-10",
            method="get",
            user=self.organization.created_by,
        )
        response = AssetUsageView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 2)
