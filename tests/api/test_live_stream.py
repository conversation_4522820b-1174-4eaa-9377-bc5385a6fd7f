import json
import uuid
from unittest import mock

import responses
from django.conf import settings
from django.test import override_settings
from django_multitenant.utils import set_current_tenant, unset_current_tenant
from knox.auth import TokenAuthentication
from rest_framework import permissions

from app.api.v1.permissions import HasOrganizationAccess
from app.api.v1.serializers.asset import AssetSerializer
from app.api.v1.views.live_stream import (
    CreateLiveStreamServerView,
    LiveStreamListCreateView,
    LiveStreamOnPublishCallbackView,
    LiveStreamOnUnPublishCallbackView,
    LiveStreamOnUploadCompleteCallbackView,
    LiveStreamServerStatusCallbackView,
    StopLiveStreamView,
)
from app.domain.drm.encryption.streams import (
    generate_and_store_fairplay_encryption_keys,
)
from app.models import (
    Asset,
    EncryptionKey,
    LiveStream,
    LiveStreamEvent,
    LiveStreamUsage,
)
from app.utils.live_stream import LiveStreamServer
from tests import TestCase
from tests.domain.data.digital_ocean_test_data import (
    DIGITAL_OCEAN_CREATE_SERVER_RESPONSE,
)
from tests.mixins import AssetMixin, OrganizationMixin


class TestLiveStreamCreateView(OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_view_should_not_allow_user_without_authentication(self):
        request = self.get_request(
            "/",
            method="POST",
            data={"title": "Big Buck Bunny Video"},
        )
        response = LiveStreamListCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(401, response.status_code)

    def test_view_should_not_allow_user_without_organization_access(self):
        new_user = self.create_user()
        unset_current_tenant()
        self.create_membership(user=new_user, organization=self.create_organization())
        request = self.get_request(
            "/", method="POST", data={"title": "Big Buck Bunny Video"}, user=new_user
        )
        response = LiveStreamListCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(403, response.status_code)

    def test_view_should_show_error_if_title_is_not_provided(self):
        request = self.get_request(
            "/", method="POST", data={}, user=self.organization.created_by
        )
        response = LiveStreamListCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(400, response.status_code)
        self.assertEqual(
            json.dumps({"title": ["This field is required."]}),
            json.dumps(response.data),
        )

    @responses.activate
    @override_settings(LIVE_STREAM_SERVER_PROVIDER="digitalocean")
    @mock.patch("app.api.v1.views.live_stream.create_chat_room_task")
    def test_view_should_return_201_and_create_asset_for_valid_post_data(
        self, mock_chat_room_task
    ):
        responses.add(
            responses.POST,
            "https://api.digitalocean.com/v2/droplets",
            status=200,
            json=DIGITAL_OCEAN_CREATE_SERVER_RESPONSE,
        )
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={"title": "Big Buck Bunny Video"},
        )
        response = LiveStreamListCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )
        asset = Asset.objects.get(title="Big Buck Bunny Video")

        self.assertIsInstance(asset.live_stream, LiveStream)
        self.assertEqual("Big Buck Bunny Video", asset.title)
        self.assertEqual(201, response.status_code)
        self.assertEqual("357107896", asset.live_stream.server_id)
        self.assertTrue(
            LiveStreamUsage.objects.filter(live_stream=asset.live_stream).exists()
        )

    @responses.activate
    @override_settings(LIVE_STREAM_SERVER_PROVIDER="digitalocean")
    @mock.patch("app.api.v1.views.live_stream.create_chat_room_task")
    def test_view_store_enable_drm_value_in_live_stream(self, mock_chat_room_task):
        responses.add(
            responses.POST,
            "https://api.digitalocean.com/v2/droplets",
            status=200,
            json=DIGITAL_OCEAN_CREATE_SERVER_RESPONSE,
        )
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={"title": "Big Buck Bunny Video", "enable_drm_for_recording": True},
        )
        response = LiveStreamListCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )
        asset = Asset.objects.get(title="Big Buck Bunny Video")

        self.assertEqual(201, response.status_code)
        self.assertIsInstance(asset.live_stream, LiveStream)
        self.assertEqual(True, asset.live_stream.enable_drm_for_recording)

    @responses.activate
    @override_settings(LIVE_STREAM_SERVER_PROVIDER="digitalocean")
    @mock.patch("app.api.v1.views.live_stream.create_chat_room_task")
    def test_resolutions_should_get_populated(self, mock_chat_room_task):
        responses.add(
            responses.POST,
            "https://api.digitalocean.com/v2/droplets",
            status=200,
            json=DIGITAL_OCEAN_CREATE_SERVER_RESPONSE,
        )
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={"title": "Big Buck Bunny Video", "enable_drm_for_recording": True},
        )
        response = LiveStreamListCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )
        asset = Asset.objects.get(title="Big Buck Bunny Video")

        self.assertEqual(201, response.status_code)
        self.assertEqual(
            asset.live_stream.resolutions,
            [
                LiveStream.Resolutions._240p,
                LiveStream.Resolutions._480p,
                LiveStream.Resolutions._720p,
            ],
        )
        self.assertEqual("Normal Latency", asset.live_stream.get_latency_display())

    @responses.activate
    @override_settings(LIVE_STREAM_SERVER_PROVIDER="digitalocean")
    @mock.patch("app.api.v1.views.live_stream.create_chat_room_task")
    def test_view_store_latency_in_live_stream(self, mock_chat_room_task):
        responses.add(
            responses.POST,
            "https://api.digitalocean.com/v2/droplets",
            status=200,
            json=DIGITAL_OCEAN_CREATE_SERVER_RESPONSE,
        )
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={"title": "Big Buck Bunny Video", "latency": "Low Latency"},
        )
        response = LiveStreamListCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )
        asset = Asset.objects.get(title="Big Buck Bunny Video")

        self.assertEqual(201, response.status_code)
        self.assertIsInstance(asset.live_stream, LiveStream)
        self.assertEqual("Low Latency", asset.live_stream.get_latency_display())

    @responses.activate
    @mock.patch("app.api.v1.views.live_stream.create_chat_room_task")
    @mock.patch("app.domain.live_stream.create_aws_live_stream_server")
    def test_view_should_return_201_and_create_asset_when_title_exceeds_256_chars(
        self, mock_create_aws_server, mock_chat_room_task
    ):
        mock_create_aws_server.return_value = LiveStreamServer(
            ip_address="***********",
            private_ip_address="********",
            id="i-1234567890abcdef0",
            status=2,
            provider=LiveStreamUsage.ServerProvider.AWS,
            cost_per_hour=None,
        )
        long_title = "Asset title Exceeding 256 chars" * 20
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={"title": long_title},
        )
        response = LiveStreamListCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )
        asset = Asset.objects.get(title=long_title)
        expected_server_name = (
            f"{long_title[:232]}-org-{self.organization.uuid}-{asset.uuid}"
        )

        self.assertIsInstance(asset.live_stream, LiveStream)
        self.assertEqual(long_title, asset.title)
        self.assertEqual(201, response.status_code)
        self.assertEqual("i-1234567890abcdef0", asset.live_stream.server_id)
        self.assertTrue(
            LiveStreamUsage.objects.filter(live_stream=asset.live_stream).exists()
        )
        mock_create_aws_server.assert_called_with(expected_server_name, asset)

    @responses.activate
    @override_settings(LIVE_STREAM_SERVER_PROVIDER="digitalocean")
    @mock.patch("app.api.v1.views.live_stream.create_chat_room_task")
    def test_view_should_respect_store_recorded_video_false(self, mock_chat_room_task):
        responses.add(
            responses.POST,
            "https://api.digitalocean.com/v2/droplets",
            status=200,
            json=DIGITAL_OCEAN_CREATE_SERVER_RESPONSE,
        )
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={
                "title": "Test Stream",
                "store_recorded_video": False,
                "transcode_recorded_video": False,
            },
        )
        response = LiveStreamListCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )
        asset = Asset.objects.get(title="Test Stream")

        self.assertEqual(201, response.status_code)
        self.assertFalse(asset.live_stream.store_recorded_video)

    @responses.activate
    @override_settings(LIVE_STREAM_SERVER_PROVIDER="digitalocean")
    @mock.patch("app.api.v1.views.live_stream.create_chat_room_task")
    def test_view_should_force_store_recorded_video_true_when_transcoding(
        self, mock_chat_room_task
    ):
        responses.add(
            responses.POST,
            "https://api.digitalocean.com/v2/droplets",
            status=200,
            json=DIGITAL_OCEAN_CREATE_SERVER_RESPONSE,
        )
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={
                "title": "Transcoding Stream",
                "store_recorded_video": False,
                "transcode_recorded_video": True,
            },
        )
        response = LiveStreamListCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )
        asset = Asset.objects.get(title="Transcoding Stream")

        self.assertEqual(201, response.status_code)
        self.assertTrue(asset.live_stream.store_recorded_video)


class TestLiveStreamServerStatusCallback(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_api_should_not_allow_if_server_id_is_incorrect(self):
        self.create_livestream(server_id="1234")
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={"server_id": "0000", "ip_address": "127.0.0.1"},
        )
        response = LiveStreamServerStatusCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(403, response.status_code)

    @mock.patch(
        "app.api.v1.views.live_stream.LiveStreamServerStatusCallbackView.schedule_for_deletion_if_idle"
    )
    @mock.patch("app.api.v1.views.live_stream.StopDisconnectedLiveStreamTask")
    @mock.patch("app.domain.live_stream.add_ip_address_to_proxy")
    def test_api_should_store_server_ip_address_for_correct_server_id(
        self,
        mock_add_ip_address_to_proxy,
        mock_stop_live_stream,
        mock_schedule_for_deletion_if_idle,
    ):
        live_stream = self.create_livestream(server_id="1234")
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={"server_id": "1234", "ip_address": "*********", "status": "created"},
        )
        response = LiveStreamServerStatusCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(200, response.status_code)
        live_stream.refresh_from_db()
        self.assertEqual("*********", live_stream.server_ip)
        self.assertEqual(LiveStream.ServerStatus.CREATED, live_stream.server_status)
        self.assertEqual(
            f"rtmp://{live_stream.server_ip}/live", "rtmp://*********/live"
        )

    @mock.patch(
        "app.api.v1.views.live_stream.LiveStreamServerStatusCallbackView.schedule_for_deletion_if_idle"
    )
    @mock.patch("app.api.v1.views.live_stream.StopDisconnectedLiveStreamTask")
    @mock.patch(
        "app.api.v1.views.live_stream.update_live_stream_server_ip_address_to_proxy"
    )
    def test_api_should_add_ip_address_to_proxy(
        self,
        mock_add_ip_address_to_proxy,
        mock_stop_live_stream,
        mock_schedule_for_deletion_if_idle,
    ):
        self.create_livestream(server_id="1234")
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={"server_id": "1234", "ip_address": "*********", "status": "created"},
        )
        response = LiveStreamServerStatusCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(200, response.status_code)
        mock_add_ip_address_to_proxy.assert_called()

    @mock.patch("app.api.v1.views.live_stream.create_chat_room_task")
    @mock.patch("app.api.v1.views.live_stream.StopDisconnectedLiveStreamTask")
    @mock.patch(
        "app.api.v1.views.live_stream.LiveStreamServerStatusCallbackView.schedule_for_deletion_if_idle"
    )
    def test_live_stream_server_status_should_be_updated_for_ready_status(
        self,
        mock_chat_room_task,
        mock_stop_live_task,
        mock_schedule_for_deletion_if_idle,
    ):
        live_stream = self.create_livestream(server_id="1234")
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={"server_id": "1234", "ip_address": "*********", "status": "ready"},
        )
        response = LiveStreamServerStatusCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(200, response.status_code)
        live_stream.refresh_from_db()
        self.assertEqual(LiveStream.ServerStatus.AVAILABLE, live_stream.server_status)

    @mock.patch("app.api.v1.views.live_stream.StopDisconnectedLiveStreamTask")
    @mock.patch(
        "app.api.v1.views.live_stream.LiveStreamServerStatusCallbackView.schedule_for_deletion_if_idle"
    )
    @mock.patch("app.models.asset.Asset.notify_webhook")
    def test_live_stream_details_should_be_notified_to_webhook(
        self,
        mock_notify_webhook,
        mock_stop_live_stream,
        mock_schedule_for_deletion_if_idle,
    ):
        self.create_livestream(server_id="1234")
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={"server_id": "1234", "ip_address": "*********", "status": "created"},
        )
        response = LiveStreamServerStatusCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(200, response.status_code)
        mock_notify_webhook.assert_called()


class TestLiveStreamOnPublishCallbackView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_api_should_return_200_for_correct_request(self):
        live_stream = self.create_livestream(
            server_id="1234", stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd"
        )
        request = self.get_request(
            "/",
            method="POST",
            data={
                "server_id": "1234",
                "ip_address": "*********",
                "stream_key": live_stream.stream_key,
            },
        )
        response = LiveStreamOnPublishCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(200, response.status_code)

    def test_api_should_return_resolutions(self):
        live_stream = self.create_livestream(
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
            resolutions=[LiveStream.Resolutions._240p, LiveStream.Resolutions._480p],
        )
        request = self.get_request(
            "/",
            method="POST",
            data={
                "server_id": "1234",
                "ip_address": "*********",
                "stream_key": live_stream.stream_key,
            },
        )
        response = LiveStreamOnPublishCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(["240p", "480p"], response.data["resolutions"])

    def test_api_should_return_drm_details(self):
        live_stream = self.create_livestream(
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
            resolutions=[LiveStream.Resolutions._240p, LiveStream.Resolutions._480p],
            enable_drm=True,
        )
        fairplay_data = generate_and_store_fairplay_encryption_keys(
            live_stream.uuid.hex, live_stream.organization
        )
        request = self.get_request(
            "/",
            method="POST",
            data={
                "server_id": "1234",
                "ip_address": "*********",
                "stream_key": live_stream.stream_key,
            },
        )
        response = LiveStreamOnPublishCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        expected_drm_data = {
            "fairplay": {
                "key": fairplay_data["key"],
                "uri": fairplay_data["uri"],
                "iv": fairplay_data["iv"],
                "content_id": live_stream.uuid.hex,
            },
            "widevine": {
                "aes_signing_iv": live_stream.organization.drm_aes_signing_iv,
                "aes_signing_key": live_stream.organization.drm_aes_signing_key,
                "content_id": live_stream.uuid.hex,
                "key_server_url": f"https://license.tpstreams.com/api/v1/{live_stream.organization.uuid}/widevine_key/",
                "signer": settings.DRM_SIGNER,
            },
        }

        expected_drm_data_json = json.dumps(expected_drm_data, sort_keys=True)
        response_data_json = json.dumps(response.data["drm"], sort_keys=True)
        self.assertEqual(expected_drm_data_json, response_data_json)

    def test_api_should_create_encryption_key_for_drm_enabled_stream_if_not_exists(
        self,
    ):
        live_stream = self.create_livestream(
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
            resolutions=[LiveStream.Resolutions._240p, LiveStream.Resolutions._480p],
            enable_drm=True,
        )
        EncryptionKey.objects.filter(content_id=live_stream.uuid).delete()

        request = self.get_request(
            "/",
            method="POST",
            data={
                "server_id": "1234",
                "ip_address": "*********",
                "stream_key": live_stream.stream_key,
            },
        )
        response = LiveStreamOnPublishCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertIn("drm", response.data)
        self.assertIn("fairplay", response.data["drm"])
        self.assertIn("widevine", response.data["drm"])

        encryption_key = EncryptionKey.objects.get(content_id=live_stream.uuid)
        self.assertIsNotNone(encryption_key)
        self.assertIsNotNone(encryption_key.fairplay_encryption_key_data)

    def test_api_should_not_send_drm_details_if_enable_drm_is_false(self):
        live_stream = self.create_livestream(
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
            resolutions=[LiveStream.Resolutions._240p, LiveStream.Resolutions._480p],
            enable_drm=False,
        )
        generate_and_store_fairplay_encryption_keys(
            live_stream.uuid.hex, live_stream.organization
        )
        request = self.get_request(
            "/",
            method="POST",
            data={
                "server_id": "1234",
                "ip_address": "*********",
                "stream_key": live_stream.stream_key,
            },
        )
        response = LiveStreamOnPublishCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual({}, response.data["drm"])

    def test_api_should_store_live_stream_event_for_correct_request(self):
        live_stream = self.create_livestream(
            server_id="1234", stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd"
        )
        request = self.get_request(
            "/",
            method="POST",
            data={
                "server_id": "1234",
                "ip_address": "*********",
                "stream_key": live_stream.stream_key,
            },
        )
        LiveStreamOnPublishCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(
            1, live_stream.events.filter(type=LiveStreamEvent.Type.ON_PUBISH).count()
        )

    def test_api_should_send_403_if_server_id_is_not_provided(self):
        live_stream = self.create_livestream(
            server_id="1234", stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd"
        )
        request = self.get_request(
            "/",
            method="POST",
            data={"ip_address": "*********", "stream_key": live_stream.stream_key},
        )
        response = LiveStreamOnPublishCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(403, response.status_code)

    def test_api_should_send_403_for_incorrect_server_id(self):
        live_stream = self.create_livestream(
            server_id="1234", stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd"
        )
        request = self.get_request(
            "/",
            method="POST",
            data={
                "server_id": "4321",
                "ip_address": "*********",
                "stream_key": live_stream.stream_key,
            },
        )
        response = LiveStreamOnPublishCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(403, response.status_code)

    def test_api_should_send_404_if_stream_key_is_not_provided(self):
        self.create_livestream(
            server_id="1234", stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd"
        )
        request = self.get_request(
            "/",
            method="POST",
            data={"ip_address": "*********", "server_id": "1234"},
        )
        response = LiveStreamOnPublishCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(404, response.status_code)


class TestLiveStreamOnUnPublishCallbackView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @mock.patch("app.api.v1.views.live_stream.StopDisconnectedLiveStreamTask")
    @mock.patch("app.api.v1.views.live_stream.schedule_stop_disconnected_live_stream")
    def test_api_should_return_200_for_correct_request(
        self, mock_schedule_stop_disconnected_live_stream, mock_stop_live_stream
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        request = self.get_request(
            "/",
            method="POST",
            data={
                "server_id": "1234",
                "ip_address": "*********",
                "stream_key": live_stream.stream_key,
            },
        )
        response = LiveStreamOnUnPublishCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(200, response.status_code)

    @mock.patch("app.api.v1.views.live_stream.StopDisconnectedLiveStreamTask")
    @mock.patch("app.api.v1.views.live_stream.schedule_stop_disconnected_live_stream")
    def test_api_should_store_live_stream_event_for_correct_request(
        self, mock_schedule_stop_disconnected_live_stream, mock_stop_live_stream
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        request = self.get_request(
            "/",
            method="POST",
            data={
                "server_id": "1234",
                "ip_address": "*********",
                "stream_key": live_stream.stream_key,
            },
        )
        LiveStreamOnUnPublishCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        live_stream.refresh_from_db()
        self.assertEqual(
            1,
            live_stream.events.filter(type=LiveStreamEvent.Type.ON_PUBISH_DONE).count(),
        )
        self.assertEqual(LiveStream.Status.DISCONNECTED, live_stream.status)

    def test_api_should_send_403_if_server_id_is_not_provided(self):
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        request = self.get_request(
            "/",
            method="POST",
            data={"ip_address": "*********", "stream_key": live_stream.stream_key},
        )
        response = LiveStreamOnUnPublishCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(403, response.status_code)

    def test_api_should_send_403_for_incorrect_server_id(self):
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        request = self.get_request(
            "/",
            method="POST",
            data={
                "server_id": "4321",
                "ip_address": "*********",
                "stream_key": live_stream.stream_key,
            },
        )
        response = LiveStreamOnUnPublishCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(403, response.status_code)

    def test_api_should_send_404_if_stream_key_is_not_provided(self):
        self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        request = self.get_request(
            "/",
            method="POST",
            data={"ip_address": "*********", "server_id": "1234"},
        )
        response = LiveStreamOnUnPublishCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(404, response.status_code)


class TestStopLiveStreamView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_api_should_allow_only_authenticated_user(self):
        self.assertTrue(
            permissions.IsAuthenticated in StopLiveStreamView.permission_classes
        )

    def test_api_should_have_organization_access_permission(self):
        self.assertTrue(HasOrganizationAccess in StopLiveStreamView.permission_classes)

    def test_api_should_allow_token_based_authentication(self):
        self.assertTrue(
            TokenAuthentication in StopLiveStreamView.authentication_classes
        )

    def test_view_works_only_on_post_method(self):
        livestream_view = StopLiveStreamView()

        self.assertEqual(["POST", "OPTIONS"], livestream_view.allowed_methods)

    def test_view_should_return_404_for_video_asset(self):
        video = self.create_video()
        request = self.get_request(
            "/", method="POST", user=self.organization.created_by
        )
        response = StopLiveStreamView.as_view()(
            request, organization_id=self.organization.uuid, asset_id=video.asset.uuid
        )

        self.assertEqual(404, response.status_code)

    @mock.patch("app.api.v1.views.live_stream.stop_live_stream")
    def test_view_should_call_stop_live_stream_for_proper_request(
        self, mock_stop_live_stream
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        request = self.get_request(
            "/", method="POST", user=self.organization.created_by
        )
        response = StopLiveStreamView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_id=live_stream.asset.uuid,
        )

        self.assertEqual(200, response.status_code)
        mock_stop_live_stream.assert_called()

    @mock.patch("app.api.v1.views.live_stream.stop_live_stream")
    def test_view_should_return_204_for_inactive_livestreams(self, mock_stop_live_stream):
        inactive_statuses = [
            LiveStream.Status.COMPLETED,
            LiveStream.Status.STOPPED,
            LiveStream.Status.ERROR,
        ]
        for status in inactive_statuses:
            with self.subTest(status=status.name):
                live_stream = self.create_livestream(
                    status=status,
                    server_id="1234",
                    stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
                )
                request = self.get_request(
                    "/", method="POST", user=self.organization.created_by
                )
                response = StopLiveStreamView.as_view()(
                    request,
                    organization_id=self.organization.uuid,
                    asset_id=live_stream.asset.uuid,
                )

                self.assertEqual(204, response.status_code)
                self.assertFalse(mock_stop_live_stream.called)
                mock_stop_live_stream.reset_mock()

    @mock.patch("app.api.v1.views.live_stream.stop_live_stream")
    def test_termination_cause_should_return_user_initiated_if_the_user_stops_live_from_api(
        self, mock_stop_live
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_ip="127.0.0.1",
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        request = self.get_request(
            "/", method="POST", user=self.organization.created_by
        )
        StopLiveStreamView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_id=live_stream.asset.uuid,
        )
        live_stream.refresh_from_db()

        self.assertEqual(live_stream.get_termination_cause_display(), "User Initiated")


class TestLiveStreamOnUploadCompleteCallbackView(
    OrganizationMixin, AssetMixin, TestCase
):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_404_should_be_returned_if_live_stream_is_not_in_recording_status(self):
        live_stream = self.create_livestream(
            status=LiveStream.Status.COMPLETED,
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        request = self.get_request(
            "/",
            method="POST",
            data={
                "server_id": "1234",
                "stream_key": live_stream.stream_key,
            },
        )
        response = LiveStreamOnUploadCompleteCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(404, response.status_code)

    @mock.patch("app.tasks.live_stream.UploadLiveStreamRecordingTask.apply_async")
    @mock.patch("app.tasks.live_stream.DeleteLiveStreamFilesTask.schedule")
    def test_upload_task_is_triggered_when_store_recorded_video_is_true(
        self, mock_delete_task, mock_upload_task
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.STOPPED,
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-efgh",
            store_recorded_video=True,
        )

        mock_id = uuid.uuid4()
        mock_upload_task.return_value.task_id = mock_id
        request = self.get_request(
            "/",
            method="POST",
            data={
                "server_id": "1234",
                "stream_key": live_stream.stream_key,
            },
        )
        response = LiveStreamOnUploadCompleteCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        live_stream.refresh_from_db()

        self.assertTrue(mock_upload_task.called)
        self.assertEqual(live_stream.upload_recording_task_id, mock_id)
        self.assertEqual(200, response.status_code)

    @mock.patch("app.api.v1.views.live_stream.delete_live_stream_server")
    @mock.patch("app.tasks.live_stream.DeleteLiveStreamFilesTask.schedule")
    def test_delete_server_and_update_status_is_called_when_store_recorded_video_is_false(
        self, mock_task_delete, mock_delete
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.STOPPED,
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-ijkl",
            store_recorded_video=False,
            transcode_recorded_video=False,
        )
        live_stream.refresh_from_db()
        request = self.get_request(
            "/",
            method="POST",
            data={
                "server_id": "1234",
                "stream_key": live_stream.stream_key,
            },
        )
        response = LiveStreamOnUploadCompleteCallbackView.as_view()(
            request, organization_id=self.organization.uuid
        )

        live_stream.refresh_from_db()

        mock_delete.assert_called()
        self.assertEqual(live_stream.status, LiveStream.Status.COMPLETED)
        self.assertEqual(200, response.status_code)


class TestLiveStreamServerCreateView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.asset = self.create_asset()

    @mock.patch("app.api.v1.views.live_stream.create_remote_live_stream_server")
    @mock.patch("app.api.v1.views.live_stream.store_live_stream_server_details")
    def test_create_live_stream_server_view_should_create_server_if_not_created(
        self, mock_create_server, mock_store_details
    ):
        self.create_livestream(
            asset=self.asset, server_status=LiveStream.ServerStatus.NOT_CREATED
        )

        request = self.get_request(
            "/", method="POST", user=self.organization.created_by
        )
        response = CreateLiveStreamServerView.as_view()(
            request, organization_id=self.organization.uuid, asset_id=self.asset.uuid
        )

        self.assertEqual(response.status_code, 200)

        self.assertTrue(mock_store_details.called)
        self.assertTrue(mock_create_server.called)

    @mock.patch("app.api.v1.views.live_stream.create_remote_live_stream_server")
    @mock.patch("app.api.v1.views.live_stream.store_live_stream_server_details")
    def test_create_live_stream_server_view_should_not_create_server_if_created(
        self, mock_server, mock_store
    ):
        self.create_livestream(
            asset=self.asset, server_status=LiveStream.ServerStatus.CREATED
        )

        request = self.get_request(
            "/", method="POST", user=self.organization.created_by
        )
        response = CreateLiveStreamServerView.as_view()(
            request, organization_id=self.organization.uuid, asset_id=self.asset.uuid
        )

        self.assertEqual(response.status_code, 200)

        self.assertEqual(
            self.asset.live_stream.server_status, LiveStream.ServerStatus.CREATED
        )
        self.assertFalse(mock_store.called)
        self.assertFalse(mock_server.called)


class TestLiveStreamListView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_live_stream_list_view_should_return_assets_with_live_streams(self):
        live_stream_1 = self.create_livestream()
        asset_with_live_stream_1 = live_stream_1.asset
        asset_without_live_stream = self.create_asset()

        request = self.get_request("/", method="GET", user=self.organization.created_by)
        response = LiveStreamListCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )
        response_data = response.data
        expected_data = AssetSerializer([asset_with_live_stream_1], many=True).data
        serialized_asset_without_live_stream = AssetSerializer(
            asset_without_live_stream
        ).data

        self.assertEqual(response.status_code, 200)
        self.assertEqual(expected_data, response_data["results"])
        self.assertNotIn(
            serialized_asset_without_live_stream,
            response_data["results"],
            msg="Asset with live stream should not be present in the response",
        )
