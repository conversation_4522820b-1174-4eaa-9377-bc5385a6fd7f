from django_multitenant.utils import set_current_tenant
from knox.auth import TokenAuthentication
from rest_framework import permissions

from app.api.v1.permissions import HasOrganizationAccess
from app.api.v1.views import WebhookViewSet
from app.models import Webhook
from tests import TestCase
from tests.mixins import OrganizationMixin


class TestWebhookViewSet(OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_api_should_allow_only_authenticated_user(self):
        self.assertTrue(
            permissions.IsAuthenticated in WebhookViewSet.permission_classes
        )

    def test_api_should_have_organization_access_permission(self):
        self.assertTrue(HasOrganizationAccess in WebhookViewSet.permission_classes)

    def test_api_should_allow_token_based_authentication(self):
        self.assertTrue(TokenAuthentication in WebhookViewSet.authentication_classes)

    def test_webhook_should_be_created_for_proper_data(self):
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={"url": "http://example.com"},
        )
        response = WebhookViewSet.as_view({"post": "create"})(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(201, response.status_code)
        self.assertTrue(
            Webhook.objects.get(url="http://example.com").uuid, response.data["id"]
        )

    def test_organization_and_user_should_be_populated_on_webhook_creation(self):
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={"url": "http://example.com"},
        )
        WebhookViewSet.as_view({"post": "create"})(
            request, organization_id=self.organization.uuid
        )

        webhook = Webhook.objects.get(url="http://example.com")
        self.assertEqual(self.organization, webhook.organization)
        self.assertEqual(self.organization.created_by, webhook.created_by)
