import base64
import json
from unittest import mock

from django_multitenant.utils import set_current_tenant

from app.api.v1.views.drm import (
    DRMLicenseViewV2,
    GenerateFairplayEncryptionKeyView,
    GenerateWidevineEncryptionKeyView,
)
from app.models import DRMLice<PERSON><PERSON>og, Encry<PERSON><PERSON><PERSON>
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin

SAMPLE_AES_KEY = "7cc93d7bb826dd1fbd81cdc78b23c25ace67dd40f8aaca3cfc3f10cfa38d223a"
SAMPLE_AES_IV = "bc76511133ce755e4ecb3e82eba3c5d5"


class TestGenerateWidevineEncryptionKeyView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization(
            drm_aes_signing_key=SAMPLE_AES_KEY, drm_aes_signing_iv=SAMPLE_AES_IV
        )
        set_current_tenant(self.organization)

    @property
    def data(self):
        return {
            "request": "eyJjb250ZW50X2lkIjoiQk5WSTAvWHF4MlBWRnhNc0t6VVlTUT09IiwicG9saWN5IjoiIiwidHJhY2tzIjpbeyJ0eXBlIj"
            "oiU0QifSx7InR5cGUiOiJIRCJ9LHsidHlwZSI6IlVIRDEifSx7InR5cGUiOiJVSEQyIn0seyJ0eXBlIjoiQVVESU8ifV0s"
            "ImRybV90eXBlcyI6WyJXSURFVklORSJdLCJwcm90ZWN0aW9uX3NjaGVtZSI6IkNFTkMifQ==",
            "signature": "Qn3QQQl9YxYkFWuH/ueQodTPn7ZjpZbRVXWZsnpgdL0=",
            "signer": "testpress",
        }

    @property
    def encryption_keys(self):
        return {
            "response": "eyJzdGF0dXMiOiJPSyIsImRybSI6W3sidHlwZSI6IldJREVWSU5FIiwic3lzdGVtX2lkIjoiZWRlZjhiYTk3OWQ2NGFjZ"
            "WEzYzgyN2RjZDUxZDIxZWQifV0sInRyYWNrcyI6W3sidHlwZSI6IlNEIiwia2V5X2lkIjoiMElmKzFCcW1XK1d4ME9qST"
            "JWeFpZZz09Iiwia2V5Ijoic1ZHMEcvTTBzSXRibElSd2tPRk5wdz09IiwicHNzaCI6W3siZHJtX3R5cGUiOiJXSURFVkl"
            "ORSIsImRhdGEiOiJJaEQzQzg1QWxQMUdFcXVzWU5tQW5Gc01TT1BjbFpzRyIsImJveGVzIjoiQUFBQU9IQnpjMmdBQUFB"
            "QTdlK0xxWG5XU3M2anlDZmMxUjBoN1FBQUFCZ2lFUGNMemtDVS9VWVNxNnhnMllDY1d3eEk0OXlWbXdZPSJ9XSwiZW50a"
            "XRsZWRfa2V5IjpbXSwiY29udGVudF9ncm91cHMiOltdLCJmZWF0dXJlIjoiIn0seyJ0eXBlIjoiSEQiLCJrZXlfaWQiOi"
            "JzZk5NNlR2RVhPeWdKYzNEQkN1N0lBPT0iLCJrZXkiOiJOMHBlTklNK2o3VnRIVjlGQ01TTHh3PT0iLCJwc3NoIjpbeyJ"
            "kcm1fdHlwZSI6IldJREVWSU5FIiwiZGF0YSI6IkloRDNDODVBbFAxR0VxdXNZTm1BbkZzTVNPUGNsWnNHIiwiYm94ZXMi"
            "OiJBQUFBT0hCemMyZ0FBQUFBN2UrTHFYbldTczZqeUNmYzFSMGg3UUFBQUJnaUVQY0x6a0NVL1VZU3E2eGcyWUNjV3d4S"
            "TQ5eVZtd1k9In1dLCJlbnRpdGxlZF9rZXkiOltdLCJjb250ZW50X2dyb3VwcyI6W10sImZlYXR1cmUiOiIifSx7InR5cG"
            "UiOiJVSEQxIiwia2V5X2lkIjoiVlhMWUdaWElYaWVDR2pnb0dQbEc5UT09Iiwia2V5IjoiWjB2M0RheHI3SDZWUW9aZ1V"
            "uN3VYUT09IiwicHNzaCI6W3siZHJtX3R5cGUiOiJXSURFVklORSIsImRhdGEiOiJJaEQzQzg1QWxQMUdFcXVzWU5tQW5G"
            "c01TT1BjbFpzRyIsImJveGVzIjoiQUFBQU9IQnpjMmdBQUFBQTdlK0xxWG5XU3M2anlDZmMxUjBoN1FBQUFCZ2lFUGNMe"
            "mtDVS9VWVNxNnhnMllDY1d3eEk0OXlWbXdZPSJ9XSwiZW50aXRsZWRfa2V5IjpbXSwiY29udGVudF9ncm91cHMiOltdLC"
            "JmZWF0dXJlIjoiIn0seyJ0eXBlIjoiVUhEMiIsImtleV9pZCI6ImV0a3NKUU5XVVBxQjU1V28zQ3V0c1E9PSIsImtleSI"
            "6ImNYeUtLK0JSS2pNMDhqSjBYSEtVOXc9PSIsInBzc2giOlt7ImRybV90eXBlIjoiV0lERVZJTkUiLCJkYXRhIjoiSWhE"
            "M0M4NUFsUDFHRXF1c1lObUFuRnNNU09QY2xac0ciLCJib3hlcyI6IkFBQUFPSEJ6YzJnQUFBQUE3ZStMcVhuV1NzNmp5Q"
            "2ZjMVIwaDdRQUFBQmdpRVBjTHprQ1UvVVlTcTZ4ZzJZQ2NXd3hJNDl5Vm13WT0ifV0sImVudGl0bGVkX2tleSI6W10sIm"
            "NvbnRlbnRfZ3JvdXBzIjpbXSwiZmVhdHVyZSI6IiJ9LHsidHlwZSI6IkFVRElPIiwia2V5X2lkIjoiU0tLbzU5dW9Ycyt"
            "NaGFqcnRVQ3EvQT09Iiwia2V5IjoiUlJBd0tQWmt5dFdSTnVKaGsyaWVFQT09IiwicHNzaCI6W3siZHJtX3R5cGUiOiJX"
            "SURFVklORSIsImRhdGEiOiJJaEQzQzg1QWxQMUdFcXVzWU5tQW5Gc01TT1BjbFpzRyIsImJveGVzIjoiQUFBQU9IQnpjM"
            "mdBQUFBQTdlK0xxWG5XU3M2anlDZmMxUjBoN1FBQUFCZ2lFUGNMemtDVS9VWVNxNnhnMllDY1d3eEk0OXlWbXdZPSJ9XS"
            "wiZW50aXRsZWRfa2V5IjpbXSwiY29udGVudF9ncm91cHMiOltdLCJmZWF0dXJlIjoiIn1dfQ=="
        }

    def test_api_should_return_200_for_valid_signature(self):
        request = self.get_request("/", method="POST", data=self.data)
        response = GenerateWidevineEncryptionKeyView.as_view()(
            request,
            organization_id=self.organization.uuid,
        )

        self.assertEqual(200, response.status_code)

    @mock.patch("app.domain.drm.encryption.streams.generate_widevine_encryption_keys")
    def test_api_should_return_fairplay_key_for_valid_signature(
        self, widevine_encryption_key_mock
    ):
        widevine_encryption_key_mock.return_value = self.encryption_keys
        request = self.get_request("/", method="POST", data=self.data)
        response = GenerateWidevineEncryptionKeyView.as_view()(
            request,
            organization_id=self.organization.uuid,
        )

        self.assertEqual(response.data, self.encryption_keys)

    def test_api_should_return_403_for_incorrect_signature(self):
        data = self.data
        data["signature"] = "NsBcxxchrA7tFw"
        request = self.get_request("/", method="POST", data=data)
        response = GenerateWidevineEncryptionKeyView.as_view()(
            request,
            organization_id=self.organization.uuid,
        )

        self.assertEqual(403, response.status_code)

    @mock.patch("app.domain.drm.encryption.streams.generate_widevine_encryption_keys")
    def test_keys_should_be_stored_in_db_for_valid_data(
        self, widevine_encryption_key_mock
    ):
        widevine_encryption_key_mock.return_value = self.encryption_keys
        request = self.get_request("/", method="POST", data=self.data)
        GenerateWidevineEncryptionKeyView.as_view()(
            request,
            organization_id=self.organization.uuid,
        )

        encryption_keys = base64.b64decode(
            self.encryption_keys.get("response")
        ).decode()
        self.assertTrue(
            EncryptionKey.objects.filter(
                content_id="04d548d3f5eac763d517132c2b351849",
                widevine_encryption_keys=encryption_keys,
            ).exists()
        )

    def test_existing_keys_should_returned_if_already_exists(self):
        encryption_keys = base64.b64decode(
            self.encryption_keys.get("response")
        ).decode()
        EncryptionKey.objects.create(
            organization=self.organization,
            content_id="04d548d3f5eac763d517132c2b351849",
            widevine_encryption_keys=encryption_keys,
        )
        request = self.get_request("/", method="POST", data=self.data)
        response = GenerateWidevineEncryptionKeyView.as_view()(
            request,
            organization_id=self.organization.uuid,
        )

        self.assertEqual(response.data, self.encryption_keys)


class TestGenerateFairplayEncryptionKeyView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization(
            drm_aes_signing_key=SAMPLE_AES_KEY, drm_aes_signing_iv=SAMPLE_AES_IV
        )
        set_current_tenant(self.organization)

    @property
    def data(self):
        content_id = json.dumps(
            {"content_id": "cf6c30dd50c1492e82344ea3e139da1d"}, separators=(",", ":")
        ).encode()
        return {
            "request": base64.b64encode(content_id).decode(),
            "signature": "nAWlQFeDZf2YAS/8lYxuM6AT30OkwmwNUg6HGjMI4CA=",
        }

    @property
    def fairplay_key_data(self):
        return {
            "iv": "f70bce4094fd4612abac60d9809c5b0c",
            "key": "3ab60de900d64edf9cb25a76f81794e6",
            "uri": "skd://e5573f8bb8ac47ea839a65beae73263d",
        }

    def test_api_should_return_200_for_valid_signature(self):
        request = self.get_request("/", method="POST", data=self.data)
        response = GenerateFairplayEncryptionKeyView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(200, response.status_code)

    @mock.patch("app.domain.drm.encryption.streams.generate_fairplay_encryption_keys")
    def test_api_should_return_fairplay_key_for_valid_signature(
        self, fairplay_encryption_key_mock
    ):
        fairplay_encryption_key_mock.return_value = self.fairplay_key_data
        request = self.get_request("/", method="POST", data=self.data)
        response = GenerateFairplayEncryptionKeyView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(
            response.data,
            {
                "iv": "f70bce4094fd4612abac60d9809c5b0c",
                "key": "3ab60de900d64edf9cb25a76f81794e6",
                "uri": "skd://e5573f8bb8ac47ea839a65beae73263d",
            },
        )

    def test_api_should_return_403_for_incorrect_signature(self):
        data = self.data
        data["signature"] = "NsBcxxchrA7tFw"
        request = self.get_request("/", method="POST", data=data)
        response = GenerateFairplayEncryptionKeyView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(403, response.status_code)

    def test_api_should_return_400_if_content_id_is_missing(self):
        data = {
            "request": base64.b64encode(
                json.dumps({"content_id": "abcde"}).encode()
            ).decode(),
            "signature": "9YKeqTTPtCYqwxtLAFndi75E7IGVg1yAbSk9EhlQAm8=",
        }
        request = self.get_request("/", method="POST", data=data)
        response = GenerateFairplayEncryptionKeyView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(400, response.status_code)
        self.assertEqual(
            "Invalid Content ID. Content ID should be a valid uuid",
            response.data[0],
        )

    def test_api_should_return_400_if_content_id_is_invalid(self):
        data = {
            "request": "",
            "signature": "XrC9zBmlkNuC6+CLE7caGMdlSV/Xa3Dge9FyjWDFr8g=",
        }
        request = self.get_request("/", method="POST", data=data)
        response = GenerateFairplayEncryptionKeyView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(400, response.status_code)
        self.assertEqual("Content ID is missing", response.data[0])

    @mock.patch("app.domain.drm.encryption.streams.generate_fairplay_encryption_keys")
    def test_keys_should_be_stored_in_db_for_valid_data(
        self, fairplay_encryption_key_mock
    ):
        fairplay_encryption_key_mock.return_value = self.fairplay_key_data
        request = self.get_request("/", method="POST", data=self.data)
        GenerateFairplayEncryptionKeyView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertTrue(
            EncryptionKey.objects.filter(
                content_id="cf6c30dd50c1492e82344ea3e139da1d",
                fairplay_encryption_key_data=self.fairplay_key_data,
            ).exists()
        )

    @mock.patch("app.domain.drm.encryption.streams.generate_fairplay_encryption_keys")
    def test_keys_should_be_stored_in_db_for_valid_data_even_if_widevine_keys_are_present(
        self, fairplay_encryption_key_mock
    ):
        EncryptionKey.objects.create(
            organization=self.organization,
            content_id="cf6c30dd50c1492e82344ea3e139da1d",
        )
        fairplay_encryption_key_mock.return_value = self.fairplay_key_data
        request = self.get_request("/", method="POST", data=self.data)
        GenerateFairplayEncryptionKeyView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertTrue(
            EncryptionKey.objects.filter(
                content_id="cf6c30dd50c1492e82344ea3e139da1d",
                fairplay_encryption_key_data=self.fairplay_key_data,
            ).exists()
        )

    def test_existing_keys_should_returned_if_already_exists(self):
        fairplay_key_data = {"iv": "abcde", "key": "efghi", "uri": "skd://ijklm"}
        EncryptionKey.objects.create(
            organization=self.organization,
            content_id="cf6c30dd50c1492e82344ea3e139da1d",
            fairplay_encryption_key_data=fairplay_key_data,
        )
        request = self.get_request("/", method="POST", data=self.data)
        response = GenerateFairplayEncryptionKeyView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(response.data, fairplay_key_data)


class TestDRMLicenseViewV2(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization(
            drm_aes_signing_key=SAMPLE_AES_KEY, drm_aes_signing_iv=SAMPLE_AES_IV
        )
        set_current_tenant(self.organization)

    def get_data(self, content_id="cf6c30dd50c1492e82344ea3e139da1d", signature=None):
        content_data = {
            "content_id": content_id,
            "drm_type": "widevine",
            "download": False,
        }
        if content_id is None:
            del content_data["content_id"]
        d = {
            "content_data": base64.b64encode(
                json.dumps(content_data).encode()
            ).decode(),
            "signature": signature or "svG1A53jMfOpX402GrhV6XSUtOZ8ejJg3LoY7vT7ttU=",
        }
        return base64.b64encode(json.dumps(d).encode()).decode()

    @mock.patch("app.api.v1.views.drm.get_tpstreams_widevine_license")
    def test_api_should_return_200_for_valid_signature(
        self, mock_get_tpstreams_license
    ):
        mock_get_tpstreams_license.return_value = "Hello, world"
        request = self.get_request(f"/?data={self.get_data()}", method="POST")
        response = DRMLicenseViewV2.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(b"Hello, world", response.content)

    def test_api_should_return_403_for_incorrect_signature(self):
        data = self.get_data(signature="abcd")
        request = self.get_request(f"/?data={data}", method="POST")
        response = DRMLicenseViewV2.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(403, response.status_code)

    @mock.patch("app.api.v1.views.drm.DRMLicenseViewV2.get_license_data")
    def test_api_should_return_400_if_license_data_has_error(self, mock_get_license_data):
        mock_get_license_data.return_value = {
            "status": "ERROR",
            "status_message": "device-certificate-revoked:",
        }

        data = self.get_data()
        request = self.get_request(f"/?data={data}", method="POST")

        response = DRMLicenseViewV2.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(400, response.status_code)

    def test_api_should_return_404_if_content_id_is_missing(self):
        data = self.get_data(
            content_id=None, signature="ti5D2ny1pCjGnN+Cl7faKvYp3kGFoPi3vtCkW4DKDhk="
        )
        request = self.get_request(f"/?data={data}", method="POST")
        response = DRMLicenseViewV2.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(404, response.status_code)

    @mock.patch("app.api.v1.views.drm.get_tpstreams_widevine_license")
    def test_drm_licensee_should_be_tracked_for_valid_data(
        self, mock_get_tpstreams_license
    ):
        mock_get_tpstreams_license.return_value = "Hello, world"
        request = self.get_request(f"/?data={self.get_data()}", method="POST")
        response = DRMLicenseViewV2.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(200, response.status_code)
        self.assertTrue(
            DRMLicenseLog.objects.filter(
                content_id="cf6c30dd50c1492e82344ea3e139da1d"
            ).exists()
        )

    @mock.patch("app.api.v1.views.drm.get_tpstreams_widevine_license")
    def test_should_return_widevine_license_with_given_specs(
        self, get_tpstreams_license_mock
    ):
        widevine_license_specs = {
            "content_key_specs": [
                {
                    "track_type": "SD",
                    "security_level": 1,
                    "required_output_protection": {"hdcp": "HDCP_V1"},
                    'rental_duration_seconds': 1296000, 'license_duration_seconds': 1296000,
                },
                {
                    "track_type": "HD",
                    "security_level": 1,
                    "required_output_protection": {"hdcp": "HDCP_V1"},
                    'rental_duration_seconds': 1296000, 'license_duration_seconds': 1296000,
                },
                {
                    "track_type": "UHD1",
                    "security_level": 1,
                    "required_output_protection": {"hdcp": "HDCP_V1"},
                    'rental_duration_seconds': 1296000, 'license_duration_seconds': 1296000,
                },
                {
                    "track_type": "UHD2",
                    "security_level": 1,
                    "required_output_protection": {"hdcp": "HDCP_V1"},
                    'rental_duration_seconds': 1296000, 'license_duration_seconds': 1296000,
                },
                {
                    "track_type": "AUDIO",
                    "security_level": 1,
                    "required_output_protection": {"hdcp": "HDCP_V1"},
                    'rental_duration_seconds': 1296000, 'license_duration_seconds': 1296000,
                },
            ],
            "can_persist": False,
        }
        body = {
            "player_payload": base64.b64encode(b"dummy").decode("utf-8"),
            "widevine": widevine_license_specs,
        }

        request = self.get_request(
            f"/?data={self.get_data()}",
            method="POST",
            data=body,
            content_type="application/json",
        )
        response = DRMLicenseViewV2.as_view()(
            request, organization_id=self.organization.uuid
        )
        widevine_license_specs["can_persist"] = False
        self.assertEqual(200, response.status_code)
        get_tpstreams_license_mock.assert_called_with(
            "cf6c30dd50c1492e82344ea3e139da1d", b"dummy", widevine_license_specs
        )
