from unittest import mock

from django_multitenant.utils import set_current_tenant
from knox.auth import TokenAuthentication
from rest_framework import permissions

from app.api.v1.permissions import HasOrganizationAccess
from app.api.v1.views.transcoding_job import (
    TranscodingJobViewSet,
    UpdateTranscodingJobStatusView,
)
from app.models import TranscodingJob
from tests import TestCase
from tests.mixins import OrganizationMixin
from tests.mixins.transcoding_job import TranscodingJobMixin


class TestTranscodingJobViewSet(OrganizationMixin, TranscodingJobMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_api_should_allow_only_authenticated_user(self):
        self.assertTrue(
            permissions.IsAuthenticated in TranscodingJobViewSet.permission_classes
        )

    def test_api_should_have_organization_access_permission(self):
        self.assertTrue(
            HasOrganizationAccess in TranscodingJobViewSet.permission_classes
        )

    def test_api_should_allow_token_based_authentication(self):
        self.assertTrue(
            TokenAuthentication in TranscodingJobViewSet.authentication_classes
        )

    @mock.patch("app.domain.transcoder.LumberjackJobTranscoder.start")
    @mock.patch("app.api.v1.serializers.transcoding_job.is_valid_url")
    def test_transcoding_job_should_be_created_for_proper_data(
        self, mock_is_valid_url, mock_start_transcoding
    ):
        mock_is_valid_url.return_value = True
        mock_start_transcoding.return_value = {
            "id": "04e721d9-04e1-4df2-ad73-8f4391dbe9c9"
        }
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={
                "resolutions": ["240p", "480p"],
                "input_url": "https://sample-videos.com/video123/mp4/720/big_buck_bunny_720p_1mb.mp4",
                "output_path": "s3://abc/video/?access_key=abc&secret_key=123&region=ap-south-1",
            },
        )
        response = TranscodingJobViewSet.as_view({"post": "create"})(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(201, response.status_code)
        transcoding_job = TranscodingJob.objects.get(
            input_url="https://sample-videos.com/video123/mp4/720/big_buck_bunny_720p_1mb.mp4"
        )
        self.assertTrue(transcoding_job.uuid, response.data["id"])
        self.assertEqual(
            transcoding_job.resolutions,
            [TranscodingJob.Resolutions._240p, TranscodingJob.Resolutions._480p],
        )

    def test_error_should_be_thrown_for_input_url_without_query_params(self):
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={
                "resolutions": ["240p", "480p"],
                "input_url": "s3://abc",
                "output_path": "s3://abc/video/?access_key=abc&secret_key=123",
            },
        )
        response = TranscodingJobViewSet.as_view({"post": "create"})(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(400, response.status_code)
        self.assertEqual(
            str(response.data["input_url"][0]),
            "Query parameters 'access_key', 'secret_key' and 'region' are required",
        )

    def test_error_should_be_thrown_for_invalid_output_url(self):
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={
                "resolutions": ["240p", "480p"],
                "input_url": "https://sample-videos.com/video123/mp4/720/big_buck_bunny_720p_1mb.mp4",
                "output_path": "https://abc/video/",
            },
        )
        response = TranscodingJobViewSet.as_view({"post": "create"})(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(400, response.status_code)
        self.assertEqual(
            str(response.data["output_path"][0]),
            "Output path format not recognized. Please use the following format: s3://bucket/path/",
        )

    @mock.patch("app.domain.transcoder.LumberjackJobTranscoder.stop")
    def test_transcoding_job_should_be_stopped_for_delete_method(
        self, mock_stop_transcoding
    ):
        transcoding_job = self.create_transcoding_job()
        request = self.get_request(
            "/",
            method="DELETE",
            user=self.organization.created_by,
        )
        response = TranscodingJobViewSet.as_view({"delete": "destroy"})(
            request, organization_id=self.organization.uuid, uuid=transcoding_job.uuid
        )

        mock_stop_transcoding.assert_called()
        self.assertEqual(204, response.status_code)


class TestUpdateTranscodingJobStatusView(
    OrganizationMixin, TranscodingJobMixin, TestCase
):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_status_should_be_updated(self):
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={
                "id": "7488f015-a8aa-47d6-b226-213c26f6aad9",
                "status": "Completed",
                "start_time": "2023-09-11T07:09:56.804344Z",
                "end_time": "2023-09-11T07:12:28.284330Z",
                "video_duration": 597,
            },
        )
        transcoding_job = self.create_transcoding_job()
        response = UpdateTranscodingJobStatusView.as_view()(
            request,
            organization_id=self.organization.uuid,
            transcoding_job_id=transcoding_job.uuid,
        )

        transcoding_job.refresh_from_db()
        self.assertEqual(200, response.status_code)
        self.assertTrue(transcoding_job.video_duration, 200)
        self.assertEqual(transcoding_job.status, TranscodingJob.Status.COMPLETED)
