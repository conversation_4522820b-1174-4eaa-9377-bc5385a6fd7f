import uuid
from datetime import timedelta
from unittest.mock import patch

from django.contrib.auth.models import AnonymousUser
from django_multitenant.utils import set_current_tenant

from app.api.v1.views.video_trim import (
    VideoTrimLogsView,
    VideoTrimRevertView,
    VideoTrimStatusView,
    VideoTrimView,
)
from app.models import LiveStream, User
from app.models.asset import Asset
from app.models.video import Video
from app.models.video_trim import TrimStatus, VideoTrim
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestVideoTrimView(TestCase, OrganizationMixin, AssetMixin):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )
        self.video = self.create_video()
        self.video.duration = timedelta(seconds=3600)
        self.video.status = Video.Status.COMPLETED
        self.video.save()

    def tearDown(self):
        VideoTrim.objects.filter(video=self.video).delete()

    @patch("app.tasks.video_trim.VideoTrimTask.apply_async")
    def test_trim_video_with_both_times(self, mock_apply_async):
        mock_apply_async.return_value.id = str(uuid.uuid4())

        request = self.get_request(
            path="/",
            method="post",
            user=self.user,
            data={"start_time": 300, "end_time": 1800},
        )
        response = VideoTrimView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(202, response.status_code)
        self.assertEqual(
            "Video trim job started successfully", response.data["message"]
        )
        self.assertIsInstance(response.data["trim_job_id"], int)
        self.assertGreater(response.data["trim_job_id"], 0)
        self.assertEqual("Pending", response.data["status"])

        trim_job = VideoTrim.objects.get(id=response.data["trim_job_id"])
        self.assertEqual(trim_job.start_time, 300)
        self.assertEqual(trim_job.end_time, 1800)
        self.assertEqual(trim_job.video, self.video)
        self.assertEqual(trim_job.created_by, self.user)

    @patch("app.tasks.video_trim.VideoTrimTask.apply_async")
    def test_trim_video_with_only_start_time(self, mock_apply_async):
        mock_apply_async.return_value.id = str(uuid.uuid4())

        request = self.get_request(
            path="/",
            method="post",
            user=self.user,
            data={"start_time": 600},
        )
        response = VideoTrimView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(202, response.status_code)

        trim_job = VideoTrim.objects.get(id=response.data["trim_job_id"])
        self.assertEqual(trim_job.start_time, 600)
        self.assertEqual(trim_job.end_time, 3600)

    @patch("app.tasks.video_trim.VideoTrimTask.apply_async")
    def test_trim_video_with_only_end_time(self, mock_apply_async):
        mock_apply_async.return_value.id = str(uuid.uuid4())

        request = self.get_request(
            path="/",
            method="post",
            user=self.user,
            data={"end_time": 1200},
        )
        response = VideoTrimView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(202, response.status_code)

        trim_job = VideoTrim.objects.get(id=response.data["trim_job_id"])
        self.assertEqual(trim_job.start_time, 0)
        self.assertEqual(trim_job.end_time, 1200)

    @patch("app.tasks.video_trim.VideoTrimTask.apply_async")
    def test_trim_live_stream_video(self, mock_apply_async):
        mock_apply_async.return_value.id = str(uuid.uuid4())

        live_stream_asset = self.create_asset(type=Asset.Type.LIVESTREAM)
        LiveStream.objects.create(
            asset=live_stream_asset, organization=self.organization
        )
        live_stream_video = Video.objects.create(
            asset=live_stream_asset,
            organization=self.organization,
            duration=timedelta(seconds=3600),
            status=Video.Status.COMPLETED,
        )

        request = self.get_request(
            path="/",
            method="post",
            user=self.user,
            data={"start_time": 300, "end_time": 1800},
        )
        response = VideoTrimView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=live_stream_asset.uuid,
        )

        self.assertEqual(202, response.status_code)
        self.assertEqual(
            "Video trim job started successfully", response.data["message"]
        )
        self.assertIsInstance(response.data["trim_job_id"], int)
        self.assertGreater(response.data["trim_job_id"], 0)

        trim_job = VideoTrim.objects.get(id=response.data["trim_job_id"])
        self.assertEqual(trim_job.video, live_stream_video)
        self.assertEqual(trim_job.start_time, 300)
        self.assertEqual(trim_job.end_time, 1800)

    def test_trim_video_with_no_parameters(self):
        request = self.get_request(
            path="/",
            method="post",
            user=self.user,
            data={},
        )
        response = VideoTrimView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(400, response.status_code)
        self.assertIn(
            "At least one of start_time or end_time must be provided",
            str(response.data),
        )

    def test_trim_video_with_invalid_times(self):
        request = self.get_request(
            path="/",
            method="post",
            user=self.user,
            data={"start_time": 1800, "end_time": 300},
        )
        response = VideoTrimView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(400, response.status_code)
        self.assertIn("must be greater than start time", str(response.data))

    def test_trim_video_start_time_exceeds_duration(self):
        request = self.get_request(
            path="/",
            method="post",
            user=self.user,
            data={"start_time": 4000},
        )
        response = VideoTrimView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(400, response.status_code)
        self.assertIn(
            "cannot be greater than or equal to video duration", str(response.data)
        )

    def test_trim_video_end_time_exceeds_duration(self):
        request = self.get_request(
            path="/",
            method="post",
            user=self.user,
            data={"end_time": 4000},
        )
        response = VideoTrimView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(400, response.status_code)
        self.assertIn("cannot be greater than video duration", str(response.data))

    def test_trim_video_without_duration(self):
        self.video.duration = None
        self.video.save()

        request = self.get_request(
            path="/",
            method="post",
            user=self.user,
            data={"start_time": 300, "end_time": 1800},
        )
        response = VideoTrimView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(400, response.status_code)
        self.assertIn("Video duration is not available", str(response.data))

    def test_trim_video_with_invalid_duration(self):
        self.video.duration = timedelta(seconds=0)
        self.video.save()

        request = self.get_request(
            path="/",
            method="post",
            user=self.user,
            data={"start_time": 300, "end_time": 1800},
        )
        response = VideoTrimView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(400, response.status_code)
        self.assertIn("Video duration is not available", str(response.data))

    def test_trim_video_with_negative_duration(self):
        self.video.duration = timedelta(seconds=-10)
        self.video.save()

        request = self.get_request(
            path="/",
            method="post",
            user=self.user,
            data={"start_time": 300, "end_time": 1800},
        )
        response = VideoTrimView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(400, response.status_code)
        self.assertIn("Video duration is invalid", str(response.data))

    def test_trim_video_unauthorized(self):
        request = self.get_request(
            path="/",
            method="post",
            user=AnonymousUser(),
            data={"start_time": 300, "end_time": 1800},
        )
        response = VideoTrimView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(401, response.status_code)

    def test_trim_video_asset_not_found(self):
        request = self.get_request(
            path="/",
            method="post",
            user=self.user,
            data={"start_time": 300, "end_time": 1800},
        )
        response = VideoTrimView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid="non-existent-uuid",
        )

        self.assertEqual(404, response.status_code)

    def test_trim_video_not_transcoded(self):
        self.video.status = Video.Status.TRANSCODING
        self.video.save()

        request = self.get_request(
            path="/",
            method="post",
            user=self.user,
            data={"start_time": 300, "end_time": 1800},
        )
        response = VideoTrimView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(400, response.status_code)
        self.assertIn(
            "Video must be fully transcoded before trimming", str(response.data)
        )

    def test_trim_video_concurrent_operation(self):
        VideoTrim.objects.create(
            video=self.video,
            start_time=100,
            end_time=200,
            created_by=self.user,
            status=TrimStatus.PROCESSING,
            organization=self.organization,
        )

        request = self.get_request(
            path="/",
            method="post",
            user=self.user,
            data={"start_time": 300, "end_time": 1800},
        )
        response = VideoTrimView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(400, response.status_code)
        self.assertIn(
            "Another trim operation is currently processing", str(response.data)
        )


class TestVideoTrimStatusView(TestCase, OrganizationMixin, AssetMixin):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )
        self.video = self.create_video()
        self.video.duration = timedelta(seconds=3600)
        self.video.status = Video.Status.COMPLETED
        self.video.save()

    def test_get_trim_status_success(self):
        trim_job = VideoTrim.objects.create(
            video=self.video,
            start_time=300,
            end_time=1800,
            created_by=self.user,
            status=TrimStatus.PROCESSING,
            organization=self.organization,
        )

        request = self.get_request(
            path="/",
            method="get",
            user=self.user,
        )
        response = VideoTrimStatusView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(trim_job.id, response.data["id"])
        self.assertEqual(300, response.data["start_time"])
        self.assertEqual(1800, response.data["end_time"])
        self.assertEqual(TrimStatus.PROCESSING, response.data["status"])
        self.assertEqual("Processing", response.data["status_display"])

    def test_get_trim_status_latest_job(self):
        VideoTrim.objects.create(
            video=self.video,
            start_time=100,
            end_time=200,
            created_by=self.user,
            status=TrimStatus.COMPLETED,
            organization=self.organization,
        )

        newer_trim = VideoTrim.objects.create(
            video=self.video,
            start_time=300,
            end_time=1800,
            created_by=self.user,
            status=TrimStatus.PROCESSING,
            organization=self.organization,
        )

        request = self.get_request(
            path="/",
            method="get",
            user=self.user,
        )
        response = VideoTrimStatusView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(newer_trim.id, response.data["id"])
        self.assertEqual(300, response.data["start_time"])

    def test_get_trim_status_no_jobs(self):
        request = self.get_request(
            path="/",
            method="get",
            user=self.user,
        )
        response = VideoTrimStatusView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(404, response.status_code)
        self.assertEqual("No trim jobs found for this video", response.data["detail"])

    def test_get_trim_status_unauthorized(self):
        request = self.get_request(
            path="/",
            method="get",
            user=AnonymousUser(),
        )
        response = VideoTrimStatusView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(401, response.status_code)


class TestVideoTrimRevertView(TestCase, OrganizationMixin, AssetMixin):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )
        self.video = self.create_video()
        self.video.duration = timedelta(seconds=3600)
        self.video.status = Video.Status.COMPLETED
        self.video.save()

    @patch("app.tasks.video_trim.VideoTrimRevertTask.apply_async")
    def test_revert_video_success(self, mock_apply_async):
        VideoTrim.objects.create(
            video=self.video,
            start_time=300,
            end_time=1800,
            created_by=self.user,
            status=TrimStatus.COMPLETED,
            organization=self.organization,
        )

        mock_apply_async.return_value.id = str(uuid.uuid4())

        request = self.get_request(
            path="/",
            method="post",
            user=self.user,
        )
        response = VideoTrimRevertView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(202, response.status_code)
        self.assertEqual(
            "Video revert job started successfully", response.data["message"]
        )
        self.assertIsInstance(response.data["task_id"], str)
        self.assertTrue(len(response.data["task_id"]) > 0)

    def test_revert_video_no_completed_trim(self):
        request = self.get_request(
            path="/",
            method="post",
            user=self.user,
        )
        response = VideoTrimRevertView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(400, response.status_code)
        self.assertIn("No completed trim found for this video", str(response.data))

    def test_revert_video_unauthorized(self):
        request = self.get_request(
            path="/",
            method="post",
            user=AnonymousUser(),
        )
        response = VideoTrimRevertView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(401, response.status_code)


class TestVideoTrimLogsView(TestCase, OrganizationMixin, AssetMixin):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )
        self.video = self.create_video()
        self.video.duration = timedelta(seconds=3600)
        self.video.status = Video.Status.COMPLETED
        self.video.save()

    def test_get_trim_logs_success(self):
        trim1 = VideoTrim.objects.create(
            video=self.video,
            start_time=100,
            end_time=200,
            created_by=self.user,
            status=TrimStatus.COMPLETED,
            organization=self.organization,
        )

        trim2 = VideoTrim.objects.create(
            video=self.video,
            start_time=300,
            end_time=1800,
            created_by=self.user,
            status=TrimStatus.PROCESSING,
            organization=self.organization,
        )

        request = self.get_request(
            path="/",
            method="get",
            user=self.user,
        )
        response = VideoTrimLogsView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(200, response.status_code)

        self.assertIn("results", response.data)
        self.assertEqual(len(response.data["results"]), 2)

        self.assertIn("count", response.data)
        self.assertIn("next", response.data)
        self.assertIn("previous", response.data)
        self.assertEqual(response.data["count"], 2)

        results = response.data["results"]

        self.assertEqual(results[0]["id"], trim2.id)
        self.assertEqual(results[0]["start_time"], 300)
        self.assertEqual(results[0]["end_time"], 1800)
        self.assertEqual(results[0]["status"], TrimStatus.PROCESSING)
        self.assertEqual(results[0]["status_display"], "Processing")
        self.assertEqual(results[0]["created_by_email"], "<EMAIL>")
        self.assertEqual(results[0]["duration"], 1500)

        self.assertEqual(results[1]["id"], trim1.id)
        self.assertEqual(results[1]["start_time"], 100)
        self.assertEqual(results[1]["end_time"], 200)
        self.assertEqual(results[1]["status"], TrimStatus.COMPLETED)
        self.assertEqual(results[1]["status_display"], "Completed")
        self.assertEqual(results[1]["duration"], 100)

    def test_get_trim_logs_no_logs(self):
        request = self.get_request(
            path="/",
            method="get",
            user=self.user,
        )
        response = VideoTrimLogsView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(200, response.status_code)
        self.assertIn("results", response.data)
        self.assertEqual(len(response.data["results"]), 0)
        self.assertEqual(response.data["count"], 0)

    def test_get_trim_logs_unauthorized(self):
        request = self.get_request(
            path="/",
            method="get",
            user=AnonymousUser(),
        )
        response = VideoTrimLogsView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(401, response.status_code)

    def test_get_trim_logs_pagination(self):
        for i in range(55):
            VideoTrim.objects.create(
                video=self.video,
                start_time=i * 10,
                end_time=(i * 10) + 100,
                created_by=self.user,
                status=TrimStatus.COMPLETED,
                organization=self.organization,
            )

        request = self.get_request(
            path="/",
            method="get",
            user=self.user,
        )
        response = VideoTrimLogsView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(200, response.status_code)
        self.assertIn("results", response.data)
        self.assertEqual(len(response.data["results"]), 50)
        self.assertEqual(response.data["count"], 55)
        self.assertIsNotNone(response.data["next"])
        self.assertIsNone(response.data["previous"])

        request = self.get_request(
            path="/?limit=10",
            method="get",
            user=self.user,
        )
        response = VideoTrimLogsView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=self.video.asset.uuid,
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(len(response.data["results"]), 10)
        self.assertEqual(response.data["count"], 55)

    def test_get_trim_logs_query_optimization(self):
        user2 = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )
        user3 = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )

        VideoTrim.objects.create(
            video=self.video,
            start_time=100,
            end_time=200,
            created_by=self.user,
            status=TrimStatus.COMPLETED,
            organization=self.organization,
        )
        VideoTrim.objects.create(
            video=self.video,
            start_time=300,
            end_time=400,
            created_by=user2,
            status=TrimStatus.PROCESSING,
            organization=self.organization,
        )
        VideoTrim.objects.create(
            video=self.video,
            start_time=500,
            end_time=600,
            created_by=user3,
            status=TrimStatus.FAILED,
            organization=self.organization,
        )

        request = self.get_request(
            path="/",
            method="get",
            user=self.user,
        )

        with self.assertNumQueries(5):
            response = VideoTrimLogsView.as_view()(
                request,
                organization_id=self.organization.uuid,
                asset_uuid=self.video.asset.uuid,
            )

        self.assertEqual(200, response.status_code)
        results = response.data["results"]
        self.assertEqual(len(results), 3)

        emails = {result["created_by_email"] for result in results}
        expected_emails = {"<EMAIL>", "<EMAIL>", "<EMAIL>"}
        self.assertEqual(emails, expected_emails)

    def test_get_trim_logs_livestream_asset(self):
        live_stream_asset = self.create_asset(type=Asset.Type.LIVESTREAM)
        video = self.create_video(asset=live_stream_asset, status=Video.Status.COMPLETED)
        video.duration = timedelta(seconds=3600)
        video.save()

        trim1 = VideoTrim.objects.create(
            video=video,
            start_time=50,
            end_time=250,
            created_by=self.user,
            status=TrimStatus.COMPLETED,
            organization=self.organization,
        )

        trim2 = VideoTrim.objects.create(
            video=video,
            start_time=300,
            end_time=600,
            created_by=self.user,
            status=TrimStatus.PROCESSING,
            organization=self.organization,
        )

        request = self.get_request(
            path="/",
            method="get",
            user=self.user,
        )
        response = VideoTrimLogsView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=live_stream_asset.uuid,
        )

        self.assertEqual(200, response.status_code)
        self.assertIn("results", response.data)
        self.assertEqual(len(response.data["results"]), 2)

        results = response.data["results"]

        self.assertEqual(results[0]["id"], trim2.id)
        self.assertEqual(results[0]["start_time"], 300)
        self.assertEqual(results[0]["end_time"], 600)
        self.assertEqual(results[0]["status"], TrimStatus.PROCESSING)

        self.assertEqual(results[1]["id"], trim1.id)
        self.assertEqual(results[1]["start_time"], 50)
        self.assertEqual(results[1]["end_time"], 250)
        self.assertEqual(results[1]["status"], TrimStatus.COMPLETED)
