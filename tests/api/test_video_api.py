import base64
import json
import uuid
from datetime import datetime, timedelta
from unittest import mock
from unittest.mock import patch

import pytz
import responses
from django.conf import settings
from django.test import Client, override_settings
from django.urls import resolve, reverse
from django_multitenant.utils import set_current_tenant, unset_current_tenant
from knox.auth import TokenAuthentication
from rest_framework import permissions

from app.api.v1.permissions import HasAccessTokenOrCheckAccess, HasOrganizationAccess
from app.api.v1.serializers.live_stream import LiveStreamMessages
from app.api.v1.views import (
    AssetListView,
    AssetUpdateDeleteRetrieveView,
    DRMLicenseView,
    VideoAssetCreateView,
)
from app.api.v1.views.asset import AssetMoveView
from app.api.v1.views.video import AESEncryptionKeyView, start_transcoding_view
from app.domain.aes import create_aes_encryption_key_if_not_present
from app.domain.video import (
    <PERSON>mber<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    get_migrated_dash_url,
    get_migrated_playback_url,
)
from app.models import Asset, DRMLicenseLog, LiveStream, Track, Video, Webhook
from tests import TestCase
from tests.mixins import OrganizationMixin
from tests.mixins.asset import AssetMixin, PreviewMixin
from tests.mixins.drm import EncryptionKeyMixin


class TestAssetListView(PreviewMixin, AssetMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_api_should_allow_only_authenticated_user(self):
        self.assertTrue(permissions.IsAuthenticated in AssetListView.permission_classes)

    def test_api_should_have_organization_access_permission(self):
        self.assertTrue(HasOrganizationAccess in AssetListView.permission_classes)

    def test_api_should_allow_token_based_authentication(self):
        self.assertTrue(TokenAuthentication in AssetListView.authentication_classes)

    def test_api_should_return_assets_list(self):
        video = self.create_video()
        request = self.get_request("/", user=self.organization.created_by)
        response = AssetListView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(str(video.asset.uuid), str(response.data["results"][0]["id"]))
        self.assertEqual(video.asset.title, response.data["results"][0]["title"])

    def test_api_should_apply_filter_correctly(self):
        self.create_asset(title="Asset 123")
        self.create_asset(title="Asset 156")
        self.create_asset(title="Asset 278")
        request = self.get_request(
            "/", user=self.organization.created_by, data={"q": "Asset 1"}
        )
        response = AssetListView.as_view()(
            request, organization_id=self.organization.uuid
        )
        titles = [result["title"] for result in response.data["results"]]
        self.assertEqual(len(response.data["results"]), len(titles), 2)
        self.assertIn("Asset 123", titles)
        self.assertIn("Asset 156", titles)

    def test_api_should_return_none_for_search_if_not_found(self):
        self.create_asset(title="Asset 1")
        self.create_asset(title="Asset 2")
        self.create_asset(title="Asset 3")
        request = self.get_request(
            "/", user=self.organization.created_by, data={"q": "abcd"}
        )
        response = AssetListView.as_view()(
            request, organization_id=self.organization.uuid
        )
        self.assertEqual(len(response.data["results"]), 0)


class TestVideoAssetCreateView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_api_should_allow_only_authenticated_user(self):
        self.assertTrue(
            permissions.IsAuthenticated in VideoAssetCreateView.permission_classes
        )

    def test_api_should_have_organization_access_permission(self):
        self.assertTrue(
            HasOrganizationAccess in VideoAssetCreateView.permission_classes
        )

    def test_api_should_allow_token_based_authentication(self):
        self.assertTrue(
            TokenAuthentication in VideoAssetCreateView.authentication_classes
        )

    @responses.activate
    @patch("app.tasks.video.DownloadAndTranscodeVideoTask.apply_async")
    def test_api_should_create_new_video_asset(self, mock_download_and_transcode_task):
        responses.add(
            responses.POST,
            f"{settings.TRANSCODER_URL}/create/",
            status=200,
            json={"id": str(uuid.uuid4())},
        )
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={
                "title": "New Title",
                "inputs": [{"url": "http://example.com"}],
                "resolutions": ["240p"],
            },
        )
        response = VideoAssetCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(201, response.status_code)
        self.assertTrue(Asset.objects.get(title="New Title").uuid, response.data["id"])

    @responses.activate
    @patch("app.tasks.video.DownloadAndTranscodeVideoTask.apply_async")
    def test_api_sets_content_protection_type_to_drm_if_enable_drm_true(
        self, mock_download_and_transcode_task
    ):
        responses.add(
            responses.POST,
            f"{settings.TRANSCODER_URL}/create/",
            status=200,
            json={"id": str(uuid.uuid4())},
        )
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={
                "title": "New Title",
                "inputs": [{"url": "http://example.com"}],
                "resolutions": ["240p"],
                "enable_drm": True,
            },
        )
        response = VideoAssetCreateView.as_view()(
            request, organization_id=self.organization.uuid
        )

        self.assertEqual(201, response.status_code)
        asset = Asset.objects.get(title="New Title")
        self.assertTrue(
            asset.video.content_protection_type, Video.ContentProtectionType.DRM
        )


class TestUpdateVideoStatusView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @mock.patch("app.api.v1.views.video.UpdateAssetSizeTask")
    @mock.patch("app.domain.storages.get_size", return_value=100000)
    def test_video_status_should_be_updated_on_status_update_webhook(
        self,
        mocked_get_file_size,
        mocked_task,
    ):
        video = self.create_video()
        start_time = "2023-08-31T10:00:00.000Z"
        end_time = "2023-08-31T11:00:00.000Z"
        request = self.get_request(
            "/",
            method="POST",
            data={
                "status": "completed",
                "start_time": start_time,
                "end_time": end_time,
                "thumbnails": ["thumbnail/path1", "thumbnail/path2"],
            },
        )
        view = resolve(
            reverse(
                "api:update-video-status",
                kwargs={
                    "asset_id": video.asset.uuid,
                    "organization_id": self.organization.uuid,
                },
            )
        )
        response = view.func(request, self.organization.uuid, video.asset.uuid)
        video.refresh_from_db()

        self.assertEqual(200, response.status_code)
        self.assertEqual(
            video.transcoding_start_time,
            datetime.strptime(start_time, "%Y-%m-%dT%H:%M:%S.%fZ").replace(
                tzinfo=pytz.UTC
            ),
        )
        self.assertEqual(
            video.transcoding_end_time,
            datetime.strptime(end_time, "%Y-%m-%dT%H:%M:%S.%fZ").replace(
                tzinfo=pytz.UTC
            ),
        )
        self.assertEqual(video.cover_thumbnail_url, "thumbnail/path1")

    @mock.patch("app.api.v1.views.video.UpdateAssetSizeTask")
    @mock.patch("app.domain.storages.get_size", return_value=100000)
    def test_api_should_throw_404_for_incorrect_id(
        self, mocked_get_file_size, mocked_task
    ):
        self.create_video()
        request = self.get_request(
            "/",
            method="POST",
            data={"status": "completed"},
        )
        view = resolve(
            reverse(
                "api:update-video-status",
                kwargs={
                    "asset_id": uuid.uuid4(),
                    "organization_id": self.organization.uuid,
                },
            )
        )
        response = view.func(request, self.organization.uuid, uuid.uuid4())

        self.assertEqual(404, response.status_code)

    @mock.patch("app.api.v1.views.video.UpdateAssetSizeTask")
    @mock.patch("app.domain.storages.get_size", return_value=100000)
    @mock.patch("app.models.asset.send_data_to_webhook_task")
    def test_webhook_should_be_called_if_registered(
        self, mocked_webhook, mocked_get_size, mocked_task
    ):
        Webhook.objects.create(
            url="https://example.com",
            created_by=self.organization.created_by,
            organization=self.organization,
            secret_token="secret",
        )
        video = self.create_video()
        request = self.get_request(
            "/",
            method="POST",
            data={"status": "completed"},
        )
        view = resolve(
            reverse(
                "api:update-video-status",
                kwargs={
                    "asset_id": video.asset.uuid,
                    "organization_id": self.organization.uuid,
                },
            )
        )
        view.func(request, self.organization.uuid, video.asset.uuid)

        video.asset.refresh_from_db()
        mocked_webhook.delay.assert_called_with(
            "https://example.com",
            self.organization.uuid,
            asset_uuid=video.asset.uuid,
            token="secret",
        )


class TestAssetUpdateDeleteRetrieveView(
    PreviewMixin, AssetMixin, OrganizationMixin, TestCase
):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_api_should_allow_only_authenticated_user(self):
        self.assertTrue(
            permissions.IsAuthenticated
            in AssetUpdateDeleteRetrieveView.permission_classes
        )

    def test_api_should_allow_token_based_authentication(self):
        self.assertTrue(
            TokenAuthentication in AssetUpdateDeleteRetrieveView.authentication_classes
        )

    @mock.patch("app.domain.video.cloudfront")
    def test_api_should_use_uuid_for_detail_view_lookup_field(
        self, mock_generate_presigned_url
    ):
        mock_generate_presigned_url.generate_presigned_url.return_value = (
            "https://example.com/download"
        )
        video = self.create_video()
        user = self.organization.created_by
        self.create_membership(user=user)
        request = self.get_request("/", user=user, method="GET")
        response = AssetUpdateDeleteRetrieveView.as_view({"get": "retrieve"})(
            request,
            organization_id=self.organization.uuid,
            uuid=video.asset.uuid,
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(str(video.asset.uuid), str(response.data["id"]))

    @mock.patch("app.models.asset.Asset.get_download_url", return_value="")
    def test_api_should_return_preview_thumbnail_details(self, mock_download_url):
        video = self.create_video()
        preview_thumbnail = self.create_preview_thumbnail()
        self.create_subtitle(
            video=video,
            type=Track.Type.PREVIEW_THUMBNAIL,
            preview_thumbnail=preview_thumbnail,
        )
        request = self.get_request("/", user=self.organization.created_by)
        response = AssetUpdateDeleteRetrieveView.as_view({"get": "retrieve"})(
            request,
            organization_id=self.organization.uuid,
            uuid=video.asset.uuid,
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(
            str(preview_thumbnail.interval),
            str(response.data["video"]["tracks"][0]["preview_thumbnail"]["interval"]),
        )
        self.assertEqual(
            str(preview_thumbnail.width),
            str(response.data["video"]["tracks"][0]["preview_thumbnail"]["width"]),
        )
        self.assertEqual(
            str(preview_thumbnail.height),
            str(response.data["video"]["tracks"][0]["preview_thumbnail"]["height"]),
        )
        self.assertEqual(
            str(preview_thumbnail.rows),
            str(response.data["video"]["tracks"][0]["preview_thumbnail"]["rows"]),
        )
        self.assertEqual(
            str(preview_thumbnail.columns),
            str(response.data["video"]["tracks"][0]["preview_thumbnail"]["columns"]),
        )

    @mock.patch("app.domain.video.cloudfront")
    def test_api_should_update_only_title(self, mock_generate_presigned_url):
        mock_generate_presigned_url.generate_presigned_url.return_value = (
            "https://example.com/download"
        )
        video = self.create_video(resolutions=[0])
        request = self.get_request(
            "/",
            method="PUT",
            user=self.organization.created_by,
            data={
                "title": "New Title",
                "inputs": [{"url": "http://example.com"}],
                "progress": 50,
            },
        )
        response = AssetUpdateDeleteRetrieveView.as_view({"put": "update"})(
            request,
            organization_id=self.organization.uuid,
            uuid=video.asset.uuid,
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(str(video.asset.uuid), str(response.data["id"]))
        self.assertEqual("New Title", response.data["title"])
        self.assertEqual(0, response.data["video"]["progress"])

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @mock.patch("app.views.assets.messages.success")
    @mock.patch("app.tasks.purge_asset.stop_transcoding_task.delay")
    def test_api_should_delete_asset_for_delete_method(
        self, mock_stop_transcoding_task, mock_message
    ):
        video = self.create_video(resolutions=[0])
        request = self.get_request(
            "/",
            method="DELETE",
            user=self.organization.created_by,
        )
        response = AssetUpdateDeleteRetrieveView.as_view({"delete": "destroy"})(
            request,
            organization_id=self.organization.uuid,
            uuid=video.asset.uuid,
        )

        self.assertEqual(202, response.status_code)
        self.assertFalse(Asset.objects.filter(uuid=video.asset.uuid).exists())

    @mock.patch("app.domain.video.cloudfront")
    def test_api_should_work_with_access_token(self, mock_generate_presigned_url):
        mock_generate_presigned_url.generate_presigned_url.return_value = (
            "https://example.com/download"
        )
        video = self.create_video()
        access_token = self.create_access_token(
            asset=video.asset, expires_after_first_usage=True
        )
        request = self.get_request(f"/?access_token={access_token.uuid}", method="GET")
        response = AssetUpdateDeleteRetrieveView.as_view({"get": "retrieve"})(
            request,
            organization_id=self.organization.uuid,
            uuid=video.asset.uuid,
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(str(video.asset.uuid), str(response.data["id"]))

    @mock.patch("app.models.asset.Asset.get_download_url", return_value="")
    @mock.patch("app.api.v1.serializers.video.cloudfront.generate_presigned_url")
    def test_should_generate_signed_playback_url_with_given_expiry(
        self, mock_generate_presigned_url, mock_get_download_url
    ):
        self.create_membership(user=self.organization.created_by)
        video = self.create_video(
            content_protection_type=Video.ContentProtectionType.AES
        )
        mock_generate_presigned_url.return_value = f"{video.playback_url}?signature=abc"
        request = self.get_request(
            "/?expiry=300", method="GET", user=self.organization.created_by
        )
        response = AssetUpdateDeleteRetrieveView.as_view({"get": "retrieve"})(
            request,
            organization_id=self.organization.uuid,
            uuid=video.asset.uuid,
        )

        mock_generate_presigned_url.assert_called_with(
            video.organization, video.playback_url, expires_in=300
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(
            f"{video.playback_url}?signature=abc",
            response.data["video"]["playback_url"],
        )

    @mock.patch("app.models.asset.Asset.get_download_url", return_value="")
    @mock.patch("app.api.v1.serializers.video.cloudfront.generate_presigned_url")
    def test_enable_drm_is_true_if_video_content_protection_type_is_drm(
        self, mock_generate_presigned_url, mock_get_download_url
    ):
        video = self.create_video(
            content_protection_type=Video.ContentProtectionType.DRM
        )
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(f"/?access_token={access_token.uuid}", method="GET")
        response = AssetUpdateDeleteRetrieveView.as_view({"get": "retrieve"})(
            request,
            organization_id=self.organization.uuid,
            uuid=video.asset.uuid,
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(str(video.asset.uuid), str(response.data["id"]))
        self.assertTrue(response.data["video"]["enable_drm"])

    def test_should_return_live_stream_ended_notice_message(self):
        live_stream = self.create_livestream(
            end=datetime.now() - timedelta(minutes=1),
            status=LiveStream.Status.COMPLETED,
            transcode_recorded_video=False,
        )
        request = self.get_request("/")
        response = AssetUpdateDeleteRetrieveView.as_view({"get": "retrieve"})(
            request,
            organization_id=self.organization.uuid,
            uuid=live_stream.asset.uuid,
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(
            LiveStreamMessages.ENDED_WITHOUT_RECORDING,
            response.data["live_stream"]["notice_message"],
        )

    @mock.patch("app.models.asset.Asset.get_download_url", return_value="")
    @mock.patch("app.api.v1.serializers.video.cloudfront.generate_presigned_url")
    def test_should_none_for_notice_message_if_recording_available(
        self, mock_generate_presigned_url, mock_get_download_url
    ):
        live_stream = self.create_livestream(
            end=datetime.now() - timedelta(minutes=1),
            status=LiveStream.Status.COMPLETED,
            transcode_recorded_video=True,
        )
        live_stream.asset.video = self.create_video(
            asset=live_stream.asset, status=Video.Status.COMPLETED
        )
        live_stream.asset.save()
        request = self.get_request("/")
        response = AssetUpdateDeleteRetrieveView.as_view({"get": "retrieve"})(
            request,
            organization_id=self.organization.uuid,
            uuid=live_stream.asset.uuid,
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(None, response.data["live_stream"]["notice_message"])

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @mock.patch("app.views.assets.messages.success")
    @mock.patch("app.tasks.purge_asset.stop_transcoding_task.delay")
    def test_api_should_update_children_count_on_delete(
        self, mock_stop_transcoding_task, mock_message
    ):
        folder = self.create_folder()
        video = self.create_asset()
        folder.children.set([video])
        folder.update_children_count()
        folder.save()
        request = self.get_request(
            "/",
            method="DELETE",
            user=self.organization.created_by,
        )
        response = AssetUpdateDeleteRetrieveView.as_view({"delete": "destroy"})(
            request,
            organization_id=self.organization.uuid,
            uuid=video.uuid,
        )

        self.assertEqual(202, response.status_code)
        self.assertFalse(Asset.objects.filter(uuid=video.uuid).exists())
        folder.refresh_from_db()
        self.assertEqual(0, folder.children_count)


class TestDRMLicenseView(OrganizationMixin, EncryptionKeyMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization(uuid="352dct")
        set_current_tenant(self.organization)

    def test_api_should_allow_token_based_authentication(self):
        self.assertTrue(TokenAuthentication in DRMLicenseView.authentication_classes)

    def test_api_should_have_organization_access_permission(self):
        self.assertTrue(
            HasAccessTokenOrCheckAccess in DRMLicenseView.permission_classes
        )

    @responses.activate
    @mock.patch("app.api.v1.views.video.get_tpstreams_license")
    def test_200_should_be_returned_for_valid_data(self, mock_license_factory):
        user = self.organization.created_by
        self.create_membership(user=user)
        mock_license_factory().create().run.return_value = "https://license_url.com"
        responses.add(responses.POST, "https://license_url.com/", status=200)
        video = self.create_video(
            content_protection_type=Video.ContentProtectionType.DRM
        )
        request = self.get_request("/", method="POST", user=user)
        response = DRMLicenseView.as_view()(
            request, organization_id=self.organization.uuid, asset_id=video.asset.uuid
        )

        self.assertEqual(200, response.status_code)

    @responses.activate
    @mock.patch("app.api.v1.views.video.get_tpstreams_license")
    def test_200_should_be_returned_for_live_stream_with_drm(
        self, mock_license_factory
    ):
        user = self.organization.created_by
        self.create_membership(user=user)
        mock_license_factory().create().run.return_value = "https://license_url.com"
        responses.add(responses.POST, "https://license_url.com/", status=200)
        video = self.create_livestream(enable_drm=True)
        request = self.get_request("/", method="POST", user=user)
        response = DRMLicenseView.as_view()(
            request, organization_id=self.organization.uuid, asset_id=video.asset.uuid
        )

        self.assertEqual(200, response.status_code)

    @mock.patch("app.api.v1.views.video.get_tpstreams_license")
    def test_404_should_be_returned_for_video_without_drm(self, mock_license_factory):
        video = self.create_video(
            content_protection_type=Video.ContentProtectionType.DISABLED
        )
        request = self.get_request(
            "/", method="POST", user=self.organization.created_by
        )
        response = DRMLicenseView.as_view()(
            request, organization_id=self.organization.uuid, asset_id=video.asset.uuid
        )

        self.assertEqual(404, response.status_code)

    @mock.patch("app.api.v1.views.video.get_tpstreams_license")
    def test_404_should_be_returned_for_live_stream_without_drm(
        self, mock_license_factory
    ):
        video = self.create_livestream()
        request = self.get_request(
            "/", method="POST", user=self.organization.created_by
        )
        response = DRMLicenseView.as_view()(
            request, organization_id=self.organization.uuid, asset_id=video.asset.uuid
        )

        self.assertEqual(404, response.status_code)

    @mock.patch("app.api.v1.views.video.get_tpstreams_license")
    def test_our_drm_integration_should_be_used_for_video_using_our_drm_provider(
        self, widevine_mock
    ):
        user = self.organization.created_by
        self.create_membership(user=user)
        widevine_mock.return_value = "data"
        video = self.create_video(
            content_protection_type=Video.ContentProtectionType.DRM
        )
        request = self.get_request(
            "/",
            method="POST",
            user=user,
            data=b"hello",
            content_type="application/octet-stream",
        )
        DRMLicenseView.as_view()(
            request, organization_id=self.organization.uuid, asset_id=video.asset.uuid
        )

        widevine_mock.assert_called()

    @mock.patch("app.api.v1.views.video.get_tpstreams_license")
    def test_drm_license_should_be_tracked_for_valid_data(self, widevine_mock):
        widevine_mock.return_value = "data"
        self.create_membership(user=self.organization.created_by)
        video = self.create_video(
            content_protection_type=Video.ContentProtectionType.DRM
        )
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data=b"hello",
            content_type="application/octet-stream",
        )
        DRMLicenseView.as_view()(
            request, organization_id=self.organization.uuid, asset_id=video.asset.uuid
        )

        self.assertTrue(DRMLicenseLog.objects.filter(asset=video.asset).exists())

    def test_unauthorized_access_with_invalid_token_raises_405(self):
        video = self.create_video(
            content_protection_type=Video.ContentProtectionType.DRM
        )
        invalid_access_token = self.create_invalid_access_token(asset=video.asset)
        user = self.create_user()
        unset_current_tenant()
        self.create_membership(organization=self.create_organization(), user=user)
        request = self.get_request(
            f"/?drm_type=fairplay&access_token={invalid_access_token.uuid}",
            method="GET",
            user=user,
        )

        response = DRMLicenseView.as_view()(
            request, organization_id=self.organization.uuid, asset_id=video.asset.uuid
        )
        self.assertEqual(405, response.status_code)

    @mock.patch("app.api.v1.views.video.get_tpstreams_license")
    def test_should_return_widevine_license_with_given_specs(
        self, get_tpstreams_license_mock
    ):
        video = self.create_video(
            content_protection_type=Video.ContentProtectionType.DRM
        )
        widevine_license_specs = {
            "content_key_specs": [
                {
                    "track_type": "SD",
                    "security_level": 1,
                    "required_output_protection": {"hdcp": "HDCP_V1"},
                    "rental_duration_seconds": 1296000,
                    "license_duration_seconds": 1296000,
                },
                {
                    "track_type": "HD",
                    "security_level": 1,
                    "required_output_protection": {"hdcp": "HDCP_V1"},
                    "rental_duration_seconds": 1296000,
                    "license_duration_seconds": 1296000,
                },
                {
                    "track_type": "UHD1",
                    "security_level": 1,
                    "required_output_protection": {"hdcp": "HDCP_V1"},
                    "rental_duration_seconds": 1296000,
                    "license_duration_seconds": 1296000,
                },
                {
                    "track_type": "UHD2",
                    "security_level": 1,
                    "required_output_protection": {"hdcp": "HDCP_V1"},
                    "rental_duration_seconds": 1296000,
                    "license_duration_seconds": 1296000,
                },
                {
                    "track_type": "AUDIO",
                    "security_level": 1,
                    "required_output_protection": {"hdcp": "HDCP_V1"},
                    "rental_duration_seconds": 1296000,
                    "license_duration_seconds": 1296000,
                },
            ],
        }
        body = {
            "player_payload": base64.b64encode(b"dummy").decode("utf-8"),
            "widevine": widevine_license_specs,
        }

        request = self.get_request(
            f"/?access_token={self.create_access_token(asset=video.asset).uuid.hex}",
            method="POST",
            user=self.organization.created_by,
            data=body,
            content_type="application/json",
        )
        response = DRMLicenseView.as_view()(
            request, organization_id=self.organization.uuid, asset_id=video.asset.uuid
        )

        widevine_license_specs["can_persist"] = False
        widevine_license_specs["rental_duration_seconds"] = 60 * 60 * 24 * 15
        widevine_license_specs["license_duration_seconds"] = 60 * 60 * 24 * 15
        self.assertEqual(200, response.status_code)
        get_tpstreams_license_mock.assert_called_with(
            video.uuid.hex, b"dummy", "widevine", widevine_license_specs
        )

    @mock.patch("app.domain.drm.license.main.generate_fairplay_license")
    def test_should_return_fairplay_license_for_given_player_payload(
        self, mocked_generate_fairplay_license
    ):
        video = self.create_video(
            content_protection_type=Video.ContentProtectionType.DRM
        )
        player_payload = base64.b64encode(b"dummy").decode("utf-8")
        self.create_fairplay_encryption_key(
            content_id=video.uuid.hex, asset=video.asset
        )

        request = self.get_request(
            f"/?access_token={self.create_access_token(asset=video.asset).uuid.hex}&drm_type=fairplay",
            method="POST",
            user=self.organization.created_by,
            data={"player_payload": player_payload},
            content_type="application/json",
        )
        response = DRMLicenseView.as_view()(
            request, organization_id=self.organization.uuid, asset_id=video.asset.uuid
        )

        self.assertEqual(200, response.status_code)
        mocked_generate_fairplay_license.assert_called_with(
            player_payload,
            self.fairplay_key_data["key"],
            self.fairplay_key_data["iv"],
            {
                "rental_duration_seconds": 1296000,
                "can_persist": False,
                "license_duration_seconds": 1296000,
            },
        )


class TestStartTranscodingView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @responses.activate
    def test_api_should_return_200_after_transcoding_video(self):
        responses.add(
            responses.POST,
            f"{settings.TRANSCODER_URL}/create/",
            status=200,
            json={"id": str(uuid.uuid4())},
        )
        video = self.create_video(
            resolutions=[Video.Resolutions._240p, Video.Resolutions._720p]
        )
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(f"/?access_id={access_token.uuid}", method="POST")
        response = start_transcoding_view(
            request, organization_id=self.organization.uuid, asset_id=video.asset.uuid
        )
        video.refresh_from_db()

        self.assertEqual(200, response.status_code)
        self.assertEqual(Video.Status.QUEUED, video.status)

    @responses.activate
    def test_api_video_transcoding_should_return_404_if_incorrect_organization_uuid_is_passed(
        self,
    ):
        responses.add(responses.POST, f"{settings.TRANSCODER_URL}/create/", status=200)
        video = self.create_video(
            resolutions=[Video.Resolutions._240p, Video.Resolutions._720p]
        )
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(f"/?access_id={access_token.uuid}", method="POST")
        response = start_transcoding_view(
            request, organization_id=0, asset_id=video.asset.uuid
        )

        self.assertEqual(404, response.status_code)

    @responses.activate
    def test_api_video_transcoding_should_return_404_if_incorrect_asset_id_is_passed(
        self,
    ):
        responses.add(responses.POST, f"{settings.TRANSCODER_URL}/create/", status=200)
        video = self.create_video()
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(f"/?access_id={access_token.uuid}", method="POST")
        response = start_transcoding_view(
            request, organization_id=self.organization.uuid, asset_id=0
        )

        self.assertEqual(404, response.status_code)

    @responses.activate
    def test_api_video_transcoding_works_only_on_post_method(self):
        responses.add(responses.POST, f"{settings.TRANSCODER_URL}/create/", status=200)
        video = self.create_video()
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(f"/?access_id={access_token.uuid}", method="GET")
        response = start_transcoding_view(
            request, organization_id=self.organization.uuid, asset_id=video.asset.uuid
        )

        self.assertEqual(405, response.status_code)


class TestBulkCreateVideoAssetView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self):
        self.client = Client()
        self.organization = self.create_organization(limited_access_enabled=False)
        self.organization.created_by.current_organization_uuid = self.organization.uuid
        self.organization.created_by.save()

        set_current_tenant(self.organization)
        self.data = [
            {
                "title": "Asset 1",
                "content_protection_type": "drm",
                "resolutions": ["240p"],
            },
            {
                "title": "Asset 2",
                "content_protection_type": "drm",
                "resolutions": ["240p"],
            },
        ]

    def test_should_create_assets(self):
        self.client.force_login(self.organization.created_by)

        url = reverse(
            "api:bulk-create-video-assets",
            kwargs={"organization_id": self.organization.uuid},
        )
        data = json.dumps(self.data)
        response = self.client.post(url, data, content_type="application/json")

        self.assertEqual(201, response.status_code)
        self.assertTrue(Asset.objects.filter(title="Asset 1").exists())
        self.assertTrue(Asset.objects.filter(title="Asset 2").exists())

    def test_should_create_asset_as_child_for_given_folder(self):
        self.client.force_login(self.organization.created_by)
        folder = self.create_folder()
        data = [
            {
                "title": "Asset 3",
                "content_protection_type": "drm",
                "resolutions": ["240p"],
                "folder": folder.uuid,
            }
        ]

        url = reverse(
            "api:bulk-create-video-assets",
            kwargs={"organization_id": self.organization.uuid},
        )
        request_data = json.dumps(data)
        response = self.client.post(url, request_data, content_type="application/json")

        self.assertEqual(201, response.status_code)
        self.assertTrue(Asset.objects.filter(title="Asset 3", parent=folder).exists())

    def test_should_populate_enable_drm_field_based_on_content_protection_type(self):
        self.client.force_login(self.organization.created_by)

        data = [
            {
                "title": "Asset with DRM",
                "content_protection_type": "drm",
                "resolutions": ["240p"],
            }
        ]

        url = reverse(
            "api:bulk-create-video-assets",
            kwargs={"organization_id": self.organization.uuid},
        )
        request_data = json.dumps(data)
        response = self.client.post(url, request_data, content_type="application/json")

        self.assertEqual(201, response.status_code)
        asset_with_drm = Asset.objects.get(title="Asset with DRM")
        self.assertTrue(asset_with_drm.video.enable_drm)

    def test_should_limit_creation_of_assets_to_two_when_limited_access_enabled(self):
        unset_current_tenant()
        organization = self.create_organization(limited_access_enabled=True)
        organization.created_by.current_organization_uuid = organization.uuid
        organization.created_by.save()
        set_current_tenant(organization)

        self.create_assets(count=2, organization=organization)
        self.client.force_login(organization.created_by)

        data = [
            {
                "title": "Asset 3",
                "content_protection_type": "drm",
                "resolutions": ["240p"],
            }
        ]

        url = reverse(
            "api:bulk-create-video-assets",
            kwargs={"organization_id": organization.uuid},
        )
        request_data = json.dumps(data)
        response = self.client.post(url, request_data, content_type="application/json")

        self.assertEqual(400, response.status_code)


class TestAESEncryptionKeyView(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.asset = self.create_asset(type=Asset.Type.VIDEO)
        self.video = self.create_video(
            content_protection_type=Video.ContentProtectionType.AES, asset=self.asset
        )
        self.encryption_key = create_aes_encryption_key_if_not_present(self.asset)

    # def test_should_raise_403_on_unauthorized_request(self):
    #     request = self.get_request("/", method="GET")
    #     response = AESEncryptionKeyView.as_view()(
    #         request,
    #         organization_id=self.organization.uuid,
    #         asset_id=self.asset.uuid,
    #     )

    #     self.assertEqual(401, response.status_code)

    def test_should_return_aes_encryption_key_on_valid_request(self):
        access_token = self.create_access_token(
            asset=self.asset, expires_after_first_usage=True
        )
        request = self.get_request(f"/?access_token={access_token.uuid}", method="GET")
        response = AESEncryptionKeyView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_id=self.asset.uuid,
        )

        self.assertEqual(200, response.status_code)

    # def test_should_expire_single_usage_access_token(self):
    #     access_token = self.create_access_token(
    #         asset=self.asset, expires_after_first_usage=True
    #     )
    #     request = self.get_request(f"/?access_token={access_token.uuid}", method="GET")
    #     response = AESEncryptionKeyView.as_view()(
    #         request,
    #         organization_id=self.organization.uuid,
    #         asset_id=self.asset.uuid,
    #     )

    #     self.assertEqual(200, response.status_code)
    #     self.assertFalse(AccessToken.objects.get(uuid=access_token.uuid).is_active)


class TestUpdateMigratedVideoStatusViewOnCallback(
    OrganizationMixin, AssetMixin, TestCase
):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @mock.patch("app.api.v1.views.video.UpdateAssetSizeTask")
    @mock.patch("app.domain.storages.get_size", return_value=100000)
    def test_lumberjack_callback_should_update_video_metadata(
        self, mocked_get_size, mocked_task
    ):
        video = self.create_video()
        request_data = {
            "status": "completed",
            "video_duration": 120,
            "progress": 100,
        }
        request = self.get_request(
            "/",
            method="POST",
            data=request_data,
        )
        view = resolve(
            reverse(
                "api:lumberjack-migrated-video-callback",
                kwargs={
                    "organization_id": self.organization.uuid,
                    "asset_id": video.asset.uuid,
                },
            )
        )
        view.func(request, self.organization.uuid, video.asset.uuid)
        video.refresh_from_db()
        data = LumberjackDataParser(request_data).__dict__()
        data["has_tpstreams_drm"] = True
        self.assertEqual(video.meta_data, data)

    @mock.patch("app.api.v1.views.video.UpdateAssetSizeTask")
    @mock.patch("app.domain.storages.get_size", return_value=100000)
    def test_lumberjack_callback_should_not_update_video_metadata_if_transcode_fails(
        self, mocked_get_size, mocked_task
    ):
        video = self.create_video()
        request_data = {
            "status": "error",
            "progress": 50,
            "video_duration": 120,
        }
        request = self.get_request(
            "/",
            method="POST",
            data=request_data,
        )
        view = resolve(
            reverse(
                "api:lumberjack-migrated-video-callback",
                kwargs={
                    "organization_id": self.organization.uuid,
                    "asset_id": video.asset.uuid,
                },
            )
        )
        view.func(request, self.organization.uuid, video.asset.uuid)
        video.refresh_from_db()
        data = LumberjackDataParser(request_data).__dict__()
        data["has_tpstreams_drm"] = False
        self.assertDictEqual(video.meta_data, data)

    @mock.patch("app.api.v1.views.video.UpdateAssetSizeTask")
    @mock.patch("app.domain.storages.get_size", return_value=100000)
    def test_lumberjack_callback_should_update_output_urls_on_success(
        self, mocked_get_size, mocked_task
    ):
        video = self.create_video()
        request_data = {
            "status": "completed",
            "video_duration": 120,
            "progress": 100,
        }
        request = self.get_request(
            "/",
            method="POST",
            data=request_data,
        )
        view = resolve(
            reverse(
                "api:lumberjack-migrated-video-callback",
                kwargs={
                    "organization_id": self.organization.uuid,
                    "asset_id": video.asset.uuid,
                },
            )
        )
        view.func(request, self.organization.uuid, video.asset.uuid)
        video.refresh_from_db()

        expected_hls_url = f"transcoded/{video.asset.uuid}/new/video.m3u8"
        expected_dash_url = f"transcoded/{video.asset.uuid}/new/video.mpd"

        self.assertEqual(video.output_urls["h264"]["hls_url"], expected_hls_url)
        self.assertEqual(video.output_urls["h264"]["dash_url"], expected_dash_url)


class TestMigratedPaths(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_get_migrated_playback_url(self):
        video = self.create_video()
        playback_url = get_migrated_playback_url(video)
        expected_url = (
            f"{video.organization.cdn_url}transcoded/{video.asset.uuid}/new/video.m3u8"
        )
        self.assertEqual(playback_url, expected_url)

    def test_get_migrated_dash_url(self):
        video = self.create_video()
        dash_url = get_migrated_dash_url(video)
        expected_url = (
            f"{video.organization.cdn_url}transcoded/{video.asset.uuid}/new/video.mpd"
        )
        self.assertEqual(dash_url, expected_url)


class TestAssetMoveView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.user = self.create_user(current_organization_uuid=self.organization.uuid)
        set_current_tenant(self.organization)
        self.folder = self.create_folder()
        self.asset = self.create_asset()

    @patch("app.tasks.rebuild_asset_tree.delay")
    def test_api_should_move_asset_to_folder(self, mock_rebuild):
        request = self.get_request(
            "/",
            method="POST",
            user=self.organization.created_by,
            data={"parent": self.folder.uuid},
        )
        response = AssetMoveView.as_view()(
            request, organization_id=self.organization.uuid, asset_id=self.asset.uuid
        )
        self.asset.refresh_from_db()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(self.asset.parent, self.folder)

    def test_api_should_move_asset_to_root_directory(self):
        request = self.get_request(
            "/", method="POST", user=self.organization.created_by, data={"parent": None}
        )
        response = AssetMoveView.as_view()(
            request, organization_id=self.organization.uuid, asset_id=self.asset.uuid
        )
        self.asset.refresh_from_db()
        self.assertEqual(response.status_code, 200)
        self.assertIsNone(self.asset.parent)
