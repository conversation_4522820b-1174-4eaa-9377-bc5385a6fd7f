from datetime import datetime

from django.utils.crypto import get_random_string
from django_multitenant.utils import set_current_tenant

from app.api.v1.views.analytics import TrackAnalyticsView
from app.models.asset import AssetViewerLog
from app.utils.base64 import encode_value
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestTrackAnaltytics(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_cookie_handling(self):
        self.video = self.create_video()
        self.access_token = self.create_access_token(
            asset=self.video.asset, expires_after_first_usage=True
        )
        visitor_id = encode_value("visitor123")
        session_id = encode_value("session456")
        browserTimeHeaderValue = datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        data = {
            "visitorID": visitor_id,
            "sessionID": session_id,
        }
        headers = {
            "X-Browser-Time": browserTimeHeaderValue,
        }
        request = self.get_request(
            path=f"/?access_token={self.access_token.uuid}",
            method="post",
            data=data,
            headers=headers,
        )
        response = TrackAnalyticsView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_id=self.video.asset.uuid,
        )

        self.assertIn("visitorID", response.cookies)
        self.assertIn("sessionID", response.cookies)

        visitor_id_cookie = response.cookies["visitorID"]
        self.assertTrue(visitor_id_cookie["secure"])
        self.assertEqual(visitor_id_cookie["samesite"], "None")

    def test_track_view_with_new_asset_view(self):
        self.video = self.create_video()
        self.access_token = self.create_access_token(
            asset=self.video.asset, expires_after_first_usage=True
        )
        browserTimeHeaderValue = datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        data = {
            "duration": 120,
        }
        headers = {
            "X-Browser-Time": browserTimeHeaderValue,
        }
        request = self.get_request(
            path=f"/?access_token={self.access_token.uuid}",
            method="post",
            data=data,
            headers=headers,
        )
        response = TrackAnalyticsView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_id=self.video.asset.uuid,
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(AssetViewerLog.objects.count(), 1)

        new_asset_view = AssetViewerLog.objects.first()
        self.assertEqual(new_asset_view.asset, self.video.asset)
        self.assertEqual(new_asset_view.duration, 120)

    def test_track_view_with_existing_asset_view(self):
        self.video = self.create_video()
        self.access_token = self.create_access_token(
            asset=self.video.asset, expires_after_first_usage=True
        )
        visitor_id = get_random_string(length=32)
        session_id = get_random_string(length=40)
        existing_asset_view = AssetViewerLog.objects.create(
            asset=self.video.asset,
            organization=self.organization,
            visitor_id=visitor_id,
            session_id=session_id,
            duration=60,
            location="Existing Location",
        )

        views_count = self.video.asset.views_count
        asset_view_count = AssetViewerLog.objects.count()
        existing_visitor = encode_value(existing_asset_view.visitor_id)
        existing_session = encode_value(existing_asset_view.session_id)

        browserTimeHeaderValue = datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ")

        data = {
            "visitorID": existing_visitor,
            "sessionID": existing_session,
            "duration": 120,
        }
        headers = {
            "X-Browser-Time": browserTimeHeaderValue,
        }
        request = self.get_request(
            path=f"/?access_token={self.access_token.uuid}",
            method="post",
            data=data,
            headers=headers,
        )
        response = TrackAnalyticsView.as_view()(
            request,
            organization_id=self.organization.uuid,
            asset_id=self.video.asset.uuid,
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(self.video.asset.views_count, views_count)
        self.assertEqual(AssetViewerLog.objects.count(), asset_view_count)
        self.assertEqual(existing_asset_view, AssetViewerLog.objects.first())
        self.assertEqual(AssetViewerLog.objects.first().duration, 120)
