from datetime import timedel<PERSON>
from unittest import mock

from django.test import TestCase
from django.utils import timezone
from django_multitenant.utils import set_current_tenant
from rest_framework.exceptions import ValidationError

from app.api.v1.serializers.asset import AdminAssetDetailSerializer, AssetSerializer
from app.api.v1.serializers.live_stream import LiveStreamSerializer
from app.api.v1.serializers.transcoding_job import TranscodingJobSerializer
from app.models.asset import Asset
from tests.mixins import AssetMixin, OrganizationMixin


class TestLiveStreamSerializer(TestCase):
    def test_validate_start_with_past_time(self):
        past_time = timezone.now() - timedelta(minutes=1)
        live_stream_data = {
            "title": "Test Stream",
            "start": past_time,
        }

        serializer = LiveStreamSerializer(data=live_stream_data)

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertEqual(
            str(context.exception.detail["start"][0]),
            "Start time must be greater than current time",
        )

    def test_validate_start_with_future_time(self):
        future_time = timezone.now() + timedelta(minutes=1)
        live_stream_data = {
            "title": "Test Stream",
            "start": future_time,
        }

        serializer = LiveStreamSerializer(data=live_stream_data)

        self.assertTrue(serializer.is_valid())


class TestTranscodingJobSerializer(TestCase):
    @mock.patch("app.api.v1.serializers.transcoding_job.is_valid_url")
    def test_validate_input_url_is_valid_for_allowed_format(self, mock_is_valid_url):
        serializer = TranscodingJobSerializer()

        input_url = (
            "https://sample-videos.com/video123/mp4/720/big_buck_bunny_720p_1mb.mp4"
        )
        mock_is_valid_url.return_value = True

        validated_url = serializer.validate_input_url(input_url)
        self.assertEqual(input_url, validated_url)

    @mock.patch("app.api.v1.serializers.transcoding_job.is_valid_url")
    def test_validate_input_url_is_invalid_for_invalid_format(self, mock_is_valid_url):
        serializer = TranscodingJobSerializer()

        invalid_url = "https://sample-videos.com/img/Sample-png-image-100kb.png"
        mock_is_valid_url.return_value = True

        with self.assertRaises(ValidationError) as context:
            serializer.validate_input_url(invalid_url)

        self.assertEqual(
            context.exception.detail[0],
            "Please provide a valid video URL with a .mp4 or .avi format.",
        )

    def test_validate_input_url_is_invalid_for_invalid_url(self):
        serializer = TranscodingJobSerializer()

        invalid_url = "https://example.com/valid"

        with self.assertRaises(ValidationError) as context:
            serializer.validate_input_url(invalid_url)

        self.assertEqual(
            context.exception.detail[0],
            "Invalid input URL",
        )


class TestAssetSerializers(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.live_stream = self.create_livestream()
        self.asset = self.create_asset(
            type=Asset.Type.LIVESTREAM, live_stream=self.live_stream
        )

    def test_asset_serializer_does_not_include_live_stream_events(self):
        serializer = AssetSerializer(instance=self.asset)
        data = serializer.data

        self.assertNotIn("activities", data["live_stream"])

    def test_admin_asset_serializer_includes_live_stream_events(self):
        serializer = AdminAssetDetailSerializer(instance=self.asset)
        self.create_live_stream_events(type=1, live_stream=self.live_stream)
        self.create_live_stream_events(type=2, live_stream=self.live_stream)

        data = serializer.data

        self.assertIn("activities", data["live_stream"])
        self.assertIsInstance(data["live_stream"]["activities"], list)
        self.assertGreater(len(data["live_stream"]["activities"]), 1)

    def test_admin_asset_serializer_includes_non_user_related_live_stream_events(self):
        serializer = AdminAssetDetailSerializer(instance=self.asset)

        self.create_live_stream_events(type=1, live_stream=self.live_stream)
        self.create_live_stream_events(type=4, live_stream=self.live_stream)
        self.create_live_stream_events(type=2, live_stream=self.live_stream)

        data = serializer.data

        live_stream_events = data["live_stream"]["activities"]
        for event in live_stream_events:
            self.assertNotEqual(event["status"], 4)
