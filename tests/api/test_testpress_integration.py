from unittest.mock import patch

from django.conf import settings
from rest_framework import status
from rest_framework.test import APIClient, APITestCase

from app.models import Organization, User


class TestpressIntegrationViewTest(APITestCase):
    def setUp(self):
        self.client = APIClient()

        self.user = User.objects.create_user(
            name="testuser", email="<EMAIL>", password="securepassword123"
        )

        self.valid_data = {
            "user_name": "testuser",
            "email": "<EMAIL>",
            "password": "securepassword123",
            "org_name": "Test Organization",
            "cdn_id": "cdn-xyz",
            "cdn_url": "http://cdn.test.com",
            "bucket_name": "test-bucket",
            "bucket_secret_token": "secret-token",
            "cloudfront_key_group_id": "cloudfront-group-id",
            "storage_region": "us-west-1",
            "storage_access_key_id": "storage-key",
            "storage_secret_access_key": "storage-secret-key",
            "cdn_access_key_id": "cdn-access-key",
            "cdn_secret_access_key": "cdn-secret-key",
            "cdn_one_year_cache_policy_id": "policy-id-1",
            "cdn_expire_in_3_seconds_cache_policy_id": "policy-id-2",
            "cdn_public_key_id": "public-key-id",
            "cdn_private_key": "private-key",
            "webhook_url": "http://webhook.test.com",
            "secret_token": "webhook-secret-token",
            "institute_id": "institute-123",
        }

    @patch("boto3.client")
    def test_integration_view_success(self, mock_boto_client):
        mock_boto_client.return_value.get_distribution_config.return_value = {
            "DistributionConfig": {
                "Origins": {"Items": []},
                "CacheBehaviors": {"Items": []},
            },
            "ETag": "etag_value",
        }
        self.client.credentials(HTTP_Authorization=settings.TESTPRESS_AUTH_TOKEN)
        response = self.client.post(
            "/api/v1/testpress/livestream_integration/", self.valid_data, format="json"
        )

        organization = Organization.objects.first()

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["tpstreams_org_id"], str(organization.uuid))
        self.assertIn("auth_token", response.data)

    def test_integration_view_invalid_token(self):
        self.client.credentials(HTTP_Authorization="invalid-token")
        response = self.client.post(
            "/api/v1/testpress/livestream_integration/", self.valid_data, format="json"
        )

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        self.assertIn("detail", response.data)
        self.assertEqual(
            response.data["detail"], "Authentication credentials were not provided."
        )

    def test_integration_view_insufficient_data(self):
        invalid_data = {
            "user_name": "testuser",
            "email": "<EMAIL>",
        }

        self.client.credentials(HTTP_Authorization=settings.TESTPRESS_AUTH_TOKEN)
        response = self.client.post(
            "/api/v1/testpress/livestream_integration/", invalid_data, format="json"
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        self.assertIn("password", response.data)
        self.assertIn("org_name", response.data)
