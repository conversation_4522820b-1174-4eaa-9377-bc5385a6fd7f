from unittest import mock

from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse

from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestUploadThumbnailView(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.asset = self.create_asset()
        self.organization.created_by.current_organization_uuid = self.organization.uuid
        self.organization.created_by.save()

    @mock.patch("app.api.v1.views.thumbnail.upload_thumbnail")
    def test_should_upload_valid_thumbnail_image(self, mock_upload_thumbnail):
        image_file = SimpleUploadedFile(
            "thumbnail.jpg", b"Subtitle file content", content_type="image/jpeg"
        )
        data = {"thumbnail": image_file}

        url = reverse(
            "api:upload-thumbnail",
            kwargs={
                "organization_id": self.organization.uuid,
                "asset_id": self.asset.uuid,
            },
        )

        self.client.force_login(self.organization.created_by)
        response = self.client.post(url, data, format="multipart")

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["detail"], "Thumbnail uploaded successfully")

    @mock.patch("app.api.v1.views.thumbnail.upload_thumbnail")
    def test_should_return_400_for_invalid_thumbnail_image(self, mock_upload_thumbnail):
        image_file = SimpleUploadedFile(
            "thumbnail.mp4", b"Subtitle file content", content_type="image/jpeg"
        )
        data = {"thumbnail": image_file}

        url = reverse(
            "api:upload-thumbnail",
            kwargs={
                "organization_id": self.organization.uuid,
                "asset_id": self.asset.uuid,
            },
        )

        self.client.force_login(self.organization.created_by)
        response = self.client.post(url, data, format="multipart")

        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.data["detail"],
            "Upload a valid Thumbnail image in .png, .jpeg, .jpg format with size <= 2MB",
        )

    @mock.patch("app.api.v1.views.thumbnail.upload_thumbnail")
    def test_should_return_400_empty_file(self, mock_upload_thumbnail):
        data = {"thumbnail": ""}

        url = reverse(
            "api:upload-thumbnail",
            kwargs={
                "organization_id": self.organization.uuid,
                "asset_id": self.asset.uuid,
            },
        )

        self.client.force_login(self.organization.created_by)
        response = self.client.post(url, data, format="multipart")

        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.data["detail"],
            "Upload a valid Thumbnail image in .png, .jpeg, .jpg format with size <= 2MB",
        )
