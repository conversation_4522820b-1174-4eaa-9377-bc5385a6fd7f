import uuid
from unittest.mock import patch

from django_multitenant.utils import set_current_tenant
from knox.auth import TokenAuthentication
from rest_framework import permissions

from app.api.v1.permissions import HasOrganizationAccess
from app.api.v1.views.access_token import AccessTokenViewSet
from tests import TestCase
from tests.mixins import OrganizationMixin
from tests.mixins.asset import AssetMixin


class TestVideoAccessViewset(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_api_should_allow_only_authenticated_user(self):
        self.assertTrue(
            permissions.IsAuthenticated in AccessTokenViewSet.permission_classes
        )

    def test_api_should_have_organization_access_permission(self):
        self.assertTrue(HasOrganizationAccess in AccessTokenViewSet.permission_classes)

    def test_api_should_allow_token_based_authentication(self):
        self.assertTrue(
            TokenAuthentication in AccessTokenViewSet.authentication_classes
        )

    # def test_should_list_access_tokens_for_asset(self):
    #     video = self.create_video()
    #     self.create_access_token(asset=video.asset)
    #     self.create_access_token(asset=self.create_video().asset)
    #     request = self.get_request(user=self.organization.created_by, path="/")
    #     response = AccessTokenViewSet.as_view({"get": "list"})(
    #         request, organization_id=self.organization.uuid, asset_uuid=video.asset.uuid
    #     )

    #     self.assertEqual(200, response.status_code)
    #     self.assertEqual(1, response.data["count"])

    def test_should_not_list_access_tokens_for_different_asset(self):
        video = self.create_video()
        self.create_access_token(asset=self.create_video().asset)
        request = self.get_request(user=self.organization.created_by, path="/")
        response = AccessTokenViewSet.as_view({"get": "list"})(
            request, organization_id=self.organization.uuid, asset_uuid=video.asset.uuid
        )

        self.assertEqual(200, response.status_code)
        self.assertEqual(0, response.data["count"])

    # def test_should_list_access_tokens_from_cache(self):
    #     video = self.create_video()
    #     self.create_access_token_in_cache(asset=video.asset)
    #     request = self.get_request(user=self.organization.created_by, path="/")
    #     with patch(
    #         "django.conf.settings.USE_ACCESS_TOKEN_FROM_CACHE", [self.organization.uuid]
    #     ):
    #         response = AccessTokenViewSet.as_view({"get": "list"})(
    #             request,
    #             organization_id=self.organization.uuid,
    #             asset_uuid=video.asset.uuid,
    #         )
    #     self.assertEqual(200, response.status_code)
    #     self.assertEqual(1, response.data["count"])

    # def test_should_list_all_available_access_tokens_for_asset_from_cache(self):
    #     video = self.create_video()
    #     self.create_access_token_in_cache(asset=video.asset)
    #     self.create_access_token_in_cache(asset=video.asset)
    #     self.create_access_token_in_cache(asset=video.asset)
    #     request = self.get_request(user=self.organization.created_by, path="/")
    #     with patch(
    #         "django.conf.settings.USE_ACCESS_TOKEN_FROM_CACHE", [self.organization.uuid]
    #     ):
    #         response = AccessTokenViewSet.as_view({"get": "list"})(
    #             request,
    #             organization_id=self.organization.uuid,
    #             asset_uuid=video.asset.uuid,
    #         )
    #     self.assertEqual(200, response.status_code)
    #     self.assertEqual(3, response.data["count"])

    def test_should_not_list_access_tokens_for_different_asset_from_cache(self):
        video = self.create_video()
        self.create_access_token_in_cache(asset=self.create_video().asset)
        request = self.get_request(user=self.organization.created_by, path="/")
        with patch(
            "django.conf.settings.USE_ACCESS_TOKEN_FROM_CACHE", [self.organization.uuid]
        ):
            response = AccessTokenViewSet.as_view({"get": "list"})(
                request,
                organization_id=self.organization.uuid,
                asset_uuid=video.asset.uuid,
            )
        token_uuid = response.get("results", [{}])[0].get("code")
        self.assertNotEqual(video.asset.uuid, token_uuid)
        self.assertEqual(200, response.status_code)

    # def test_should_return_default_access_token_if_token_not_found_in_cache(self):
    #     video = self.create_video()
    #     request = self.get_request(user=self.organization.created_by, path="/")
    #     with patch(
    #         "django.conf.settings.USE_ACCESS_TOKEN_FROM_CACHE", [self.organization.uuid]
    #     ):
    #         response = AccessTokenViewSet.as_view({"get": "list"})(
    #             request,
    #             organization_id=self.organization.uuid,
    #             asset_uuid=video.asset.uuid,
    #         )
    #     self.assertEqual(200, response.status_code)
    #     self.assertEqual(1, response.data["count"])

    # def test_should_create_access_token_for_proper_data(self):
    #     video = self.create_video()
    #     request = self.get_request(
    #         "/",
    #         method="POST",
    #         user=self.organization.created_by,
    #         data={},
    #     )
    #     response = AccessTokenViewSet.as_view({"post": "create"})(
    #         request, organization_id=self.organization.uuid, asset_uuid=video.asset.uuid
    #     )

    #     self.assertEqual(201, response.status_code)
    #     self.assertEqual(1, video.asset.access_tokens.count())

    # @freeze_time("2000-10-01")
    # def test_should_create_access_token_with_validity_and_expiry_after_first_user(self):
    #     video = self.create_video()
    #     request = self.get_request(
    #         "/",
    #         method="POST",
    #         user=self.organization.created_by,
    #         data=json.dumps({"time_to_live": "60", "expires_after_first_usage": True}),
    #     )
    #     response = AccessTokenViewSet.as_view({"post": "create"})(
    #         request, organization_id=self.organization.uuid, asset_uuid=video.asset.uuid
    #     )

    #     self.assertEqual(201, response.status_code)
    #     access_token = AccessToken.objects.get(uuid=response.data.get("code"))
    #     self.assertEqual(access_token.valid_until, now() + timedelta(seconds=60))
    #     self.assertTrue(access_token.expires_after_first_usage)

    # def test_should_create_access_token_with_annotation(self):
    #     video = self.create_video()
    #     request = self.get_request(
    #         "/",
    #         method="POST",
    #         user=self.organization.created_by,
    #         data=json.dumps(
    #             {
    #                 "annotations": [
    #                     {
    #                         "type": "dynamic",
    #                         "text": "moving text",
    #                         "opacity": 0.8,
    #                         "color": "#FF0000",
    #                         "size": 15,
    #                         "interval": 1000,
    #                     },
    #                     {
    #                         "type": "dynamic",
    #                         "text": "moving text 2",
    #                         "opacity": 0.8,
    #                         "color": "#FF0000",
    #                         "size": 15,
    #                         "interval": 1000,
    #                     },
    #                 ]
    #             }
    #         ),
    #     )
    #     response = AccessTokenViewSet.as_view({"post": "create"})(
    #         request, organization_id=self.organization.uuid, asset_uuid=video.asset.uuid
    #     )

    #     self.assertEqual(201, response.status_code)
    #     self.assertTrue(
    #         AccessToken.objects.filter(uuid=response.data.get("code")).exists()
    #     )
    #     self.assertEqual(
    #         AccessTokenAnnotation.objects.filter(
    #             access_token__uuid=response.data.get("code")
    #         ).count(),
    #         2,
    #     )
    #     self.assertEqual(
    #         AccessTokenAnnotation.objects.filter(
    #             access_token__uuid=response.data.get("code")
    #         )[0].text,
    #         "moving text",
    #     )

    # def test_should_raise_error_for_invalid_annotation(self):
    #     video = self.create_video()
    #     request = self.get_request(
    #         "/",
    #         method="POST",
    #         user=self.organization.created_by,
    #         data=json.dumps(
    #             {
    #                 "annotations": [
    #                     {
    #                         "type": "invalid_type",
    #                         "text": "moving text",
    #                         "opacity": 0.8,
    #                         "color": "",
    #                         "size": 15,
    #                         "interval": 1000,
    #                     },
    #                     {
    #                         "type": "dynamic",
    #                         "text": "moving text 2",
    #                         "opacity": 0.8,
    #                         "color": "#FF0000",
    #                         "size": 15,
    #                         "interval": 1000,
    #                     },
    #                 ]
    #             }
    #         ),
    #     )
    #     response = AccessTokenViewSet.as_view({"post": "create"})(
    #         request, organization_id=self.organization.uuid, asset_uuid=video.asset.uuid
    #     )

    #     self.assertEqual(400, response.status_code)
    #     self.assertEqual(
    #         '"invalid_type" is not a valid choice.',
    #         response.data["annotations"][0]["type"][0],
    #     )
    #     self.assertEqual(
    #         "This field may not be blank.", response.data["annotations"][0]["color"][0]
    #     )

    def test_detail_view_should_return_404_for_invalid_access_token(self):
        video = self.create_video()
        request = self.get_request(user=self.organization.created_by, path="/")
        response = AccessTokenViewSet.as_view({"get": "retrieve"})(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=video.asset.uuid,
            uuid=uuid.uuid4(),
        )

        self.assertEqual(404, response.status_code)

    def test_detail_view_should_valid_data_for_correct_access_token(self):
        video = self.create_video()
        access_token = self.create_access_token(asset=video.asset)
        request = self.get_request(user=self.organization.created_by, path="/")
        response = AccessTokenViewSet.as_view({"get": "retrieve"})(
            request,
            organization_id=self.organization.uuid,
            asset_uuid=video.asset.uuid,
            uuid=access_token.uuid,
        )

        self.assertEqual(200, response.status_code)

    # @freeze_time("2000-10-01")
    # def test_should_update_access_token_as_expired_if_time_to_live_is_0(self):
    #     video = self.create_video()
    #     access_token = self.create_access_token(asset=video.asset)
    #     request = self.get_request(
    #         "/",
    #         method="put",
    #         user=self.organization.created_by,
    #         data=json.dumps({"time_to_live": 0}),
    #     )
    #     response = AccessTokenViewSet.as_view({"put": "update"})(
    #         request,
    #         organization_id=self.organization.uuid,
    #         asset_uuid=video.asset.uuid,
    #         uuid=access_token.uuid,
    #     )

    #     self.assertEqual(200, response.status_code)
    #     self.assertEqual(now(), AccessToken.objects.get(id=access_token.id).valid_until)
    #     self.assertEqual("Expired", response.data["status"])
