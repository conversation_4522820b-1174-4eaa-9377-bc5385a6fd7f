import uuid

from app.models import Encry<PERSON><PERSON>ey
from tests.mixins import AssetMixin


class EncryptionKeyMixin(AssetMixin):
    @property
    def fairplay_key_data(self):
        return {
            "iv": "f70bce4094fd4612abac60d9809c5b0c",
            "key": "3ab60de900d64edf9cb25a76f81794e6",
            "uri": "skd://e5573f8bb8ac47ea839a65beae73263d",
        }

    def create_fairplay_encryption_key(self, **kwargs):
        content_id = kwargs.pop("content_id", uuid.uuid4().hex)
        asset = kwargs.pop("asset", self.create_asset())
        key_data = kwargs.pop("key_data", self.fairplay_key_data)

        return EncryptionKey.objects.create(
            content_id=content_id,
            asset=asset,
            fairplay_encryption_key_data=key_data,
            organization=asset.organization,
        )
