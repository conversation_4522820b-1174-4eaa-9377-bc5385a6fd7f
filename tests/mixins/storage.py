from app.models import AssetUsage
from tests.factories import StorageUsageFactory


class StorageMixin:
    def create_daily_storage_usage(self, date):
        return StorageUsageFactory.create(organization=self.organization, date=date)  # type: ignore

    def create_daily_storage_usages(self, date, count=2):
        return StorageUsageFactory.create_batch(
            count, organization=self.organization, date=date  # type: ignore
        )

    def create_monthly_storage_usage(self, date):
        return StorageUsageFactory.create(
            organization=self.organization,  # type: ignore
            time_frame=AssetUsage.TimeFrames.MONTHLY,
            date=date,
        )

    def create_monthly_storage_usages(self, date, count=2):
        return StorageUsageFactory.create_batch(
            count,
            organization=self.organization,  # type: ignore
            time_frame=AssetUsage.TimeFrames.MONTHLY,
            date=date,
        )
