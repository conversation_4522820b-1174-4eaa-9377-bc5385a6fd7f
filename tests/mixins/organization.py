import factory
from django.db.models import signals

from tests.factories import MembershipFactory, OrganizationFactory, UserFactory


class OrganizationMixin:
    @factory.django.mute_signals(signals.post_save)
    def create_organization(self, **kwargs):
        return OrganizationFactory(
            created_by=kwargs.get("user") or self.create_user(), **kwargs
        )

    def create_user(self, **kwargs):
        current_organization_uuid = kwargs.pop("current_organization_uuid", None)
        return UserFactory(
            current_organization_uuid=current_organization_uuid, **kwargs
        )

    def create_membership(self, **kwargs):
        organization = kwargs.pop("organization", self.organization)  # type: ignore
        return MembershipFactory(organization=organization, **kwargs)
