from app.models import AssetUsage
from tests.factories import AssetUsageFactory


class BandwidthMixin:
    def create_daily_bandwidth_usage(self, date):
        return AssetUsageFactory.create(organization=self.organization, date=date)  # type: ignore

    def create_daily_bandwidth_usages(self, date, count=2):
        return AssetUsageFactory.create_batch(
            count, organization=self.organization, date=date  # type: ignore
        )

    def create_monthly_bandwidth_usage(self, date):
        return AssetUsageFactory.create(
            organization=self.organization,  # type: ignore
            time_frame=AssetUsage.TimeFrames.MONTHLY,
            date=date,
        )

    def create_monthly_bandwidth_usages(self, date, count=2):
        return AssetUsageFactory.create_batch(
            count,
            organization=self.organization,  # type: ignore
            time_frame=AssetUsage.TimeFrames.MONTHLY,
            date=date,
        )
