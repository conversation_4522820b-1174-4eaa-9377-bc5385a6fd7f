import json
from datetime import timedelta

from django.core.cache import cache
from django.utils.crypto import get_random_string
from django.utils.timezone import now

from app.models import (
    AccessToken,
    AccessTokenAnnotation,
    Asset,
    LiveStream,
    LiveStreamEvent,
    LiveStreamUsage,
)
from app.models.asset import AssetViewerLog
from tests.factories import (
    AssetFactory,
    PreviewThumbnailFactory,
    TrackFactory,
    VideoFactory,
    VideoInputFactory,
)


class AssetMixin:
    def create_asset(self, **kwargs):
        title = kwargs.pop("title", None)
        organization = kwargs.pop("organization", None)
        if title:
            return AssetFactory(organization=self.organization, title=title, **kwargs)  # type: ignore
        elif organization:
            return AssetFactory(organization=organization, **kwargs)  # type: ignore
        else:
            return AssetFactory(organization=self.organization, **kwargs)  # type: ignore

    def create_assets(self, count=2, **kwargs):
        assets = []
        for _ in range(count):
            assets.append(self.create_asset(**kwargs))
        return assets

    def create_folder(self, **kwargs):
        return self.create_asset(type=Asset.Type.FOLDER, **kwargs)

    def create_video(self, **kwargs):
        asset = kwargs.pop("asset", self.create_asset())
        video = VideoFactory(asset=asset, organization=self.organization, **kwargs)  # type: ignore
        VideoInputFactory(organization=self.organization, video=video)  # type: ignore
        return video

    def create_access_token(self, **kwargs):
        return AccessToken.objects.create(organization=self.organization, **kwargs)  # type: ignore

    def create_access_token_in_cache(self, asset, **kwargs):
        access_token = AccessToken.objects.create(organization=self.organization, asset=asset, **kwargs)  # type: ignore
        cache.set(
            f"org_id_{self.organization.uuid}_asset_id_{asset.uuid}_token_{access_token.uuid}",
            json.dumps(access_token.to_dict()),
        )
        return access_token

    def create_invalid_access_token(self, **kwargs):
        return AccessToken.objects.create(
            organization=self.organization, valid_until=now(), **kwargs  # type: ignore
        )

    def create_access_token_annotation(self, **kwargs):
        return AccessTokenAnnotation.objects.create(
            organization=self.organization, **kwargs  # type: ignore
        )

    def create_video_input(self, url, **kwargs):
        asset = self.create_asset()
        video = VideoFactory(asset=asset, organization=self.organization, **kwargs)  # type: ignore
        return VideoInputFactory(organization=self.organization, video=video, url=url)  # type: ignore

    def create_livestream(self, **kwargs):
        asset = kwargs.pop("asset", None) or self.create_asset()
        live_stream = LiveStream.objects.create(
            asset=asset, organization=self.organization, **kwargs  # type: ignore
        )
        LiveStreamUsage.objects.create(
            live_stream=live_stream,
            start_time=now(),
            server_ip="127.0.0.1",
            organization=live_stream.organization,
            server_provider=LiveStreamUsage.ServerProvider.AWS,
        )
        return live_stream

    def create_live_stream_events(self, type, live_stream, **kwargs):
        LiveStreamEvent.objects.create(
            live_stream=live_stream, type=type, organization=live_stream.organization
        )

    def create_live_stream_usage(self, start_time, live_stream, **kwargs):
        from app.models import LiveStreamUsage

        end_time = start_time + timedelta(hours=1)
        LiveStreamUsage.objects.create(
            live_stream=live_stream,
            organization=live_stream.organization,
            end_time=end_time,
            start_time=start_time,
        )


class AssetViewerLogMixin(AssetMixin):
    def create_asset_viewer_log(
        self, asset=None, session_id=None, visitor_id=None, duration=None, **kwargs
    ):
        asset = asset or self.create_asset()
        session_id = session_id or get_random_string(length=40)
        visitor_id = visitor_id or get_random_string(length=32)
        duration = duration or 0

        asset_viewer_log = AssetViewerLog.objects.create(
            organization=asset.organization,
            asset=asset,
            session_id=session_id,
            visitor_id=visitor_id,
            duration=duration,
        )
        return asset_viewer_log


class TrackMixin(AssetMixin):
    def create_subtitle(self, **kwargs):
        video = kwargs.pop("video", None) or self.create_video()
        return TrackFactory(organization=self.organization, video=video, **kwargs)  # type: ignore


class PreviewMixin(TrackMixin):
    def create_preview_thumbnail(self, **kwargs):
        track = kwargs.pop("track", None) or self.create_subtitle()
        return PreviewThumbnailFactory(organization=self.organization, track=track, **kwargs)  # type: ignore
