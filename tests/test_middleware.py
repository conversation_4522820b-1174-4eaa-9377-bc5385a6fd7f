from django.http import HttpResponse
from django.utils import timezone

from app.middlewares.timezone import TimezoneMiddleware
from tests import TestCase
from tests.mixins import OrganizationMixin


class TestTimezoneMiddleware(OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        self.user = self.organization.created_by

    def test_timezone_activates_with_user_timezone(self):
        middleware = TimezoneMiddleware(get_response=lambda request: HttpResponse())
        self.user.timezone = "America/New_York"
        self.user.save()
        request = self.get_request("/", user=self.user, method="GET")

        middleware(request)

        self.assertEqual(
            timezone.get_current_timezone_name(),
            "America/New_York",
            "The middleware should activate the correct timezone",
        )

    def test_timezone_activates_with_default_timezone(self):
        middleware = TimezoneMiddleware(get_response=lambda request: HttpResponse())
        request = self.get_request("/", user=self.user, method="GET")

        middleware(request)

        self.assertEqual(timezone.get_current_timezone_name(), "Asia/Kolkata")
