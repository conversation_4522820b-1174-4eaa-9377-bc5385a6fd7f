import io
from unittest import mock

from django.conf import settings
from django_multitenant.utils import set_current_tenant

from app.domain.subtitle import UploadError
from app.domain.thumbnail import update_cover_thumbnail, upload_thumbnail
from tests import TestCase
from tests.mixins import <PERSON>set<PERSON><PERSON><PERSON>, OrganizationMixin


class TestUpdateThumbnail(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @mock.patch(
        "app.domain.thumbnail.upload_thumbnail_to_cloud_storage",
        return_value="transcoded/sample/thumbnail.png",
    )
    @mock.patch("app.domain.thumbnail.invalidate_cache")
    def test_upload_thumbnail_methods_should_return_thumbnail_path(
        self, mock_invalidate_cache, mock_upload_image_file
    ):
        video = self.create_video()
        mock_file = io.BytesIO(b"Your mock file content")
        upload_thumbnail(input_file=mock_file, asset=video.asset)
        thumbnail_path = "transcoded/sample/thumbnail.png"
        self.assertIn(thumbnail_path, video.thumbnails)

    @mock.patch("app.domain.thumbnail.get_client")
    @mock.patch(
        "app.domain.thumbnail.generate_thumbnail_upload_path", return_value="test"
    )
    def test_upload_thumbnail_should_throw_error_for_invalid_asset(
        self, mock_get_client, mock_generate_thumbnail_upload_path
    ):
        mock_file = io.BytesIO(b"Your mock file content")
        mock_asset = mock.Mock()
        mock_asset.organization = None

        with self.assertRaises(UploadError):
            upload_thumbnail(input_file=mock_file, asset=mock_asset)

    def test_should_update_given_thumbanail_as_cover_thumbnail(self):
        video = self.create_video()
        thumbnail_path = "transcoded/sample/thumbnail.png"
        update_cover_thumbnail(asset=video.asset, path=thumbnail_path)
        self.assertEqual(video.cover_thumbnail_url, thumbnail_path)

    @mock.patch("app.domain.thumbnail.get_client")
    @mock.patch("app.domain.thumbnail.invalidate_cache")
    def test_should_upload_thumbnails_multiple_custom_thumbnail(
        self, mock_invalidate_cache, mock_delete_file
    ):
        video = self.create_video()
        video.thumbnails = []
        video.save(update_fields=["thumbnails"])
        asset = video.asset

        for _ in range(settings.MAXIMUM_THUMBNAIL_COUNT):
            mock_file = io.BytesIO(b"Mock file content")
            upload_thumbnail(asset, mock_file)
        asset.refresh_from_db()
        video.refresh_from_db()
        self.assertEqual(len(video.thumbnails), settings.MAXIMUM_THUMBNAIL_COUNT)
