from unittest import mock

from app.domain.aws import AWSOpenRestyServer
from tests import TestCase


class TestAWSOpenRestyServer(TestCase):
    def setUp(self):
        self.server = AWSOpenRestyServer(
            server_type="t2.medium",
            image_id="ami-12345678",
            region_name="us-east-1",
            security_group_id="sg-87654321",
            volume_size=100,
            volume_type="gp2",
        )

    @mock.patch("app.domain.aws.boto3.client")
    def test_spawn_proxy_server(self, mock_boto3_client):
        mock_ec2 = mock.Mock()
        mock_boto3_client.return_value = mock_ec2

        mock_response = {
            "Instances": [
                {
                    "InstanceId": "i-0abcd1234efgh5678",
                    "State": {"Name": "pending"},
                    "NetworkInterfaces": [
                        {
                            "PrivateIpAddress": "********",
                            "Association": {"PublicIp": "********"},
                        }
                    ],
                }
            ]
        }
        mock_ec2.run_instances.return_value = mock_response

        user_data_script = self.server.get_user_data_script_for_openresty()
        server_details = self.server.spawn_proxy_server(
            name="TestServer", user_data_script=user_data_script
        )

        mock_ec2.run_instances.assert_called_once()

        self.assertEqual(server_details["instance_id"], "i-0abcd1234efgh5678")
        self.assertEqual(server_details["state"], "pending")
        self.assertEqual(server_details["public_ip"], "********")
        self.assertEqual(server_details["private_ip"], "********")
