import datetime
from unittest import mock

from django.core.files.base import ContentFile
from django.utils import timezone
from django_multitenant.utils import set_current_tenant
from freezegun import freeze_time

from app.domain.subtitle import (
    SubtitleDataParser,
    UploadError,
    convert_srt_to_vtt,
    delete_subtitle_server_if_needed,
    get_subtitles_generation_minutes_for_day,
    get_subtitles_generation_minutes_for_month,
    save_subtitles_generation_minutes,
    store_subtitle_generation_details,
    upload_subtitle,
    upload_subtitle_from_url,
)
from app.models import AssetUsage, Track
from app.utils.wasabi import get_wasabi_config
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin
from tests.mixins.asset import TrackMixin


class TestSubtitle(TrackMixin, OrganizationMixin, AssetMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.video = self.create_video()

    @mock.patch(
        "app.domain.subtitle.upload_subtitle_to_cloud_storage",
        return_value="transcoded/sample/en.vtt",
    )
    @mock.patch("app.domain.subtitle.get_size", return_value=1000)
    def test_upload_subtile_methods_should_save_url_path(
        self, mock_get_size, mock_upload_text_file
    ):
        video = self.create_video()
        mock_file = ContentFile(b"file_content", name="mock_file.vtt")
        upload_subtitle(
            input_file=mock_file, asset=video.asset, name="name", language="en"
        )

        self.assertEqual(Track.objects.first().url, "transcoded/sample/en.vtt")

    @mock.patch("app.domain.subtitle.get_client")
    def test_upload_subtile_methods_should_throw_error_for_invalid_asset(
        self, mock_upload_text_file
    ):
        mock_file = ContentFile(b"file_content", name="mock_file.vtt")

        with self.assertRaises(UploadError):
            upload_subtitle(
                input_file=mock_file, asset="asset", name="name", language="en"
            )

    @mock.patch("app.domain.subtitle.upload_from_url_to_wasabi")
    @mock.patch("app.domain.subtitle.get_size", return_value=30)
    def test_upload_subtitle_from_url_method_should_save_url_path(
        self, mock_get_size, mock_upload_from_url_to_wasabi
    ):
        name = "Subtitle"
        language = "en"
        input_url = "http://example.com/subtitle.vtt"

        mock_upload_from_url_to_wasabi.return_value = None

        upload_subtitle_from_url(self.video.asset, name, language, input_url)
        config = get_wasabi_config(self.organization)
        mock_upload_from_url_to_wasabi.assert_called_once_with(
            input_url,
            f"{self.video.asset.organization.bucket_name}/transcoded/{self.video.asset.uuid}/{language}.vtt",
            config,
            make_public=True,
        )
        self.assertEqual(
            Track.objects.first().url,
            f"transcoded/{self.video.asset.uuid}/{language}.vtt",
        )

    @mock.patch("app.domain.subtitle.upload_from_url_to_wasabi")
    def test_upload_subtile_from_url_methods_should_throw_error_for_invalid_asset(
        self, mock_upload_from_url_to_wasabi
    ):
        input_url = "http://example.com/subtitle.vtt"

        with self.assertRaises(UploadError):
            upload_subtitle_from_url(
                input_url=input_url, asset="asset", name="name", language="en"
            )

    @mock.patch(
        "app.domain.subtitle.upload_from_url_to_wasabi",
        side_effect=Exception("Mocked Wasabi upload error"),
    )
    @mock.patch("app.domain.subtitle.get_size", return_value=30)
    def test_upload_subtitle_throws_error_for_invalid_url(
        self, mock_get_size, mock_upload_from_url_to_wasabi
    ):
        invalid_url = "not_a_url"

        with self.assertRaises(UploadError):
            upload_subtitle_from_url(
                input_url=invalid_url,
                asset=self.video.asset,
                name="name",
                language="en",
            )

    @mock.patch(
        "app.domain.subtitle.upload_subtitle_to_cloud_storage",
        return_value="transcoded/sample/en.vtt",
    )
    @mock.patch("app.domain.subtitle.get_size", return_value=1000)
    def test_track_object_created_while_uploading_subtitle(
        self, mock_get_size, mock_upload_captions
    ):
        video = self.create_video()
        mock_file = ContentFile(b"file_content", name="mock_file.vtt")
        upload_subtitle(
            input_file=mock_file, asset=video.asset, name="name", language="en"
        )

        self.assertEqual(Track.objects.first().language, "en")
        self.assertEqual(Track.objects.first().name, "name")
        self.assertEqual(Track.objects.first().video, video)

    @mock.patch(
        "app.domain.subtitle.timezone.now",
        return_value=datetime.datetime(
            2024, 2, 16, 2, 4, 33, tzinfo=datetime.timezone.utc
        ),
    )
    def test_store_subtitle_data_should_not_replace_existing_data(self, mock_time):
        track = self.create_subtitle()
        store_subtitle_generation_details(track, "STARTED", server_id="123")
        store_subtitle_generation_details(track, "UPLOADED")
        track = Track.objects.first()
        expected_subtitle_data = {
            "server_id": "123",
            "STARTED": "2024-02-16T02:04:33+00:00",
            "UPLOADED": "2024-02-16T02:04:33+00:00",
        }
        self.assertEqual(track.subtitle_data, expected_subtitle_data)

    @mock.patch("app.domain.subtitle.delete_aws_server")
    @mock.patch("app.domain.subtitle.delete_file")
    @mock.patch("app.domain.subtitle.send_subtitle_generation_status_email")
    @freeze_time("2024-03-14T11:38:00.229221+00:00")
    def test_delete_subtitle_server_if_needed(
        self, mock_send_email, mock_delete_file, mock_delete_server
    ):
        track = self.create_subtitle()
        server_details = {
            "volume_size": 64,
            "server_type_cost_per_hour_dollar": 1.006,
            "volume_cost_per_gb_per_hour_dollar": 0.000138,
        }
        store_subtitle_generation_details(
            track, "SERVER_STARTED", server_id="123", server_details=server_details
        )
        delete_subtitle_server_if_needed(track, "UPLOADED")
        mock_delete_server.assert_called_once_with("123", region_name="us-east-1")
        expected_subtitle_data = {
            "server_id": "123",
            "volume_size": 64,
            "server_type_cost_per_hour_dollar": 1.006,
            "volume_cost_per_gb_per_hour_dollar": 0.000138,
            "SERVER_STARTED": "2024-03-14T11:38:00.229221+00:00",
            "subtitle_generation_cost_dollar": None,
            "SERVER_DELETED": "2024-03-14T11:38:00.229221+00:00",
        }
        self.assertEqual(track.subtitle_data, expected_subtitle_data)
        mock_delete_server.reset_mock()
        track.subtitle_data = {}
        delete_subtitle_server_if_needed(track, "UPLOADED")
        mock_delete_server.assert_not_called()
        self.assertNotIn("SERVER_DELETED", track.subtitle_data)
        track.subtitle_data = {"server_id": "123"}
        delete_subtitle_server_if_needed(track, "AUDIO_EXTRACTED")
        mock_delete_server.assert_not_called()
        self.assertNotIn("SERVER_DELETED", track.subtitle_data)

    def test_convert_srt_to_vtt(self):
        srt_content = """1
        00:00:05,759 --> 00:00:07,580
        foreign"""

        expected_vtt_content = (
            "WEBVTT\n\n00:00:05.759 --> 00:00:07.580\n        foreign"
        )

        srt_file = ContentFile(srt_content.encode(), name="mock_file.srt")
        converted_vtt_file = convert_srt_to_vtt(srt_file)

        converted_vtt_file.seek(0)
        self.assertEqual(converted_vtt_file.read().decode(), expected_vtt_content)


class TestSubtitleDataParser(TrackMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.track = self.create_subtitle()
        self.current_time = timezone.now().isoformat()

    @mock.patch(
        "app.domain.subtitle.timezone.now",
        return_value=datetime.datetime(
            2024, 2, 16, 2, 4, 33, tzinfo=datetime.timezone.utc
        ),
    )
    def test_subtitle_data_property(self, mock_time):
        self.track.subtitle_data = {}
        self.track.subtitle_data["server_id"] = "123"
        self.track.save()
        parser = SubtitleDataParser(self.track, "STARTED")
        expected_subtitle_data = {
            "server_id": "123",
            "STARTED": "2024-02-16T02:04:33+00:00",
        }
        self.assertEqual(parser.subtitle_data, expected_subtitle_data)

    @mock.patch("app.domain.subtitle.has_object", return_value=True)
    def test_url_property(self, mock_has_object):
        parser = SubtitleDataParser(self.track, "UPLOADED")
        expected_url = (
            f"transcoded/{self.track.video.asset.uuid}/subtitles/en-autogenerated.vtt"
        )
        self.assertEqual(parser.url, expected_url)

    @mock.patch("app.domain.subtitle.has_object", return_value=True)
    @mock.patch("app.domain.subtitle.get_size", return_value=123)
    def test_bytes_property(self, mock_size, mock_has_object):
        parser = SubtitleDataParser(self.track, "UPLOADED")
        expected_bytes = 123
        self.assertEqual(parser.bytes, expected_bytes)
        mock_size.assert_called_with(
            self.track.organization,
            f"transcoded/{self.track.video.asset.uuid}/subtitles/en-autogenerated.vtt",
        )


class TestSubtitleGenerationDuration(TrackMixin, OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.video_10s = self.create_video(duration=datetime.timedelta(seconds=10))
        self.video_15s = self.create_video(duration=datetime.timedelta(seconds=15))
        self.video_30s = self.create_video(duration=datetime.timedelta(seconds=30))

    @freeze_time("2023-02-28")
    def test_should_return_minutes_of_subtitles_generated_for_given_day(self):
        date = timezone.now().date()
        yesterday = timezone.now() - datetime.timedelta(days=1)
        auto_track_today = self.create_subtitle(
            video=self.video_10s,
            subtitle_type=Track.SubtitleType.AUTO_GENERATED,
            url="url",
            bytes=4,
        )
        self.create_subtitle(
            video=self.video_15s,
            subtitle_type=Track.SubtitleType.AUTO_GENERATED,
            url="url",
            bytes=4,
            created=yesterday,
        )
        actual_minutes_of_subtitles_generated = (
            get_subtitles_generation_minutes_for_day(self.organization, date)
        )
        expected_minutes_of_subtitles_generated = auto_track_today.video.duration
        expected_minutes_of_subtitles_generated = round(
            (expected_minutes_of_subtitles_generated.total_seconds() or 0) / 60
        )
        self.assertEqual(
            expected_minutes_of_subtitles_generated,
            actual_minutes_of_subtitles_generated,
        )

    @freeze_time("2023-02-28")
    def test_should_return_minutes_of_subtitles_generated_for_given_month(self):
        date = timezone.now().date()
        yesterday = timezone.now() - datetime.timedelta(days=1)
        last_month = timezone.now() - datetime.timedelta(days=40)
        auto_track_today = self.create_subtitle(
            video=self.video_10s,
            subtitle_type=Track.SubtitleType.AUTO_GENERATED,
            url="url",
            bytes=4,
        )
        auto_track_yesterday = self.create_subtitle(
            video=self.video_15s,
            subtitle_type=Track.SubtitleType.AUTO_GENERATED,
            url="url",
            bytes=4,
            created=yesterday,
        )
        self.create_subtitle(
            video=self.video_30s,
            subtitle_type=Track.SubtitleType.AUTO_GENERATED,
            url="url",
            bytes=4,
            created=last_month,
        )
        actual_minutes_of_subtitles_generated = (
            get_subtitles_generation_minutes_for_month(self.organization, date)
        )
        expected_minutes_of_subtitles_generated = (
            auto_track_today.video.duration + auto_track_yesterday.video.duration
        )
        expected_minutes_of_subtitles_generated = round(
            (expected_minutes_of_subtitles_generated.total_seconds() or 0) / 60
        )
        self.assertEqual(
            expected_minutes_of_subtitles_generated,
            actual_minutes_of_subtitles_generated,
        )

    @freeze_time("2023-02-28")
    def test_save_subtitles_generated_seconds_creates_new_record(self):
        date = timezone.now().date()
        subtitle_mintues = 300
        save_subtitles_generation_minutes(self.organization, date, subtitle_mintues)
        asset_usage = AssetUsage.objects.get(
            date=date,
            organization=self.organization,
            time_frame=AssetUsage.TimeFrames.DAILY,
        )
        self.assertIsNotNone(asset_usage)
        self.assertEqual(asset_usage.subtitle_generation_minutes, subtitle_mintues)
