from unittest import mock

from django_multitenant.utils import set_current_tenant

from app.domain.cloud_storage import DeleteError, delete_file
from app.domain.purge_asset import (
    delete_source_folder,
    delete_transcoded_folder,
    delete_video,
)
from app.domain.video import stop_transcoding
from app.models import Video
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestHardDelete(TestCase, AssetMixin, OrganizationMixin):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @mock.patch("app.domain.purge_asset.delete_folder")
    def test_delete_transcoded_folder_method_should_delete_the_transcoded_file(
        self, mock_delete_file
    ):
        video = self.create_video()

        delete_transcoded_folder(video)

        mock_delete_file.assert_called_with(
            self.organization, f"transcoded/{video.asset.uuid}"
        )

    @mock.patch("app.domain.purge_asset.delete_folder")
    @mock.patch(
        "app.domain.purge_asset.get_deleted_source_folder_paths",
        return_value=[
            "private/source_file.mp4",
        ],
    )
    def test_delete_source_folder_method_should_delete_the_source_file(
        self, mock_source_file_paths, mock_delete_file
    ):
        video = self.create_video()

        delete_source_folder(video)

        mock_delete_file.assert_called_with(
            self.organization, "private/source_file.mp4"
        )

    @mock.patch("app.domain.cloud_storage.get_all_object_paths")
    def test_hard_delete_error_should_raise_for_incorrect_file_path(
        self, mock_get_all_object_paths
    ):
        mock_get_all_object_paths.return_value = [
            "correct/file/path",
            "another/correct/path",
        ]
        with self.assertRaises(DeleteError):
            delete_file(self.organization, file_path="incorrect/file/path")

    @mock.patch("app.domain.purge_asset.delete_transcoded_folder")
    @mock.patch("app.domain.purge_asset.delete_source_folder")
    def test_delete_video_should_change_video_state_to_deleting(
        self, mock_delete_transcoded_folder, mock_delete_source_folder
    ):
        video = self.create_video()
        delete_video(video)
        video.refresh_from_db()

        self.assertEqual(video.get_status_display(), "Deleting")


class TestStopTranscoding(TestCase, AssetMixin, OrganizationMixin):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @mock.patch("app.domain.transcoder.get_transcoder")
    def test_stop_transcoding_should_call_stop_for_queued_status(
        self, mock_get_transcoder
    ):
        video_queued = self.create_video(status=Video.Status.QUEUED)
        asset = video_queued.asset
        mock_transcoder = mock_get_transcoder.return_value
        mock_transcoder.stop = mock.MagicMock()

        stop_transcoding(asset)
        mock_transcoder.stop.assert_called_once_with()

    @mock.patch("app.domain.transcoder.get_transcoder")
    def test_stop_transcoding_should_call_stop_for_transcoding_status(
        self, mock_get_transcoder
    ):
        video_transcoding = self.create_video(status=Video.Status.TRANSCODING)
        asset = video_transcoding.asset

        mock_transcoder = mock_get_transcoder.return_value
        mock_transcoder.stop = mock.MagicMock()

        stop_transcoding(asset)
        mock_transcoder.stop.assert_called_once_with()

    @mock.patch("app.domain.transcoder.get_transcoder")
    def test_stop_transcoding_should_not_call_stop_for_completed_status(
        self, mock_get_transcoder
    ):
        video_completed = self.create_video(status=Video.Status.COMPLETED)
        asset = video_completed.asset

        mock_transcoder = mock_get_transcoder.return_value
        mock_transcoder.stop = mock.MagicMock()

        stop_transcoding(asset)
        mock_transcoder.stop.assert_not_called()
