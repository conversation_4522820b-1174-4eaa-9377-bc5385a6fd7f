from datetime import timed<PERSON><PERSON>
from unittest import mock

import responses
from django.utils.timezone import now
from django_multitenant.utils import set_current_tenant
from freezegun import freeze_time

from app.domain.live_stream import (
    calculate_total_stream_duration,
    create_live_stream,
    stop_live_stream,
    terminate_live_server_on_transcoding_completion,
)
from app.models import (
    Encryption<PERSON>ey,
    LiveStream,
    LiveStreamEvent,
    ScheduledTaskReference,
    Video,
)
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin, ScheduledTaskReferenceMixin


class TestStopLiveStream(
    OrganizationMixin, AssetMixin, ScheduledTaskReferenceMixin, TestCase
):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @mock.patch("app.domain.live_stream.is_live_stream_content_available")
    @responses.activate
    def test_live_streaming_server_stop_transcoding_api_should_be_called(
        self, mock_is_live_stream_content_available
    ):
        responses.add(responses.POST, "http://127.0.0.1/stop_transcoding/", status=200)
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_ip="127.0.0.1",
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        stop_live_stream(live_stream)

    @mock.patch("boto3.client")
    @mock.patch("app.domain.live_stream.schedule_check_live_server_deletion")
    @mock.patch("app.tasks.send_mail.send_email_task.apply_async")
    def test_live_stream_server_should_be_deleted_if_stream_is_not_started(
        self, mock_send_email, mock_schedule_check, mock_boto3
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.NOT_STARTED,
            server_ip="127.0.0.1",
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        stop_live_stream(live_stream)

        mock_boto3().terminate_instances.assert_called_with(
            InstanceIds=["1234"], DryRun=False
        )

    @mock.patch("app.domain.live_stream.is_live_stream_content_available")
    @responses.activate
    @freeze_time("2023-03-07 12:34:56")
    def test_live_streaming_should_have_end_time(
        self, mock_is_live_stream_content_available
    ):
        responses.add(responses.POST, "http://127.0.0.1/stop_transcoding/", status=200)
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_ip="127.0.0.1",
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        stop_live_stream(live_stream)

        self.assertIsNotNone(live_stream.end)
        self.assertEqual(
            live_stream.end.strftime("%Y-%m-%d %H:%M:%S"), "2023-03-07 12:34:56"
        )

    @mock.patch("app.domain.live_stream.is_live_stream_content_available")
    @responses.activate
    @freeze_time("2023-03-07 12:34:56")
    def test_stop_live_stream_should_create_stop_event(
        self, mock_is_live_stream_content_available
    ):
        responses.add(responses.POST, "http://127.0.0.1/stop_transcoding/", status=200)
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_ip="127.0.0.1",
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        stop_live_stream(live_stream)

        stop_event = LiveStreamEvent.objects.get(
            type=LiveStreamEvent.Type.STOPPED,
            live_stream=live_stream,
            organization=live_stream.organization,
        )

        self.assertEqual(stop_event.type, LiveStreamEvent.Type.STOPPED)
        self.assertEqual(stop_event.live_stream, live_stream)
        self.assertEqual(stop_event.organization, live_stream.organization)

    @responses.activate
    def test_scheduled_task_should_be_revoked_when_live_stream_stops(self):
        task = self.create_scheduled_task_reference()
        responses.add(responses.POST, "http://127.0.0.1/stop_transcoding/", status=200)
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_ip="127.0.0.1",
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
            server_termination_task_id=task.task_id,
        )
        stop_live_stream(live_stream)

        deleted_scheduled_task = ScheduledTaskReference.objects.deleted_only().first()
        self.assertIsNotNone(deleted_scheduled_task)
        self.assertEqual(
            deleted_scheduled_task.task_id, live_stream.server_termination_task_id
        )

    @responses.activate
    def test_stop_live_stream_should_continue_even_if_task_already_revoked(self):
        task = self.create_scheduled_task_reference()
        responses.add(responses.POST, "http://127.0.0.1/stop_transcoding/", status=200)
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_ip="127.0.0.1",
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
            server_termination_task_id=task.task_id,
        )

        ScheduledTaskReference.objects.revoke(
            task_id=live_stream.server_termination_task_id
        )
        self.assertEqual(
            ScheduledTaskReference.objects.filter(
                task_id=live_stream.server_termination_task_id
            ).count(),
            0,
        )

        stop_live_stream(live_stream)
        self.assertEqual(live_stream.status, LiveStream.Status.STOPPED)

    @mock.patch("app.domain.video_trim.core.start_video_trim_background_task")
    @mock.patch("app.domain.live_stream.delete_live_stream_server")
    def test_terminate_live_server_executes_pending_trim(
        self, mock_delete_server, mock_trim_task
    ):
        asset = self.create_asset()
        live_stream = self.create_livestream(asset=asset)
        video = self.create_video(asset=asset, status=Video.Status.COMPLETED)
        video.duration = timedelta(seconds=200)
        video.save()

        live_stream.scheduled_trim_data = {
            "start_time": 30,
            "end_time": 120,
            "user_id": self.organization.created_by.id,
        }
        live_stream.save(update_fields=["scheduled_trim_data"])

        asset.refresh_from_db()
        asset.live_stream.refresh_from_db()

        terminate_live_server_on_transcoding_completion(asset)

        mock_trim_task.assert_called_once_with(
            video, 30, 120, self.organization.created_by
        )
        mock_delete_server.assert_called_once()

        asset.live_stream.refresh_from_db()
        self.assertEqual(
            asset.live_stream.scheduled_trim_data,
            {
                "start_time": 30,
                "end_time": 120,
                "user_id": self.organization.created_by.id,
            },
        )

    @mock.patch("app.domain.video_trim.core.start_video_trim_background_task")
    @mock.patch("app.domain.live_stream.delete_live_stream_server")
    def test_terminate_live_server_without_pending_trim(
        self, mock_delete_server, mock_trim_task
    ):
        asset = self.create_asset()
        self.create_livestream(asset=asset)
        self.create_video(asset=asset, status=2)

        terminate_live_server_on_transcoding_completion(asset)
        mock_trim_task.assert_not_called()
        mock_delete_server.assert_called_once()


class TestCreateLiveStream(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.user = self.organization.created_by

    @mock.patch("app.domain.live_stream.create_remote_live_stream_server")
    @mock.patch("app.domain.live_stream.store_live_stream_server_details")
    def test_create_live_stream_should_start_server_instantly(
        self, mock_create_server, mock_store_details
    ):
        live_stream = create_live_stream(
            title="Test Live Stream",
            organization=self.organization,
            enable_drm_for_recording=True,
            user=self.user,
            transcode_recorded_video=True,
            start=now() + timedelta(minutes=1),  # Should start instantly
        )
        self.assertEqual(live_stream, LiveStream.objects.first())
        self.assertTrue(mock_store_details.called)
        self.assertTrue(mock_create_server.called)

    @mock.patch("app.domain.live_stream.create_remote_live_stream_server")
    @mock.patch("app.domain.live_stream.store_live_stream_server_details")
    def test_create_live_stream_should_not_start_server_instantly(
        self, mock_create_server, mock_store_details
    ):
        live_stream = create_live_stream(
            title="Test Live Stream",
            organization=self.organization,
            enable_drm_for_recording=True,
            user=self.user,
            transcode_recorded_video=True,
            start=now() + timedelta(minutes=10),  # Should not start instantly
        )
        self.assertEqual(live_stream, LiveStream.objects.first())
        self.assertFalse(mock_store_details.called)
        self.assertFalse(mock_create_server.called)

    @mock.patch("app.domain.live_stream.create_remote_live_stream_server")
    @mock.patch("app.domain.live_stream.store_live_stream_server_details")
    def test_create_live_stream_should_create_encryption_key_if_enable_drm_is_true(
        self, mock_create_server, mock_store_details
    ):
        live_stream = create_live_stream(
            title="Test Live Stream",
            organization=self.organization,
            enable_drm_for_recording=True,
            user=self.user,
            transcode_recorded_video=True,
            start=now() + timedelta(minutes=1),
            enable_drm=True,
        )
        self.assertTrue(
            EncryptionKey.objects.filter(
                content_id=live_stream.uuid, fairplay_encryption_key_data__isnull=False
            ).exists()
        )

    @mock.patch("app.domain.live_stream.create_remote_live_stream_server")
    @mock.patch("app.domain.live_stream.store_live_stream_server_details")
    def test_create_live_stream_should_not_create_encryption_key_if_enable_drm_is_false(
        self, mock_create_server, mock_store_details
    ):
        live_stream = create_live_stream(
            title="Test Live Stream",
            organization=self.organization,
            enable_drm_for_recording=True,
            user=self.user,
            transcode_recorded_video=True,
            start=now() + timedelta(minutes=1),
            enable_drm=False,
        )
        self.assertFalse(
            EncryptionKey.objects.filter(
                content_id=live_stream.uuid, fairplay_encryption_key_data__isnull=False
            ).exists()
        )


class TestLiveStreamDuration(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_calculate_total_stream_duration_should_return_stream_duration(
        self,
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_ip="127.0.0.1",
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        with freeze_time("2024-02-21 13:00:00"):
            self.create_live_stream_events(type=1, live_stream=live_stream)
        with freeze_time("2024-02-21 14:00:00"):
            self.create_live_stream_events(type=2, live_stream=live_stream)
        duration = calculate_total_stream_duration(live_stream)
        one_hour_in_seconds = 3600
        self.assertEqual(duration, one_hour_in_seconds - 70)

    def test_live_stream_duration_return_zero_if_there_is_only_start_event(
        self,
    ):
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_ip="127.0.0.1",
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        with freeze_time("2024-02-21 13:00:00"):
            self.create_live_stream_events(type=1, live_stream=live_stream)
        duration = calculate_total_stream_duration(live_stream)
        self.assertEqual(duration, 0)
