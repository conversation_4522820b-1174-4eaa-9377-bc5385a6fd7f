import datetime
from unittest import mock
from unittest.mock import patch

import pytz
from django_multitenant.utils import set_current_tenant

from app.domain.video import (
    LumberjackDataParser,
    get_all_asset_data,
    get_playlist_paths,
    store_video_playlist_info,
)
from app.models.video import Video
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestVideo(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_get_all_asset_data_should_return_asset_data_for_completed_asset(self):
        from app.models import Asset, Video

        video = self.create_video()
        video.status = Video.Status.COMPLETED
        video.save()

        data = get_all_asset_data()

        expected_data = Asset.objects.filter(
            uuid=video.asset.uuid,
            video__isnull=False,
            video__status=Video.Status.COMPLETED,
        ).values_list("title", "uuid", "bytes", "video__duration", named=True)

        self.assertEqual(list(data), list(expected_data))

    def test_get_all_asset_data_should_not_return_asset_data_for_incompleted_asset(
        self,
    ):
        self.create_video()
        data = get_all_asset_data()

        self.assertEqual(data.first(), None)


class TestLumberjackDataParser(TestCase):
    def setUp(self):
        self.sample_data = {
            "status": "queued",
            "start_time": "2023-11-22T19:07:04.031015Z",
            "end_time": "2023-11-22T19:07:13.825918Z",
        }
        self.parser = LumberjackDataParser(self.sample_data)

    def test_transcoding_status_queued(self):
        self.assertEqual(self.parser.transcoding_status, Video.Status.QUEUED)

    def test_transcoding_status_transcoding(self):
        self.parser.data["status"] = "processing"
        self.assertEqual(self.parser.transcoding_status, Video.Status.TRANSCODING)

    def test_transcoding_status_completed(self):
        self.parser.data["status"] = "completed"
        self.assertEqual(self.parser.transcoding_status, Video.Status.COMPLETED)

    def test_transcoding_status_error(self):
        self.parser.data["status"] = "unknown_status"
        self.assertEqual(self.parser.transcoding_status, Video.Status.ERROR)

    def test_duration(self):
        self.parser.data["video_duration"] = 120
        self.assertEqual(self.parser.duration, datetime.timedelta(seconds=120))

    def test_duration_is_none(self):
        self.parser.data["video_duration"] = None
        self.assertEqual(self.parser.duration, None)

    def test_transcoding_start_time(self):
        expected_start_time = datetime.datetime.strptime(
            "2023-11-22T19:07:04.031015Z", "%Y-%m-%dT%H:%M:%S.%fZ"
        ).replace(tzinfo=pytz.utc)
        self.assertEqual(self.parser.transcoding_start_time, expected_start_time)

    def test_transcoding_end_time(self):
        expected_end_time = datetime.datetime.strptime(
            "2023-11-22T19:07:13.825918Z", "%Y-%m-%dT%H:%M:%S.%fZ"
        ).replace(tzinfo=pytz.utc)
        self.assertEqual(self.parser.transcoding_end_time, expected_end_time)


class TestVideoPlaylistInfo(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.video = self.create_video()

    @patch("app.domain.video.get_transcoded_files_info")
    @patch("app.domain.track.create_playlist_track")
    def test_store_video_playlist_info_should_create_tracks(
        self, mock_create_track, mock_get_info
    ):
        self.video.resolutions = [0, 1]
        self.video.save()
        mock_get_info.return_value = [
            {
                "name": "240p_hls",
                "path": f"transcoded/{self.video.asset.uuid}/240p_hls",
                "size": 1024,
                "width": 426,
                "height": 240,
            },
            {
                "name": "360p_dash",
                "path": f"transcoded/{self.video.asset.uuid}/360p_dash",
                "size": 2048,
                "width": 640,
                "height": 360,
            },
        ]
        mock_create_track.return_value = (None, True)
        store_video_playlist_info(self.video)

        expected_calls = [
            mock.call(
                self.video.asset,
                name="240p_hls",
                path=f"transcoded/{self.video.asset.uuid}/240p_hls",
                size=1024,
                width=426,
                height=240,
            ),
            mock.call(
                self.video.asset,
                name="360p_dash",
                path=f"transcoded/{self.video.asset.uuid}/360p_dash",
                size=2048,
                width=640,
                height=360,
            ),
        ]
        mock_create_track.assert_has_calls(expected_calls, any_order=True)

    @patch("app.domain.track.create_playlist_track")
    def test_should_not_store_video_playlist_info_with_no_video_playlists(
        self, mock_create_track
    ):
        self.video.resolutions = []
        store_video_playlist_info(self.video)
        mock_create_track.assert_not_called()

    @patch("app.domain.track.create_playlist_track")
    def test_store_video_playlist_info_should_avoid_duplicates(self, mock_create_track):
        mock_create_track.side_effect = [(None, False)] * 2

        store_video_playlist_info(self.video)

        mock_create_track.assert_not_called()

    def test_should_get_playlist_file_paths_content_protection_disabled(self):
        self.video.content_protection_type = Video.ContentProtectionType.DISABLED
        paths = get_playlist_paths(self.video, 0)
        self.assertEqual(paths, [f"transcoded/{self.video.asset.uuid}/240p"])

    def test_should_get_playlist_file_paths_drm(self):
        self.video.content_protection_type = Video.ContentProtectionType.DRM
        paths = get_playlist_paths(self.video, 0)
        expected_paths = [
            f"transcoded/{self.video.asset.uuid}/240p_dash",
            f"transcoded/{self.video.asset.uuid}/240p_hls",
        ]
        self.assertCountEqual(paths, expected_paths)
