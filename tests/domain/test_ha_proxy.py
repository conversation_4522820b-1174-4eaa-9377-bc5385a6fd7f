import json

import responses
from django.test import override_settings

from app.utils.haproxy import (
    add_backend_switching_rule,
    add_ip_address_to_backend,
    add_new_backend_server,
    get_configuration_version_number,
    remove_backend,
    remove_backend_switching_rule,
)
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin

BACKEND_SWITCHING_RULES = {
    "_version": 301,
    "data": [
        {
            "cond": "if",
            "cond_test": "{ path_beg /live/6rng4k/5rNn8ubsbX4 }",
            "index": 0,
            "name": "backend_6rng4k_5rNn8ubsbX4",
        },
        {
            "cond": "if",
            "cond_test": "{ path_beg /live/6rng4k/9yn89TB6ps7 }",
            "index": 1,
            "name": "backend_6rng4k_9yn89TB6ps7",
        },
    ],
}


@override_settings(PROXY_URL="http://127.0.0.1", PROXY_API_KEY="abc")
class TestHAProxy(OrganizationMixin, AssetMixin, TestCase):
    @responses.activate
    def test_get_configuration_version_number_should_call_info_api_and_return_version(
        self,
    ):
        responses.add(
            responses.GET,
            "http://127.0.0.1/v2/info",
            json={"_version": 1},
            adding_headers={"Configuration-Version": "1"},
        )
        version = get_configuration_version_number()

        self.assertEqual("1", version)

    @responses.activate
    def test_add_new_backend_server_should_call_api_with_backend_name(
        self,
    ):
        responses.add(
            responses.POST,
            "http://127.0.0.1/v2/services/haproxy/configuration/backends?version=1",
            json={},
            adding_headers={"Configuration-Version": "2"},
        )
        version = add_new_backend_server("abc", 1)

        self.assertEqual("2", version)
        self.assertEqual(json.dumps({"name": "abc"}), responses.calls[0].request.body)

    @responses.activate
    def test_add_ip_address_to_backend_should_call_api_with_proper_parameters(
        self,
    ):
        responses.add(
            responses.POST,
            "http://127.0.0.1/v2/services/haproxy/configuration/servers?version=1&parent_type=backend&parent_name=abc",
            json={},
            adding_headers={"Configuration-Version": "3"},
        )
        version = add_ip_address_to_backend("***********", "abc", 1)

        expected_data = {
            "name": "server1",
            "address": "***********",
            "port": 80,
        }
        self.assertEqual("3", version)
        self.assertEqual(json.dumps(expected_data), responses.calls[0].request.body)

    @responses.activate
    def test_add_backend_switching_rule_should_call_api_with_proper_parameters(
        self,
    ):
        responses.add(
            responses.POST,
            "http://127.0.0.1/v2/services/haproxy/configuration/backend_switching_rules?frontend=livestream&version=2",
            json={},
            adding_headers={"Configuration-Version": "3"},
        )
        add_backend_switching_rule("abc", "/live/abc", 2)

        expected_data = {
            "cond": "if",
            "cond_test": "{ path_beg /live/abc }",
            "index": 0,
            "name": "abc",
        }
        self.assertEqual(json.dumps(expected_data), responses.calls[0].request.body)

    @responses.activate
    def test_remove_backend_switching_rule_should_remove_rule_within_transaction(self):
        responses.add(
            responses.GET,
            "http://127.0.0.1/v2/services/haproxy/configuration/backend_switching_rules?frontend=livestream&version=2",
            json=BACKEND_SWITCHING_RULES,
            adding_headers={"Configuration-Version": "3"},
        )
        responses.add(
            responses.POST,
            "http://127.0.0.1/v2/services/haproxy/transactions?version=2",
            json={
                "_version": 304,
                "id": "be024d65-8ab0-44d5-8d27-2a46745a6aa2",
                "status": "in_progress",
            },
            adding_headers={"Configuration-Version": "3"},
        )
        responses.add(
            responses.DELETE,
            "http://127.0.0.1/v2/services/haproxy/configuration/"
            "backend_switching_rules/1?transaction_id=be024d65-8ab0-44d5-8d27-2a46745a6aa2&frontend=livestream",
            adding_headers={"Configuration-Version": "3"},
        )
        responses.add(
            responses.PUT,
            "http://127.0.0.1/v2/services/haproxy/transactions/be024d65-8ab0-44d5-8d27-2a46745a6aa2?version=3",
        )

        remove_backend_switching_rule(2, "backend_6rng4k_9yn89TB6ps7")

    @responses.activate
    def test_remove_backend_should_call_appropriate_api(self):
        responses.add(
            responses.DELETE,
            "http://127.0.0.1/v2/services/haproxy/configuration/backends/backend_6rng4k_9yn89TB6ps7?version=100",
            json={},
            adding_headers={"Configuration-Version": "3"},
        )
        remove_backend(100, "backend_6rng4k_9yn89TB6ps7")
