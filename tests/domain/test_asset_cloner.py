from django_multitenant.utils import set_current_tenant

from app.domain.asset import AssetCloner
from app.models.asset import Asset
from tests import TestCase
from tests.mixins import OrganizationMixin
from tests.mixins.asset import AssetMixin


class TestAssetCloner(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.destination_org = self.create_organization()
        self.cloner = AssetCloner(self.organization, self.destination_org)

    def test_should_clone_folder_and_its_children(self):
        set_current_tenant(self.organization)
        parent_folder = self.create_folder(title="Parent Folder")
        child_folder = self.create_folder(title="Child Folder", parent=parent_folder)
        asset = self.create_asset(
            title="Sample video asset", parent=child_folder, type=Asset.Type.VIDEO
        )
        self.create_video(asset=asset)

        self.cloner.clone(parent_folder.uuid)

        set_current_tenant(self.destination_org)
        self.assertEqual(Asset.objects.filter(type=Asset.Type.FOLDER).count(), 2)
        self.assertEqual(Asset.objects.filter(type=Asset.Type.VIDEO).count(), 1)
        self.assertEqual(
            Asset.objects.get(
                title="Parent Folder", type=Asset.Type.FOLDER
            ).organization,
            self.destination_org,
        )
        self.assertEqual(
            Asset.objects.get(
                title="Child Folder", type=Asset.Type.FOLDER
            ).organization,
            self.destination_org,
        )
        self.assertEqual(
            Asset.objects.get(
                title="Sample video asset", type=Asset.Type.VIDEO
            ).organization,
            self.destination_org,
        )
        migrated_parent = Asset.objects.get(
            title="Parent Folder", type=Asset.Type.FOLDER
        )
        migrated_child = Asset.objects.get(title="Child Folder", type=Asset.Type.FOLDER)
        migrated_nested_asset = Asset.objects.get(
            title="Sample video asset", type=Asset.Type.VIDEO
        )
        self.assertEqual(migrated_child.parent, migrated_parent)
        self.assertEqual(migrated_nested_asset.parent, migrated_child)
