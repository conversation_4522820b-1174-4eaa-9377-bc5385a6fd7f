from django.test import TestCase
from rest_framework.exceptions import ValidationError

from app.domain.organization import (
    create_organization_for_testpress,
    generate_auth_token,
)
from app.models import Membership, Organization, User


class TestOrganizationDomainMethods(TestCase):
    def setUp(self):
        self.data = {
            "user_name": "Test Userr",
            "email": "<EMAIL>",
            "password": "password123",
            "org_name": "Test Organizationn",
            "cdn_id": "cdn123",
            "cdn_url": "https://cdn.example.com",
            "bucket_name": "test-bucket",
            "bucket_secret_token": "secret-token",
            "cloudfront_key_group_id": "cloudfront123",
            "storage_region": "us-east-1",
            "storage_access_key_id": "access-key-id",
            "storage_secret_access_key": "secret-access-key",
            "cdn_access_key_id": "cdn-access-key",
            "cdn_secret_access_key": "cdn-secret-key",
            "cdn_one_year_cache_policy_id": "cache-policy-1",
            "cdn_expire_in_3_seconds_cache_policy_id": "cache-policy-2",
            "cdn_public_key_id": "public-key-id",
            "cdn_private_key": "private-key",
        }

    def test_create_organization_for_testpress(self):
        organization, org_created = create_organization_for_testpress(self.data)
        user = User.objects.get(email=self.data["email"])
        self.assertEqual(user.name, self.data["user_name"])
        self.assertTrue(user.check_password(self.data["password"]))
        self.assertEqual(user.current_organization_uuid, organization.uuid)
        self.assertEqual(organization.name, self.data["org_name"])
        self.assertEqual(organization.created_by, user)
        self.assertEqual(organization.cdn_url, self.data["cdn_url"])
        membership = Membership.objects.filter(
            organization=organization, user=user
        ).exists()
        self.assertTrue(membership)
        self.assertTrue(org_created)

    def test_create_organization_existing_user(self):
        user = User.objects.create(
            name=self.data["user_name"], email=self.data["email"]
        )
        user.set_password(self.data["password"])
        user.save()
        organization, org_created = create_organization_for_testpress(self.data)

        self.assertEqual(User.objects.count(), 1)
        self.assertEqual(organization.created_by, user)
        self.assertEqual(Organization.objects.count(), 1)
        self.assertEqual(Membership.objects.count(), 1)
        self.assertTrue(org_created)

    def test_generate_auth_token_success(self):
        organization, _ = create_organization_for_testpress(self.data)
        token = generate_auth_token(
            organization, self.data["email"], self.data["password"]
        )
        self.assertTrue(len(token) == 64, "The token length should be equal to 64.")

    def test_generate_auth_token_invalid_user(self):
        organization, _ = create_organization_for_testpress(self.data)
        with self.assertRaises(ValidationError) as context:
            generate_auth_token(organization, "invalid_user", "invalid_password")

        self.assertIn(
            "Unable to log in with provided credentials.", str(context.exception)
        )

    def test_should_not_recreate_organization_when_updating_config(self):
        organization, org_created_first = create_organization_for_testpress(self.data)
        updated_data = self.data.copy()
        updated_data["cdn_secret_access_key"] = "new-cdn-secret-access-key"
        updated_data["cdn_one_year_cache_policy_id"] = "new-cdn-one-year-cache-policy-id"
        updated_data["cdn_expire_in_3_seconds_cache_policy_id"] = "new-cdn-expire-in-3-seconds-cache-policy-id"
        updated_data["cdn_public_key_id"] = "new-cdn-public-key-id"
        updated_data["cdn_private_key"] = "new-cdn-private-key"

        organization_updated, org_created_second = create_organization_for_testpress(updated_data)

        self.assertEqual(organization.uuid, organization_updated.uuid)
        self.assertTrue(org_created_first)
        self.assertFalse(org_created_second)

    def test_should_return_existing_organization_on_subsequent_calls(self):
        organization, org_created_first = create_organization_for_testpress(self.data)
        organization_updated, org_created_second = create_organization_for_testpress(self.data)

        self.assertEqual(organization, organization_updated)
        self.assertTrue(org_created_first)
        self.assertFalse(org_created_second)
