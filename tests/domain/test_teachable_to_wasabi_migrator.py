from unittest import mock

import responses
from django_multitenant.utils import set_current_tenant

from app.domain.teachable_to_wasabi_migrator import TeachableMigrator
from app.models import Video
from app.models.videoimport import ImportedFolder, ImportedVideo
from tests import TestCase
from tests.mixins import OrganizationMixin


class TestTeachableMigrator(OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.api_key = "test_key"
        self.org_id = self.organization.uuid
        self.course_data = {
            "id": 123,
            "name": "Test Course",
            "description": "Test Description",
            "lecture_sections": [
                {
                    "id": 1,
                    "lectures": [
                        {
                            "id": 456,
                            "name": "Test Lecture",
                            "position": 1,
                            "is_published": True,
                        }
                    ],
                }
            ],
        }
        self.lecture_data = {
            "id": 456,
            "name": "Test Lecture",
            "position": 1,
            "is_published": True,
            "attachments": [
                {
                    "id": 789,
                    "kind": "video",
                    "name": "Test Video",
                    "url": "https://example.com/video.m3u8",
                    "duration": 120,
                    "thumbnail_url": "https://example.com/thumb.jpg",
                    "status": "READY",
                }
            ],
        }

    @responses.activate
    @mock.patch(
        "app.domain.teachable_to_wasabi_migrator.MigrateTeachableVideoTask.apply_async"
    )
    def test_should_call_celery_task_with_imported_video_instance_id(
        self, mocked_migrate_video_task
    ):
        migrator = TeachableMigrator(
            self.api_key,
            self.org_id,
            content_protection_type=Video.ContentProtectionType.DISABLED,
        )
        responses.add(
            responses.GET,
            f"https://developers.teachable.com/v1/courses/{self.course_data['id']}",
            status=200,
            json={"course": self.course_data},
        )
        responses.add(
            responses.GET,
            f"https://developers.teachable.com/v1/courses/{self.course_data['id']}/lectures/{self.lecture_data['id']}",
            status=200,
            json={"lecture": self.lecture_data},
        )
        responses.add(
            responses.GET,
            (
                f"https://developers.teachable.com/v1/courses/{self.course_data['id']}"
                f"/lectures/{self.lecture_data['id']}/videos/{self.lecture_data['attachments'][0]['id']}"
            ),
            status=200,
            json={
                "video": {
                    "id": self.lecture_data["attachments"][0]["id"],
                    "status": "READY",
                    "video_asset": {"url": self.lecture_data["attachments"][0]["url"]},
                    "media_duration": self.lecture_data["attachments"][0]["duration"],
                    "url_thumbnail": self.lecture_data["attachments"][0][
                        "thumbnail_url"
                    ],
                }
            },
        )

        migrator.migrate_course(course_id=self.course_data["id"])

        self.assertEqual(
            1,
            ImportedVideo.objects.filter(
                uri=self.lecture_data["attachments"][0]["url"],
                source=ImportedVideo.Source.TEACHABLE,
            ).count(),
        )
        imported_video = ImportedVideo.objects.get(
            uri=self.lecture_data["attachments"][0]["url"]
        )
        mocked_migrate_video_task.assert_called_once_with(
            kwargs={
                "organization_uuid": self.organization.uuid,
                "access_token": self.api_key,
                "imported_video_id": imported_video.id,
                "content_protection_type": Video.ContentProtectionType.DISABLED,
                "transcoding_queue_name": None,
            },
            queue="migration_queue",
        )

    @responses.activate
    def test_should_create_course_folder_with_correct_data(self):
        migrator = TeachableMigrator(self.api_key, self.org_id)
        responses.add(
            responses.GET,
            f"https://developers.teachable.com/v1/courses/{self.course_data['id']}",
            status=200,
            json={"course": self.course_data},
        )
        responses.add(
            responses.GET,
            f"https://developers.teachable.com/v1/courses/{self.course_data['id']}/lectures/{self.lecture_data['id']}",
            status=200,
            json={"lecture": self.lecture_data},
        )

        migrator.migrate_course(course_id=self.course_data["id"])

        folder = ImportedFolder.objects.first()
        self.assertEqual(folder.name, self.course_data["name"])
        self.assertEqual(folder.organization, self.organization)
        self.assertEqual(folder.details, self.course_data)

    @responses.activate
    @mock.patch(
        "app.domain.teachable_to_wasabi_migrator.MigrateTeachableVideoTask.apply_async"
    )
    def test_should_migrate_lectures_to_folder(self, mocked_migrate_video_task):
        migrator = TeachableMigrator(self.api_key, self.org_id)
        responses.add(
            responses.GET,
            f"https://developers.teachable.com/v1/courses/{self.course_data['id']}",
            status=200,
            json={"course": self.course_data},
        )
        responses.add(
            responses.GET,
            f"https://developers.teachable.com/v1/courses/{self.course_data['id']}/lectures/{self.lecture_data['id']}",
            status=200,
            json={"lecture": self.lecture_data},
        )
        responses.add(
            responses.GET,
            (
                f"https://developers.teachable.com/v1/courses/{self.course_data['id']}"
                f"/lectures/{self.lecture_data['id']}/videos/{self.lecture_data['attachments'][0]['id']}"
            ),
            status=200,
            json={
                "video": {
                    "id": self.lecture_data["attachments"][0]["id"],
                    "status": "READY",
                    "video_asset": {"url": self.lecture_data["attachments"][0]["url"]},
                    "media_duration": self.lecture_data["attachments"][0]["duration"],
                    "url_thumbnail": self.lecture_data["attachments"][0][
                        "thumbnail_url"
                    ],
                }
            },
        )

        course_folder = migrator._create_course_folder(self.course_data)
        migrator._migrate_lectures_to_folder(self.course_data, course_folder)

        self.assertEqual(
            1,
            ImportedVideo.objects.filter(
                uri=self.lecture_data["attachments"][0]["url"],
                source=ImportedVideo.Source.TEACHABLE,
            ).count(),
        )

        imported_video = ImportedVideo.objects.get(
            uri=self.lecture_data["attachments"][0]["url"]
        )
        mocked_migrate_video_task.assert_called_once_with(
            kwargs={
                "organization_uuid": self.organization.uuid,
                "access_token": self.api_key,
                "imported_video_id": imported_video.id,
                "content_protection_type": None,
                "transcoding_queue_name": None,
            },
            queue="migration_queue",
        )

    @responses.activate
    def test_should_not_migrate_lectures_when_folder_creation_fails(self):
        migrator = TeachableMigrator(self.api_key, self.org_id)
        responses.add(
            responses.GET,
            f"https://developers.teachable.com/v1/courses/{self.course_data['id']}",
            status=404,
        )

        migrator.migrate_course(course_id=self.course_data["id"])

        self.assertEqual(ImportedFolder.objects.count(), 0)
        self.assertEqual(ImportedVideo.objects.count(), 0)

    @responses.activate
    def test_should_handle_missing_video_asset(self):
        migrator = TeachableMigrator(self.api_key, self.org_id)
        responses.add(
            responses.GET,
            f"https://developers.teachable.com/v1/courses/{self.course_data['id']}",
            status=200,
            json={"course": self.course_data},
        )
        responses.add(
            responses.GET,
            f"https://developers.teachable.com/v1/courses/{self.course_data['id']}/lectures/{self.lecture_data['id']}",
            status=200,
            json={"lecture": self.lecture_data},
        )
        responses.add(
            responses.GET,
            (
                f"https://developers.teachable.com/v1/courses/{self.course_data['id']}"
                f"/lectures/{self.lecture_data['id']}/videos/{self.lecture_data['attachments'][0]['id']}"
            ),
            status=200,
            json={
                "video": {
                    "id": self.lecture_data["attachments"][0]["id"],
                    "status": "READY",
                }
            },
        )

        migrator.migrate_course(course_id=self.course_data["id"])
        self.assertEqual(ImportedVideo.objects.count(), 0)

    @responses.activate
    def test_should_handle_missing_video_url(self):
        migrator = TeachableMigrator(self.api_key, self.org_id)
        responses.add(
            responses.GET,
            f"https://developers.teachable.com/v1/courses/{self.course_data['id']}",
            status=200,
            json={"course": self.course_data},
        )
        responses.add(
            responses.GET,
            f"https://developers.teachable.com/v1/courses/{self.course_data['id']}/lectures/{self.lecture_data['id']}",
            status=200,
            json={"lecture": self.lecture_data},
        )
        responses.add(
            responses.GET,
            (
                f"https://developers.teachable.com/v1/courses/{self.course_data['id']}"
                f"/lectures/{self.lecture_data['id']}/videos/{self.lecture_data['attachments'][0]['id']}"
            ),
            status=200,
            json={
                "video": {
                    "id": self.lecture_data["attachments"][0]["id"],
                    "status": "READY",
                    "video_asset": {},
                }
            },
        )

        migrator.migrate_course(course_id=self.course_data["id"])
        self.assertEqual(ImportedVideo.objects.count(), 0)
