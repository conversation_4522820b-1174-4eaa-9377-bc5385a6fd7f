import datetime

from django.utils import timezone
from django_multitenant.utils import set_current_tenant
from freezegun import freeze_time

from app.domain.live_stream_usage import (
    update_daily_live_stream_usage,
    update_monthly_live_stream_usage,
)
from app.models import LiveStream
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestLiveStreamUsage(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_update_daily_live_stream_usage_should_return_correct_usage(
        self,
    ):
        today_live_stream_1 = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_ip="127.0.0.1",
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        with freeze_time("2024-02-21 13:00:00"):
            self.create_live_stream_usage(
                live_stream=today_live_stream_1, start_time=timezone.now()
            )

        today_live_stream_2 = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_ip="127.0.0.1",
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        with freeze_time("2024-02-21 13:00:00"):
            self.create_live_stream_usage(
                live_stream=today_live_stream_2, start_time=timezone.now()
            )

        day_usage = update_daily_live_stream_usage(
            organization=today_live_stream_1.organization,
            date=datetime.date(2024, 2, 21),
        )
        two_hours_in_seconds = 7200

        self.assertEqual(day_usage, two_hours_in_seconds)

    def test_update_daily_live_stream_usage_updates_usage_for_specified_date_only(
        self,
    ):
        today_live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_ip="127.0.0.1",
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        with freeze_time("2024-02-21 13:00:00"):
            self.create_live_stream_usage(
                live_stream=today_live_stream, start_time=timezone.now()
            )

        yesterday_live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_ip="127.0.0.1",
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )

        with freeze_time("2024-02-22 13:00:00"):
            self.create_live_stream_usage(
                live_stream=yesterday_live_stream, start_time=timezone.now()
            )

        day_usage = update_daily_live_stream_usage(
            organization=today_live_stream.organization, date=datetime.date(2024, 2, 21)
        )
        one_hours_in_seconds = 3600

        self.assertEqual(day_usage, one_hours_in_seconds)

    def test_update_monthly_live_stream_usage_updates_usage_for_whole_month(
        self,
    ):
        live_stream_in_month_start = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_ip="127.0.0.1",
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
        )
        with freeze_time("2024-01-01 13:00:00"):
            self.create_live_stream_usage(
                live_stream=live_stream_in_month_start, start_time=timezone.now()
            )

            live_stream_in_month_end = self.create_livestream(
                status=LiveStream.Status.STREAMING,
                server_ip="127.0.0.1",
                server_id="1234",
                stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
            )
        with freeze_time("2024-01-31 13:00:00"):
            self.create_live_stream_usage(
                live_stream=live_stream_in_month_end, start_time=timezone.now()
            )

        month_usage = update_monthly_live_stream_usage(
            organization=live_stream_in_month_end.organization,
            date=datetime.date(2024, 1, 31),
        )
        two_hours_in_seconds = 7200

        self.assertEqual(month_usage, two_hours_in_seconds)
