import json

import responses
from django.test import SimpleTestCase, override_settings

from app.domain.drm.encryption.streams import generate_widevine_encryption_keys
from app.domain.drm.license.streams import generate_widevine_license

WIDEVINE_REQUEST_DATA = (
    "eyJjb250ZW50X2lkIjogInFQRVJYMUZIUi9TRi9KQWhDVVNhaWc9PSIsICJwb2xpY3kiOiAiIiwgInRyYWNrcyI6IFt"
    "7InR5cGUiOiAiU0QifSwgeyJ0eXBlIjogIkhEIn0sIHsidHlwZSI6ICJVSEQxIn0sIHsidHlwZSI6ICJVSEQyIn0sIH"
    "sidHlwZSI6ICJBVURJTyJ9XSwgImRybV90eXBlcyI6IFsiV0lERVZJTkUiXSwgInByb3RlY3Rpb25fc2NoZW1lIjogI"
    "kNFTkMifQ=="
)


@override_settings(
    WIDEVINE_AES_KEY="7625e224dc0f0ec91ad28c1ee67b1eb96d1a5459533c5c950f44aae1e32f2da3",
    WIDEVINE_IV="a8f1115f514747f485fc902109449a8a",
    WIDEVINE_CONTENT_KEY_URL="https://example.com",
)
class TestGenerateEncryptionKeys(SimpleTestCase):
    @responses.activate
    def test_should_generate_proper_request_data(self):
        responses.add(
            responses.POST, "https://example.com", status=200, json={"response": "Abcd"}
        )
        generate_widevine_encryption_keys(WIDEVINE_REQUEST_DATA)

        expected_result = {
            "request": "eyJjb250ZW50X2lkIjogInFQRVJYMUZIUi9TRi9KQWhDVVNhaWc9PSIsICJwb2xpY3kiOiAiIiwgInRyYWNrcyI6IFt7InR"
            "5cGUiOiAiU0QifSwgeyJ0eXBlIjogIkhEIn0sIHsidHlwZSI6ICJVSEQxIn0sIHsidHlwZSI6ICJVSEQyIn0sIHsidHlwZS"
            "I6ICJBVURJTyJ9XSwgImRybV90eXBlcyI6IFsiV0lERVZJTkUiXSwgInByb3RlY3Rpb25fc2NoZW1lIjogIkNFTkMifQ==",
            "signature": "NDyv3eP06Hxtxdu9z1nSEFV5GxEl2iSLfw/uy1zNwFg=",
            "signer": "testpress",
        }
        self.assertEqual(expected_result, json.loads(responses.calls[0].request.body))  # type: ignore


@override_settings(
    WIDEVINE_AES_KEY="7625e224dc0f0ec91ad28c1ee67b1eb96d1a5459533c5c950f44aae1e32f2da3",
    WIDEVINE_IV="a8f1115f514747f485fc902109449a8a",
    WIDEVINE_LICENSE_KEY_URL="https://example.com",
)
class TestGenerateLicense(SimpleTestCase):
    @responses.activate
    def test_generate_license(self):
        responses.add(
            responses.POST, "https://example.com", status=200, json={"response": "Abcd"}
        )
        player_payload = "abcd"
        content_keys = [
            {
                "track_type": "SD",
                "key_id": "onecHjiTUXmTS0eFwH+b7w==",
                "key": "hLvTKztbYRD5mXkg8L0SXA==",
            }
        ]

        generate_widevine_license(
            "a8f1115f514747f485fc902109449a8a", content_keys, player_payload
        )

        expected_result = {
            "request": "eyJwYXlsb2FkIjogImFiY2QiLCAiY29udGVudF9pZCI6ICJxUEVSWDFGSFIvU0YvSkFoQ1VTYWlnPT0iLCAicHJvdmlkZXIiOiAidGVzdHByZXNzIiwgImNvbnRlbnRfa2V5X3NwZWNzIjogW3sidHJhY2tfdHlwZSI6ICJTRCIsICJrZXlfaWQiOiAib25lY0hqaVRVWG1UUzBlRndIK2I3dz09IiwgImtleSI6ICJoTHZUS3p0YllSRDVtWGtnOEwwU1hBPT0ifV0sICJwb2xpY3lfb3ZlcnJpZGVzIjogeyJjYW5fcGxheSI6IHRydWUsICJjYW5fcGVyc2lzdCI6IGZhbHNlLCAibGljZW5zZV9kdXJhdGlvbl9zZWNvbmRzIjogMTI5NjAwMCwgInJlbnRhbF9kdXJhdGlvbl9zZWNvbmRzIjogMTI5NjAwMH0sICJzZXNzaW9uX2luaXQiOiB7Im92ZXJyaWRlX2RldmljZV9yZXZvY2F0aW9uIjogdHJ1ZX19",
            "signature": "jy0zqta4WPfygfUoTvk3torN4qNxuHsjoYkVkRs5o3c=",
            "signer": "testpress",
        }
        self.assertEqual(expected_result, json.loads(responses.calls[0].request.body))  # type: ignore
