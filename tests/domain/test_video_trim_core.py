from datetime import timedelta

from django_multitenant.utils import set_current_tenant

from app.domain.video_trim.core import (
    create_trim_job,
    create_trimmed_output,
    delete_trimmed_outputs,
    ensure_video_outputs_exist,
    has_video_outputs,
    mark_trim_completed,
    revert_to_original,
)
from app.domain.video_trim.exceptions import VideoTrimValidationError
from app.models import User
from app.models.video_trim import OutputType, TrimStatus, VideoOutput, VideoTrim
from app.utils.video_trim import validate_trim_parameters
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestVideoTrimCore(TestCase, OrganizationMixin, AssetMixin):
    def setUp(self):
        self.organization = self.create_organization()
        self.organization.cdn_url = "https://example.com/"
        self.organization.bucket_name = "test-bucket"
        set_current_tenant(self.organization)
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpass123"
        )
        self.video = self.create_video()
        self.video.duration = timedelta(seconds=3600)
        self.video.save()

    def test_video_trim_validator_valid_parameters(self):
        validate_trim_parameters(self.video, 300, 1800)

    def test_video_trim_validator_invalid_start_negative(self):
        with self.assertRaises(VideoTrimValidationError) as context:
            validate_trim_parameters(self.video, -10, 300)
        self.assertIn("Start time cannot be negative", str(context.exception))

    def test_video_trim_validator_invalid_end_before_start(self):
        with self.assertRaises(VideoTrimValidationError) as context:
            validate_trim_parameters(self.video, 1800, 300)
        self.assertIn(
            "End time must be greater than start time", str(context.exception)
        )

    def test_video_trim_validator_start_exceeds_duration(self):
        with self.assertRaises(VideoTrimValidationError) as context:
            validate_trim_parameters(self.video, 4000, 4500)
        self.assertIn("Start time cannot exceed video duration", str(context.exception))

    def test_video_trim_validator_end_exceeds_duration(self):
        with self.assertRaises(VideoTrimValidationError) as context:
            validate_trim_parameters(self.video, 300, 4000)
        self.assertIn("End time cannot exceed video duration", str(context.exception))

    def test_create_trim_job(self):
        trim_job = create_trim_job(self.video, 300, 1800, self.user)
        self.assertEqual(trim_job.video, self.video)
        self.assertEqual(trim_job.start_time, 300)
        self.assertEqual(trim_job.end_time, 1800)
        self.assertEqual(trim_job.created_by, self.user)
        self.assertEqual(trim_job.status, TrimStatus.PENDING)
        self.assertEqual(trim_job.organization, self.organization)

    def test_create_trim_job_existing_pending_job(self):
        VideoTrim.objects.create(
            video=self.video,
            start_time=100,
            end_time=200,
            created_by=self.user,
            status=TrimStatus.PENDING,
            organization=self.organization,
        )

        with self.assertRaises(VideoTrimValidationError) as context:
            create_trim_job(self.video, 300, 1800, self.user)
        self.assertIn("A trim operation is already in progress", str(context.exception))

    def test_create_trimmed_output(self):
        output = create_trimmed_output(
            self.video,
            OutputType.TRIMMED_HLS,
            "h264",
            1500,
            "https://example.com/trimmed.m3u8",
        )
        self.assertEqual(output.video, self.video)
        self.assertEqual(output.output_type, OutputType.TRIMMED_HLS)
        self.assertEqual(output.codec, "h264")
        self.assertEqual(output.duration, 1500)
        self.assertEqual(output.url, "https://example.com/trimmed.m3u8")
        self.assertTrue(output.is_active)

    def test_delete_trimmed_outputs(self):
        VideoOutput.objects.create(
            video=self.video,
            output_type=OutputType.TRIMMED_HLS,
            codec="h264",
            duration=1500,
            url="https://example.com/trimmed.m3u8",
            organization=self.organization,
            is_active=True,
        )
        delete_trimmed_outputs(self.video)

        active_trimmed = VideoOutput.objects.filter(
            video=self.video,
            output_type__in=[OutputType.TRIMMED_HLS, OutputType.TRIMMED_DASH],
            is_active=True,
        )
        self.assertEqual(active_trimmed.count(), 0)

    def test_mark_trim_completed(self):
        trim_job = VideoTrim.objects.create(
            video=self.video,
            start_time=300,
            end_time=1800,
            created_by=self.user,
            status=TrimStatus.PROCESSING,
            organization=self.organization,
        )

        trimmed_outputs = {
            "h264": {
                "hls": "https://example.com/trimmed.m3u8",
                "dash": "https://example.com/trimmed.mpd",
            }
        }

        mark_trim_completed(self.video, trim_job, trimmed_outputs)

        trim_job.refresh_from_db()
        self.assertEqual(trim_job.status, TrimStatus.COMPLETED)

        hls_output = VideoOutput.objects.filter(
            video=self.video, output_type=OutputType.TRIMMED_HLS, codec="h264"
        ).first()
        self.assertIsNotNone(hls_output)
        self.assertEqual(hls_output.url, "https://example.com/trimmed.m3u8")

    def test_revert_to_original(self):
        completed_trim = VideoTrim.objects.create(
            video=self.video,
            start_time=300,
            end_time=1800,
            created_by=self.user,
            status=TrimStatus.COMPLETED,
            organization=self.organization,
        )

        VideoOutput.objects.create(
            video=self.video,
            output_type=OutputType.TRIMMED_HLS,
            codec="h264",
            duration=1500,
            url="https://example.com/trimmed.m3u8",
            organization=self.organization,
            is_active=True,
        )

        reverted_trim = revert_to_original(self.video)

        self.assertEqual(reverted_trim.id, completed_trim.id)
        self.assertEqual(reverted_trim.status, TrimStatus.REVERTED)

    def test_has_video_outputs_with_outputs(self):
        VideoOutput.objects.create(
            video=self.video,
            output_type=OutputType.HLS,
            codec="h264",
            duration=3600,
            url="https://example.com/master.m3u8",
            organization=self.organization,
            is_active=True,
        )
        self.assertTrue(has_video_outputs(self.video))

    def test_has_video_outputs_without_outputs(self):
        self.assertFalse(has_video_outputs(self.video))

    def test_ensure_video_outputs_exist_skips_if_outputs_exist(self):
        VideoOutput.objects.create(
            video=self.video,
            output_type=OutputType.HLS,
            codec="h264",
            duration=3600,
            url="https://example.com/existing.m3u8",
            organization=self.organization,
            is_active=True,
        )

        self.video.playback_url = "transcoded/assets/123/master.m3u8"
        self.video.save()

        ensure_video_outputs_exist(self.video)

        hls_outputs = VideoOutput.objects.filter(
            video=self.video, output_type=OutputType.HLS
        )
        self.assertEqual(hls_outputs.count(), 1)
        self.assertEqual(hls_outputs.first().url, "https://example.com/existing.m3u8")
