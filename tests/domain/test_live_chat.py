import uuid
from unittest import mock

import responses
from django.conf import settings
from django.test import override_settings
from django_multitenant.utils import set_current_tenant

from app.domain.live_chat import (
    create_chat_room,
    disable_chat_room,
    get_chat_messages,
    update_chat_room_as_exported,
)
from app.domain.live_stream import stop_live_stream
from app.models import LiveStream
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestLiveChat(TestCase, AssetMixin, OrganizationMixin):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @responses.activate
    @override_settings(CHAT_URL="http://127.0.0.1", CHAT_ADMIN_KEY="abc")
    def test_chat_room_is_created_when_create_chat_room_method_was_called(self):
        asset = self.create_asset(live_stream=self.create_livestream())
        responses.add(
            responses.POST,
            f"{settings.CHAT_URL}rest/v1/chat_room",
            status=200,
            json=[{"id": "f91c82a4-04c9-469f-a0f5-0cf5c634f577"}],
        )

        response = create_chat_room(asset)

        self.assertEqual(response, [{"id": "f91c82a4-04c9-469f-a0f5-0cf5c634f577"}])

    @responses.activate
    @override_settings(CHAT_URL="http://127.0.0.1", CHAT_ADMIN_KEY="abc")
    def test_chat_room_is_disabled_when_disable_chat_room_method_was_called(self):
        chat_room_id = "f91c82a4-04c9-469f-a0f5-0cf5c634f577"
        responses.add(
            responses.PATCH,
            f"{settings.CHAT_URL}rest/v1/chat_room?id=eq.{chat_room_id}",
            status=200,
            json={"is_active": False},
        )

        response = disable_chat_room(chat_room_id=chat_room_id)

        self.assertEqual(response, {"is_active": False})

    @mock.patch("app.domain.live_stream.disable_chat_room")
    @mock.patch("app.domain.live_stream.ExportLiveChatTask")
    @mock.patch("app.domain.live_stream.is_live_stream_content_available")
    @responses.activate
    def test_chat_should_be_disabled_when_the_live_stream_was_stopped(
        self,
        mock_is_live_stream_content_available,
        mock_export_chat,
        mocked_disable_chat_room,
    ):
        responses.add(responses.POST, "http://127.0.0.1/stop_transcoding/", status=200)
        live_stream = self.create_livestream(
            status=LiveStream.Status.STREAMING,
            server_ip="127.0.0.1",
            server_id="1234",
            stream_key=f"org-{self.organization.uuid}-live-QZDv9-abcd",
            chat_room_id=uuid.uuid4(),
        )
        stop_live_stream(live_stream)

        mocked_disable_chat_room.assert_called()

    @responses.activate
    @override_settings(CHAT_URL="http://127.0.0.1", CHAT_ADMIN_KEY="abc")
    def test_get_chat_messages_should_return_chat_messages_in_response(self):
        chat_room_id = "f91c82a4-04c9-469f-a0f5-0cf5c634f577"
        url = (
            f"{settings.CHAT_URL}rest/v1/messages?room_id=eq.{chat_room_id}"
            f"&select=timestamp,content,is_deleted,user(id,name,is_blocked)"
            f"&offset=0&limit=500&order=timestamp.asc"
        )
        data = [
            {
                "timestamp": "2023-07-21T06:05:54.303337",
                "content": "hello",
                "is_deleted": False,
                "user": "test user",
            }
        ]

        responses.add(
            responses.GET,
            url,
            status=200,
            json=data,
        )

        response = get_chat_messages(chat_room_id, offset=0)

        self.assertEqual(response, data)

    @responses.activate
    @override_settings(CHAT_URL="http://127.0.0.1", CHAT_ADMIN_KEY="abc")
    def test_update_chat_room_as_exported_method_should_update_exported_status(self):
        chat_room_id = "f91c82a4-04c9-469f-a0f5-0cf5c634f577"
        url = f"{settings.CHAT_URL}rest/v1/chat_room?id=eq.{chat_room_id}"
        data = {"is_exported": True}

        responses.add(
            responses.PATCH,
            url,
            status=200,
            json=data,
        )

        response = update_chat_room_as_exported(chat_room_id)

        self.assertEqual(response, data)
