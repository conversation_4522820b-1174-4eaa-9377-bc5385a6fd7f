from unittest.mock import Mock, patch

import requests
from django.test import override_settings
from django_multitenant.utils import set_current_tenant

from app.domain.zoom import (
    ZoomOAuthError,
    refresh_zoom_token,
    revoke_zoom_account,
    store_zoom_account_in_db,
)
from app.models.zoom import ConnectionStatus, ZoomAccount
from tests import TestCase
from tests.mixins import OrganizationMixin


class TestZoomCore(OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.user = self.organization.created_by
        self.user.current_organization = self.organization
        set_current_tenant(self.organization)

        self.mock_tokens = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "expires_in": 3600,
        }
        self.mock_user_info = {
            "id": "test_zoom_user_id",
            "email": "<EMAIL>",
        }

    def test_should_create_zoom_account(self):
        store_zoom_account_in_db(self.user, self.mock_user_info, self.mock_tokens)

        self.assertEqual(ZoomAccount.objects.count(), 1)
        zoom_account = ZoomAccount.objects.first()
        self.assertEqual(zoom_account.zoom_user_id, self.mock_user_info["id"])
        self.assertEqual(zoom_account.email, self.mock_user_info["email"])
        self.assertEqual(zoom_account.access_token, self.mock_tokens["access_token"])
        self.assertEqual(zoom_account.refresh_token, self.mock_tokens["refresh_token"])
        self.assertEqual(zoom_account.import_destination.title, "Zoom Recordings")

    @patch("requests.post")
    def test_should_revoke_zoom_account(self, mock_post):
        store_zoom_account_in_db(self.user, self.mock_user_info, self.mock_tokens)
        zoom_account = ZoomAccount.objects.first()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response

        revoke_zoom_account(zoom_account)

        zoom_account.refresh_from_db()
        self.assertEqual(zoom_account.status, ConnectionStatus.DISCONNECTED)
        self.assertIsNotNone(zoom_account.disconnected_at)
        mock_post.assert_called_once()

    @override_settings(
        ZOOM_CLIENT_ID="test_client_id",
        ZOOM_CLIENT_SECRET="test_client_secret",
    )
    @patch("requests.post")
    def test_should_refresh_zoom_token(self, mock_post):
        store_zoom_account_in_db(self.user, self.mock_user_info, self.mock_tokens)
        zoom_account = ZoomAccount.objects.first()
        new_tokens = {
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
            "expires_in": 3600,
        }
        mock_response = Mock()
        mock_response.json.return_value = new_tokens
        mock_post.return_value = mock_response

        new_access_token = refresh_zoom_token(zoom_account)

        self.assertEqual(new_access_token, new_tokens["access_token"])
        zoom_account.refresh_from_db()
        self.assertEqual(zoom_account.access_token, new_tokens["access_token"])
        self.assertEqual(zoom_account.refresh_token, new_tokens["refresh_token"])

    @patch("requests.post")
    def test_should_raise_zoom_oauth_error_if_refresh_token_is_expired(self, mock_post):
        store_zoom_account_in_db(self.user, self.mock_user_info, self.mock_tokens)
        zoom_account = ZoomAccount.objects.first()
        mock_response = Mock()
        mock_response.status_code = 400
        mock_post.side_effect = requests.exceptions.HTTPError(response=mock_response)

        with self.assertRaises(ZoomOAuthError):
            refresh_zoom_token(zoom_account)
        self.assertEqual(zoom_account.status, ConnectionStatus.REFRESH_TOKEN_EXPIRED)
        self.assertIsNotNone(zoom_account.disconnected_at)
