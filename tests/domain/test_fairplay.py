import base64
import binascii
import uuid
from unittest import mock

from django.test import SimpleTestCase, override_settings

from app.domain.drm.license.fairplay.ckc import CKC, SPCTags
from app.domain.drm.license.fairplay.spc import Spc

R2 = base64.b64decode("Efe+YSypXvXgB85RiWrkUCyj2IAb")
Dask = base64.b64decode("2HznomCB3i6OuKzvOm3BeQ==")
spc = (
    "AAAAAQAAAACIiMWpQhMDI6pMnx2nfIiImQZjqj91XmwLddwqAmq5bRSfGlKvfPZkey/keCOrySJLIbN7vPDqSXVapVMF4GiM/9YY7l/1u/TbA"
    "seSjwDgx9/W8s08zOeDv/UEg8rXTUsM/oHWAbRGJI+Z6Y8pEgYw7l9s7xRaKvi14MfEzm4Tl2FPZlROuej1yX5hMPLbzqlVYZgKpR8DqgS8ok"
    "VWyEGnFzbHcQAACuBgYVhvOLE/GVWSz9yGiMYDD07fsx8o9tU2GBigwC/klbIPxbdA09IGmws5dDJah6dRVELuozlqF0j2sgL/4pVVDorcAGr"
    "B6FOVKLFQdoHflYgj5RVd6esUlN5XkFfl9N2rjj9pB8MgeSBKAkowlBtSlaoSrr+EBCqc9eLxNEjI8mjt3rrsDeTrup2Za6BdOptvzcMVLMqj"
    "pR8nD6v283IcJLTOHqann++0u0oYPSHsGBDygduasmU+BpGdaMW27JF8pgut5C0ItLATNpbtJTAfAwCDPKpPI7JcS87Xyg+HRSMWEs57bzgLh"
    "wCpQYZpVfWf6vLzlVCDvgy0onZ9iVLK8XsllcupEB3vWkWl5dINpA2k2c7P/pWQ4KJZ0jrfbM/8h0tHs8pJxTQRVfLziHHBLIiNRBiUM8e0b8"
    "sQs5yO4EXhagtS1PZ+xoIWbQK7A5r/cehoV8PxXuoCiUV6NwsnvOVG5uahDfHOBi6Wkhd+MKaSfUFsrSFYouQm+XFx4SWfytFAGBZjGtcW9yB"
    "IPqZdlS8gSWmGx/BM1kZjreuYvhinZbUbWGffCv8fXjhOv5FXpzysTAgog67TcXPtH4JBbzApb+Fld4xqUujuLdQxgoFgrOlIJsKxUJIaJa1i"
    "u8RmcGN1wQGj/Mtcosrq8Cru6agK0wACCFN1E6fB0B3kZ9Dt/p0bZuNsR2XOLDbJB8/43Kq6X4tVELVLLp2ON91C5PAbIqL9P22pHH2ElxWWU"
    "2LhQokyHi1th8ysvht9nGBWyiz9qIxYbK6ia4SfOpT8dyNkFJiueimCIXBy8JqOV8oTl3J1HhC1Ny5PAvwGnmVCtir4G5hunwTsqzoFM5PqoG"
    "/IyIpk6NNkKIhTk0D2P9U1fxPYELpE69+uZJ92C/HPiw41nU88p8YivW88lCFpccG8MZrZHIGX1eeWO1M23MeYKMdk4Sj05v6012wEXt5S56M"
    "7Ij5qHTXreALcS6qRW0x1A3F/Xouv7SpSzDeqWeAqs3GhJDXIA2RqxkMtws1oWk6zEHodqo4hbvlw6fw13f0VfGO32C2KDo41qFezYUtlO+fp"
    "npiWxuyNXZi8XfUITIQCenWWhrcI4owJG9qejwFjXoCQigoan0eevycgiYW9k1lS8aytR0ZWfAJGD3f6oXaNm8hHoZIRPr4y7JwjWHcDccDcB"
    "3lsKcsQOoCW24zgGlpYk2UO/av2nBcVEB0ku0I7WfJQsrX5tuh/JTGGeefxIjL3SXHaDMvo0LUdmI+b/eJQaEt1shd3/Ez54AB78WwcO0geSr"
    "zNQlYinEQFuDXZIKxFVeCqgtYV4GCFMquTjTR1Z/EbfxNgSTFpL0RtzOgrER29ShZCd4FCvWdv1RwlaVE33imRIjebtLRI6FIrnt40gP1qCp8"
    "drbDM5XNjqZR80V3uVaPmaviEj/RMQHnL1jzmzBPtZnR0a4Xx40ygoQ1UAQ8Vs6z7yGkZVMfD0GBycxJM0olhC1nAKLvxSNhgFkMq4mgLFdww"
    "Rv9LsGN0IiSguCyS8q9DDKOJOL6ppYjF9oC9xiuVlSoPc45o322f7z9Xp5CKL1B5p8wCzIE0sHtJZFzjkfuwHNGfHFQqPbdVRDRewgd6PJJgM"
    "EyfYqZAkrvR5X+UtcQ7T+1uATRs61vj+D86+jtGf2UPc5kJMVxz1Sqg19WJynxk9l05VlqQKUSjW4mVtWyIGKoubKSTLjAM9z9RtkKgaKZrUI"
    "xTZ/Ob1smU33WDvJ0pu2ZrehGwW3J5/51h8tVOGEuDeryXpK6Q2DFh/oI+yMn+Iy2vUtobv2Hw4jXOXdBwlU006fI3ujp46Jvz93R9EH5ziiq"
    "F0NLOmW8jsxdpJYrJRQXmfIR+9XYRlig68X5OpALBhj/3SjRhDWi8kSbRtsqHAOLEf9TeCRU5aHUvYntL341x903rDvYYS7hg5v2+vJfSx91q"
    "2POlXRTEygTPK0MNhKwhobXMVs+m4zMVURb9b9JIJlsmGdhF6vNCFj7STEWx4GLDoKl7fUnw9W23cD2WFsvktuVnhJJe27+zXDYErA38EHYN+"
    "Vwj/HghQuigI8Hv3sjKPQ7/T+zZfTV7Q/TSWvVvYyd2xzl5lUSzFXwJs6aFn81kAejYfPb3Fx5MUrumSJ3RRJW/ZavvLBCWjLdonsHR0tjJtn"
    "wYDFYTZl6rUidaQCMgTDBqLZaxOlULsaAxff79MBnA2FJqbiNiY52th9tNm8aJI/YgR2+r5N58cmVed3qfcHGNP4yE+MABpgYnab2ySZs18L+"
    "pwXIL69UHtUTEuMll9qGSQ/1cZLzChcqBUNV646dwWW5JmGzchHVy9cGT8H73ed7fH/Nvwmx0HlOEwz4lPKhsyaKVA8ZhHBC4mtnBUyT4a1pQ"
    "Jg4NBhUTOITp2UPPWVOHzIxHaGMYFhOUjrzXuRc4X/vdWjsxF7MQvLhZaUZ5Lgyh3rtw8JsNCAXvN0ix0HIXlk1eN7Ig1sP2ddyvY6Sl3mXWs"
    "KoNuFw9r9VxApMwSRUISbek5WCoRRPWQjLhEMykH2IoBxlkfGMhGkLTVBcC2ZFTV0BbZ4c14NVzvRRMCGjBlnyqOEP8ZejiHfUCm5WtDg/nVP"
    "EnRDvrWRB1F4/wwv5VlL4gBaZ9HZmCfRanzi3M8SZkarqFf9qIeAt9AQ2iyxo3Wk76uIi2Eggxd0V8GiXl5KUQswcHuO0BLDkGJhKx/oYe9Tc"
    "6sWEbVy9Q1xd7R1PNIOsnmVAJf6VVNj8MzEwqGmiwTr0zlLp4LKzzW5nlpTyrL+xmYExUaDc3WAy/8tggrHA8ywZbE9yvJgmTt2R3cImVzWnu"
    "d/ofUt1lw2PPibR+dow3R4wX2Bny6G4VJIoFizyW6Ync41kDFGzTRmi5uODIfxcXoSqiSJddTr0HagFQZnCGlnrMR7SUGe2cMeg0id8KFGawc"
    "gpnjF4q38J861yQL9md2kjgFDmbMB9cGAaO127Z3ZQOhqj5t6PUuI46PCPR5gjsctYRZc7TpwnIEGj8j1sDvVUbwRZw9TNd6Dqa8Hq0hk5jaO"
    "T0iwlaGmG0xz0xaqYJtT4uhPjcPi9PGQ+UOy/BIJB+zYMbyIzh7WRGAijhAofSMDE/UFRNzQXxolIzF6ifSTT6VSdSC80BLUiYb7rTIu3LQDzF"
    "RG+nwfWkqdGavtrDdb+Fhmvjx5N/VPA5BLAjiciGmBm0fHHWrp1PMG/HOzHPmqYjE8fBc2hiyiJq+6vlk1WRfwGHM9+I5m0aLdJYlbPWxnfLbT"
    "JgzGS4QUaimOoNGZ5zQ8YvfybWT4EmN0HEkV1lC1MMqcsN5GlplFtYfb57e5eP8FY+J1inL+/1QIQqLlSN/TAvWj4gaHcg0JaUA0ibpJ5m6fKY"
    "ys42qc5Uinh9KqbjLeX5n7VkAyO3d9Gj6eChVE1HmYdsyHc6tX0IBJK9KU4e2oThKuRzp8I+wnfQCuBSzFzdbyCTk68vtPnrl2bMcSBawNp/la"
    "tH5yww0hQ/6np53Yf4+gMmT1VecN5TJLcJKZ3dGfgaIpxaCrPrOejB7exkAeWrGOPrYfJF49ckFhQfQbHnXZb375EOLj2LoVeqa9kq4krhj5op"
    "dMXhKGJKgq+9ykmWA7onWb1bg3F1ucR79eJvlcpqhVf8iIajL/dR10Q8VexXp1K32xoa2566g9GTFkBh8q63+WVY8yrnX9ro3qSg04KSPJBGB1"
    "s+E10kpYaMG6A3Awg="
)


@override_settings(
    FAIRPLAY_PRIVATE_KEY=b"-----BEGIN RSA PRIVATE KEY-----\nMIICXAIBAAKBgQC0XgENuL2ujKrsVJDsq1SxNL43N6792teqp3Siihi+m"
    b"n6ZDdfc\nXP3FsPQNltBQkiWIJGPosoRjwDuAS1ntEEqJyc9PBnXhDEtsJo/O4fDy3umMrvCH\nWt4R/rJ3O4b9m8V"
    b"+QCO9hE0kFpRJZMt7RywmFdram01uARkGb7xOC3zPrQIDAQAB\nAoGBAIO+vkpFjNd4jEi/pHQa2WvuuJogpENsnGd"
    b"clYc8E8L1mk81m1ys1/iUvk9G\nv7Z6acu9uPR5oNYzzcJyR6cvZSFxtGIZnWNdDOAB71b+YqMvj3lr6MgUdMUgUfx"
    b"Z\nEDXLEhIoVzyQWIt+f6hjSG/hzyw+Jglo4ogCWPsV3S6UG2WBAkEA5HPddGIUa34k\n2/EGQqyCAo4VYlCUdCFTp"
    b"9+eFIUedequgsSIZhgblT+FSvMPYARuG/ywLoOivRy1\ndFl0dIB1sQJBAModyMskK0r312kro+URq8VxlwwY0fv2r"
    b"F1aS0/clQUw5OH/OxEn\nDgz3l3PNTXDCcQDh9wyEZV0SgIp7SYCDrL0CQEo8HEolVN1ZMEEIITCpPdX2tZws\n8xCJ"
    b"g9WZJJUmbK+EgxCbLHeAffYRng6szOI2jlEp21ZCEC/DlHMqXl09IQECQGSn\nEoC/oWOzKy4v0m3YL/+iwsL+dUwSG"
    b"uJefhTmV7v/DmzRixvOpDum7WB5BDC8VERJ\nQ5uTL1t7RFIydXcvm80CQH/E17mWT66PPeqloAfSH/5tJyak2gagku"
    b"FnMh779JRF\nrl5YIIiAh+q5DkcjWw6eni5O4+UuwXRp29vZaxmDlIE=\n-----END RSA PRIVATE KEY-----\n",
    FAIRPLAY_PASSWORD=None,
    FAIRPLAY_ASK=None,
)
class TestFairplayLicenseGeneration(SimpleTestCase):
    @mock.patch("app.domain.drm.license.fairplay.tllv.R2.get_dask")
    def test_r1_value_should_be_generated(self, get_dask_mock):
        get_dask_mock.return_value = Dask
        parsed_spc = Spc(spc).parse()
        sk_r1 = parsed_spc.tllv_blocks[SPCTags.SK_R1.value]
        r2 = parsed_spc.tllv_blocks.get(SPCTags.R2.value)
        dask = r2.get_dask()
        session_key, r1 = sk_r1.decrypt(dask)

        self.assertEqual(
            "bbbb2262f75b87afde905fd2c516411d3f18c84b885feddbe082f4749c6fc224b9d7bbbbbbbbbbbbbbbbbbbb",
            binascii.hexlify(r1).decode(),
        )

    @mock.patch("app.domain.drm.license.fairplay.tllv.R2.get_dask")
    def test_generated_ckc_message_should_follow_proper_structure(self, get_dask_mock):
        get_dask_mock.return_value = Dask
        parsed_spc = Spc(spc).parse()
        ckc = CKC(parsed_spc, uuid.uuid4().hex, uuid.uuid4().hex).generate()

        self.assertEqual(binascii.hexlify(ckc[:4]).decode(), "00000001")
        self.assertEqual(binascii.hexlify(ckc[4:8]).decode(), "00000000")
        self.assertEqual(binascii.hexlify(ckc[24:28]).decode(), "00000350")
