from unittest import mock

import responses
from django_multitenant.utils import set_current_tenant

from app.domain.vimeo_to_wasabi_migrator import Implementation, ImportedVideo
from app.models import Video
from app.utils.vimeo import Interface as Vimeo
from tests import TestCase
from tests.domain.data.vimeo_video_detail import VIMEO_VIDEO_DETAIL_RESPONSE
from tests.mixins import OrganizationMixin


class TestImplementation(OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    @responses.activate
    @mock.patch(
        "app.domain.vimeo_to_wasabi_migrator.MigrateImportedVideoTask.apply_async"
    )
    def test_should_call_celery_task_with_imported_video_instance_id(
        self, mocked_migrate_specific_video_resolution_task
    ):
        migrator_implementation = Implementation(
            "abcd",
            self.organization.uuid,
            content_protection_type=Video.ContentProtectionType.DISABLED,
        )
        responses.add(
            responses.GET,
            Vimeo.VIDEO_DETAIL_URL.format("797262223"),
            status=200,
            json=VIMEO_VIDEO_DETAIL_RESPONSE,
        )
        migrator_implementation.migrate_video("https://vimeo.com/797262223")

        self.assertEqual(
            1, ImportedVideo.objects.filter(uri="https://vimeo.com/797262223").count()
        )
        mocked_migrate_specific_video_resolution_task.assert_called_once_with(
            kwargs={
                "organization_uuid": self.organization.uuid,
                "access_token": "abcd",
                "imported_video_id": ImportedVideo.objects.get(
                    uri="https://vimeo.com/797262223"
                ).id,
                "content_protection_type": Video.ContentProtectionType.DISABLED,
            },
            queue="migration_queue",
        )
