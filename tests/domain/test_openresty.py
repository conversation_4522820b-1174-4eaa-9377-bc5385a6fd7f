from unittest import  mock

from app.domain.openresty import create_openresty_proxy_server
from tests import TestCase


class TestCreateOpenRestyServer(TestCase):
    @mock.patch("app.domain.openresty.AWSOpenRestyServer")
    def test_should_create_openresty_server(self, mock_aws_openresty_server):
        mock_server_instance = mock.Mock()
        mock_aws_openresty_server.return_value = mock_server_instance
        mock_server_instance.create_server.return_value = {
            "instance_id": "i-0abcd1234efgh5678",
            "state": "running",
            "public_ip": "********",
            "private_ip": "********",
        }

        result = create_openresty_proxy_server(name="TestServer",)
        mock_aws_openresty_server.assert_called()

        mock_server_instance.create_server.assert_called_once_with("TestServer")
        
        self.assertEqual(result["instance_id"], "i-0abcd1234efgh5678")
        self.assertEqual(result["state"], "running")
        self.assertEqual(result["public_ip"], "********")
        self.assertEqual(result["private_ip"], "********")

    @mock.patch("app.domain.aws.sentry_sdk.capture_exception")
    @mock.patch("app.domain.aws.AWSOpenRestyServer")
    def test_create_openresty_server_exception(self, mock_aws_openresty_server, mock_sentry_capture):
        mock_aws_openresty_server.side_effect = Exception("Mocked exception")
        result = create_openresty_proxy_server(
            name="TestServer",
            server_type="t2.medium",
            image_id="ami-12345678",
            region_name="us-east-1",
            security_group_id="sg-87654321",
            volume_size=100,
            volume_type="gp2",
        )

        mock_sentry_capture.assert_called_once()
        self.assertIsNone(result)