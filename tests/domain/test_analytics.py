import datetime
from unittest import mock

from django.test import override_settings
from django.utils.crypto import get_random_string
from django_multitenant.utils import set_current_tenant
from freezegun import freeze_time

from app.domain.analytics import (
    calculate_average_watch_time,
    calculate_total_watch_time,
    calculate_unique_viewers_count,
    get_analytics_file_path_in_bucket,
    get_metrics,
    get_video_analytics_from_storage_in_batch,
    store_video_analytics,
    update_watch_metrics_for_assets,
)
from app.models.asset import Asset, AssetViewerLog
from tests import TestCase
from tests.mixins import OrganizationMixin
from tests.mixins.asset import AssetViewerLogMixin


class TestTrackAnaltytics(AssetViewerLogMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_calculate_total_watch_time(self):
        asset = self.create_asset()
        self.create_asset_viewer_log(asset=asset, duration=10)
        self.create_asset_viewer_log(asset=asset, duration=20)
        self.create_asset_viewer_log(asset=asset, duration=30)

        total_watch_time = calculate_total_watch_time(asset)

        self.assertEqual(total_watch_time, 60)

    def test_calculate_average_watched_time(self):
        asset = self.create_asset(type=Asset.Type.VIDEO, views_count=3)
        self.create_asset_viewer_log(asset=asset, duration=10)
        self.create_asset_viewer_log(asset=asset, duration=20)
        self.create_asset_viewer_log(asset=asset, duration=30)

        # Total watch time = 10 + 20 + 30 = 60
        # Number of views = 3
        # Average watched time = 60 / 3 = 20
        expected_average_watched_time = 20

        average_watched_time = calculate_average_watch_time(asset)

        self.assertEqual(average_watched_time, expected_average_watched_time)

    def test_calculate_unique_viewers_count(self):
        asset = self.create_asset()
        self.create_asset_viewer_log(
            asset=asset, duration=10, visitor_id="user1", session_id="session1"
        )
        self.create_asset_viewer_log(
            asset=asset, duration=20, visitor_id="user2", session_id="session2"
        )
        self.create_asset_viewer_log(
            asset=asset, duration=30, visitor_id="user2", session_id="session3"
        )

        unique_viewers_count = calculate_unique_viewers_count(asset)

        self.assertEqual(unique_viewers_count, 2)

    @freeze_time("2023-08-22")
    def test_get_metrics(self):
        start_date = datetime.date(2023, 8, 22)
        end_date = datetime.date(2023, 8, 22)
        asset_1 = self.create_asset(type=Asset.Type.VIDEO)
        session_id_1 = get_random_string(length=40)
        visitor_id_1 = get_random_string(length=32)
        asset_2 = self.create_asset(type=Asset.Type.VIDEO)
        visitor_id_2 = get_random_string(length=32)
        session_id_2 = get_random_string(length=40)
        session_id_3 = get_random_string(length=40)

        self.create_asset_viewer_log(
            asset=asset_1,
            duration=10,
            visitor_id=visitor_id_1,
            session_id=session_id_1,
        )
        self.create_asset_viewer_log(
            asset=asset_2,
            duration=20,
            visitor_id=visitor_id_2,
            session_id=session_id_2,
        )
        self.create_asset_viewer_log(
            asset=asset_1,
            duration=30,
            visitor_id=visitor_id_1,
            session_id=session_id_3,
        )

        analytics_metrics = get_metrics(asset_1, start_date, end_date)

        expected_metrics = {
            datetime.date(2023, 8, 22): {
                "total_watch_time": 40,
                "views_count": 2,
            }
        }

        self.assertEqual(analytics_metrics, expected_metrics)

    @freeze_time("2023-08-22")
    def test_get_metrics_empty_logs(self):
        asset_1 = self.create_asset(type=Asset.Type.VIDEO)
        start_date = datetime.date(2023, 1, 1)
        end_date = datetime.date(2023, 1, 3)

        analytics_metrics = get_metrics(asset_1, start_date, end_date)

        expected_metrics = {}  # type: ignore
        self.assertEqual(analytics_metrics, expected_metrics)


class TestUpdateVideoAnalytics(AssetViewerLogMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.asset = self.create_asset()

    @override_settings(
        ANALYTICS_LOG_BUCKET="mock_bucket",
    )
    @mock.patch("app.domain.analytics.get_object_paths")
    @mock.patch("app.domain.analytics.get_analytics_data_from_paths")
    def test_should_get_video_analytics_from_storage(
        self, mock_extract_data, mock_get_objects
    ):
        mock_get_objects.return_value = (["path1", "path2"], "")
        mock_extract_data.return_value = [
            {"mock_data": "data1"},
            {"mock_data": "data2"},
        ]
        date = datetime.date.today() - datetime.timedelta(days=1)
        result, batch_token = get_video_analytics_from_storage_in_batch(
            organization=self.organization, date=date, next_batch_token=""
        )
        mock_get_objects.assert_called_once_with(
            f"{self.organization.uuid}/{date.year}/{date.month:02d}/{date.day:02d}/",
            "mock_bucket",
            "",
        )
        mock_extract_data.assert_called_once_with(["path1", "path2"])
        self.assertEqual(result, [{"mock_data": "data1"}, {"mock_data": "data2"}])

    def test_should_store_analytics_in_db(self):
        data = [
            {
                "assetId": self.asset.uuid,
                "organization": self.organization,
                "visitor_id": "visitor_id",
                "session_id": "session_id",
                "duration": 219,
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
            }
        ]

        store_video_analytics(data, self.organization)
        self.assertTrue(AssetViewerLog.objects.exists())

    def test_should_return_analytics_file_path_in_bucket(self):
        organization_uuid = self.organization.uuid
        test_date = datetime.date(2023, 11, 30)
        expected_path = f"{organization_uuid}/2023/11/30/"
        result_path = get_analytics_file_path_in_bucket(self.organization, test_date)
        self.assertEqual(result_path, expected_path)

    @override_settings(
        ANALYTICS_LOG_BUCKET="mock_bucket",
    )
    @mock.patch("app.domain.analytics.get_object_paths")
    @mock.patch("app.domain.analytics.sentry_sdk.capture_exception")
    def test_should_raise_exception_if_getting_analytics_from_storage_fails(
        self, mock_capture_exception, mock_get_paths
    ):
        mock_get_paths.side_effect = Exception("Mocked exception")
        get_video_analytics_from_storage_in_batch(
            organization=self.organization,
            date=datetime.date.today(),
            next_batch_token="",
        )
        mock_capture_exception.assert_called_once()

    @freeze_time("2023-03-07")
    def test_should_update_watch_metrics_for_assets(self):
        asset_1 = self.create_asset()
        asset_2 = self.create_asset()
        self.create_asset_viewer_log(asset=asset_1, duration=10)
        self.create_asset_viewer_log(asset=asset_2, duration=20)
        self.create_asset_viewer_log(asset=asset_2, duration=30)
        update_watch_metrics_for_assets()
        asset_1.refresh_from_db()
        asset_2.refresh_from_db()
        self.assertEqual(1, asset_1.views_count)
        self.assertEqual(2, asset_2.views_count)
        self.assertEqual(50, asset_2.total_watch_time)
