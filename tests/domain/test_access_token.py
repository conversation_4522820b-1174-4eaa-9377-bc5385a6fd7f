from datetime import timedelta

from django.utils.timezone import now
from django_multitenant.utils import set_current_tenant

from app.domain.access_token import get_or_create_access_token_without_validity
from tests import TestCase
from tests.mixins import <PERSON><PERSON><PERSON><PERSON><PERSON>, OrganizationMixin


class TestAccessTokenMethods(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.asset = self.create_asset()

    def test_returns_access_token_without_expiry(self):
        yesterday = now() - timedelta(hours=24)
        self.create_access_token(
            asset=self.asset, expires_after_first_usage=False, valid_until=yesterday
        )

        access_token = get_or_create_access_token_without_validity(self.asset)

        self.assertTrue(access_token.is_active)
