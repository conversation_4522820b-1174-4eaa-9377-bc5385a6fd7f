VIMEO_VIDEO_DETAIL_RESPONSE = {
    "uri": "/videos/*********",
    "name": "Neonatal jaundice",
    "description": None,
    "type": "video",
    "link": "https://vimeo.com/*********",
    "player_embed_url": "https://player.vimeo.com/video/*********?h=88eecb6549",
    "duration": 4464,
    "width": 1280,
    "language": None,
    "height": 720,
    "created_time": "2023-02-09T07:35:49+00:00",
    "modified_time": "2023-07-18T09:22:23+00:00",
    "release_time": "2023-02-09T07:35:49+00:00",
    "content_rating": ["unrated"],
    "content_rating_class": "unrated",
    "rating_mod_locked": False,
    "license": None,
    "privacy": {
        "view": "anybody",
        "embed": "public",
        "download": True,
        "add": False,
        "comments": "nobody",
    },
    "tags": [],
    "stats": {"plays": 89},
    "categories": [],
    "manage_link": "/manage/videos/*********",
    "user": {
        "uri": "/users/*********",
        "name": "Testpaperlive Classes",
        "link": "https://vimeo.com/user*********",
        "capabilities": {
            "hasLiveSubscription": False,
            "hasEnterpriseLihp": False,
            "hasSvvTimecodedComments": True,
            "hasSimplifiedEnterpriseAccount": False,
        },
        "location": "",
        "gender": "",
        "bio": None,
        "short_bio": None,
        "created_time": "2021-06-20T15:27:26+00:00",
        "pictures": {
            "uri": None,
            "active": False,
            "type": "default",
            "base_link": "https://i.vimeocdn.com/portrait/defaults-blue",
            "sizes": [
                {
                    "width": 30,
                    "height": 30,
                    "link": "https://i.vimeocdn.com/portrait/defaults-blue_30x30",
                },
                {
                    "width": 72,
                    "height": 72,
                    "link": "https://i.vimeocdn.com/portrait/defaults-blue_72x72",
                },
                {
                    "width": 75,
                    "height": 75,
                    "link": "https://i.vimeocdn.com/portrait/defaults-blue_75x75",
                },
                {
                    "width": 100,
                    "height": 100,
                    "link": "https://i.vimeocdn.com/portrait/defaults-blue_100x100",
                },
                {
                    "width": 144,
                    "height": 144,
                    "link": "https://i.vimeocdn.com/portrait/defaults-blue_144x144",
                },
                {
                    "width": 216,
                    "height": 216,
                    "link": "https://i.vimeocdn.com/portrait/defaults-blue_216x216",
                },
                {
                    "width": 288,
                    "height": 288,
                    "link": "https://i.vimeocdn.com/portrait/defaults-blue_288x288",
                },
                {
                    "width": 300,
                    "height": 300,
                    "link": "https://i.vimeocdn.com/portrait/defaults-blue_300x300",
                },
                {
                    "width": 360,
                    "height": 360,
                    "link": "https://i.vimeocdn.com/portrait/defaults-blue_360x360",
                },
            ],
            "resource_key": "06cd312fcc3908e2d839aeb00ccaaf434acb0859",
            "default_picture": True,
        },
        "websites": [],
        "metadata": {
            "connections": {
                "albums": {
                    "uri": "/users/*********/albums",
                    "options": ["GET"],
                    "total": 0,
                },
                "appearances": {
                    "uri": "/users/*********/appearances",
                    "options": ["GET"],
                    "total": 0,
                },
                "categories": {
                    "uri": "/users/*********/categories",
                    "options": ["GET"],
                    "total": 0,
                },
                "channels": {
                    "uri": "/users/*********/channels",
                    "options": ["GET"],
                    "total": 0,
                },
                "feed": {"uri": "/users/*********/feed", "options": ["GET"]},
                "followers": {
                    "uri": "/users/*********/followers",
                    "options": ["GET"],
                    "total": 1,
                },
                "following": {
                    "uri": "/users/*********/following",
                    "options": ["GET"],
                    "total": 0,
                },
                "groups": {
                    "uri": "/users/*********/groups",
                    "options": ["GET"],
                    "total": 0,
                },
                "likes": {
                    "uri": "/users/*********/likes",
                    "options": ["GET"],
                    "total": 0,
                },
                "membership": {
                    "uri": "/users/*********/membership/",
                    "options": ["PATCH"],
                },
                "moderated_channels": {
                    "uri": "/users/*********/channels?filter=moderated",
                    "options": ["GET"],
                    "total": 0,
                },
                "portfolios": {
                    "uri": "/users/*********/portfolios",
                    "options": ["GET"],
                    "total": 0,
                },
                "videos": {
                    "uri": "/users/*********/videos",
                    "options": ["GET"],
                    "total": 1601,
                },
                "watchlater": {
                    "uri": "/users/*********/watchlater",
                    "options": ["GET"],
                    "total": 0,
                },
                "shared": {
                    "uri": "/users/*********/shared/videos",
                    "options": ["GET"],
                    "total": 0,
                },
                "pictures": {
                    "uri": "/users/*********/pictures",
                    "options": ["GET", "POST"],
                    "total": 0,
                },
                "watched_videos": {
                    "uri": "/me/watched/videos",
                    "options": ["GET"],
                    "total": 0,
                },
                "folders_root": {
                    "uri": "/users/*********/folders/root",
                    "options": ["GET"],
                },
                "folders": {
                    "uri": "/users/*********/folders",
                    "options": ["GET", "POST"],
                    "total": 56,
                },
                "teams": {
                    "uri": "/users/*********/teams",
                    "options": ["GET"],
                    "total": 1,
                },
                "permission_policies": {
                    "uri": "/users/*********/permission_policies",
                    "options": ["GET"],
                    "total": 6,
                },
                "block": {"uri": "/me/block", "options": ["GET"], "total": 0},
            }
        },
        "location_details": {
            "formatted_address": "",
            "latitude": None,
            "longitude": None,
            "city": None,
            "state": None,
            "neighborhood": None,
            "sub_locality": None,
            "state_iso_code": None,
            "country": None,
            "country_iso_code": None,
        },
        "skills": [],
        "available_for_hire": False,
        "can_work_remotely": False,
        "preferences": {
            "videos": {
                "rating": ["unrated"],
                "privacy": {
                    "view": "unlisted",
                    "comments": "nobody",
                    "embed": "public",
                    "download": False,
                    "add": False,
                    "allow_share_link": True,
                },
            },
            "webinar_registrant_lower_watermark_banner_dismissed": [],
        },
        "content_filter": [
            "language",
            "drugs",
            "violence",
            "nudity",
            "safe",
            "unrated",
        ],
        "upload_quota": {
            "space": {
                "free": *************,
                "max": *************,
                "used": ************,
                "showing": "lifetime",
                "unit": "video_size",
            },
            "periodic": {
                "period": None,
                "unit": None,
                "free": None,
                "max": None,
                "used": None,
                "reset_date": None,
            },
            "lifetime": {
                "unit": "video_size",
                "free": *************,
                "max": *************,
                "used": ************,
            },
        },
        "resource_key": "7b3c2692d848e930ac38149594056822e7377072",
        "account": "business",
    },
    "last_user_action_event_date": "2023-07-16T11:10:26+00:00",
    "parent_folder": {
        "created_time": "2022-11-01T03:38:12+00:00",
        "modified_time": "2023-07-16T11:10:26+00:00",
        "last_user_action_event_date": "2023-07-16T11:10:26+00:00",
        "name": "Raj.& UP or MP CHO live Course",
        "privacy": {"view": "nobody"},
        "resource_key": "880082e58b158bec325bae6cde1cf2cfab38f6f0",
        "uri": "/users/*********/projects/********",
        "link": None,
        "pinned_on": None,
        "is_pinned": False,
        "is_private_to_user": False,
        "user": {
            "uri": "/users/*********",
            "name": "Testpaperlive Classes",
            "link": "https://vimeo.com/user*********",
            "capabilities": {
                "hasLiveSubscription": False,
                "hasEnterpriseLihp": False,
                "hasSvvTimecodedComments": True,
                "hasSimplifiedEnterpriseAccount": False,
            },
            "location": "",
            "gender": "",
            "bio": None,
            "short_bio": None,
            "created_time": "2021-06-20T15:27:26+00:00",
            "pictures": {
                "uri": None,
                "active": False,
                "type": "default",
                "base_link": "https://i.vimeocdn.com/portrait/defaults-blue",
                "sizes": [
                    {
                        "width": 30,
                        "height": 30,
                        "link": "https://i.vimeocdn.com/portrait/defaults-blue_30x30",
                    },
                    {
                        "width": 72,
                        "height": 72,
                        "link": "https://i.vimeocdn.com/portrait/defaults-blue_72x72",
                    },
                    {
                        "width": 75,
                        "height": 75,
                        "link": "https://i.vimeocdn.com/portrait/defaults-blue_75x75",
                    },
                    {
                        "width": 100,
                        "height": 100,
                        "link": "https://i.vimeocdn.com/portrait/defaults-blue_100x100",
                    },
                    {
                        "width": 144,
                        "height": 144,
                        "link": "https://i.vimeocdn.com/portrait/defaults-blue_144x144",
                    },
                    {
                        "width": 216,
                        "height": 216,
                        "link": "https://i.vimeocdn.com/portrait/defaults-blue_216x216",
                    },
                    {
                        "width": 288,
                        "height": 288,
                        "link": "https://i.vimeocdn.com/portrait/defaults-blue_288x288",
                    },
                    {
                        "width": 300,
                        "height": 300,
                        "link": "https://i.vimeocdn.com/portrait/defaults-blue_300x300",
                    },
                    {
                        "width": 360,
                        "height": 360,
                        "link": "https://i.vimeocdn.com/portrait/defaults-blue_360x360",
                    },
                ],
                "resource_key": "06cd312fcc3908e2d839aeb00ccaaf434acb0859",
                "default_picture": True,
            },
            "websites": [],
            "metadata": {
                "connections": {
                    "albums": {
                        "uri": "/users/*********/albums",
                        "options": ["GET"],
                        "total": 0,
                    },
                    "appearances": {
                        "uri": "/users/*********/appearances",
                        "options": ["GET"],
                        "total": 0,
                    },
                    "categories": {
                        "uri": "/users/*********/categories",
                        "options": ["GET"],
                        "total": 0,
                    },
                    "channels": {
                        "uri": "/users/*********/channels",
                        "options": ["GET"],
                        "total": 0,
                    },
                    "feed": {"uri": "/users/*********/feed", "options": ["GET"]},
                    "followers": {
                        "uri": "/users/*********/followers",
                        "options": ["GET"],
                        "total": 1,
                    },
                    "following": {
                        "uri": "/users/*********/following",
                        "options": ["GET"],
                        "total": 0,
                    },
                    "groups": {
                        "uri": "/users/*********/groups",
                        "options": ["GET"],
                        "total": 0,
                    },
                    "likes": {
                        "uri": "/users/*********/likes",
                        "options": ["GET"],
                        "total": 0,
                    },
                    "membership": {
                        "uri": "/users/*********/membership/",
                        "options": ["PATCH"],
                    },
                    "moderated_channels": {
                        "uri": "/users/*********/channels?filter=moderated",
                        "options": ["GET"],
                        "total": 0,
                    },
                    "portfolios": {
                        "uri": "/users/*********/portfolios",
                        "options": ["GET"],
                        "total": 0,
                    },
                    "videos": {
                        "uri": "/users/*********/videos",
                        "options": ["GET"],
                        "total": 1601,
                    },
                    "watchlater": {
                        "uri": "/users/*********/watchlater",
                        "options": ["GET"],
                        "total": 0,
                    },
                    "shared": {
                        "uri": "/users/*********/shared/videos",
                        "options": ["GET"],
                        "total": 0,
                    },
                    "pictures": {
                        "uri": "/users/*********/pictures",
                        "options": ["GET", "POST"],
                        "total": 0,
                    },
                    "watched_videos": {
                        "uri": "/me/watched/videos",
                        "options": ["GET"],
                        "total": 0,
                    },
                    "folders_root": {
                        "uri": "/users/*********/folders/root",
                        "options": ["GET"],
                    },
                    "folders": {
                        "uri": "/users/*********/folders",
                        "options": ["GET", "POST"],
                        "total": 56,
                    },
                    "teams": {
                        "uri": "/users/*********/teams",
                        "options": ["GET"],
                        "total": 1,
                    },
                    "permission_policies": {
                        "uri": "/users/*********/permission_policies",
                        "options": ["GET"],
                        "total": 6,
                    },
                    "block": {"uri": "/me/block", "options": ["GET"], "total": 0},
                }
            },
            "location_details": {
                "formatted_address": "",
                "latitude": None,
                "longitude": None,
                "city": None,
                "state": None,
                "neighborhood": None,
                "sub_locality": None,
                "state_iso_code": None,
                "country": None,
                "country_iso_code": None,
            },
            "skills": [],
            "available_for_hire": False,
            "can_work_remotely": False,
            "preferences": {
                "videos": {
                    "rating": ["unrated"],
                    "privacy": {
                        "view": "unlisted",
                        "comments": "nobody",
                        "embed": "public",
                        "download": False,
                        "add": False,
                        "allow_share_link": True,
                    },
                },
                "webinar_registrant_lower_watermark_banner_dismissed": [],
            },
            "content_filter": [
                "language",
                "drugs",
                "violence",
                "nudity",
                "safe",
                "unrated",
            ],
            "upload_quota": {
                "space": {
                    "free": *************,
                    "max": *************,
                    "used": ************,
                    "showing": "lifetime",
                    "unit": "video_size",
                },
                "periodic": {
                    "period": None,
                    "unit": None,
                    "free": None,
                    "max": None,
                    "used": None,
                    "reset_date": None,
                },
                "lifetime": {
                    "unit": "video_size",
                    "free": *************,
                    "max": *************,
                    "used": ************,
                },
            },
            "resource_key": "7b3c2692d848e930ac38149594056822e7377072",
            "account": "business",
        },
        "access_grant": None,
        "metadata": {
            "connections": {
                "items": {
                    "uri": "/users/*********/projects/********/items",
                    "options": ["GET"],
                    "total": 12,
                },
                "videos": {
                    "uri": "/users/*********/projects/********/videos",
                    "options": ["GET", "DELETE", "PUT"],
                    "total": 3,
                },
                "folders": {
                    "uri": "/users/*********/projects/********/items",
                    "options": ["GET", "DELETE", "PUT"],
                    "total": 9,
                },
                "ancestor_path": [],
            },
            "interactions": {
                "edit": {
                    "uri": "/users/*********/projects/********",
                    "options": ["PATCH"],
                },
                "move_video": {
                    "uri": "/users/*********/projects/********",
                    "options": ["PATCH"],
                },
                "upload_video": {
                    "uri": "/users/*********/projects/********",
                    "options": ["POST"],
                },
                "view": {
                    "uri": "/users/*********/projects/********",
                    "options": ["GET"],
                },
                "invite": {
                    "uri": "/users/*********/projects/********",
                    "options": ["POST"],
                },
                "edit_settings": {
                    "uri": "/users/*********/projects/********",
                    "options": ["PATCH"],
                },
                "delete": {
                    "uri": "/users/*********/projects/********",
                    "options": ["DELETE"],
                },
                "delete_video": {
                    "uri": "/users/*********/projects/********",
                    "options": ["DELETE"],
                },
                "add_subfolder": {
                    "uri": "/user/*********/projects",
                    "options": ["POST"],
                    "can_add_subfolders": True,
                    "subfolder_depth_limit_reached": False,
                    "content_type": "application/vnd.vimeo.folder",
                    "properties": [
                        {"name": "name", "required": False, "value": ""},
                        {
                            "name": "parent_folder_uri",
                            "required": True,
                            "value": "/users/*********/projects/********",
                        },
                    ],
                },
            },
        },
    },
    "review_page": {
        "active": True,
        "link": "https://vimeo.com/user*********/review/*********/449fca3b11",
        "is_shareable": True,
    },
    "files": [
        {
            "quality": "sd",
            "rendition": "240p",
            "type": "video/mp4",
            "width": 426,
            "height": 240,
            "link": "https://player.vimeo.com/progressive_redirect/playback/*********/rendition/240p/file.mp4?loc=external&oauth2_token_id=1732524733&signature=5aaa35afd4ad60cf2e44f2f4f0af3b95a8ef190e530f658a8f95579bb58b528b",  # noqa
            "created_time": "2023-02-09T07:42:52+00:00",
            "fps": 30,
            "size": 163056892,
            "md5": "85a274a15fd70b7de618cccd181002b3",
            "public_name": "240p",
            "size_short": "155.5MB",
        },
        {
            "quality": "hd",
            "rendition": "720p",
            "type": "video/mp4",
            "width": 1280,
            "height": 720,
            "link": "https://player.vimeo.com/progressive_redirect/playback/*********/rendition/720p/file.mp4?loc=external&oauth2_token_id=1732524733&signature=0a56ac7bdb5629f6505e71b7761f1c03bb504314476eaa04e82b11a97c116372",  # noqa
            "created_time": "2023-02-09T07:49:44+00:00",
            "fps": 30,
            "size": 645568932,
            "md5": "e6c497c8370a63605de3c628f3d9b2a0",
            "public_name": "720p",
            "size_short": "615.66MB",
        },
        {
            "quality": "sd",
            "rendition": "360p",
            "type": "video/mp4",
            "width": 640,
            "height": 360,
            "link": "https://player.vimeo.com/progressive_redirect/playback/*********/rendition/360p/file.mp4?loc=external&oauth2_token_id=1732524733&signature=f1dcaa7d7cc9df3c036976b0beb93118c2c0840a9d4955c5ab50833efad5de04",  # noqa
            "created_time": "2023-02-09T07:48:47+00:00",
            "fps": 30,
            "size": 299410818,
            "md5": "d451ad6bba1d11e5a3d0be17134aec5f",
            "public_name": "360p",
            "size_short": "285.54MB",
        },
        {
            "quality": "sd",
            "rendition": "540p",
            "type": "video/mp4",
            "width": 960,
            "height": 540,
            "link": "https://player.vimeo.com/progressive_redirect/playback/*********/rendition/540p/file.mp4?loc=external&oauth2_token_id=1732524733&signature=3ea497f57f30a409c2ff28313b17dee3d206077ccf1ad98aa9fe5db53c6d8e90",  # noqa
            "created_time": "2023-02-09T07:57:41+00:00",
            "fps": 30,
            "size": 535424795,
            "md5": "2d2536322e38ff8dd922dfb5b9920af7",
            "public_name": "540p",
            "size_short": "510.62MB",
        },
        {
            "quality": "hls",
            "rendition": "adaptive",
            "type": "video/mp4",
            "link": "https://player.vimeo.com/external/*********.m3u8?s=cc9254553d4101a6eeaf3cc6e12488ee20fecac4&oauth2_token_id=1732524733",  # noqa
            "created_time": "2023-02-09T07:42:52+00:00",
            "fps": 30,
            "size": 163056892,
            "md5": "85a274a15fd70b7de618cccd181002b3",
            "public_name": "240p",
            "size_short": "155.5MB",
        },
    ],
    "download": [
        {
            "quality": "hd",
            "rendition": "720p",
            "type": "video/mp4",
            "width": 1280,
            "height": 720,
            "expires": "2023-07-19T10:43:45+00:00",
            "link": "https://player.vimeo.com/progressive_redirect/playback/*********/rendition/720p/file.mp4?loc=external&oauth2_token_id=1732524733&signature=0a56ac7bdb5629f6505e71b7761f1c03bb504314476eaa04e82b11a97c116372",  # noqa
            "created_time": "2023-02-09T07:49:44+00:00",
            "fps": 30,
            "size": 645568932,
            "md5": "e6c497c8370a63605de3c628f3d9b2a0",
            "public_name": "720p",
            "size_short": "615.66MB",
        },
        {
            "quality": "sd",
            "rendition": "360p",
            "type": "video/mp4",
            "width": 640,
            "height": 360,
            "expires": "2023-07-19T10:43:45+00:00",
            "link": "https://player.vimeo.com/progressive_redirect/download/*********/container/0698d997-de7a-46d9-82dc-7f9bfacf9fe0/5c2ea850/neonatal_jaundice%20%28360p%29.mp4?expires=1689763425&loc=external&oauth2_token_id=1732524733&signature=a88ec22c881a6db4b71119b27114e08e4d6abc6f1c7eac19cd6f3f220662238a",  # noqa
            "created_time": "2023-02-09T07:48:47+00:00",
            "fps": 30,
            "size": 299410818,
            "md5": "d451ad6bba1d11e5a3d0be17134aec5f",
            "public_name": "360p",
            "size_short": "285.54MB",
        },
        {
            "quality": "sd",
            "rendition": "540p",
            "type": "video/mp4",
            "width": 960,
            "height": 540,
            "expires": "2023-07-19T10:43:45+00:00",
            "link": "https://player.vimeo.com/progressive_redirect/download/*********/container/0698d997-de7a-46d9-82dc-7f9bfacf9fe0/5f7c3da2/neonatal_jaundice%20%28540p%29.mp4?expires=1689763425&loc=external&oauth2_token_id=1732524733&signature=440dd4ccc4455625e0be0d942967a007254e6dd4f0be5da0ae0c214a8317b79d",  # noqa
            "created_time": "2023-02-09T07:57:41+00:00",
            "fps": 30,
            "size": 535424795,
            "md5": "2d2536322e38ff8dd922dfb5b9920af7",
            "public_name": "540p",
            "size_short": "510.62MB",
        },
        {
            "quality": "sd",
            "rendition": "240p",
            "type": "video/mp4",
            "width": 426,
            "height": 240,
            "expires": "2023-07-19T10:43:45+00:00",
            "link": "https://player.vimeo.com/progressive_redirect/download/*********/container/0698d997-de7a-46d9-82dc-7f9bfacf9fe0/0e598207/neonatal_jaundice%20%28240p%29.mp4?expires=1689763425&loc=external&oauth2_token_id=1732524733&signature=e12a4374734cece33ae01dd101e66b2d245857a320bce33cab42b8a996476ffb",  # noqa
            "created_time": "2023-02-09T07:42:52+00:00",
            "fps": 30,
            "size": 163056892,
            "md5": "85a274a15fd70b7de618cccd181002b3",
            "public_name": "240p",
            "size_short": "155.5MB",
        },
    ],
    "app": {"name": "Parallel Uploader", "uri": "/apps/87099"},
    "play": {
        "progressive": [
            {
                "type": "video/mp4",
                "codec": "H264",
                "width": 960,
                "height": 540,
                "link_expiration_time": "2023-07-19T10:43:45+00:00",
                "link": "https://player.vimeo.com/progressive_redirect/playback/*********/container/0698d997-de7a-46d9-82dc-7f9bfacf9fe0/5f7c3da2?expires=1689763425&loc=external&log_user=0&oauth2_token_id=1732524733&signature=29b01291ae2cdb15e6872ae483e56076660503c153300529f7405e370016614f",  # noqa
                "created_time": "2023-02-09T07:57:41+00:00",
                "fps": 30,
                "size": 535424795,
                "md5": "2d2536322e38ff8dd922dfb5b9920af7",
                "rendition": "540p",
            },
            {
                "type": "video/mp4",
                "codec": "H264",
                "width": 426,
                "height": 240,
                "link_expiration_time": "2023-07-19T10:43:45+00:00",
                "link": "https://player.vimeo.com/progressive_redirect/playback/*********/container/0698d997-de7a-46d9-82dc-7f9bfacf9fe0/0e598207?expires=1689763425&loc=external&log_user=0&oauth2_token_id=1732524733&signature=4d99b0342fbe727687933313a4baf8ce8aa266914c7f746cb8f7254a0f8c1b0f",  # noqa
                "created_time": "2023-02-09T07:42:52+00:00",
                "fps": 30,
                "size": 163056892,
                "md5": "85a274a15fd70b7de618cccd181002b3",
                "rendition": "240p",
            },
            {
                "type": "video/mp4",
                "codec": "H264",
                "width": 1280,
                "height": 720,
                "link_expiration_time": "2023-07-19T10:43:45+00:00",
                "link": "https://player.vimeo.com/progressive_redirect/playback/*********/container/0698d997-de7a-46d9-82dc-7f9bfacf9fe0/2e58ce09?expires=1689763425&loc=external&log_user=0&oauth2_token_id=1732524733&signature=0d18ccba570c7a20359c070084a00bbeb55a9a1977b36362e159d7fe39706f43",  # noqa
                "created_time": "2023-02-09T07:49:44+00:00",
                "fps": 30,
                "size": 645568932,
                "md5": "e6c497c8370a63605de3c628f3d9b2a0",
                "rendition": "720p",
            },
            {
                "type": "video/mp4",
                "codec": "H264",
                "width": 640,
                "height": 360,
                "link_expiration_time": "2023-07-19T10:43:45+00:00",
                "link": "https://player.vimeo.com/progressive_redirect/playback/*********/container/0698d997-de7a-46d9-82dc-7f9bfacf9fe0/5c2ea850?expires=1689763425&loc=external&log_user=0&oauth2_token_id=1732524733&signature=1da38486abc24dd702a05547b2daa65776912b4a77b5cc53ce577f15d2f4b661",  # noqa
                "created_time": "2023-02-09T07:48:47+00:00",
                "fps": 30,
                "size": 299410818,
                "md5": "d451ad6bba1d11e5a3d0be17134aec5f",
                "rendition": "360p",
            },
        ],
        "hls": {
            "link_expiration_time": "2023-07-19T10:41:46+00:00",
            "link": "https://player.vimeo.com/play/0698d997-de7a-46d9-82dc-7f9bfacf9fe0/hls.m3u8?s=*********_1689763306_82eeff4cbb22325022ccd236c437c953&context=Vimeo%5CController%5CApi%5CResources%5CVideoController.&log_user=0&oauth2_token_id=1732524733",  # noqa
        },
        "dash": {
            "link_expiration_time": "2023-07-19T10:41:46+00:00",
            "link": "https://player.vimeo.com/play/0698d997-de7a-46d9-82dc-7f9bfacf9fe0/dash.mpd?s=*********_1689763306_82eeff4cbb22325022ccd236c437c953&context=Vimeo%5CController%5CApi%5CResources%5CVideoController.&log_user=0&oauth2_token_id=1732524733",  # noqa
        },
        "status": "playable",
    },
    "status": "available",
    "resource_key": "7409b05be36cb584231c67bfbcf151c6161938fa",
    "upload": {
        "status": "complete",
        "link": None,
        "upload_link": None,
        "form": None,
        "approach": None,
        "size": None,
        "redirect_url": None,
    },
    "transcode": {"status": "complete"},
    "is_playable": True,
    "has_audio": True,
}
