import io

from django.core.handlers.wsgi import LimitedStream

WSGI_INPUT = LimitedStream(
    io.BytesIO(
        b'162\r\n{"request":"eyJjb250ZW50X2lkIjoiM2QwZHJvaGlRNzZGY1d3ZnVJbFdSUT09IiwicG9'
        b"saWN5IjoiIiwidHJhY2tzIjpbeyJ0eXBlIjoiU0QifSx7InR5cGUiOiJIRCJ9LHsidHlwZSI6IlVIRD"
        b"EifSx7InR5cGUiOiJVSEQyIn0seyJ0eXBlIjoiQVVESU8ifV0sImRybV90eXBlcyI6WyJXSURFVklORS"
        b'JdLCJwcm90ZWN0aW9uX3NjaGVtZSI6IkNFTkMifQ==","signature":"xb8/LzfSguBVDguEdqetcodmUzXxswwKIJsJzNArS48=",'
        b'"signer":"testpress"}\r\n0\r\n\r\n'
    ),
    0,
)

SHAKA_PACKAGER_RESPONSE_DATA = {
    "SERVER_PORT": "8000",
    "SERVER_NAME": "example.com",
    "REQUEST_METHOD": "GET",
    "HTTP_TRANSFER_ENCODING": "chunked",
    "wsgi.input": WSGI_INPUT,
    "HTTP_USER_AGENT": "ShakaPackager/v2.5.1-9f11077-release",
}
