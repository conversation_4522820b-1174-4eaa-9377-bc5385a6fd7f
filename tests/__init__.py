from importlib import import_module

from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from django.test import RequestFactory
from django.test import TestCase as DjangoTestCase

session = None


class TestCase(DjangoTestCase):
    def get_session(self):
        if hasattr(self, "session") and self.session:  # type: ignore
            return self.session  # type: ignore
        else:
            engine = import_module(settings.SESSION_ENGINE)
            return engine.SessionStore()  # type: ignore

    def get_request(
        self, path, method="get", data=None, user=None, headers=None, **kwargs
    ):
        self.request_factory = RequestFactory()
        if "content_type" not in kwargs:
            kwargs["content_type"] = "application/json"

        if method.lower() == "post":
            request = self.request_factory.post(path, data=data, **kwargs)
        elif method.lower() == "put":
            request = self.request_factory.put(path, data, **kwargs)
        elif method.lower() == "patch":
            request = self.request_factory.patch(path, data, **kwargs)
        elif method.lower() == "delete":
            request = self.request_factory.delete(path, data, **kwargs)
        else:
            request = self.request_factory.get(path, data, **kwargs)
        if headers:
            for header, value in headers.items():
                request.META["HTTP_" + header.replace("-", "_").upper()] = value
        request.user = user or AnonymousUser()
        request.session = self.get_session()
        request.session.save()
        request._dont_enforce_csrf_checks = True  # type: ignore
        return request
