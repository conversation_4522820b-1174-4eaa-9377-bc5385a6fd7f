from datetime import timedelta
from unittest.mock import patch

from django.utils.timezone import now

from app.models.scheduled_task import ScheduledTaskReference
from tests import TestCase


class ScheduledTaskReferenceModel(TestCase):
    @patch("app.models.scheduled_task.ScheduledTaskReference.execute_task")
    @patch("app.models.scheduled_task.requests.post")
    def test_process_due_tasks_includes_59_seconds_with_microseconds(
        self, mock_post, mock_execute_task
    ):
        now_time = now().replace(second=0, microsecond=0)
        upper_time_limit = now_time + timedelta(seconds=59, microseconds=999999)

        ScheduledTaskReference.objects.create(
            task_id="836d99ec-1f59-429b-9637-8a3a80fd6c8d",
            organization_uuid="org-123",
            status=ScheduledTaskReference.Status.WAITING,
            run_at=upper_time_limit,
        )
        ScheduledTaskReference.objects.process_due_tasks()
        mock_execute_task.assert_called_once()
        mock_post.assert_called_once()
