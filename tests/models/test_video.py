import json
import uuid
from datetime import timedelta
from unittest import mock

import responses
from django.conf import settings
from django_multitenant.utils import set_current_tenant, unset_current_tenant

from app.domain.video import get_source_video_download_url, start_transcoding
from app.domain.video_output import create_video_output
from app.models import Video
from app.models.track import Track
from app.models.video_trim import OutputType, VideoOutput
from tests import TestCase
from tests.factories.asset import VideoInputFactory
from tests.mixins import OrganizationMixin
from tests.mixins.asset import AssetMixin, TrackMixin


class TestVideoInputModel(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()

    def test_input_url_should_return_same_url_for_http_url(self):
        actual_url = "http://rodriguez.biz/"
        video_input = self.create_video_input(actual_url)
        excepted = video_input.get_input_url()

        self.assertEqual(excepted, actual_url)

    @mock.patch("app.models.video.generate_presigned_url")
    def test_generate_presigned_url_method_was_called(
        self, mock_generate_presigned_url
    ):
        video_input = self.create_video_input(url="www.test.com")
        video_input.get_input_url()

        mock_generate_presigned_url.assert_called()

    # @mock.patch("app.domain.transcoder.get_transcoder")
    # def test_start_transcoding_should_generate_and_store_fairplay_data(
    #     self, mock_transcoder
    # ):
    #     self.organization.uuid = "edee9b"
    #     mock_transcoder.return_value.start.return_value = {"id": 123}
    #     video = self.create_video()
    #     video.start_transcoding()
    #
    #     video.refresh_from_db()
    #     self.assertTrue(video.fairplay_encryption_key_data is not None)

    @mock.patch("app.domain.video.cloudfront")
    def test_get_download_url_via_cloudfront(self, mock_generate_presigned_url):
        unset_current_tenant()
        self.organization = self.create_organization(storage_vendor=1)
        self.video = self.create_video()
        video_input = VideoInputFactory(
            organization=self.organization, video=self.video
        )
        video_input.url = "private/2b3f95ef4f974b8cbcf3d0fbed2bb1b6.mp4"
        video_input.save()

        mock_generate_presigned_url.generate_presigned_url.return_value = (
            "https://example.com/download"
        )

        download_url = get_source_video_download_url(video_input)

        self.assertEqual(download_url, "https://example.com/download")
        mock_generate_presigned_url.generate_presigned_url.assert_called_once_with(
            self.video.organization,
            video_input.url,
            download=True,
            download_as=video_input.video.asset.title + ".mp4",
            expires_in=3600,
        )

    @mock.patch("app.models.video.cloudfront")
    def test_get_download_url_not_downloadable(self, mock_generate_presigned_url):
        video = self.create_video()
        video_input = VideoInputFactory(organization=self.organization, video=video)
        video_input.is_video_ready_to_download = mock.Mock(return_value=False)

        mock_generate_presigned_url.generate_presigned_url.return_value = (
            "https://example.com/download"
        )

        download_url = get_source_video_download_url(video_input)
        self.assertEqual(download_url, "")
        mock_generate_presigned_url.generate_presigned_url.assert_not_called()

    def test_is_video_ready_for_download(self):
        video_uploading = self.create_video()
        video_uploading.status = Video.Status.UPLOADING
        video_uploading.save()
        video_input = VideoInputFactory(
            organization=self.organization, video=video_uploading
        )
        is_ready_for_upload = video_input.is_video_ready_to_download()

        self.assertFalse(is_ready_for_upload)

        video_uploading.status = Video.Status.QUEUED
        video_uploading.save()
        video_input = VideoInputFactory(
            organization=self.organization, video=video_uploading
        )
        is_ready_for_upload = video_input.is_video_ready_to_download()

        self.assertTrue(is_ready_for_upload)

    def test_get_source_video_download_url_returns_empty_string_when_video_input_is_none(
        self,
    ):
        video = self.create_video()
        video.inputs.all().delete()
        download_url = get_source_video_download_url(video.inputs.first())
        self.assertEqual(download_url, "")


class TestStartTranscodingMethod(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)
        self.video = self.create_video(
            resolutions=[Video.Resolutions._240p, Video.Resolutions._720p]
        )
        self.video.inputs.update(url="http://example.com/video.mp4")

    @responses.activate
    def test_should_update_video_status_with_error_state_if_transcoding_api_errors(
        self,
    ):
        responses.add(responses.POST, f"{settings.TRANSCODER_URL}/create/", status=400)
        start_transcoding(self.video)

        self.video.refresh_from_db()
        self.assertEqual(Video.Status.ERROR, self.video.status)

    @responses.activate
    def test_should_store_job_id_of_transcoding_job(self):
        job_id = str(uuid.uuid4())
        responses.add(
            responses.POST,
            f"{settings.TRANSCODER_URL}/create/",
            status=200,
            json={"id": job_id},
        )
        start_transcoding(self.video)

        self.video.refresh_from_db()
        self.assertEqual(job_id, str(self.video.job_id))

    @responses.activate
    def test_should_start_transcoding_in_specified_transcoding_queue(self):
        job_id = str(uuid.uuid4())
        responses.add(
            responses.POST,
            f"{settings.TRANSCODER_URL}/create/",
            status=200,
            json={"id": job_id},
        )
        start_transcoding(self.video, transcoding_queue_name="migrate_m3u8")

        self.assertEqual(job_id, str(self.video.job_id))
        self.assertEqual(
            json.loads(responses.calls[0].request.body)["settings"]["task_queue_name"],
            "migrate_m3u8",
        )

    @responses.activate
    def test_should_not_start_transcoding_if_resolutions_not_speficied(self):
        responses.add(
            responses.POST,
            f"{settings.TRANSCODER_URL}/create/",
            status=200,
            json={"id": str(uuid.uuid4())},
        )
        video = self.create_video(resolutions=[])
        start_transcoding(video)

        self.assertTrue(len(responses.calls) == 0)


class TestVideoModel(TrackMixin, OrganizationMixin, AssetMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_should_return_true_generate_subtitle_is_enabled_and_auto_generated_subtitle_does_not_exist(
        self,
    ):
        video = self.create_video(generate_subtitle=True)
        self.assertTrue(
            not video.tracks.filter(
                subtitle_type=Track.SubtitleType.AUTO_GENERATED
            ).exists()
        )
        self.assertEqual(True, video.should_generate_subtitle())

    def test_should_return_false_generate_subtitle_is_enabled(self):
        video = self.create_video(generate_subtitle=False)
        self.assertTrue(
            not video.tracks.filter(
                subtitle_type=Track.SubtitleType.AUTO_GENERATED
            ).exists()
        )
        self.assertEqual(False, video.should_generate_subtitle())

    def test_should_return_false_if_auto_generated_subtitle_exists(self):
        track = self.create_subtitle(subtitle_type=Track.SubtitleType.AUTO_GENERATED)
        video = track.video
        video.generate_subtitle = True
        video.save()
        self.assertTrue(
            video.tracks.filter(
                subtitle_type=Track.SubtitleType.AUTO_GENERATED
            ).exists()
        )
        self.assertEqual(False, video.should_generate_subtitle())

    def test_create_video_output_creates_new_output(self):
        video = self.create_video()
        video.duration = timedelta(minutes=5)
        video.save()

        output_type = OutputType.HLS
        codec = "h264"
        url = "example.com/video.m3u8"

        create_video_output(video, output_type, codec, url)

        video_output = VideoOutput.objects.get(
            organization=self.organization,
            video=video,
            output_type=output_type,
            codec=codec,
        )

        self.assertIn(url, video_output.url)
        self.assertEqual(video_output.duration, 300)
        self.assertTrue(video_output.is_active)

    def test_create_video_output_updates_existing_output(self):
        video = self.create_video()
        video.duration = timedelta(minutes=5)
        video.save()

        output_type = OutputType.HLS
        codec = "h264"
        initial_url = "https://example.com/old.m3u8"
        new_url = "example.com/new.m3u8"

        create_video_output(video, output_type, codec, initial_url)

        create_video_output(video, output_type, codec, new_url)

        video_output = VideoOutput.objects.get(
            organization=self.organization,
            video=video,
            output_type=output_type,
            codec=codec,
        )

        self.assertIn(new_url, video_output.url)
        self.assertEqual(video_output.duration, 300)
        self.assertTrue(video_output.is_active)

        self.assertEqual(
            VideoOutput.objects.filter(
                organization=self.organization,
                video=video,
                output_type=output_type,
                codec=codec,
            ).count(),
            1,
        )
