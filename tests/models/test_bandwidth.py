from django.db.utils import IntegrityError
from django.utils.timezone import localtime, now
from pytz import timezone

from app.models import AssetUsage
from tests import TestCase
from tests.mixins import OrganizationMixin


class TestAssetUsageModel(OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()

    def test_unique_constraint(self):
        today = localtime(now(), timezone=timezone("Asia/Kolkata")).date()
        AssetUsage.objects.create(
            organization=self.organization, date=today, time_frame=1
        )

        with self.assertRaises(IntegrityError):
            AssetUsage.objects.create(
                organization=self.organization, date=today, time_frame=1
            )
