from unittest.mock import patch

from django_multitenant.utils import set_current_tenant, unset_current_tenant

from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestOrganizationtModel(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_created_organization_has_uuid_with_length_of_6(self):
        unset_current_tenant()
        organization = self.create_organization()

        self.assertEqual(len(organization.uuid), 6)

    def test_organization_uuid_contains_only_valid_characters(self):
        unset_current_tenant()
        organization = self.create_organization()

        for char in organization.uuid:
            self.assertIn(char, "abcdefghjkmnpqrstuxyz23456789")

    @patch("app.models.organization.populate_uuid")
    def test_populate_uuid_called_only_for_organization_instance(
        self, mock_populate_uuid
    ):
        unset_current_tenant()
        organization = self.create_organization()

        mock_populate_uuid.assert_called_with(
            organization,
            length=6,
        )

    @patch("app.models.organization.populate_uuid")
    def test_populate_uuid_not_called_for_asset_instance(self, mock_populate_uuid):
        self.create_asset()

        mock_populate_uuid.assert_not_called()


class TestOrganizaionModelMethods(AssetMixin, OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_is_member_method_return_true_for_valide_user(self):
        user = self.create_user()
        self.create_membership(user=user)

        self.assertTrue(self.organization.is_member(user))

    def test_is_member_method_return_false_for_invalide_user(self):
        user = self.create_user()

        self.assertFalse(self.organization.is_member(user))
