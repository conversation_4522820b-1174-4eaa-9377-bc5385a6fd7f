from unittest.mock import patch

from django_multitenant.utils import set_current_tenant, unset_current_tenant

from tests import TestCase
from tests.mixins import OrganizationMixin
from tests.mixins.asset import AssetMixin


class TestAssetModel(OrganizationMixin, AssetMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        set_current_tenant(self.organization)

    def test_created_asset_has_uuid_with_length_of_11(self):
        asset = self.create_asset()

        self.assertEqual(len(asset.uuid), 11)

    def test_asset_uuid_contains_only_valid_characters(self):
        asset = self.create_asset()

        for char in asset.uuid:
            self.assertIn(char, "ABCDEFGHJKMNPQRSTUXYZabcdefghjkmnpqrstuxyz23456789")

    @patch("app.models.asset.populate_uuid")
    def test_populate_uuid_called_only_for_asset_instance(self, mock_populate_uuid):
        asset = self.create_asset()

        mock_populate_uuid.assert_called_with(
            asset,
            alphabet="ABCDEFGHJKMNPQRSTUXYZabcdefghjkmnpqrstuxyz23456789",
            length=11,
        )

    @patch("app.models.asset.populate_uuid")
    def test_populate_uuid_not_called_for_organization_instance(
        self, mock_populate_uuid
    ):
        unset_current_tenant()
        self.create_organization()

        mock_populate_uuid.assert_not_called()
