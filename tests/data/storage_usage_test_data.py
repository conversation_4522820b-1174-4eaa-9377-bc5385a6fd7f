DAY_STORAGE_USAGE_RESPONSE = [
    {
        "BucketUtilizationNum": 360426317,
        "AcctNum": 104509,
        "AcctPlanNum": 303868,
        "BucketNum": 1270728,
        "StartTime": "2023-06-20T00:00:00Z",
        "EndTime": "2023-06-21T00:00:00Z",
        "CreateTime": "2023-06-21T00:09:32Z",
        "NumBillableObjects": 21051,
        "NumBillableDeletedObjects": 3821,
        "RawStorageSizeBytes": 10878210145,
        "PaddedStorageSizeBytes": 10879995565,
        "MetadataStorageSizeBytes": 1025197,
        "DeletedStorageSizeBytes": 1043973205,
        "OrphanedStorageSizeBytes": 0,
        "NumAPICalls": 64,
        "UploadBytes": 596640,
        "DownloadBytes": 1937287,
        "StorageWroteBytes": 525402,
        "StorageReadBytes": 1098632,
        "TimeToCalc": 0,
        "NumGETCalls": 26,
        "NumPUTCalls": 14,
        "NumDELETECalls": 0,
        "NumLISTCalls": 2,
        "NumHEADCalls": 12,
        "VaultName": "prod3",
        "DeleteBytes": 0,
        "Bucket": "resolver-for-buildstep-for-stepbuilderdjangooptions-for-organizationfactory-"
        "strategycreate-tpstreams.com",
    },
    {
        "BucketUtilizationNum": 360426317,
        "AcctNum": 104509,
        "AcctPlanNum": 303868,
        "BucketNum": 1270728,
        "StartTime": "2023-06-20T00:00:00Z",
        "EndTime": "2023-06-21T00:00:00Z",
        "CreateTime": "2023-06-21T00:09:32Z",
        "NumBillableObjects": 21051,
        "NumBillableDeletedObjects": 3821,
        "RawStorageSizeBytes": 10878210145,
        "PaddedStorageSizeBytes": 10879877445,
        "MetadataStorageSizeBytes": 1013086,
        "DeletedStorageSizeBytes": 1043984134,
        "OrphanedStorageSizeBytes": 0,
        "NumAPICalls": 64,
        "UploadBytes": 596640,
        "DownloadBytes": 1937287,
        "StorageWroteBytes": 525402,
        "StorageReadBytes": 1098632,
        "TimeToCalc": 0,
        "NumGETCalls": 26,
        "NumPUTCalls": 14,
        "NumDELETECalls": 0,
        "NumLISTCalls": 2,
        "NumHEADCalls": 12,
        "VaultName": "prod3",
        "DeleteBytes": 0,
        "Bucket": "resolver-for-buildstep-for-stepbuilderdjangooptions-for-organizationfactory-"
        "strategycreate-tpstreams.com",
    },
]


MONTHLY_STORAGE_USAGE_RESPONSE = [
    {
        "BucketUtilizationNum": 360426317,
        "AcctNum": 104509,
        "AcctPlanNum": 303868,
        "BucketNum": 1270728,
        "StartTime": "2023-06-20T00:00:00Z",
        "EndTime": "2023-06-21T00:00:00Z",
        "CreateTime": "2023-06-21T00:09:32Z",
        "NumBillableObjects": 21051,
        "NumBillableDeletedObjects": 3821,
        "RawStorageSizeBytes": 10878210145,
        "PaddedStorageSizeBytes": 10879877445,
        "MetadataStorageSizeBytes": 1024086,
        "DeletedStorageSizeBytes": 1043973205,
        "OrphanedStorageSizeBytes": 0,
        "NumAPICalls": 64,
        "UploadBytes": 596640,
        "DownloadBytes": 1937287,
        "StorageWroteBytes": 525402,
        "StorageReadBytes": 1098632,
        "TimeToCalc": 0,
        "NumGETCalls": 26,
        "NumPUTCalls": 14,
        "NumDELETECalls": 0,
        "NumLISTCalls": 2,
        "NumHEADCalls": 12,
        "VaultName": "prod3",
        "DeleteBytes": 0,
        "Bucket": "resolver-for-buildstep-for-stepbuilderdjangooptions-for-organizationfactory-"
        "strategycreate-tpstreams.com",
    },
]
