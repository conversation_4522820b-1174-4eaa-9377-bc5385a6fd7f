from django.test import override_settings

from config.wsgi import application
from tests import TestCase
from tests.domain.data.shaka_packager_test_data import (
    SHAKA_PACKAGER_RESPONSE_DATA,
    WSGI_INPUT,
)


@override_settings(
    DEBUG=True,
)
class ApplicationTestCase(TestCase):
    def test_application_handles_chunked_data_from_shakapackager(self):
        def start_response(status, headers):
            pass

        application(SHAKA_PACKAGER_RESPONSE_DATA, start_response)

        self.assertNotIn("HTTP_TRANSFER_ENCODING", SHAKA_PACKAGER_RESPONSE_DATA)
        self.assertEqual(
            SHAKA_PACKAGER_RESPONSE_DATA["CONTENT_LENGTH"],
            WSGI_INPUT.stream.getbuffer().nbytes,  # type: ignore
        )
        self.assertEqual(
            SHAKA_PACKAGER_RESPONSE_DATA["CONTENT_TYPE"], "application/shaka-json"
        )
