import datetime
from unittest.mock import patch

from django_multitenant.utils import unset_current_tenant
from freezegun import freeze_time

from app.domain.bandwidth_calculator import (
    update_day_bandwidth_usage,
    update_monthly_bandwidth_usage,
    update_org_bandwidth_usage,
)
from app.models import AssetUsage, Organization
from tests import TestCase
from tests.mixins import OrganizationMixin


@freeze_time("2023-03-07")
class TestBandWidthCalculator(OrganizationMixin, TestCase):
    def setUp(self) -> None:
        self.organization = self.create_organization()

    @patch("app.domain.bandwidth_calculator.get_bandwidth_used", return_value=10000)
    def test_update_monthly_bandwidth_usage_should_calculate_bandwidth_for_given_date(
        self, mock_get_bandwidth_used
    ):
        update_monthly_bandwidth_usage(self.organization, datetime.date(2022, 5, 15))

        self.assertEqual(
            AssetUsage.objects.get(
                date=datetime.date(2022, 5, 1),
                time_frame=AssetUsage.TimeFrames.MONTHLY,
            ).bandwidth_used,
            10000,
        )

    @patch("app.domain.bandwidth_calculator.get_bandwidth_used", return_value=10000)
    def test_update_day_bandwidth_usage_should_calculate_bandwidth_for_given_date(
        self, mock_get_bandwidth_used
    ):
        date = datetime.date(2022, 5, 15)
        update_day_bandwidth_usage(self.organization, date)

        self.assertEqual(
            AssetUsage.objects.get(
                date=datetime.date(2022, 5, 15),
                time_frame=AssetUsage.TimeFrames.DAILY,
            ).bandwidth_used,
            10000,
        )

    @patch("app.domain.bandwidth_calculator.get_bandwidth_used", return_value=10000)
    def test_get_bandwidth_used_called_with_valide_parameters_for_monthly_usage(
        self, mock_get_bandwidth_used
    ):
        date = datetime.date(2022, 5, 15)
        update_monthly_bandwidth_usage(self.organization, date)

        mock_get_bandwidth_used.assert_called_with(
            self.organization,
            from_date="2022-05-01-00-00-00",
            to_date="2022-05-31-23-59-59",
        )

    @patch("app.domain.bandwidth_calculator.get_bandwidth_used", return_value=10000)
    def test_get_bandwidth_used_called_with_valide_parameters_for_day_usage(
        self, mock_get_bandwidth_used
    ):
        date = datetime.date(2022, 5, 15)
        update_day_bandwidth_usage(self.organization, date)

        mock_get_bandwidth_used.assert_called_with(
            self.organization,
            from_date="2022-05-15-00-00-00",
            to_date="2022-05-15-23-59-59",
        )

    @patch("app.domain.bandwidth_calculator.update_day_bandwidth_usage")
    @patch("app.domain.bandwidth_calculator.update_monthly_bandwidth_usage")
    def test_should_not_update_bandwidth_usage_for_blocked_organization(
        self, mock_update_org_bandwidth_usage, mock_update_monthly_bandwidth_usage
    ):
        unset_current_tenant()
        organization = self.create_organization(status=Organization.Status.BLOCKED)
        date = datetime.date(2022, 5, 15)
        update_org_bandwidth_usage(organization, date)

        mock_update_org_bandwidth_usage.assert_not_called()
        mock_update_monthly_bandwidth_usage.assert_not_called()

    @patch("app.domain.bandwidth_calculator.update_day_bandwidth_usage")
    @patch("app.domain.bandwidth_calculator.update_monthly_bandwidth_usage")
    def test_should_update_bandwidth_usage_for_active_organization(
        self, mock_update_org_bandwidth_usage, mock_update_monthly_bandwidth_usage
    ):
        unset_current_tenant()
        organization = self.create_organization(status=Organization.Status.ACTIVE)
        date = datetime.date(2022, 5, 15)
        update_org_bandwidth_usage(organization, date)

        mock_update_org_bandwidth_usage.assert_called()
        mock_update_monthly_bandwidth_usage.assert_called()
