from datetime import date, timedelta

import factory

from app.models import AssetUsage
from tests.factories import OrganizationFactory


class AssetUsageFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = AssetUsage

    organization = factory.SubFactory(OrganizationFactory)
    time_frame = AssetUsage.TimeFrames.DAILY
    date = factory.Sequence(lambda n: date.today() - timedelta(days=n))
    bandwidth_used = factory.Faker("pyint", min_value=0, max_value=1000000)
