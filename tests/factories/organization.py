import factory
from django.utils.text import slugify
from factory.django import DjangoModelFactory

from app.models import Membership, Organization

from .user import UserFactory


class OrganizationFactory(DjangoModelFactory):
    class Meta:
        model = Organization

    name = factory.Sequence(lambda n: "Organization %d" % n)
    created_by = factory.SubFactory(UserFactory)
    bucket_name = factory.LazyAttribute(lambda obj: "%s-tpstreams.com" % slugify(obj))


class MembershipFactory(DjangoModelFactory):
    class Meta:
        model = Membership

    user = factory.SubFactory(UserFactory)
