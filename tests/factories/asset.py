import factory
from factory.django import DjangoModelFactory

from app.models import Asset, PreviewThumbnail, Track, TranscodingJob, Video, VideoInput
from tests.factories import OrganizationFactory


class VideoInputFactory(DjangoModelFactory):
    class Meta:
        model = VideoInput

    url = factory.Faker("url")


class AssetFactory(DjangoModelFactory):
    class Meta:
        model = Asset

    title = factory.Sequence(lambda n: "Asset %d" % n)
    organization = factory.SubFactory(OrganizationFactory)


class VideoFactory(DjangoModelFactory):
    class Meta:
        model = Video

    asset = factory.SubFactory(AssetFactory)
    organization = factory.SubFactory(OrganizationFactory)


class TranscodingJobFactory(DjangoModelFactory):
    class Meta:
        model = TranscodingJob

    organization = factory.SubFactory(OrganizationFactory)


class TrackFactory(DjangoModelFactory):
    class Meta:
        model = Track

    name = factory.Sequence(lambda n: "Track %d" % n)

    type = Track.Type.SUBTITLE
    language = "en"
    video = factory.SubFactory(VideoFactory)
    url = factory.LazyAttribute(
        lambda a: f"transcoded/assets/{a.video.asset.uuid}/{a.language}.vtt"
    )
    organization = factory.LazyAttribute(lambda obj: obj.video.organization)


class PreviewThumbnailFactory(DjangoModelFactory):
    class Meta:
        model = PreviewThumbnail

    width = 160
    height = 90
    rows = 10
    columns = 10
    interval = 5
    url = factory.LazyAttribute(lambda a: "transcoded/assets/123/sprite.png")
    track = factory.SubFactory(TrackFactory)
    organization = factory.LazyAttribute(lambda obj: obj.organization)
