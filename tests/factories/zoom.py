import factory
from django.utils import timezone
from factory.django import DjangoModelFactory

from app.models.zoom import EventType, WebhookStatus, ZoomAccount, ZoomWebhookLog
from tests.factories import OrganizationFactory, UserFactory


class ZoomWebhookLogFactory(DjangoModelFactory):
    class Meta:
        model = ZoomWebhookLog

    organization = factory.SubFactory(OrganizationFactory)
    payload = factory.LazyFunction(
        lambda: {"event": "recording.completed", "payload": {}}
    )
    received_at = factory.LazyFunction(timezone.now)
    event_type = factory.Iterator([choice[0] for choice in EventType.choices])
    status = factory.Iterator([choice[0] for choice in WebhookStatus.choices])
    error_message = None


class ZoomAccountFactory(DjangoModelFactory):
    class Meta:
        model = ZoomAccount

    organization = factory.SubFactory(OrganizationFactory)
    zoom_user_id = factory.Faker("uuid4")
    access_token = factory.Faker("uuid4")
    refresh_token = factory.Faker("uuid4")
    user = factory.SubFactory(UserFactory)
