from datetime import date, timedelta

import factory

from app.models import AssetUsage
from tests.factories import OrganizationFactory


class StorageUsageFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = AssetUsage

    organization = factory.SubFactory(OrganizationFactory)
    time_frame = AssetUsage.TimeFrames.DAILY
    date = factory.Sequence(lambda n: date.today() - timedelta(days=n))
    active_storage_bytes = factory.Faker("pyint", min_value=0, max_value=1000000)
    deleted_storage_bytes = factory.Faker("pyint", min_value=0, max_value=1000000)
