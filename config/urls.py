"""streams URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from __future__ import annotations

from collections.abc import Sequence

from django.conf import settings
from django.contrib import admin
from django.urls import URLPattern, URLResolver, include, path

from app.api.v1.views.authtoken import LoginView
from app.views import (
    AccountLoginView,
    AdvanceSettingsView,
    AssetDebugView,
    AssetDeleteListView,
    AssetDeleteSearchView,
    AssetDetailView,
    AssetInternalsView,
    AssetListView,
    AssetMoveView,
    AssetRetriggerView,
    AssetSearchView,
    AssetUploadView,
    AssetUsageListView,
    ChapterView,
    ChatEmbedView,
    CompletedLiveStreamListView,
    CustomPasswordChangeView,
    FolderDeleteView,
    GeneralView,
    IntegrationsView,
    InternalsView,
    LiveStreamCreateView,
    LiveStreamDetailView,
    LiveStreamListView,
    LiveStreamMoveView,
    LiveStreamSearchView,
    LiveStreamUpdateView,
    OrganizationVideoPreferencesView,
    ScheduledLiveStreamListView,
    ScheduleLiveStreamView,
    SignupView,
    StopLiveStreamView,
    SubtitleUploadView,
    TPStreamsAdminLiveStreamListView,
    VideoPreferencesView,
    VideoSettingsView,
    create_live_stream_server_view,
    download_asset_source_video_view,
)
from app.views.assets import EmptyTrashView
from app.views.embed import AssetUploadEmbedView, EmbedView
from app.views.export import (
    LiveStreamUsageExportView,
    SubtitlesGenerationUsageExportView,
)
from app.views.live_streams import (
    AcknowledgeStreamEventView,
    LiveStreamActivityLogView,
    PendingEventsView,
    StreamStatusView,
)
from app.views.subtitle import SubtitleDeleteView, SubtitleUpdateView
from app.views.thumbnail import ThumbnailUpdateView
from app.views.zoom import (
    ZoomAccountUpdateView,
    ZoomAuthorizeView,
    ZoomDisconnectView,
    ZoomOAuthCallbackView,
    ZoomRecordingWebhookView,
)

urlpatterns: Sequence[URLPattern | URLResolver] = [
    path("admin/", admin.site.urls),
    path("accounts/login/", AccountLoginView.as_view(), name="account_login"),
    path("accounts/signup/", SignupView.as_view(), name="account_signup"),
    path("accounts/", include("allauth.urls")),
    path("", AssetListView.as_view(), name="assets"),
    path("assets/upload/", AssetUploadView.as_view(), name="upload"),
    path("assets/move/", AssetMoveView.as_view(), name="asset_move"),
    path("assets/deleted/", AssetDeleteListView.as_view(), name="deleted_assets"),
    path("assets/empty-trash/", EmptyTrashView.as_view(), name="empty_trash"),
    path(
        "assets/folder/<str:uuid>/delete/",
        FolderDeleteView.as_view(),
        name="delete_folder",
    ),
    path("search/", AssetSearchView.as_view(), name="search"),
    path("deleted_search/", AssetDeleteSearchView.as_view(), name="deleted_search"),
    path(
        "assets/download/<str:asset_uuid>/",
        download_asset_source_video_view,
        name="asset_download",
    ),
    path(
        "assets/<str:asset_uuid>/chapters",
        ChapterView.as_view(),
        name="chapters",
    ),
    path(
        "assets/<str:asset_uuid>",
        AssetDetailView.as_view(),
        name="asset_detail",
    ),
    path(
        "assets/<str:asset_uuid>/log/",
        AssetDebugView.as_view(),
        name="asset_debug",
    ),
    path(
        "assets/<str:asset_uuid>/thumbnail",
        ThumbnailUpdateView.as_view(),
        name="update_thumbnail",
    ),
    path(
        "assets/<str:uuid>/internals/",
        AssetInternalsView.as_view(),
        name="asset_internals",
    ),
    path(
        "assets/<str:asset_uuid>/subtitle/",
        SubtitleUploadView.as_view(),
        name="upload_subtitle",
    ),
    path(
        "assets/<str:asset_uuid>/delete_subtitle/<int:subtitle_id>/",
        SubtitleDeleteView.as_view(),
        name="delete_subtitle",
    ),
    path(
        "assets/<str:asset_uuid>/<int:subtitle_id>/edit/",
        SubtitleUpdateView.as_view(),
        name="edit_subtitle",
    ),
    path(
        "assets/<str:asset_uuid>/edit/",
        AdvanceSettingsView.as_view(),
        name="edit_asset",
    ),
    path(
        "assets/<str:asset_uuid>/edit/player/",
        VideoPreferencesView.as_view(),
        name="edit_video",
    ),
    path(
        "assets/<str:asset_uuid>/retrigger/",
        AssetRetriggerView.as_view(),
        name="retrigger",
    ),
    path(
        "embed/<str:organization_id>/asset-uploader/",
        AssetUploadEmbedView.as_view(),
        name="asset_upload_embed_view",
    ),
    path(
        "embed/<str:organization_id>/<str:uuid>/",
        EmbedView.as_view(),
        name="embed-view",
    ),
    path(
        "live/",
        LiveStreamListView.as_view(),
        name="live_streams",
    ),
    path(
        "live/scheduled/",
        ScheduledLiveStreamListView.as_view(),
        name="scheduled_live_streams",
    ),
    path(
        "live/completed/",
        CompletedLiveStreamListView.as_view(),
        name="completed_live_streams",
    ),
    path(
        "live/internals/",
        TPStreamsAdminLiveStreamListView.as_view(),
        name="live_streams_internals",
    ),
    path(
        "live/stream-status/",
        StreamStatusView.as_view(),
        name="live_stream_status",
    ),
    path(
        "live/pending-events/",
        PendingEventsView.as_view(),
        name="live_stream_pending_events",
    ),
    path(
        "live/acknowledge-event/",
        AcknowledgeStreamEventView.as_view(),
        name="acknowledge_live_stream_event",
    ),
    path("live/search/", LiveStreamSearchView.as_view(), name="live_search"),
    path(
        "live/<str:uuid>/log/",
        LiveStreamActivityLogView.as_view(),
        name="live_stream_log",
    ),
    path(
        "live/create/",
        LiveStreamCreateView.as_view(),
        name="create_live_stream",
    ),
    path(
        "live/schedule/",
        ScheduleLiveStreamView.as_view(),
        name="schedule_live_stream",
    ),
    path(
        "live_stream/<str:uuid>/create/",
        create_live_stream_server_view,
        name="create_live_stream_server",
    ),
    path(
        "live/<str:uuid>/streaming/",
        LiveStreamDetailView.as_view(),
        name="live_stream_settings",
    ),
    path(
        "live_stream/<str:uuid>/stop/",
        StopLiveStreamView.as_view(),
        name="stop_live_stream",
    ),
    path("live_stream/move/", LiveStreamMoveView.as_view(), name="live_stream_move"),
    path(
        "live/<str:uuid>/update/",
        LiveStreamUpdateView.as_view(),
        name="live_stream_update",
    ),
    path(
        "live-chat/<str:organization_id>/<str:uuid>/",
        ChatEmbedView.as_view(),
        name="chat_embed_view",
    ),
    path(
        "settings/",
        GeneralView.as_view(),
        name="general_settings",
    ),
    path(
        "settings/usages/",
        AssetUsageListView.as_view(),
        name="usages",
    ),
    path(
        "settings/password/",
        CustomPasswordChangeView.as_view(),
        name="password_settings",
    ),
    path(
        "settings/videos/",
        VideoSettingsView.as_view(),
        name="video_settings",
    ),
    path(
        "settings/player/",
        OrganizationVideoPreferencesView.as_view(),
        name="player_settings",
    ),
    path(
        "settings/integrations/",
        IntegrationsView.as_view(),
        name="integrations_settings",
    ),
    path(
        "settings/integrations/zoom/authorize/",
        ZoomAuthorizeView.as_view(),
        name="zoom_authorize",
    ),
    path(
        "settings/integrations/zoom/callback/",
        ZoomOAuthCallbackView.as_view(),
        name="zoom_oauth_callback",
    ),
    path(
        "settings/integrations/zoom/disconnect/",
        ZoomDisconnectView.as_view(),
        name="zoom_disconnect",
    ),
    path(
        "webhooks/zoom/recording/",
        ZoomRecordingWebhookView.as_view(),
        name="zoom_recording_webhook",
    ),
    path(
        "settings/integrations/zoom/update/",
        ZoomAccountUpdateView.as_view(),
        name="zoom_account_update",
    ),
    path(
        "settings/internals/",
        InternalsView.as_view(),
        name="internals",
    ),
    path(
        "export/live-stream-usage/",
        LiveStreamUsageExportView.as_view(),
        name="export-live-stream-usage",
    ),
    path(
        "export/live-stream-usage/",
        SubtitlesGenerationUsageExportView.as_view(),
        name="export-subtitles_generation_usage",
    ),
    path("api/auth/login/", LoginView.as_view(), name="auth-token"),
    path("api/auth/", include("knox.urls")),
    path("api/v1/", include("app.api.v1.urls"), name="api"),
    path("api/uploader/", include("app.api.uploader.urls"), name="uploader"),
]

if settings.DEBUG:  # pragma: no cover
    import debug_toolbar  # noqa: WPS433
    from django.conf.urls.static import static  # noqa: WPS433

    urlpatterns = [
        # URLs specific only to django-debug-toolbar:
        path("__debug__/", include(debug_toolbar.urls)),
        path("__reload__/", include("django_browser_reload.urls")),
        *urlpatterns,
        # Serving media files in development only:
        *static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT),
    ]
