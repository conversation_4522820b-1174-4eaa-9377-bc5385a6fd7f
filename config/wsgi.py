"""
WSGI config for streams project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/howto/deployment/wsgi/
"""

import os
from io import BytesIO

from django.conf import settings
from django.core.handlers.wsgi import LimitedStream
from django.core.wsgi import get_wsgi_application

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.production")

django_application = get_wsgi_application()


def application(environ, start_response):
    """
    Shaka packager sends data as chunked which is not supported by django. So we add the below snippet to
    add proper `CONTENT_LENGTH` header to that data. We also change `CONTENT_TYPE` since data we receive has
    line breaks which we need to remove.
    This is being done only for debug mode as nginx adds content length in production
    """
    if settings.DEBUG and "ShakaPackager" in environ.get("HTTP_USER_AGENT"):
        del environ["HTTP_TRANSFER_ENCODING"]
        data = environ["wsgi.input"].stream.read1()
        environ["CONTENT_LENGTH"] = len(data)
        environ["CONTENT_TYPE"] = "application/shaka-json"
        environ["wsgi.input"] = LimitedStream(BytesIO(data), len(data))
    return django_application(environ, start_response)
