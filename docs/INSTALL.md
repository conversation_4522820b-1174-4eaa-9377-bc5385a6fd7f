## Setup instructions for TPStreams on your laptop

**Prerequisites:** Ubuntu 20.04 or WSL2

Install python-dev, lsb_release, virtualenvwrapper

```bash
sudo apt-get update
sudo apt-get install -y python3-all-dev
sudo apt install virtualenv
sudo apt install virtualenvwrapper
```

Run the following

```bash
export WORKON_HOME=~/workspace/
source /usr/share/virtualenvwrapper/virtualenvwrapper.sh
```

If you get the following error:

```bash
source /usr/local/bin/virtualenvwrapper.sh
/usr/bin/python: No module named virtualenvwrapper
virtualenvwrapper.sh: There was a problem running the initialization hooks.

If Python could not import the module virtualenvwrapper.hook_loader,
check that virtualenvwrapper has been installed for
VIRTUALENVWRAPPER_PYTHON=/usr/bin/python and that PATH is
set properly.
```

Do the following

```bash
export VIRTUALENVWRAPPER_PYTHON=`which python3`
```

Now create a virtualenv for tpstreams

```bash
mkvirtualenv -p python3 tpstreams
cdvirtualenv
```

If you are setting up app server, then install the below. This makes psycopg2 to be installed without installing postgtres
```
sudo apt-get install libpq-dev
```
### Setup repository
#### Add deploy key to github (Only for production)
```bash
ssh-keygen -t ed25519 -C "<EMAIL>"
eval "$(ssh-agent -s)"
ssh-add ~/.ssh/id_ed25519
cat /home/<USER>/.ssh/id_ed25519.pub
```
Copy the above response and add it to https://github.com/testpress/testpress_python/settings/keys

Clone the repo

```bash
<NAME_EMAIL>:testpress/streams.git
cd streams
pip install -r requirements/local.txt
```



Install postgresql latest using the following commands

```bash
sudo sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list'
wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo apt-key add -
sudo apt-get update
sudo apt-get -y install postgresql-15
```

Install citus

```bash
# Add Citus repository for package manager
curl https://install.citusdata.com/community/deb.sh | sudo bash

# install the server and initialize db
sudo apt-get -y install postgresql-15-citus-11.1
```

Ensure `/etc/postgresql/15/main/pg_hba.conf` allows accessing db via localhost by adding trust

```bash
# IPv4 local connections:
host    all             all             127.0.0.1/32            trust
# IPv6 local connections:
host    all             all             ::1/128                 trust
```

Load citus extension to postgres and restart

```bash
echo "shared_preload_libraries = 'citus'" | sudo tee -a /etc/postgresql/15/main/postgresql.conf
sudo service postgresql restart
```

Copy the .env file to your project location

```bash
cdvirtualenv
cd streams
cp ~/env .env
```

Create a user testpress for Postgres. Set the password mentioned in the .env file 👀

```bash
sudo -u postgres createuser testpress -sW
```

Create database for streams
```bash
sudo -u postgres createdb streams -O testpress
```

Load citus extension and verify the same
```bash
sudo -u postgres psql streams -c "CREATE EXTENSION citus;"
sudo -u postgres psql streams -c "SELECT * FROM citus_version();"
```

The above command will give an output like the following
```
                                           citus_version
----------------------------------------------------------------------------------------------------
 Citus 11.1.4 on x86_64-pc-linux-gnu, compiled by gcc (Ubuntu 9.4.0-1ubuntu1~20.04.1) 9.4.0, 64-bit
(1 row)
```

Migrate streams

```bash
./manage.py migrate
```

Install and tailwind server
- To install tailwind, we need to install npm
```
sudo apt install nodejs
sudo apt install npm

```

```bash
./manage.py tailwind install
./manage.py tailwind start
```

In another tab run server

```bash
./manage.py runserver 0.0.0.0:8000
```

This should start your server in http://127.0.0.1:8000/

For convenience, you can add the following in ~/.bash_profile or ~/.bash_aliases

```bash
export WORKON_HOME=~/workspace
export VIRTUALENVWRAPPER_PYTHON=`which python3`
source /usr/local/bin/virtualenvwrapper.sh
workon tpstreams
```
## Steps to setup app server
### Setup supervisor
```bash
sudo apt install supervisor
sudo mkdir -p /var/log/supervisord
sudo mkdir -p /var/log/gunicorn
```

#### Create gunicorn.conf in supervisor

`sudo vim /etc/supervisor/conf.d/gunicorn.conf`

```shell
[program:gunicorn]
command=/home/<USER>/workspace/tpstreams/bin/python /home/<USER>/workspace/tpstreams/bin/gunicorn config.wsgi:application -c /etc/gunicorn_conf.py --log-level=DEBUG
directory=/home/<USER>/workspace/tpstreams/streams
autostart=true
autorestart=true
stdout_logfile=/var/log/gunicorn/stdout.log
stderr_logfile=/var/log/gunicorn/stderr.log
stdout=/var/log/gunicorn/stdout.log
stderr=/var/log/gunicorn/stderr.log
user=ubuntu
env=PYTHONUNBUFFERED=1
```

#### Create gunicorn's conf
`sudo vim /etc/gunicorn_conf.py`

```shell
from multiprocessing import cpu_count

bind = "0.0.0.0:8000"
workers = cpu_count() * 2 + 1
logfile = "/var/log/gunicorn/gunicorn.log"
```

#### Create logrotate
`sudo vim /etc/logrotate.d/gunicorn`

```shell
/var/log/gunicorn/*.log {
    daily
    create 700 ubuntu ubuntu
    rotate 52
    dateext
    maxage 100
    missingok
    postrotate
        /usr/local/bin/supervisorctl restart gunicorn > /dev/null
    endscript
}
```

#### Add to supervisor

```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl status
```


Open `/etc/nginx/sites-available/default` in a text editor (vim/nano). Add the following inside `server` block

```bash
server {
 ....

 location / {
  ....

  if ($http_user_agent ~* "ShakaPackager") {
    add_header Content-Length $content_length;
  }

  ....
 }

 ....
}

```
The above code adds `Content-Length` HTTP header to requests which have "ShakaPackager" as user agent.
This is needed because Shaka packager will send chunked request to our server to get encryption keys. But Django(WSGI) do
not support chunked header, so request BODY will be empty for those requests.
If we add content length header then Django will parse the request body.
