
# Live Streaming Manual Setup
This document can help you manually setup a live streaming server using RTMP & HLS backend. This will give you an idea of how live streaming works under the hood.

- We will be using SRS Server for RTMP input and output.
- FFMpeg would transcode the output of SRS server to HLS. 
- This would be served via HTTP using nginx.
- To scale the output we shall use a CDN (Cloudfront in our case)
- We shall have a HAProxy act as a liason between our nginx HTTP server and cloudfront. (This is to reduce the delay caused if we add our new nginx server to cloudfront)
- In other words, the purpose of using HAProxy in this context is to improve the performance and minimize any potential delays when integrating a new nginx server with cloudfront.

## Droplet creation
- Create a droplet in Bangalore
- Ubuntu OS, Shared Regular $7/mo (1 GB RAM, 25 GB NVME SSD, 1000GB Transfer)
- Select password for login
- Create Droplet and wait for it to complete
- SSH into the droplet using root username and password (Provided above)
- To make the process simpler and easier, please create a user with name `ubuntu` and give it sudo privileges.

```bash
sudo adduser ubuntu
sudo gpasswd -a ubuntu sudo
```

## SRS Live Stream Server Setup
- SSH into the droplet using ubuntu username and password (Provided above)
- Clone the SRS Repo and build the same
```bash
git clone -b 4.0release https://github.com/ossrs/srs.git
cd srs/trunk
./configure
make
```
- Run srs 
```bash
./objs/srs -c conf/srs.conf
```

Try visiting the SRS status page in your browser using the ip address and 8080 port. For ex: If IP address of the digital ocean droplet is ************, then you need to visit
```bash
http://************:8080/
```

## FFMpeg Setup

Install ffmpeg
```bash
sudo apt-get install ffmpeg
```

We will run ffmpeg to transcode the output of srs rtmp and store it in a local folder.

**Assumptions**
- We shall stream to a path called `livestream`. So the rtmp url would be `rtmp://localhost:1935/live/livestream`
- The output shall be stored inside the home directory in livestream folder. This should be created
- We will transcode into three resolutions 240p, 480p, 720p

```bash
mkdir ~/livestream
ffmpeg -i rtmp://localhost:1935/live/livestream -map 0:v:0 -map 0:a:0 -map 0:v:0 -map 0:a:0 -map 0:v:0 -map 0:a:0 -c:v libx264 -crf 22 -c:a aac -ar 48000 -filter:v:0 scale=w=426:h=240 -maxrate:v:0 300k -b:a:0 64k -filter:v:1 scale=w=854:h=480 -maxrate:v:1 800k -b:a:1 128k -c:v:2 copy -c:a:2 copy -var_stream_map "v:0,a:0,name:240p v:1,a:1,name:480p v:2,a:2,name:720p" -preset fast -hls_list_size 0 -threads 0 -f hls -hls_playlist_type event -hls_time 3 -hls_flags independent_segments -master_pl_name "video.m3u8" ~/livestream/video-%v.m3u8
```


## OBS Setup
- In your local computer, install OBS Stream Studio
- In settings, go to stream settings
- For server, paste the rtmp without the key. For example, in our case it would be
```
rtmp://************/live/
```
- For Stream Key, use `livestream`
- Click Apply and OK
- Click Start Streaming

## Nginx serve HLS
- We shall install nginx to serve the output of ffmpeg via HTTP.
- We can then view the same via a standalone HLS Player
- Install nginx using the following command
```bash
sudo apt-get install nginx
```
Add the following in `/etc/nginx/sites-enabled/default` above `location / {` block

```nginx
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET";

        location ~ ^/live/(.+\.ts)$ {
            alias /home/<USER>/livestream/$1;

            # Let the MPEG-TS video chunks be cacheable
            expires max;
        }

        location ~ ^/live/(.+\.m3u8)$ {
            alias /home/<USER>/livestream/$1;

            # The M3U8 playlists should not be cacheable
            expires -1d;
        }
```
- In the above we have instructed nginx to serve the ts and m3u8 files present in ~/livestream folder. We have also allowed all origins to prevent CORS blocking as we will be trying with a javascript HLS player
- Check if nginx configuration is correct
```bash
sudo nginx -t
```
- Should get the following output
```bash
ubuntu@livestreaming:~/srs/trunk$ sudo nginx -t
nginx: the configuration file /etc/nginx/nginx.conf syntax is ok
nginx: configuration file /etc/nginx/nginx.conf test is successful
```
- Instruct nginx to use the updated conf
```
sudo nginx -s reload
```
- Try visiting the HTTP server via browser first. For ex:
```
http://************/live/video.m3u8
```
- If you get 404 error, you've not followed the above steps properly. However you will get # 403 Forbidden error. This is because nginx runs as www-data user. We shall change it to ubuntu user as a quick hack.
- Open `/etc/nginx/nginx.conf` and change user from `www-data` to `ubuntu`
```bash
user ubuntu;
```
- Restart nginx
```bash
sudo service nginx restart
```
- Ensure ffmpeg is running using the command from previous section
- Now you can play the ffmpeg output via Akamai HLS player
- Visit http://players.akamai.com/players/hlsjs
- Ensure the above URL is http and not https

## HAProxy Setup
- Create a droplet as mentioned above
- Install HAProxy
```bash
sudo apt -y install haproxy
```
Append the following in `/etc/haproxy/haproxy.cfg`'. Change `************` to your Droplet IP Address.

```bash
frontend livestream
  bind *:80
  mode http
  use_backend server_a if { path /live } || { path_beg /live/ }

backend server_a
  server server1 ************:80 check
```

Run the following command as well
```bash
echo "ENABLED=1" | sudo tee -a /etc/default/haproxy
```

Restart HAProxy
```bash
sudo service haproxy restart
```

Visit Akamai HLS Player using the HAProxy IP Address. It should play.

## Cloudfront Setup
- Add a domain to HAProxy IP Address using route53 (Or your nameserver)
- Visit Akamai HLS Player using the HAProxy Domain URL. It should play.
- Add the proxy domain as an origin to a cloudfront distribution
- Try playing the HLS via Cloudfront distribution URL
