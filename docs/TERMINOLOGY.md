# TERMINOLOGY

## Codec

#### Name meaning :

- The term "**codec**" is a combination of the words "coder" and "decoder."

#### What is Codec ?

- codec is a device or software that is used to compress(**encoding**) and decompress(**decoding**) digital audio or video data.

#### Why we need Codec ?

- When audio or video data is recorded or transmitted, it can take up a large amount of storage or bandwidth. A codec is used to compress this data into a smaller, more manageable size for storage or transmission. When the data is played back or received, the codec decompresses it to its original form.

#### What is encoding ?

- The process of converting **analog signals** into **digital signals**
- To store some audio in computer/device it should be in 0s and 1s. The process of converting analog audio to 0s and 1s is called encoding.

#### What is decoding ?

- The process of converting **digital signals** into **analog signals**
- Humans can't hear 0s and 1s. To make these digital signals audible, we have to convert them into analog signals. This process is called decoding.

#### What is Analog signal ?

- An analog signal is a continuous signal that can take on any value within a certain range. It is typically used to transmit information such as sound or video, as well as to control systems such as mechanical systems
- Analog signals are typically used to represent information such as sound or video, which are also continuous in nature. For example, sound waves can have any amplitude (loudness) and frequency (pitch), and video signals can have any brightness and color.
- The continuous nature of analog signals allows for a more accurate representation of the original information.

#### What is PCM (Pulse Code Modulation) ?

- PCM  is a method of digitally representing analog audio signals. It works by taking samples of the analog signal at regular intervals and encoding the amplitude of each sample as a binary number. This allows the analog signal to be stored and transmitted digitally, which is useful for applications such as digital audio recording and streaming

#### What is Digital signal ?

- A digital signal is a discrete signal that can take on only a limited number of values, typically represented by a series of **zeroes and ones**. These values are used to represent information such as audio, video, and control data
- Digital signals can be transmitted over long distances without degrading the signal quality, unlike analog signals which can be affected by noise and distortion. They can also be easily stored, edited and processed using digital signal processing techniques

#### What are these zeroes and ones ?

- #####  Memory cell

#### What is Memory cell ?

- A memory cell is a basic element of a computer's memory system. It is a small unit of storage that can hold one unit of information, such as a single bit (**binary digit**(zeroes and ones)) of data. Memory cells are typically grouped together to form memory modules or chips, which can store larger amounts of data
- The most common memory cell architecture is MOS memory, which consists of metal–oxide–semiconductor (MOS) memory cells.

#### Where binary digits come from ?

- Binary Values must be set to store a logic 1 (high voltage level) and reset to store a logic 0 (low voltage level). Its value is maintained/stored until it is changed by the set/reset process

- This process takes place in **transistor**



 #### What is a transistor ?

- A transistor is a semiconductor device that can be used to  switch electronic signals. It consists of a small piece of semiconductor material, typically silicon, with impurities added to create regions of positive and negative charge, known as the source, drain, and the gate
- A  type of transistor is the field-effect transistor (FET), which has three terminals: the source, the drain, and the gate. The gate terminal controls the flow of current between the source and the drain terminals by applying an electric field.
- FETs are widely used in digital circuits, like memory cells, as a switch, to turn on(1) and off(0) the flow of current between the source and the drain terminals.
- An Average Android smartphones has around 7.8 billion transistors(which is equivalent to worlds population).
- Modern random-access memory (RAM) uses MOS field-effect transistors (MOSFETs) as **flip-flops**

#### What is flip-flops?

- Flip-flops are used to store and manipulate digital data in digital circuits. They are digital circuits that can store one bit of data and can change its state only when a specific set of input conditions are met
- Transistors are used as the basic switching element in flip-flops. They are used to control the flow of current through the flip-flop circuit
- Logic gates are used to control the flow of data through the flip-flop
  - Example
    - SR Flip-flop: The SR flip-flop uses two logic gates, an AND gate and an OR gate, to control the flow of data. The AND gate is used to set the flip-flop's output to 1, while the OR gate is used to reset the flip-flop's output to 0.

## Bitrate

#### What is bitrate ?

- Bitrate is a measure of the amount of data used to represent a certain amount of video or audio. It is typically measured in bits per second (bps) and is used to indicate the quality and file size of a video or audio file. A higher bitrate generally results in a higher quality video or audio, but also a larger file size

#### What is adaptive bitrate ?

- Adaptive Bitrate (ABR) refers to a method of streaming video or audio content over the internet in which the bitrate (i.e. the amount of data transmitted per second) is dynamically adjusted based on the viewer's internet connection and device capabilities. This allows the viewer to receive the highest quality stream possible without interruption due to buffering.

#### What is preset ?

- A preset is a predefined configuration of settings used by an encoder to compress and convert video or audio data. For example, in a video encoding application, a preset may be used to specify the desired resolution, bitrate, and frame rate of the output video. The preset can be selected by the user or automatically applied based on the specific requirements of the target device or platform

## RTMP(Real Time Messaging Protocol)

#### What is RTMP?

- RTMP (Real-Time Messaging Protocol) is a proprietary protocol developed by Macromedia which was later purchased Adobe
- It is the systems for streaming audio, video, and data over the internet, between a flash player and a server. It is commonly used for live streaming on platforms such as Facebook Live, YouTube Live, and Twitch.

## HLS(HTTP Live Streaming)

#### What is HLS ?

- HLS (HTTP Live Streaming) is a protocol for streaming audio and video over the internet. It was developed by Apple as a way to stream live and on-demand media to Apple devices, such as iPhones and iPads.

- HLS works by breaking up the video or audio into small segments, and then creating a playlist of those segments. Each segment is typically a few seconds long and is encoded at multiple bitrates. The client (e.g. an iPhone) requests the playlist, and then requests the segments as needed. The client also switches between the different bitrate versions of the segments depending on the available network conditions.

- However, HLS is not limited to just Apple devices and it is widely supported by many other platforms and devices, such as Android smartphones and tablets, smart TVs, and web browsers. HLS is an HTTP-based protocol, which makes it easy to deliver and access content on a wide range of devices. This is why it became a popular streaming protocol.

#### What is HLS list ?

- A HLS List is a collection of links to different video segments that can be played sequentially to stream a video. Each link in the list typically points to a small video file, called a "chunk," that contains a short segment of the video. The HLS list file contains the URLs for each of the video chunks, along with other metadata such as codec information and subtitles, and it is usually in the form of a **.m3u8** file. The player then requests and plays the chunks in sequence to stream the video.
- This allows the video to be streamed in small chunks rather than having to download the entire video file before it can be played. It also allows for adaptive streaming

#### What is HLS list size ?

- The HLS "list size" refers to the number of segments that are included in the playlist. This can be used to control the duration of the playback buffer, as well as the granularity of the seek operations.

#### What is playback buffer ?

- A playback buffer is a temporary storage area used to hold video or audio data that is being played back on a device. The data is typically streamed over a network and is stored in the buffer before it is played by the media player. The buffer serves several purposes, including:

  1. Smooth playback: By storing a small amount of video or audio data in the buffer before it is played, the media player can ensure a smooth playback experience, even if the network connection is not perfect.
  2. Network buffering: A playback buffer allows the media player to download and store a small amount of video or audio data before it is needed for playback. This can help ensure that there is always enough data available for smooth playback, even if the network connection is slow or unreliable.
  3. Seek operations: By having a large playback buffer, the player can quickly jump to different parts of the video or audio without waiting for the data to be downloaded.
  4. Adaptive streaming: For adaptive streaming, the buffer can be used to adapt the quality of the video to the changing network conditions.

  The size of the playback buffer is typically measured in seconds, and it can be adjusted depending on the desired playback experience, network conditions, and other factors.

#### What is granularity ?

- Granularity refers to the level of detail or the level of fineness of a measurement or division. In the context of a playback buffer, granularity refers to how fine or coarse the buffer can be divided.
- For example, if the granularity of the buffer is low, it means that the buffer can be divided into large segments, making it less precise. This could make it difficult to jump to specific parts of the video or audio without experiencing a delay.
- On the other hand, if the granularity of the buffer is high, it means that the buffer can be divided into smaller segments, making it more precise. This allows for more accurate seeking, and makes it easier to jump to specific parts of the video or audio without experiencing a delay.

#### What is metadata in HLS ?

- In HLS, metadata refers to additional information included in the HLS list file that describes the video content and its characteristics. Some examples of metadata in HLS include:

  - Codec information: This includes details about the codecs used to compress the video and audio, such as the video codec (e.g. H.264), audio codec (e.g. AAC), and container format (e.g. MP4).
  - Resolution and bitrate: Information about the resolution and bitrate of the video chunks, allowing the player to adjust the video quality based on the viewer's internet connection.
  - Subtitles: Information about subtitles tracks that are available, including the language, format, and the URLs to the subtitle files.
  - Closed captions: Information about closed captions tracks that are available, including the language, format, and the URLs to the closed captions files.

  This metadata is used by the player to determine how to play the video content and to provide the viewer with options for adjusting the video quality and selecting subtitles/closed captions.

- Basic Tags in HLS are

  - EXTM3U - Indicates that the file is an Extended M3U Playlist file.This must be the first line of playlist.

    - EXT-X-VERSION - Version of playlist file

    - Media Segment Tags - URI of media segments

    - EXTINF - Duration of media segment


## Frames

#### What is Frame? What are its types?

- In video compression, a frame refers to a single image in a sequence of images that make up a video. A frame is a single still image that is captured and displayed for a brief moment in time. When these frames are played back in succession, they create the illusion of motion.

  There are several different types of frames in video compression, including:

  - I-frames (Intra-frames): I-frames are key frames that contain all the information needed to decode the video. They are compressed using a technique called intra-frame compression, which compresses each frame individually. I-frames are used as reference frames for other frames in the video stream.
  - P-frames (Predicted frames): P-frames are inter-frames that contain information about the difference between the current frame and the preceding I-frame or P-frame. They are compressed using a technique called inter-frame compression, which takes advantage of the similarities between consecutive frames.
  - B-frames (Bidirectional frames): B-frames are also inter-frames that contain information about the difference between the current frame and both the preceding and succeeding I-frame or P-frame. They are also compressed using inter-frame compression.

#### What is IDR frame ?

- Instantaneous Decoder Refresh Frame
- IDR frame is a special type of I frame and it specifies that no frame after this IDR frame can reference any frame before it.
- So when video has IDR frame then from that frame to next IDR frame it will maintain buffer and once next IDR frame is reached buffer will be cleared

#### What is IDR Interval ?

- IDR interval is the interval between IDR frames, lesser the interval video will buffer less, but video size will be higher.

#### What is scenecut?

- Position in which one scene is replace by another

#### What is Presentation Timestamp ?

- Presentation Timestamp (PTS) is a timestamp used in digital video and audio systems to indicate when a specific frame or audio sample should be presented to the viewer or listener. It is used to ensure **synchronization of video and audio**, and to determine when to display or play a specific frame

## Transmux

#### Name meaning :

- The name "transmuxing" is a combination of the words "**transcode**" and "**multiplexing**".

#### What is transcoding ?

- Transcoding is the process of converting a digital video or audio file from one format to another. This is typically done to make the content compatible with a specific device or platform, or to reduce the file size while maintaining a certain level of quality. Transcoding can also involve changing the resolution, bitrate, or other parameters of the video or audio. The result of the process is a new file that can be played on the desired device or platform.

#### What is multiplexing ?

- Multiplexing, also known as muxing, is the process of combining multiple audio, video, or data streams into a single stream. An example of multiplexing can be seen in digital video and audio streaming.
- For example, imagine a live sports event that is being streamed over the internet. The event is being shot with multiple cameras and each camera is recording a separate video stream. The audio is also being recorded separately. In order to transmit the event over the internet, the multiple video and audio streams need to be combined into a single stream.

#### What is Transmuxing ?


- Transmuxing, also known as "transcoding multiplexing" or "repackaging," is the process of converting a video or audio stream from one container format to another, without changing the codecs of the video or audio.
- In other words, it is the process of changing the "wrapper" of a media file, like changing the file extension or container, without changing the actual video or audio data inside the file. Transmuxing keeps the codecs and the quality of the original stream, but it allows the stream to be played on different devices or platforms that support different container formats.
- Transmuxing is faster than transcoding
- For example, a video file that is in MP4 container format can be transmuxed to a MKV container format, and the resulting file will still have the same video and audio codecs but in a different container format.
- The main difference between transcoding and transmuxing is that transcoding involves changing the codecs or the bitrate of the video or audio, while transmuxing does not change the codecs or bitrate. Transcoding is typically used when the original video or audio file is not compatible with a specific device or platform, while transmuxing is used when the original video or audio file is compatible with the device or platform but the container format is not.

## H264

#### Name meaning ?

- The name H.264 is derived from the ITU-T (International Telecommunications Union - Telecommunications Standardization Sector) recommendation H.264, which is the official name for the standard. The "H" stands for "high", and the "264" refers to the 64-bit length of a "**macroblock**" in the video compression process. It is also known as MPEG-4 Part 10, or MPEG-4 AVC (Advanced Video Coding)

#### What is macroblock ?

- A macroblock is a block of pixels in a video image that is used in video compression techniques such as H.264. It is a group of pixels that are processed together to reduce the amount of data required to represent the image. Macroblocks are typically square, and are usually made up of 16x16 or 8x8 pixels.

  When a video is compressed, the encoder examines each macroblock to determine if it is similar to neighboring macroblocks. If it is, the encoder will only store the difference between the macroblock and its neighbors, rather than storing the entire macroblock. This process is called prediction and it is used to reduce the amount of data required to represent the image.

#### What is H264?

- H.264 is a video compression standard that is widely used for the recording, compression, and distribution of high-definition video. It is also known as MPEG-4 Part 10, or MPEG-4 AVC (Advanced Video Coding). H.264 is known for its high compression efficiency, allowing for high-quality video to be compressed to relatively low bitrates. It is also widely supported by a variety of hardware and software video playback devices, making it a popular choice for video streaming and other applications that require high-quality video with low bandwidth requirements.

#### What is x264?

- x264 is an open-source software library for encoding video streams into the H.264/MPEG-4 AVC format. It is one of the most widely used codecs for video compression and is known for its high efficiency and good quality. x264 is used in a wide range of applications, including video conferencing, streaming media, and video on demand.

## Encoder

#### What is encoder ?

- An encoder is a device or software that converts data from one format to another, typically for the purpose of compression or data transmission optimization.

####  Why encoder ?

- Encoder reduces the amount of bandwidth needed to transmit or store the video

#### What is bandwidth?

- Bandwidth refers to the amount of data that can be transmitted over a network or internet connection within a specific time period. It is often measured in bits per second (bps) or bytes per second. A higher bandwidth means more data can be transferred at once, resulting in faster internet speeds and smoother streaming or downloading of large files.
- For example, if you have a bandwidth of 100Mbps (megabits per second), you can download a 1GB (gigabyte) movie in about 8 minutes, while if you have a bandwidth of only 10Mbps, it could take up to 80 minutes to download the same movie.
- Another real-time example of bandwidth usage can be seen in streaming videos online. If you have a high-bandwidth internet connection, you will be able to stream videos in high definition without buffering or interruption. On the other hand, if you have a low-bandwidth internet connection, you may experience buffering or low-quality videos.

#### Is encoding lossless or lossy? Why?

- Encoding can be either lossless or lossy. Lossless encoding is a method of compression that preserves all original data and results in a file that is identical to the original when decompressed.
- Lossy encoding, on the other hand, discards some of the data in order to reduce file size. The reason for this is that some data may be deemed less important or less noticeable to the human eye or ear, so it can be removed without greatly impacting the overall quality of the file. Lossy compression is used to make the file smaller but the quality of the compressed file will not be as good as the original one.
- In case of video, hevec((High Efficiency Video Coding), also known as H.264) encoding can be used to get high lossless compression

## Decoder

#### What is decoder ?

- A decoder is a device or software that converts encoded data back into its original format for viewing or processing

#### Why decoder ?

- It is used to restore or interpret the encoded information in a way that it can be understood and utilized by the end user.

## Sampling

#### What is sampling ?

- Sampling is the process of taking regular measurements of a continuous signal to convert it into a discrete representation.

#### What is sample rate ?

- The number of samples taken per second is called the sample rate, and is measured in hertz (Hz). The standard sample rate for audio CDs is 44.1 kHz, which means that 44,100 samples are taken per second.

#### What is down sampling ?

- Downsampling is the process of reducing the sample rate of a digital audio signal. This is done by discarding some of the samples, effectively reducing the resolution of the signal. Downsampling can be used to reduce the file size of an audio file or to make it compatible with a device or software that does not support a high sample rate. However, downsampling also reduces the quality of the audio, as high-frequency information is lost.
- In image processing, downsampling is the process of reducing the resolution of an image by removing pixels. This can be done by averaging groups of pixels together, or by discarding pixels altogether. Downsampling can be used to reduce the file size of an image or to make it compatible with a device or software that does not support a high resolution. However, downsampling also reduces the quality of the image, as detailed information is lost.
- It's important to mention that, the process of downsampling is not reversible and the original information can't be recovered.

## Scaling

#### What is scaling ?

- Scaling is the process of changing the size of an image, video or digital signal. It can be used to increase or decrease the size of an image or video. Scaling can be performed using different algorithms, each with its own advantages and disadvantages. The most common algorithms are:

  1. Nearest-neighbor: This is the simplest and fastest algorithm, but it can produce jagged edges and a lot of distortion in the image.
  2. Bilinear: This algorithm produces a better quality image than nearest-neighbor, but it is slower. It calculates the color of each pixel by averaging the colors of the pixels that would be covered by the image if it were enlarged.
  3. Bicubic: This algorithm produces the best quality image, but it is also the slowest. It calculates the color of each pixel by averaging the colors of the pixels that would be covered by the image if it were enlarged, but it also takes into account the color of the pixels around them.
  4. Lanczos: This algorithm is similar to bicubic, but it uses a different function to calculate the color of each pixel. It's slower than bicubic but it produce better quality image.

  The choice of algorithm depends on the application and the desired trade-off between image quality and processing time.

#### What is scaler ?

- A scaler is a device or algorithm that is used to change the resolution of an image or video. This can include resizing the image or video to a different size, or converting the image or video from one aspect ratio to another.
- A scaler is often used in devices such as televisions, monitors, and projectors to adjust the resolution of the incoming video signal to match the native resolution of the display.

#### What is sws flags ?

- SWS stands for "Scaling Wrapper SWS", which is a software library used in FFmpeg and other media processing tools. It provides implementations of various image scaling and color space conversion algorithms.

- In FFmpeg, the `-sws_flags` option is used to set the scaling algorithm and various other options for the SWS library
- For example, to use the fast bilinear algorithm and disable the source image's chroma when scaling, you would use the following command :

```python

ffmpeg -i input.mp4 -vf scale=640:360 -sws_flags fast_bilinear-chroma 0 output.mp4

```

- By default Bilinear will be used
