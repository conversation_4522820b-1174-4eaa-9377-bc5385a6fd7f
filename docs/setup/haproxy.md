## New Server Setup
### Setup steps

- Create a new EC2 instance. Currently we `r5a.large` type instance in AWS

```bash
sudo apt-get install --no-install-recommends software-properties-common
sudo apt install haproxy
```

- To configure HAProxy
- Open `vim /etc/haproxy/haproxy.cfg`. Add the following at end of the file
```bash
frontend livestream
        bind *:80
        mode http
```
- Now execute the following command to enable HAProxy
```bash
echo "ENABLED=1" | sudo tee -a /etc/default/haproxy
```

- Now restart HAProxy
```bash
sudo service haproxy restart
```

## Enable API Support
HAProxy by default don't have API support. We need to install and configure Dataplane API for that.

- Download HAProxy Dataplane API
```bash
wget https://github.com/haproxytech/dataplaneapi/releases/download/v2.7.5/dataplaneapi_2.7.5_Linux_i386.tar.gz
```

- Unzip the file and enable permissions
```bash
tar -zxvf <previously downloaded file>
chmod +x build/dataplaneapi
```
- Copy to bin dir
```bash
sudo cp build/dataplaneapi /usr/local/bin/
```

- Create HAproxy Dataplane config by new file named `/etc/haproxy/dataplaneapi.hcl`
- Note: The password we used for production is `Ht79!^w4@$sh` We have to base64 encode the username:password for API. In our case, `YWRtaW46SHQ3OSFedzRAJHNo`

```bash
dataplaneapi {
  host = "0.0.0.0"
  port = 5555

  user "admin" {
    insecure = true
    password = "welcome1$"  # Replace it with your password
  }

  transaction {
    transaction_dir = "/tmp/haproxy"
  }
}

haproxy {
  config_file = "/etc/haproxy/haproxy.cfg"
  haproxy_bin = "/usr/sbin/haproxy"

  reload {
    reload_cmd  = "service haproxy reload"
    restart_cmd = "service haproxy restart"
    reload_delay = "5"
  }
}
```
- Run API with Haproxy Process Manager
- Open vim /etc/haproxy/haproxy.cfg
```bash
global
    master-worker

program api
    command dataplaneapi -f /etc/haproxy/dataplaneapi.hcl
    no option start-on-reload
```

- Now restart the HAProxy
```bash
sudo systemctl restart haproxy
sudo systemctl status haproxy
```
- You can test the setup by going to http://<proxy_server_ip_address>:5555/v2/info




### Basic HAProxy config
- Basic config for haproxy is
```
# _md5hash=a192222583e0252aa2eca41f631aa9a4
# _version=14
# Dataplaneapi managed File
# changing file directly can cause a conflict if dataplaneapi is running

frontend mysite
  bind *:80
  use_backend server_a if { path /live } || { path_beg /live/ }

backend server_a
  server server1 ************:80 check maxconn 30
```
- In the above config, backend refers to the live_stream server detail. Frontend refers to the server which should be used when user enters specific path.
- If user goes to [http://<ha_proxy_server_ip>/live](http://<ha_proxy_server_ip>/live) then it will access the ************:80/live



#### How tpstreams live is connected with HAProxy?
- Whenever live stream is created, once EC2 instance is spawned we will get a callback.
- We will add path for that live stream and server ip address to haproxy.cfg
	- Lets say asset id is "8r8xzyAQb82", organiation uuid is "grrmgy" and then livestream server ip is "*************".
	- Then the path we will assign for that live stream is `/live/grrmgy/8r8xzyAQb82`
	- So if user goes to http://live.tpstreams.com//live/grrmgy/8r8xzyAQb82/video.m3u8 then they can see the video
- See the below sample code for how the above example is translated in haproxy config
- Go to `sudo vim /etc/haproxy/haproxy.cfg`

```
...


frontend livestream from unnamed_defaults_1
  mode http
  bind *:80
  use_backend backend_grrmgy_8r8xzyAQb82 if { path_beg /live/grrmgy/8r8xzyAQb82 }


backend backend_grrmgy_8r8xzyAQb82
  server server1 *************:80

...
```

To check if HAProxy is running 

`sudo systemctl status haproxy`

To check if HAProxy dataplane API is running or not. Run the following in HAProxy server
```bash
curl -X GET --user admin http://localhost:5555/v2/info
```
It will ask for password. Provide the password you have specified in `/etc/haproxy/dataplaneapi.hcl`


#### FAQs
- What to do if HAProxy server is not working?
  - Search if the asset is present in both frontend and backend part of HAProxy config `vim /etc/haproxy/haproxy.cfg`. If it is not there
  then you can assign proxy for livestream from shel(`domain/haproxy.py` has method).
  If adding from shell is not working, you can manually add by adding similar code from previous section.
- How to quickly setup new HAProxy server?
  - We can a snapshot for HAProxy in AMI section in AWS EC2 (ap-south1). Spawn a new instance(make sure to choose security group as `livestream`).
  - Now check if HAProxy is working by `http://<ip>:5555/v2/info`
  - If it works then unassign the elastic ip of old HAProxy and assign it to this new one

