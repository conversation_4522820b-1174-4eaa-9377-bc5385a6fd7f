# Architecture
TPStreams has three main functionalities
- Transcoding
- DRM
- Live Stream

Code structure wise there are few major components
- The main django web app resides in https://github.com/testpress/streams
- The background celery server also resides in https://github.com/testpress/streams
- Liveflow is the live stream web application which resides in https://github.com/testpress/live-flow
- Livechat is the chat functionality which resides in https://github.com/testpress/live-chat
- Transcoding and DRM functionality is provided by https://github.com/testpress/lumberjack
- For streaming client we will be using OBS
- For rtmp server we are using SRS which resides in https://github.com/ossrs/srs
- For LLHLS we are using gohlslib which resides in https://github.com/testpress/gohlslib/
- Subtitle generation has to be extracted (Todo)
- Download upload

## Jargons
### Transcoding
- Coder Decoder
- Code here means digital signal (1 and 0). So Coder Decoder means analog to digital and viceversa
- There are standard specifications for this conversion which are called codecs. They also help in compressing the raw analog signal.
- Why we need codec? - It is essential for storage and transmission purposes, as raw, uncompressed data can be very large.
- We are using H-264 for video and AAC for audio
- **Encoding**: The coder part of a codec takes an original, uncompressed audio or video signal and transforms it into a smaller, more manageable digital format.
- **Decoding**: The decoder takes the compressed data stream and converts it back into its original format, enabling playback or further processing
- **Transcoding**: This involves converting a media file from one format to another, which often includes changing the codec used for compressing the video and audio data

### Transmuxing
- Containers for coded files
- typically cannot play a raw H.264 encoded output directly. H.264 is a video codec, which specifies how the video data is compressed. It defines how the video information is encoded for storage or transmission, but it doesn't include additional information needed for playback, such as:
- **Container:** A container format like MP4, MKV, or AVI acts as a wrapper that holds the encoded video data (H.264 in your case) along with other essential elements like:
    - Audio track (if there's audio)
    - Timing information to display the video at the correct speed
    - Metadata (information about the video like title, artist, etc.)
- **Player support:** Most media players are designed to understand container formats and the codecs they contain. A player that can understand H.264 might not be able to handle a raw stream without additional information on how to interpret it.
- **Put it in a container:** You can use tools like ffmpeg to put your H.264 video data into a playable container format like MP4. This process is called muxing.
- These containers are standard specifications
- Examples of container format - **MP4 (MPEG-4 Part 14)**, **MOV (QuickTime File Format)**, **AVI (Audio Video Interleaved)**, **MKV (Matroska)**, **FLV (Flash Video)**, **3GP (3rd Generation Partnership Project)**
- We are using MP4 and MPEG-TS container formats
- **MP4:** Designed for storing and playing back multimedia files. It's like a universal container that can hold video, audio, subtitles, and other data.
- **MPEG-TS:** Optimized for streaming applications, particularly live streaming over networks like cable, satellite, or the internet. It breaks down the media into smaller chunks for efficient transmission and easier recovery from errors.
- Transmuxing, also referred to as repackaging or packetizing, is the process of changing the container format of a video or audio file without altering the underlying content itself
### ffmpeg
- This is a command line tool
- It is used to convert videos and audio between different formats. Basically we use ffmpeg for transcoding and transmuxing.
- This involves converting a media file from one format to another, which often includes changing the codec used for compressing the video and audio data.
- For example, you could use FFmpeg to transcode a high-resolution MKV file to a smaller MP4 with a more web-friendly codec. During transcoding, FFmpeg decodes the original file, re-encodes it using the new format and codec specifications, and then creates a new output file.
### Bitrate
Bitrate, in the context of digital media, refers to the amount of data used to represent a unit of time in a video or audio recording. It essentially determines the quality and file size of the media. Here's a breakdown:
- **Measurement:** Bitrate is measured in bits per second (bps). Common prefixes like kilo (kbps), mega (Mbps), and giga (Gbps) are used for larger bitrates.
- **Data Used:** It specifies how many bits are used to encode each second of video or audio. A higher bitrate means more data is used per second, resulting in potentially better quality but also a larger file size
- For 240p, 

### Shaka Packager
- This is a command line tool
- It is used to add DRM protection for transcoded & transmuxed videos
- It expects input as UDP or file or file pointer
- It supports both widevine and fairplay
### Diagram
![Live Stream Architecture drawio (2)](https://github.com/testpress/streams/assets/328112/d08d91f2-3646-495e-bd25-3eb15207b9b1)

## Live Streaming

### State Diagram
![Live stream state diagram](https://github.com/testpress/streams/assets/41418580/cff0a790-9c7f-4b70-9dd6-34c16b156081)


For live streaming we have three cases:

# Normal RTMP to HLS Live Streaming (Without DRM)
- This is the basic live streaming
- 15 seconds latency
- For this we will stream using OBS. OBS will push to an RTMP server address
- RTMP is by adobe. So OBS will push the stream as FLV.
- RTMP, the Real-Time Messaging Protocol, was originally developed by **Macromedia**. Macromedia was acquired by Adobe.
- **FLV (Flash Video)**: This is a common container format historically used for RTMP streaming, especially with Adobe Flash Player
- This will be received by SRS which acts as the RTMP server
- SRS will push it to ffmpeg to convert the FLV for two purposes
	- Recording - Will change FLV to MP4 without modifying the codec.
	- Streaming
		- to h264 for video codec to provide for TS for different resolution
		- to aac for audio codec
		- ffmpeg will also transmux and combine different resolutions to create HLS m3u8 index file
		- HLS m3u8 will have all the paths for different resolutions ts files
- ffmpeg will store output to hard disk
- This hard disk will be pointed to by HAProxy
- And CDN will be mapped to HAProxy
- User will stream from CDN
### Normal RTMP to DRM Live Streaming
- Same as above (Normal RTMP to HLS Live Streaming)
- Instead of ffmpeg storing output to hard disk, it will give output as UDP which we will give to shaka packager as input
- We will have different resolutions in different UDP ports. Right now we increase the port number by one for each resolution.
- For each resolution we will have two UDP ports
- There will be only one ffmpeg process to push to these UDP ports
- There will be two shaka packager process. One for widevine and one for fairplay.
- Shaka packager will encrypt to DRM using widevine and fairplay
- Stores output to harddisk and then the same process as above
- Assuming we are supporting 3 resolutions (240p, 480p, 720p) we will have 6 UDP ports. 1 ffmpeg process, 2 shaka packager processes
### Normal RTMP to LLHLS Live Streaming (Without DRM)
- This is now our next level of decreasing latency
- Right now 7 seconds latency
- Same as above as we do in DRM Live Streaming
- Instead of shaka packager, we will have gohlslib muxer which will break the transcoded video to LLHLS muxing specification chunks
- Here however we will have only 1 UDP port for each resolution
- Assuming we are supporting 3 resolutions (240p, 480p, 720p) we will have 3 UDP ports. 1 ffmpeg process, 1 gohlslib muxer process

## Recording
- Ideally we need to just upload the output of ffmpeg recording process to external storage (wasabi in our case). For this we can use simply rclone tool
- But AWS costs more in terms of egress cost if we upload from EC2 to outside.
- Data transferred between Amazon EC2, Amazon RDS, Amazon Redshift, Amazon ElastiCache instances, and Elastic Network Interfaces in the same Availability Zone is free.
- Data transferred "in" to and "out" from Amazon Classic and Application Elastic Load Balancers using private IP addresses, between EC2 instances and the load balancer in the same AWS VPC is free.
- Basically you get free of cost only on following conditions
	- EC2 to AWS Cloudfront is free
	- EC2 to EC2 is free if both EC2 are in same availability zone (AZ) and you are using the private IP address of the source EC2
	- Anything else pay for Jeff Bezos alimony
- So to avoid this we are doing a hack
- We use hetzner instance to download the recorded video using Amazon Cloudfront URL
- Downloading from Cloudfront is comparitively cheaper because we are having a private pricing for cloudfront at reduced cost
- Then we upload the video to wasabi using hetzner because there is no egress cost in hetzner till 10 TB
- This is done by a celery worker in hetzner
