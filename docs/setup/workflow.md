# Workflow
This document will build on top of architecture document to help you explain how everything works together.

Following are the major parts
- Django Web Application
- Lumberjack for Transcoding
- Liveflow for live streaming
- Livechat for chat
- Celery for Background Task

## Questions to ponder?
- If I start a live stream from the web ui, and my AWS account is blocked because of non payment of bills, will i know in the web UI?
	- The boto api will fail and we will know
- Assume that because of some network issue, we didn't receive the callback from the live stream server. Can I poll to check if the live stream server is ready?
	- We don't store the status. However we do have an API to poll to check whether server is ready. Will return 200 if ready. (simple Hello API)
## Django Web Application
This is our front facing web application. This has the following responsibilities
- Authentication and User management
- User Interface for uploading video files and logic to integrate with 3rd party block storage (Wasabi in our case)
- User interface for organizing uploaded files in folders
- Managing Roles & Permissions for Access Control (TODO)
- Displaying detailed information of the organizations contents (Video & Live Stream)
	- This includes providing embed code
	- Asset ID for API usage
- User interface for CRUD operation of assets
- API for third party integrations
- Analytics usage tracking by uploading the info to wasabi and rendering the same
- The code resides in https://github.com/testpress/streams
## Liveflow
This is our live streaming application. This does the following on start
- Launch SRS (launched by AWS EC2 system service part of snapshot)
	- SRS has a conf file called srs.conf (Should be part of github. But not right now)
	- We inform srs to call OnPublishCallbackView when OBS has started streaming
	- Todo - Write document on how to start srs locally and integrate with liveflow
- Launch ffmpeg (launched by Django liveflow app OnPublishCallbackView - app/views.py)
- Launch packager (launched by Django liveflow app OnPublishCallbackView - app/views.py)
- Launch gohlslib (launched by Django liveflow app OnPublishCallbackView - app/views.py)
- Need to get callbacks or API
- Todo: Store the status of each of the launches. Expose API to get info about the same
- Our design should answer the following questions
	- What time did ffmpeg/shaka/gohls/srs process started?
	- What are the pids of these processes?
	- Are these process currently running?
	- Did any event happened for any of these processes? Like SIGKILL, SIGHUP etc?
### Questions to ponder for Liveflow?
- If I ssh and kill ffmpeg, what will happen?
- If I ssh and kill srs, what will happen?
- If I ssh and kill shaka packager what will happen?
- If I ssh and kill gohlslib what will happen?
- Web App should be aware of the above


