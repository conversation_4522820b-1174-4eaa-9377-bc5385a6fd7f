# DRM as service

## How DRM works?
- We have a video which needs to be protected.
- We transcode it into fragmented MP4.
- We finally package it with our encryption keys
- When playing back we request a license to decrypt the content

## Overview
DRM Service consists of two parts
- Encrypting the content
- Getting license to playback the content

DRM is provided by two vendors right now
- Widevine by Google (For Windows, Linux and Android Devices)
- Fairplay by Apple (For Mac and iOS Devices)


## Prerequisites
- AES Signing Key and AES Signing IV will be provided to client who wants to use our DRM as service.
- These signing key and IV can be obtained from our Organization model.

## Encryption Service
- The steps to encrypt contents differs based on the underlying technology used.
- So widevine and fairplay have different entry paths and different parameters.

### Shaka Packager
- Usually clients use Shaka Packager for encrypting their contents
- Shaka Packager has seamless integration for widevine.
- However for fairplay, we take the raw key method where the encryption keys are provided to <PERSON>hak<PERSON> Packager directly.

### Widevine Encryption Key API
- `/api/v1/{{org_code}}/widevine_key/`
- This API can be used to get widevine encryption key
- Usually, the client won't be calling this API. They would provide this URL along with <PERSON>ES signing key and IV to Shaka Packager
- Shaka Packager will automatically create the request and send it to our server.
- Content ID is a unique ID which would refer to that content.

```bash
  packager \
  in=https://static.testpress.in/BigBuckBunny.mp4,stream=audio,output=audio.mp4 \
  in=https://static.testpress.in/BigBuckBunny.mp4,stream=video,output=h264_360p.mp4 \
  --enable_widevine_encryption \
  --key_server_url https://app.tpstreams.com/api/v1/<org_code>/widevine_key/ \
  --content_id <content_id> \
  --signer testpress \
  --aes_signing_key <WIDEWINE_AES_KEY> \
  --aes_signing_iv <WIDEWINE_IV> \
  --mpd_output h264.mpd
```

### Fairplay Encryption key API

- `/api/v1/{{org_code}}/fairplay_key/`
- This API can be used to obtain fairplay encryption key
- Client needs to provide a unique identifier(will be referred as `content_id`) which identifies their video. This should be a uuid4 hex  string.

`content_id` should be added to json and converted to base64

```python
import json
import base64

data = {"content_id": "cf6c30dd50c1492e82344ea3e139da1d"}
data = json.dumps(data, separators=(',', ':'))
result = base64.urlsafe_b64encode(data.encode())
# Result is eyJjb250ZW50X2lkIjoiY2Y2YzMwZGQ1MGMxNDkyZTgyMzQ0ZWEzZTEzOWRhMWQifQ==
```

This `result` should be sent as `request` value in the following structure as POST body.

```json
{
  "request": "eyJjb250ZW50X2lkIjoiY2Y2YzMwZGQ1MGMxNDkyZTgyMzQ0ZWEzZTEzOWRhMWQifQ==",
  "signature": "NsBcxxchrA7tFw/O86SCM5hwiM6Np/+JnZgjlV0vRyo="
}
```

#### Signature calculation
The value for signature in the above POST data can be calculated as follows:

- Base64 encode the request data which is `{"content_id": "cf6c30dd50c1492e82344ea3e139da1d"}`
- Now calculate sha1 hash for the base64 encoded data
- Add padding to the hash as per `AES pkcs7`
- Now encrypt the above padded hash using AES CBC mode with AES signing key and AES signing IV provided(Check prerequisites)

Following is a sample code to calculate signature in python 3

```python
import base64
import binascii
import hashlib
import json


from Cryptodome.Cipher import AES
from Cryptodome.Util.Padding import pad

def generate_signature(data, key, iv):
    hash = hashlib.sha1(base64.b64encode(data.encode())).digest()
    cipher = AES.new(
        binascii.unhexlify(key),
        AES.MODE_CBC,
        binascii.unhexlify(iv),
    )
    padded_hash = pad(hash, AES.block_size, style="pkcs7")
    signature = cipher.encrypt(padded_hash)
    return base64.b64encode(signature).decode()

key = "xxxx"  # AES Signing key
iv = "yyy"  # AES Signing iv
data = {
  "content_id": "cf6c30dd50c1492e82344ea3e139da1d"
}
data = json.dumps(data, separators=(',', ':'))
signature = generate_signature(data)
result = base64.urlsafe_b64encode(data.encode())

post_data = {
  "request": result,
  "signature": signature
}
```
The response of this API will be as follows:

```json
{
  "iv": "f70bce4094fd4612abac60d9809c5b0c",
  "key": "3ab60de900d64edf9cb25a76f81794e6",
  "uri": "skd://e5573f8bb8ac47ea839a65beae73263d",
}
```

## License Service
- This API is used to create temporary URLs that allows playback of DRM videos on device.

### Steps to generate license URL
Form a JSON string with the following details:

```json
{
  "content_id": <content_id>,
  "download": true/false,
  "drm_type": "widevine/fairplay"
}
```

Base64 encode the above data. Now generate signature for the encoded data. You will create a json like the following with the content_data value as base64 encoded data. For signature, use the process mentioned above to calculate the same.

```json
{
    "content_data": "eyJkb3dubG9hZCI6ZmFsc2UsImRybV90eXBlIjoid2lkZXZpbmUiLCJjb250ZW50X2lkIjoiYTM1NmFhZWYzMGFhNGNkODlmNzY2ZjVmNjUzN2FjMzMifQ==",
    "signature": "NsBcxxchrA7tFw/O86SCM5hwiM6Np/+JnZgjlV0vRyo="
}
```

 base64 encode the above data and append the result to the following url

 `api/v1/<org_code>/drm_license/?data=<encoded_data>`

Following is the sample code to generate in python 3:

```python
import base64
import binascii
import hashlib
import json


from Cryptodome.Cipher import AES
from Cryptodome.Util.Padding import pad

def generate_signature(data, key, iv):
    hash = hashlib.sha1(base64.b64encode(data.encode())).digest()
    cipher = AES.new(
        binascii.unhexlify(key),
        AES.MODE_CBC,
        binascii.unhexlify(iv),
    )
    padded_hash = pad(hash, AES.block_size, style="pkcs7")
    signature = cipher.encrypt(padded_hash)
    return base64.b64encode(signature).decode()

key = "xxxx"  # AES Signing key
iv = "yyy"  # AES Signing iv
org_code = "xxxx"

content_data = {"content_id": "cf6c30dd50c1492e82344ea3e139da1d", "download": "true", "drm_type": "widevine"}
content_data = json.dumps(content_data, separators=(',', ':'))
encoded_content_data = base64.urlsafe_b64encode(content_data.encode())
signature = generate_signature(encoded_content_data)
data = {"content_data": encoded_content_data, "signature": signature}
data = json.dumps(data, separators=(',', ':'))
encoded_data = base64.urlsafe_b64encode(data)

LICENSE_URL = f"https://app.tpstreams.com/api/v1/{org_code}/drm_license/?data={encoded_data}"
```
