## Common problems and their solutions

### Citus issues

#### Error
```
DETAIL:  Distributed relations cannot have UNIQUE, EXCLUDE, or PRIMARY KEY constraints that do not include the partition column (with an equality operator if EXCLUDE).
```
#### Reason
As the error states, if we have any unique field or primary field in a model then there should be constraint with that field on distribution_column ("organization_id" in our case)

If this error occurs as a result of using TenantOneField, then check if `TenantOneToOneField` is imported from `app/models/fields.py`.
