name: Run Tests
on:
  push:
    branches:
      - main
    paths:
      - 'app/**'
      - 'tests/**'
  pull_request:
    branches:
      - main
    paths:
      - 'app/**'
      - 'tests/**'
  workflow_dispatch:
jobs:
  Run-Tests:
    runs-on: self-hosted
    env:
      DATABASE_NAME: streams
      DATABASE_USER: streams
      DATABASE_USER_PASSWORD: streams
      DATABASE_URL: psql://streams:streams@127.0.0.1:5432/streams
      WASABI_ACCESS_KEY_ID: "xxx"
      WASABI_S3_REGION_CODE: "xxx"
      WASABI_SECRET_ACCESS_KEY: "xxx"
      AWS_ACCESS_KEY_ID: "xxx"
      AWS_S3_REGION_CODE: "xxx"
      AWS_SECRET_ACCESS_KEY: "xxx"
      AWS_STORAGE_BUCKET_NAME: "xxx"
      CELERY_BROKER_URL: "localhost:6379"
      CELERY_RESULT_BACKEND: "localhost:6379"
      CSRF_TRUSTED_ORIGINS: "http://localhost"
      DRM_SIGNER: "abc"
      AES_SIGNING_IV: "abcd316f4bd1236b688c6456af3a43525"
      AES_SIGNING_KEY: "95abcef76e456096192ff123d123e168355123c16dcb6876ff89aef312373935"
      CONTENT_KEY_URL: "https://example.com"
      VDO_CIPHER_API_KEY: "abcdeftelaVFBTGabcdeAsdfkIjxnjBIsHdlefesEef6oSONasfersLq"
      FAIRPLAY_CERTIFICATE_URL: "https://example.com"
      SECRET_KEY: "ml6%i^g(8&ey(vk^cn&8m2qomk80!xa(r0b3&ml(w6y!_dohqm"
      WIDEVINE_AES_KEY: "7cc93d7bb826dd1fbd81cdc78b23c25ace67dd40f8aaca3cfc3f10cfa38d223a"
      WIDEVINE_IV: "bc76511133ce755e4ecb3e82eba3c5d5"
      WIDEVINE_CONTENT_KEY_URL: "https://license.uat.widevine.com/cenc/getcontentkey/testpress"
      WIDEVINE_LICENSE_KEY_URL: "https://license.uat.widevine.com/cenc/getlicense/testpress"
      FAIRPLAY_PRIVATE_KEY: ""
      SENTRY_URL: ""
      LUMBERJACK_URL: "http://localhost"
      DIGITAL_OCEAN_API_KEY: "abc"
      PROXY_API_KEY: "abc"
      ANALYTICS_LOG_BUCKET: "log"

    services:
      postgres:
        image: citusdata/citus:11.3.0-alpine
        env:
          POSTGRES_DB: streams
          POSTGRES_USER: streams
          POSTGRES_PASSWORD: streams
        ports:
          - 5432:5432
        options:
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@0.11.0
        with:
          access_token: ${{ github.token }}

      - name: Check out repository code
        uses: actions/checkout@v3.5.2

      - name: Setup Python
        uses: actions/setup-python@v4.6.0
        with:
          python-version: '3.9'

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ env.pythonLocation }}-${{ hashFiles('**/requirements/*.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install Dependencies
        run: |
          pip install -r requirements/local.txt

      - name: Run Tests
        run: |
          python -Wd manage.py test
