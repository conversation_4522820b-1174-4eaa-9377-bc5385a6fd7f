name: Deploy (For Production)
on:
  workflow_dispatch:
    inputs:
      run_collectstatic:
        description: 'Run collectstatic?'
        required: true
        default: 'no'
        type: choice
        options:
          - 'yes'
          - 'no'
      restart_celery:
        description: 'Restart Celery?'
        required: true
        default: 'no'
        type: choice
        options:
          - 'yes'
          - 'no'

jobs:
  deploy:
    runs-on: self-hosted
    env:
      APPS: "********"
      ALL_HOSTS: "******** ********"
    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Setup SSH and known_hosts
      run: |
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh
        for IP in $ALL_HOSTS; do
          ssh-keyscan -H $IP >> ~/.ssh/known_hosts
        done
        chmod 644 ~/.ssh/known_hosts

    - name: SSH and git pull on managed nodes
      env:
        PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
      run: |
        echo "$PRIVATE_KEY" > private_key.pem
        chmod 600 private_key.pem
        for IP in $ALL_HOSTS; do
          ssh -i private_key.pem ubuntu@$IP "cd ~/workspace/tpstreams/streams && git pull"
        done
        rm -f private_key.pem

    - name: Check if collectstatic should run
      if: ${{ github.event.inputs.run_collectstatic == 'yes' }}
      env:
        PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
      run: |
        echo "$PRIVATE_KEY" > private_key.pem
        chmod 600 private_key.pem
        for IP in $APPS; do
          ssh -i private_key.pem ubuntu@$IP << 'EOF'
              cd ~/workspace/tpstreams/streams
              ~/workspace/tpstreams/bin/python ./manage.py tailwind build --settings=config.production
              ~/workspace/tpstreams/bin/python ./manage.py collectstatic --settings=config.production --noinput
        EOF
        done
        rm -f private_key.pem

    - name: Restart Gunicorn on managed nodes
      env:
        PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
        SUDO_PASSWORD: ${{ secrets.SUDO_PASSWORD }}
      run: |
        echo "$PRIVATE_KEY" > private_key.pem
        chmod 600 private_key.pem
        for IP in $APPS; do
          ssh -i private_key.pem ubuntu@$IP 'echo '"$SUDO_PASSWORD"' | sudo -S supervisorctl status gunicorn | sed "s/.*[pid ]\([0-9]\+\)\,.*/\\1/" | xargs kill -HUP'
        done
        rm -f private_key.pem

    - name: Restart Celery
      if: ${{ github.event.inputs.restart_celery == 'yes' }}
      env:
        PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
        SUDO_PASSWORD: ${{ secrets.SUDO_PASSWORD }}
      run: |
        echo "$PRIVATE_KEY" > private_key.pem
        chmod 600 private_key.pem
        ssh -i private_key.pem ubuntu@******** 'echo '"$SUDO_PASSWORD"' | sudo -S supervisorctl restart celeryd_webhook celeryd_purge_assets celeryd_migration_queue'
        ssh -i private_key.pem ubuntu@******** 'echo '"$SUDO_PASSWORD"' | sudo -S supervisorctl restart all'
        rm -f private_key.pem
