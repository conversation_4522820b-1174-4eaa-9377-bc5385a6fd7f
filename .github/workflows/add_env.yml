name: Add Env
on:
  workflow_dispatch:
    inputs:
      key:
        description: 'Environment variable key'
        required: true
      value:
        description: 'Environment variable value'
        required: true

jobs:
  deploy:
    runs-on: self-hosted
    env:
      ALL_HOSTS: "******** ******** *********"
    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Setup SSH and known_hosts
      run: |
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh
        for IP in $ALL_HOSTS; do
          ssh-keyscan -H $IP >> ~/.ssh/known_hosts
        done
        chmod 644 ~/.ssh/known_hosts

    - name: Add Env
      env:
        PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
      run: |
        echo "$PRIVATE_KEY" > private_key.pem
        chmod 600 private_key.pem
        ENV_KEY="${{ github.event.inputs.key }}"
        ENV_VALUE="${{ github.event.inputs.value }}"

        for IP in $ALL_HOSTS; do
          ssh -i private_key.pem ubuntu@$IP "bash -c 'cd ~/workspace/tpstreams/streams && ~/workspace/tpstreams/bin/python ./manage.py add_env --key \"$ENV_KEY\" --value \"$ENV_VALUE\" --settings=config.production'"
        done

        rm -f private_key.pem
