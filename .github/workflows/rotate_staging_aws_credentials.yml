name: Rotate Staging AWS Credentials

on:
  schedule:
    - cron: '0 2 * * *'
  workflow_dispatch: {}

jobs:
  rotate:
    name: Rotate Staging AWS Credentials
    runs-on: self-hosted
    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Setup SSH and known_hosts
      run: |
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh
        ssh-keyscan -H ********* >> ~/.ssh/known_hosts
        chmod 644 ~/.ssh/known_hosts

    - name: Setup SSH agent
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

    - name: Configure AWS CLI
      run: |
        aws configure set aws_access_key_id ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws configure set aws_secret_access_key ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws configure set region ap-south-1

    - name: Rotate AWS credentials
      id: rotate_credentials
      run: |
        # Set the target IAM user
        TARGET_USER="tpstreams-staging-apr-28"
        echo "Target IAM user: $TARGET_USER"

        # Create a new access key
        echo "Creating new access key..."
        NEW_KEY_RESPONSE=$(aws iam create-access-key --user-name $TARGET_USER)

        # Extract the new credentials
        NEW_ACCESS_KEY=$(echo $NEW_KEY_RESPONSE | jq -r '.AccessKey.AccessKeyId')
        NEW_SECRET_KEY=$(echo $NEW_KEY_RESPONSE | jq -r '.AccessKey.SecretAccessKey')

        # Verify that the credentials are not empty
        if [ -z "$NEW_ACCESS_KEY" ]; then
          echo "Error: Failed to create new AWS Access Key"
          exit 1
        fi

        if [ -z "$NEW_SECRET_KEY" ]; then
          echo "Error: Failed to create new AWS Secret Key"
          exit 1
        fi

        # Print the new credentials (partially masked for security)
        # Removed credential logging for security:
        echo "New Access Key: ${NEW_ACCESS_KEY:0:5}...${NEW_ACCESS_KEY: -5}"
        echo "New Secret Key: ${NEW_SECRET_KEY:0:5}...${NEW_SECRET_KEY: -5}"
        echo "::add-mask::${NEW_ACCESS_KEY}"
        echo "::add-mask::${NEW_SECRET_KEY}"

        # Store the new credentials as environment variables
        echo "NEW_ACCESS_KEY=$NEW_ACCESS_KEY" >> $GITHUB_ENV
        echo "NEW_SECRET_KEY=$NEW_SECRET_KEY" >> $GITHUB_ENV

        # Get the list of access keys for the user
        ACCESS_KEYS=$(aws iam list-access-keys --user-name $TARGET_USER --query 'AccessKeyMetadata[*].AccessKeyId' --output text)

        # Find the old access key (the one that's not the new one)
        for KEY in $ACCESS_KEYS; do
          if [ "$KEY" != "$NEW_ACCESS_KEY" ]; then
            OLD_ACCESS_KEY=$KEY
            break
          fi
        done

        # Store the old access key for later deletion
        echo "OLD_ACCESS_KEY=$OLD_ACCESS_KEY" >> $GITHUB_ENV
        # Removed credential logging for security:
        # echo "Old Access Key: ${OLD_ACCESS_KEY:0:5}...${OLD_ACCESS_KEY: -5}"

        echo "AWS credentials rotated successfully"

    - name: Update AWS credentials
      run: |
        # Create a temporary file for the SSH key
        echo "${{ secrets.SSH_PRIVATE_KEY }}" > private_key.pem
        chmod 600 private_key.pem

        # Verify that the credentials are not empty
        if [ -z "$NEW_ACCESS_KEY" ]; then
          echo "Error: AWS Access Key is empty"
          exit 1
        fi

        if [ -z "$NEW_SECRET_KEY" ]; then
          echo "Error: AWS Secret Key is empty"
          exit 1
        fi

        echo "Using newly rotated AWS credentials"

        # Update AWS credentials in production
        echo "Updating AWS Access Key..."
        ssh -i private_key.pem -o StrictHostKeyChecking=no ubuntu@********* "cd ~/workspace/tpstreams/streams && ~/workspace/tpstreams/bin/python ./manage.py add_env --key TPSTREAMS_AWS_ACCESS_KEY_ID --value '$NEW_ACCESS_KEY' --settings=config.production"

        echo "Updating AWS Secret Key..."
        ssh -i private_key.pem -o StrictHostKeyChecking=no ubuntu@********* "cd ~/workspace/tpstreams/streams && ~/workspace/tpstreams/bin/python ./manage.py add_env --key TPSTREAMS_AWS_SECRET_ACCESS_KEY --value '$NEW_SECRET_KEY' --settings=config.production"

        # Clean up
        rm -f private_key.pem

    - name: Delete old access key
      run: |
        # Set the target IAM user
        TARGET_USER="tpstreams-staging-apr-28"

        # Delete the old access key
        # Removed credential logging for security:
        # echo "Deleting old access key: ${OLD_ACCESS_KEY:0:5}...${OLD_ACCESS_KEY: -5}"
        echo "Deleting old access key..."
        aws iam delete-access-key --user-name $TARGET_USER --access-key-id $OLD_ACCESS_KEY
        echo "Old access key deleted successfully"

    - name: Restart Gunicorn
      env:
        PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
        SUDO_PASSWORD: ${{ secrets.STAGING_SUDO_PASSWORD }}
      run: |
        echo "$PRIVATE_KEY" > private_key.pem
        chmod 600 private_key.pem
        ssh -i private_key.pem ubuntu@********* 'echo '"$SUDO_PASSWORD"' | sudo -S supervisorctl status gunicorn | sed "s/.*[pid ]\([0-9]\+\)\,.*/\\1/" | xargs kill -HUP'
        rm -f private_key.pem
