name: Cleanup Old AWS Keys

on:
  schedule:
    - cron: '0 */4 * * *'  # Run every 4 hours
  workflow_dispatch: {}

jobs:
  cleanup:
    name: Cleanup Old AWS Keys
    runs-on: self-hosted

    steps:
      - uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
      - name: Check out repository code
        uses: actions/checkout@v4

      - name: Configure AWS Credentials
        id: creds
        uses: aws-actions/configure-aws-credentials@v4.1.0
        with:
          aws-region: ap-south-1
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Cleanup Old AWS Keys
        shell: bash
        run: |
          # Set the target IAM user
          TARGET_USER="tpstreams-apr-21"
          echo "Target IAM user: $TARGET_USER"

          # Get current time in seconds since epoch
          CURRENT_TIME=$(date +%s)
          # Calculate 20 hours ago in seconds
          TWENTY_HOURS_AGO=$((CURRENT_TIME - 20*3600))

          # Get all access keys for the user
          ACCESS_KEYS=$(aws iam list-access-keys --user-name $TARGET_USER --query 'AccessKeyMetadata[*].[AccessKeyId,CreateDate]' --output json)

          # Count total active keys
          TOTAL_KEYS=$(echo "$ACCESS_KEYS" | jq '. | length')
          echo "Total active keys: $TOTAL_KEYS"

          echo "Checking last used time for all access keys:"
          echo "$ACCESS_KEYS" | jq -r '.[] | @tsv' | while IFS=$'\t' read -r key_id create_date; do
            # Get last used time for each key
            LAST_USED=$(aws iam get-access-key-last-used --access-key-id "$key_id" --query 'AccessKeyLastUsed.LastUsedDate' --output text)
            echo "Access Key: ${key_id:0:5}...${key_id: -5}"
            echo "  Created: $create_date"
            echo "  Last Used: $LAST_USED"

            # Convert last used date to seconds since epoch
            if [ "$LAST_USED" != "N/A" ] && [ "$LAST_USED" != "None" ]; then
              last_used_time=$(date -d "$LAST_USED" +%s)

              # Check if last used is older than 20 hours
              if [ $last_used_time -lt $TWENTY_HOURS_AGO ]; then
                # Skip deletion if this is the only active key
                if [ $TOTAL_KEYS -le 1 ]; then
                  echo "  Action: Will keep (only active key)"
                else
                  echo "  Action: Will delete (last used more than 20 hours ago)"
                  aws iam delete-access-key --user-name $TARGET_USER --access-key-id "$key_id"
                fi
              else
                echo "  Action: Will keep (last used within 20 hours)"
              fi
            else
              echo "  Action: Will keep (never used)"
            fi
            echo "----------------------------------------"
          done
