name: Deploy (For Staging)
on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to deploy'
        required: true
        default: 'main'
        type: string
      run_collectstatic:
        description: 'Run collectstatic?'
        required: true
        default: 'no'
        type: choice
        options:
          - 'yes'
          - 'no'
      restart_celery:
        description: 'Restart Celery?'
        required: true
        default: 'no'
        type: choice
        options:
          - 'yes'
          - 'no'

jobs:
  deploy:
    runs-on: self-hosted
    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Setup SSH and known_hosts
      run: |
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh
        ssh-keyscan -H ********* >> ~/.ssh/known_hosts
        chmod 644 ~/.ssh/known_hosts

    - name: SSH and git pull on managed node
      env:
        PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
      run: |
        echo "$PRIVATE_KEY" > private_key.pem
        chmod 600 private_key.pem
        ssh -i private_key.pem ubuntu@********* "cd ~/workspace/tpstreams/streams && git fetch origin ${{ github.event.inputs.branch }} && git checkout --force ${{ github.event.inputs.branch }} && git reset --hard origin/${{ github.event.inputs.branch }}"
        rm -f private_key.pem

    - name: Check if collectstatic should run
      if: ${{ github.event.inputs.run_collectstatic == 'yes' }}
      env:
        PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
      run: |
        echo "$PRIVATE_KEY" > private_key.pem
        chmod 600 private_key.pem
        ssh -i private_key.pem ubuntu@********* << 'EOF'
            cd ~/workspace/tpstreams/streams
            ~/workspace/tpstreams/bin/python ./manage.py tailwind build --settings=config.production
            ~/workspace/tpstreams/bin/python ./manage.py collectstatic --settings=config.production --noinput
        EOF
        rm -f private_key.pem

    - name: Restart Gunicorn
      env:
        PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
        SUDO_PASSWORD: ${{ secrets.STAGING_SUDO_PASSWORD }}
      run: |
        echo "$PRIVATE_KEY" > private_key.pem
        chmod 600 private_key.pem
        ssh -i private_key.pem ubuntu@********* 'echo '"$SUDO_PASSWORD"' | sudo -S supervisorctl status gunicorn | sed "s/.*[pid ]\([0-9]\+\)\,.*/\\1/" | xargs kill -HUP'
        rm -f private_key.pem

    - name: Restart Celery
      if: ${{ github.event.inputs.restart_celery == 'yes' }}
      env:
        PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
        SUDO_PASSWORD: ${{ secrets.STAGING_SUDO_PASSWORD }}
      run: |
        echo "$PRIVATE_KEY" > private_key.pem
        chmod 600 private_key.pem
        ssh -i private_key.pem ubuntu@37.27.46.130 << 'EOF'
            echo "$SUDO_PASSWORD" | sudo -S supervisorctl restart celeryd celeryd_purge_assets celeryd_webhook
        EOF
        rm -f private_key.pem
