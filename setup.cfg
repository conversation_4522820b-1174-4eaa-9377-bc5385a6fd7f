[flake8]
max-line-length = 120
exclude = .tox,.git,*/migrations/*,*/static/CACHE/*,docs,node_modules,venv
extend-ignore = E203

[pycodestyle]
max-line-length = 120
exclude = .tox,.git,*/migrations/*,*/static/CACHE/*,docs,node_modules,venv

[isort]
line_length = 88
known_first_party = app,config
multi_line_output = 3
default_section = THIRDPARTY
skip = venv/
skip_glob = **/migrations/*.py
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true

[mypy]
python_version = 3.9
check_untyped_defs = True
ignore_missing_imports = True
warn_unused_ignores = True
warn_redundant_casts = True
warn_unused_configs = True
plugins = mypy_django_plugin.main

[mypy.plugins.django-stubs]
django_settings_module = config.test

[mypy-*.migrations.*]
# Django migrations should not produce any errors:
ignore_errors = True

[coverage:run]
include = app/*
omit = *migrations*, *tests*
plugins =
    django_coverage_plugin
