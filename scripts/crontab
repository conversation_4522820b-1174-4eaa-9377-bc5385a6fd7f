* * * * * /bin/bash /home/<USER>/workspace/tpstreams/streams/scripts/cron_scheduled_tasks.sh >> /var/log/cron.log 2>&1
30 0 * * * /bin/bash /home/<USER>/workspace/tpstreams/streams/scripts/cron_update_organization_bandwidth_usage.sh >> /var/log/cron.log 2>&1
30 6 * * * /bin/bash /home/<USER>/workspace/tpstreams/streams/scripts/cron_update_organization_storage_usage.sh >> /var/log/cron.log 2>&1
30 3 * * * /bin/bash /home/<USER>/workspace/tpstreams/streams/scripts/cron_update_organization_video_analytics.sh >> /var/log/cron.log 2>&1
30 0 * * * /bin/bash /home/<USER>/workspace/tpstreams/streams/scripts/cron_update_organization_live_stream_usage.sh >> /var/log/cron.log 2>&1
30 0 * * * /bin/bash /home/<USER>/workspace/tpstreams/streams/scripts/cron_update_organization_subtitle_generated_minutes.sh >> /var/log/cron.log 2>&1
